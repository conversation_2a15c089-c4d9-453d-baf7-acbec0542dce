"use client"

import { useState } from 'react';

// 定义人员数据类型
export interface Personnel {
  id: string;
  name: string;
  employeeId: string;
  department: string;
  position: string;
  hireDate: string;
  contractType: string;
  contractEnd: string;
  status: string;
  email?: string;
  phone?: string;
  address?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  education?: string;
}

// 初始数据
const initialPersonnel: Personnel[] = [
  {
    id: "1",
    name: "张三",
    employeeId: "EMP001",
    department: "安全管理部",
    position: "安全员",
    hireDate: "2025-01-15",
    contractType: "全职",
    contractEnd: "2026-01-14",
    status: "在职",
    email: "zhang<PERSON>@example.com",
    phone: "13800138000",
    education: "本科"
  },
  {
    id: "2",
    name: "李四",
    employeeId: "EMP002",
    department: "工程管理部",
    position: "工程师",
    hireDate: "2025-01-20",
    contractType: "全职",
    contractEnd: "2026-01-19",
    status: "在职",
    email: "<EMAIL>",
    phone: "13900139000",
    education: "硕士"
  },
  {
    id: "3",
    name: "王五",
    employeeId: "EMP003",
    department: "人事管理部",
    position: "人事专员",
    hireDate: "2025-02-10",
    contractType: "全职",
    contractEnd: "2026-02-09",
    status: "在职",
    email: "<EMAIL>",
    phone: "13700137000",
    education: "本科"
  },
  {
    id: "4",
    name: "赵六",
    employeeId: "EMP004",
    department: "财务管理部",
    position: "财务主管",
    hireDate: "2025-01-01",
    contractType: "全职",
    contractEnd: "2025-04-01",
    status: "离职",
    email: "<EMAIL>",
    phone: "13600136000",
    education: "博士"
  },
  {
    id: "5",
    name: "钱七",
    employeeId: "EMP005",
    department: "工程管理部",
    position: "技术员",
    hireDate: "2025-02-15",
    contractType: "兼职",
    contractEnd: "2025-04-14",
    status: "在职",
    email: "<EMAIL>",
    phone: "13500135000",
    education: "本科"
  }
];

interface FilterOptions {
  search?: string;
  department?: string;
  contractType?: string;
  status?: string;
  dateRange?: [Date | null, Date | null];
}

export function usePersonnelData() {
  const [personnel, setPersonnel] = useState<Personnel[]>(initialPersonnel);
  const [loading, setLoading] = useState(false);

  const filterPersonnel = (options: FilterOptions) => {
    return personnel.filter(person => {
      const matchesSearch = !options.search ||
        person.name.toLowerCase().includes(options.search.toLowerCase()) ||
        person.employeeId.toLowerCase().includes(options.search.toLowerCase()) ||
        person.department.toLowerCase().includes(options.search.toLowerCase()) ||
        person.position.toLowerCase().includes(options.search.toLowerCase());

      const matchesDepartment = !options.department || options.department === 'all' ||
        person.department === options.department;

      const matchesContractType = !options.contractType || options.contractType === 'all' ||
        person.contractType === options.contractType;

      const matchesStatus = !options.status || options.status === 'all' ||
        person.status === options.status;

      const matchesDateRange = !options.dateRange || !options.dateRange[0] || !options.dateRange[1] ||
        (new Date(person.hireDate) >= options.dateRange[0] &&
         new Date(person.hireDate) <= options.dateRange[1]);

      return matchesSearch && matchesDepartment && matchesContractType && matchesStatus && matchesDateRange;
    });
  };

  const addPersonnel = (newPerson: Omit<Personnel, 'id'>) => {
    const person: Personnel = {
      ...newPerson,
      id: Date.now().toString(),
    };
    setPersonnel(prev => [...prev, person]);
  };

  const updatePersonnel = (id: string, updates: Partial<Personnel>) => {
    setPersonnel(prev => prev.map(person =>
      person.id === id ? { ...person, ...updates } : person
    ));
  };

  const deletePersonnel = (id: string) => {
    setPersonnel(prev => prev.filter(person => person.id !== id));
  };

  const batchDeletePersonnel = (ids: string[]) => {
    setPersonnel(prev => prev.filter(person => !ids.includes(person.id)));
  };

  const getPersonnel = (id: string) => {
    return personnel.find(person => person.id === id);
  };

  const getExpiringContracts = (days: number = 30) => {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);

    return personnel.filter(person => {
      const contractEnd = new Date(person.contractEnd);
      return contractEnd >= today && contractEnd <= futureDate;
    });
  };

  return {
    personnel,
    loading,
    filterPersonnel,
    addPersonnel,
    updatePersonnel,
    deletePersonnel,
    batchDeletePersonnel,
    getPersonnel,
    getExpiringContracts,
  };
}