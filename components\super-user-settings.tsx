"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>riangle, Key, RefreshC<PERSON>, Shield, Lock, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { message } from "antd"

export function SuperUserSettings() {
  const [passwordResetEnabled, setPasswordResetEnabled] = useState(true)
  const [emergencyAccessEnabled, setEmergencyAccessEnabled] = useState(true)
  const [twoFactorRequired, setTwoFactorRequired] = useState(true)
  const [sessionTimeout, setSessionTimeout] = useState("30")
  const [loading, setLoading] = useState(false)

  // 处理保存设置
  const handleSave = () => {
    setLoading(true)
    // 模拟保存操作
    setTimeout(() => {
      setLoading(false)
      message.success('设置已保存')
    }, 1000)
  }

  return (
    <div className="space-y-6 p-6 max-w-[1200px] mx-auto">
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">超级用户设置</h2>
          <p className="text-muted-foreground mt-1">配置系统超级用户的权限和安全设置</p>
        </div>
        <Button onClick={handleSave} disabled={loading}>
          {loading ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {loading ? '保存中...' : '保存设置'}
        </Button>
      </div>

      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12">
          <Tabs defaultValue="security" className="w-full">
            <TabsList className="w-full md:w-auto grid grid-cols-3 md:inline-flex h-auto p-1 bg-muted/20 rounded-lg">
              <TabsTrigger value="security" className="flex items-center gap-2 py-2">
                <Shield className="h-4 w-4" />
                <span>安全设置</span>
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center gap-2 py-2">
                <Lock className="h-4 w-4" />
                <span>权限设置</span>
              </TabsTrigger>
              <TabsTrigger value="access" className="flex items-center gap-2 py-2">
                <Users className="h-4 w-4" />
                <span>访问控制</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="security" className="space-y-4 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="shadow-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-primary" />
                      超级用户安全设置
                    </CardTitle>
                    <CardDescription>配置超级用户账户的安全选项</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="space-y-0.5">
                          <Label className="text-base">双因素认证</Label>
                          <p className="text-sm text-muted-foreground">要求超级用户必须使用双因素认证登录系统</p>
                        </div>
                        <Switch checked={twoFactorRequired} onCheckedChange={setTwoFactorRequired} />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="space-y-0.5">
                          <Label className="text-base">密码重置功能</Label>
                          <p className="text-sm text-muted-foreground">允许超级用户重置其他用户的密码</p>
                        </div>
                        <Switch checked={passwordResetEnabled} onCheckedChange={setPasswordResetEnabled} />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="space-y-0.5">
                          <Label className="text-base">紧急访问权限</Label>
                          <p className="text-sm text-muted-foreground">允许超级用户在紧急情况下获取临时的完全访问权限</p>
                        </div>
                        <Switch checked={emergencyAccessEnabled} onCheckedChange={setEmergencyAccessEnabled} />
                      </div>

                      <Separator />

                      <div className="space-y-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                        <Label className="text-base">会话超时时间（分钟）</Label>
                        <p className="text-sm text-muted-foreground mb-2">设置超级用户账户的会话超时时间</p>
                        <Select value={sessionTimeout} onValueChange={setSessionTimeout}>
                          <SelectTrigger className="w-full md:w-[200px]">
                            <SelectValue placeholder="选择超时时间" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="15">15 分钟</SelectItem>
                            <SelectItem value="30">30 分钟</SelectItem>
                            <SelectItem value="60">60 分钟</SelectItem>
                            <SelectItem value="120">120 分钟</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="flex items-center text-sm text-amber-600 bg-amber-50 p-2 rounded-lg w-full">
                      <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span>更改安全设置可能会影响系统安全性，请谨慎操作</span>
                    </div>
                  </CardFooter>
                </Card>

                <Card className="shadow-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Key className="h-5 w-5 text-primary" />
                      密码策略
                    </CardTitle>
                    <CardDescription>配置超级用户的密码复杂度要求</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="min-length">最小密码长度</Label>
                        <Input 
                          id="min-length" 
                          type="number" 
                          defaultValue={12} 
                          min={8} 
                          max={32}
                          className="focus-visible:ring-2 focus-visible:ring-primary" 
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="password-expiry">密码过期天数</Label>
                        <Input 
                          id="password-expiry" 
                          type="number" 
                          defaultValue={90} 
                          min={30} 
                          max={365}
                          className="focus-visible:ring-2 focus-visible:ring-primary" 
                        />
                      </div>
                    </div>

                    <div className="space-y-2 mt-2">
                      <Label>密码复杂度要求</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                        <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                          <Checkbox id="req-uppercase" defaultChecked />
                          <Label htmlFor="req-uppercase">必须包含大写字母</Label>
                        </div>
                        <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                          <Checkbox id="req-lowercase" defaultChecked />
                          <Label htmlFor="req-lowercase">必须包含小写字母</Label>
                        </div>
                        <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                          <Checkbox id="req-numbers" defaultChecked />
                          <Label htmlFor="req-numbers">必须包含数字</Label>
                        </div>
                        <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                          <Checkbox id="req-special" defaultChecked />
                          <Label htmlFor="req-special">必须包含特殊字符</Label>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-4 mt-6">
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5 text-primary" />
                    超级用户权限
                  </CardTitle>
                  <CardDescription>配置超级用户可以执行的操作</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-system-config" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-system-config" className="text-base">
                          系统配置权限
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户修改系统配置和设置</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-user-admin" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-user-admin" className="text-base">
                          用户管理权限
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户创建、修改和删除用户账户</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-role-admin" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-role-admin" className="text-base">
                          角色管理权限
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户创建、修改和删除角色</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-audit-logs" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-audit-logs" className="text-base">
                          审计日志访问
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户查看系统审计日志</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-backup" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-backup" className="text-base">
                          备份与恢复
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户执行系统备份和恢复操作</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <Checkbox id="perm-api-access" defaultChecked />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor="perm-api-access" className="text-base">
                          API访问控制
                        </Label>
                        <p className="text-sm text-muted-foreground">允许超级用户管理API访问令牌和权限</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="access" className="space-y-4 mt-6">
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-primary" />
                    访问控制设置
                  </CardTitle>
                  <CardDescription>配置超级用户的访问限制</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="allowed-ips">允许的IP地址</Label>
                        <p className="text-sm text-muted-foreground mb-2">
                          限制超级用户只能从特定IP地址登录（每行一个IP，留空表示不限制）
                        </p>
                        <Input 
                          id="allowed-ips" 
                          placeholder="例如: ***********" 
                          className="focus-visible:ring-2 focus-visible:ring-primary"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="access-schedule">访问时间限制</Label>
                        <p className="text-sm text-muted-foreground mb-2">限制超级用户只能在特定时间段登录</p>
                        <Select defaultValue="anytime">
                          <SelectTrigger id="access-schedule" className="focus-visible:ring-2 focus-visible:ring-primary">
                            <SelectValue placeholder="选择访问时间" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="anytime">任何时间</SelectItem>
                            <SelectItem value="business-hours">工作时间 (9:00-18:00)</SelectItem>
                            <SelectItem value="custom">自定义时间段</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                        <Checkbox id="require-approval" />
                        <div className="grid gap-1.5 leading-none">
                          <Label htmlFor="require-approval" className="text-base">
                            需要审批
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            超级用户的某些操作需要其他管理员审批
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h4 className="text-sm font-medium mb-2">访问控制提示</h4>
                        <ul className="text-sm text-muted-foreground space-y-2">
                          <li className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                            IP限制可以提高账户安全性
                          </li>
                          <li className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                            建议设置访问时间限制
                          </li>
                          <li className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                            重要操作建议开启审批流程
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

