"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  Package,
  Boxes,
  Truck,
  Warehouse,
  AlertCircle,
  CheckCircle2,
  Clock,
  XCircle,
  BarChart2,
  Printer,
  ArrowUpCircle,
  ArrowDownCircle,
  RefreshCw,
  FileText,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Tabs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'

interface Material {
  id: string
  name: string
  code: string
  category: string
  specification: string
  unit: string
  stock: number
  minStock: number
  maxStock: number
  location: string
  supplier: string
  lastPurchaseDate: string
  nextPurchaseDate: string
  status: string
  turnoverRate: number
  lastUpdateTime?: string
  price?: number
  totalValue?: number
}

interface StockRecord {
  id: string
  materialId: string
  materialName: string
  type: "入库" | "出库"
  quantity: number
  operator: string
  date: string
  remarks?: string
}

export function MaterialManagement() {
  const [isAddMaterialOpen, setIsAddMaterialOpen] = useState(false)
  const [isStockRecordOpen, setIsStockRecordOpen] = useState(false)
  const [isStockInOutOpen, setIsStockInOutOpen] = useState(false)
  const [currentTab, setCurrentTab] = useState("all")
  const [searchText, setSearchText] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null)
  const [selectedRows, setSelectedRows] = useState<string[]>([])

  // 示例数据
  const [materials, setMaterials] = useState<Material[]>([
    {
      id: "1",
      name: "钢筋",
      code: "GJ001",
      category: "建筑材料",
      specification: "Φ12mm",
      unit: "吨",
      stock: 150,
      minStock: 50,
      maxStock: 200,
      location: "A区-01-01",
      supplier: "某某钢材有限公司",
      lastPurchaseDate: "2025-03-10",
      nextPurchaseDate: "2025-04-10",
      status: "正常",
      turnoverRate: 85,
      price: 4800,
      totalValue: 720000,
      lastUpdateTime: "2025-03-18 10:30:00"
    },
    {
      id: "2",
      name: "水泥",
      code: "SN002",
      category: "建筑材料",
      specification: "P.O42.5",
      unit: "吨",
      stock: 80,
      minStock: 100,
      maxStock: 300,
      location: "B区-02-03",
      supplier: "某某水泥厂",
      lastPurchaseDate: "2025-03-05",
      nextPurchaseDate: "2025-03-25",
      status: "库存不足",
      turnoverRate: 92,
    },
    {
      id: "3",
      name: "模板",
      code: "MB003",
      category: "周转材料",
      specification: "1.2m×2.4m",
      unit: "张",
      stock: 200,
      minStock: 100,
      maxStock: 500,
      location: "C区-03-02",
      supplier: "某某模板厂",
      lastPurchaseDate: "2025-02-28",
      nextPurchaseDate: "2025-03-28",
      status: "正常",
      turnoverRate: 78,
    },
    {
      id: "4",
      name: "脚手架",
      code: "JJ004",
      category: "周转材料",
      specification: "Φ48mm",
      unit: "米",
      stock: 1000,
      minStock: 800,
      maxStock: 2000,
      location: "D区-04-01",
      supplier: "某某脚手架厂",
      lastPurchaseDate: "2025-03-01",
      nextPurchaseDate: "2025-04-01",
      status: "正常",
      turnoverRate: 88,
    },
    {
      id: "5",
      name: "混凝土",
      code: "HNT005",
      category: "建筑材料",
      specification: "C30",
      unit: "立方米",
      stock: 50,
      minStock: 100,
      maxStock: 200,
      location: "E区-05-02",
      supplier: "某某混凝土公司",
      lastPurchaseDate: "2025-03-15",
      nextPurchaseDate: "2025-03-25",
      status: "库存不足",
      turnoverRate: 95,
    },
  ])

  // 示例库存记录数据
  const [stockRecords, setStockRecords] = useState<StockRecord[]>([
    {
      id: "1",
      materialId: "1",
      materialName: "钢筋",
      type: "入库",
      quantity: 50,
      operator: "张三",
      date: "2025-03-18 10:30:00",
      remarks: "常规采购入库"
    },
    {
      id: "2",
      materialId: "1",
      materialName: "钢筋",
      type: "出库",
      quantity: 20,
      operator: "李四",
      date: "2025-03-18 14:20:00",
      remarks: "项目领用"
    },
  ])

  // 物资类型统计
  const materialTypeStats = [
    { type: "建筑材料", count: 156, icon: <Package className="h-6 w-6 text-blue-600" /> },
    { type: "周转材料", count: 89, icon: <Boxes className="h-6 w-6 text-green-600" /> },
    { type: "库存不足", count: 12, icon: <AlertCircle className="h-6 w-6 text-red-600" /> },
    { type: "待采购", count: 8, icon: <Truck className="h-6 w-6 text-orange-600" /> },
  ]

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "库存不足":
        return <Badge className="bg-red-500">库存不足</Badge>
      case "待采购":
        return <Badge className="bg-yellow-500">待采购</Badge>
      case "已停用":
        return <Badge className="bg-gray-500">已停用</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理导出
  const handleExport = () => {
    try {
      const exportData = materials.map(material => ({
        '物资编码': material.code,
        '物资名称': material.name,
        '类别': material.category,
        '规格型号': material.specification,
        '单位': material.unit,
        '当前库存': material.stock,
        '最低库存': material.minStock,
        '最高库存': material.maxStock,
        '存放位置': material.location,
        '供应商': material.supplier,
        '状态': material.status,
        '周转率': material.turnoverRate + '%',
        '上次采购日期': material.lastPurchaseDate,
        '下次采购日期': material.nextPurchaseDate,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)
      XLSX.utils.book_append_sheet(wb, ws, '物资清单')
      // 使用2025年的固定日期而不是当前日期
      XLSX.writeFile(wb, `物资清单_2025-03-15.xlsx`)
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  // 处理打印
  const handlePrint = () => {
    const printContent = document.createElement('div')
    printContent.innerHTML = `
      <style>
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .header { margin-bottom: 20px; }
        .footer { margin-top: 20px; text-align: right; }
        @media print {
          button { display: none; }
        }
      </style>
      <div class="header">
        <h2>物资清单</h2>
        <p>打印时间：2025-03-15 10:30:45</p>
      </div>
      <table>
        <thead>
          <tr>
            <th>物资编码</th>
            <th>物资名称</th>
            <th>类别</th>
            <th>规格型号</th>
            <th>单位</th>
            <th>当前库存</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          ${materials.map(material => `
            <tr>
              <td>${material.code}</td>
              <td>${material.name}</td>
              <td>${material.category}</td>
              <td>${material.specification}</td>
              <td>${material.unit}</td>
              <td>${material.stock}</td>
              <td>${material.status}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      <div class="footer">
        <p>总计：${materials.length} 条记录</p>
      </div>
    `

    const printWindow = window.open('', '_blank')
    printWindow?.document.write(printContent.innerHTML)
    printWindow?.document.close()
    printWindow?.print()
  }

  // 处理入库/出库
  const handleStockInOut = (type: "入库" | "出库") => {
    if (!selectedMaterial) return

    // 这里应该调用后端 API 处理入库/出库
    const newRecord: StockRecord = {
      id: Date.now().toString(),
      materialId: selectedMaterial.id,
      materialName: selectedMaterial.name,
      type,
      quantity: 0, // 应该从表单获取
      operator: "当前用户", // 应该从登录用户信息获取
      date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      remarks: ""
    }

    setStockRecords([newRecord, ...stockRecords])
    setIsStockInOutOpen(false)
  }

  // 过滤数据
  const filteredMaterials = materials.filter(material => {
    const matchesSearch =
      material.name.toLowerCase().includes(searchText.toLowerCase()) ||
      material.code.toLowerCase().includes(searchText.toLowerCase())
    const matchesCategory = selectedCategory === "all" || material.category === selectedCategory
    const matchesStatus = selectedStatus === "all" || material.status === selectedStatus
    const matchesTab = currentTab === "all" ||
      (currentTab === "warning" && material.stock <= material.minStock) ||
      (currentTab === "turnover" && material.turnoverRate < 50)
    return matchesSearch && matchesCategory && matchesStatus && matchesTab
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">物资维护和周转材料管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            打印
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          <Dialog open={isAddMaterialOpen} onOpenChange={setIsAddMaterialOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加物资
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加物资</DialogTitle>
                <DialogDescription>记录新的物资信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="material-name">物资名称</Label>
                    <Input id="material-name" placeholder="请输入物资名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-code">物资编码</Label>
                    <Input id="material-code" placeholder="请输入物资编码" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-category">物资类别</Label>
                    <Select>
                      <SelectTrigger id="material-category">
                        <SelectValue placeholder="选择物资类别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="building">建筑材料</SelectItem>
                        <SelectItem value="turnover">周转材料</SelectItem>
                        <SelectItem value="equipment">设备材料</SelectItem>
                        <SelectItem value="other">其他材料</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-specification">规格型号</Label>
                    <Input id="material-specification" placeholder="请输入规格型号" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-unit">计量单位</Label>
                    <Input id="material-unit" placeholder="请输入计量单位" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-stock">当前库存</Label>
                    <Input id="material-stock" type="number" placeholder="请输入当前库存" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-min-stock">最低库存</Label>
                    <Input id="material-min-stock" type="number" placeholder="请输入最低库存" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-max-stock">最高库存</Label>
                    <Input id="material-max-stock" type="number" placeholder="请输入最高库存" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-location">存放位置</Label>
                    <Input id="material-location" placeholder="请输入存放位置" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material-supplier">供应商</Label>
                    <Input id="material-supplier" placeholder="请输入供应商" />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddMaterialOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsAddMaterialOpen(false)}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {materialTypeStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-gray-100 p-3">
                    {stat.icon}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.type}</p>
                    <h3 className="text-2xl font-bold">{stat.count}</h3>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>物资列表</CardTitle>
              <CardDescription>查看和管理所有物资信息</CardDescription>
            </div>
            <Tabs defaultValue="all" value={currentTab} onValueChange={setCurrentTab}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="warning">库存预警</TabsTrigger>
                <TabsTrigger value="turnover">周转率分析</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索物资..."
                  className="pl-8 w-[250px]"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="物资类别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类别</SelectItem>
                  <SelectItem value="建筑材料">建筑材料</SelectItem>
                  <SelectItem value="周转材料">周转材料</SelectItem>
                  <SelectItem value="设备材料">设备材料</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="正常">正常</SelectItem>
                  <SelectItem value="库存不足">库存不足</SelectItem>
                  <SelectItem value="待采购">待采购</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {selectedRows.length > 0 && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <ArrowUpCircle className="h-4 w-4 mr-2" />
                  批量入库
                </Button>
                <Button variant="outline" size="sm">
                  <ArrowDownCircle className="h-4 w-4 mr-2" />
                  批量出库
                </Button>
                <Button variant="outline" size="sm" className="text-red-500">
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </div>
            )}
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">物资信息</TableHead>
                  <TableHead>类别</TableHead>
                  <TableHead>规格</TableHead>
                  <TableHead>库存</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>周转率</TableHead>
                  <TableHead>位置</TableHead>
                  <TableHead>最后更新</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMaterials.map((material) => (
                  <TableRow key={material.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{material.name}</span>
                        <span className="text-sm text-muted-foreground">{material.code}</span>
                      </div>
                    </TableCell>
                    <TableCell>{material.category}</TableCell>
                    <TableCell>{material.specification}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{material.stock} {material.unit}</span>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <span>最低: {material.minStock}</span>
                          <span>|</span>
                          <span>最高: {material.maxStock}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(material.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={material.turnoverRate} className="w-[60px]" />
                        <span>{material.turnoverRate}%</span>
                      </div>
                    </TableCell>
                    <TableCell>{material.location}</TableCell>
                    <TableCell>{material.lastUpdateTime}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setSelectedMaterial(material)}>
                            <FileText className="h-4 w-4 mr-2" />
                            详情
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setIsStockInOutOpen(true)}>
                            <ArrowUpCircle className="h-4 w-4 mr-2" />
                            入库
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setIsStockInOutOpen(true)}>
                            <ArrowDownCircle className="h-4 w-4 mr-2" />
                            出库
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 入库/出库对话框 */}
      <Dialog open={isStockInOutOpen} onOpenChange={setIsStockInOutOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>物资出入库</DialogTitle>
            <DialogDescription>
              记录物资的出入库信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>物资名称</Label>
                <Input value={selectedMaterial?.name} disabled />
              </div>
              <div className="space-y-2">
                <Label>当前库存</Label>
                <Input value={`${selectedMaterial?.stock} ${selectedMaterial?.unit}`} disabled />
              </div>
              <div className="space-y-2">
                <Label>操作类型</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择操作类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="in">入库</SelectItem>
                    <SelectItem value="out">出库</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>数量</Label>
                <Input type="number" placeholder={`请输入数量(${selectedMaterial?.unit})`} />
              </div>
            </div>
            <div className="space-y-2">
              <Label>备注</Label>
              <Textarea placeholder="请输入备注信息" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStockInOutOpen(false)}>
              取消
            </Button>
            <Button onClick={() => handleStockInOut("入库")}>确认</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 库存记录对话框 */}
      <Dialog open={isStockRecordOpen} onOpenChange={setIsStockRecordOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>库存记录</DialogTitle>
            <DialogDescription>
              查看物资的出入库记录
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>操作类型</TableHead>
                  <TableHead>数量</TableHead>
                  <TableHead>操作人</TableHead>
                  <TableHead>操作时间</TableHead>
                  <TableHead>备注</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stockRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      {record.type === "入库" ? (
                        <Badge className="bg-green-500">入库</Badge>
                      ) : (
                        <Badge className="bg-blue-500">出库</Badge>
                      )}
                    </TableCell>
                    <TableCell>{record.quantity}</TableCell>
                    <TableCell>{record.operator}</TableCell>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>{record.remarks}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}