"use client"

import React from "react"
import { useEffect, useRef } from "react"
import * as d3 from "d3"

export interface Node extends d3.SimulationNodeDatum {
  id: string
  name: string
  type: string
  x?: number
  y?: number
}

export interface Edge extends d3.SimulationLinkDatum<Node> {
  sourceId: string
  targetId: string
  relationType: string
}

interface NetworkGraphProps {
  nodes: Node[]
  edges: Edge[]
  onNodeClick?: (node: Node) => void
}

export function NetworkGraph({ nodes, edges, onNodeClick }: NetworkGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)

  useEffect(() => {
    if (!svgRef.current || !nodes.length) return

    // 清除现有内容
    d3.select(svgRef.current).selectAll("*").remove()

    // 设置画布尺寸
    const width = 800
    const height = 400
    const svg = d3.select(svgRef.current)
      .attr("width", width)
      .attr("height", height)

    // 准备数据
    const links = edges.map(edge => ({
      ...edge,
      source: nodes.find(n => n.id === edge.sourceId)!,
      target: nodes.find(n => n.id === edge.targetId)!
    }))

    // 创建力导向图布局
    const simulation = d3.forceSimulation(nodes)
      .force("link", d3.forceLink(links).distance(100))
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(width / 2, height / 2))

    // 绘制连线
    const link = svg.append("g")
      .selectAll("line")
      .data(links)
      .join("line")
      .attr("stroke", "#999")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", 2)

    // 创建节点组
    const nodeGroup = svg.append("g")
      .selectAll<SVGGElement, Node>("g")
      .data(nodes)
      .join("g")

    // 添加拖拽行为
    const drag = d3.drag<SVGGElement, Node>()
      .on("start", (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart()
        d.fx = d.x
        d.fy = d.y
      })
      .on("drag", (event, d) => {
        d.fx = event.x
        d.fy = event.y
      })
      .on("end", (event, d) => {
        if (!event.active) simulation.alphaTarget(0)
        d.fx = null
        d.fy = null
      })

    nodeGroup.call(drag)
      .on("click", (event: MouseEvent, d: Node) => onNodeClick?.(d))

    // 添加节点圆圈
    nodeGroup.append("circle")
      .attr("r", 20)
      .attr("fill", "#69b3a2")

    // 添加节点文本
    nodeGroup.append("text")
      .text(d => d.name)
      .attr("x", 25)
      .attr("y", 5)
      .attr("font-size", "12px")

    // 更新力导向图布局
    simulation.on("tick", () => {
      link
        .attr("x1", d => (d.source as Node).x!)
        .attr("y1", d => (d.source as Node).y!)
        .attr("x2", d => (d.target as Node).x!)
        .attr("y2", d => (d.target as Node).y!)

      nodeGroup
        .attr("transform", d => `translate(${d.x},${d.y})`)
    })

    return () => {
      simulation.stop()
    }
  }, [nodes, edges, onNodeClick])

  return (
    <svg
      ref={svgRef}
      className="w-full h-full"
      style={{ minHeight: "400px" }}
    />
  )
} 