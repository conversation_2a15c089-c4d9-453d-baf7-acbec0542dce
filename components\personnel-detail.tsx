"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Phone, 
  Building, 
  Calendar, 
  GraduationCap, 
  MapPin, 
  Briefcase,
  Clock,
  ChartBar,
  Target,
  Award
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Personnel, usePersonnelData } from "@/hooks/use-personnel-data"
import { Bar, Line, Radar } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartData,
  ChartOptions
} from 'chart.js'

// 注册 ChartJS 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface PersonnelDetailProps {
  id: string
  onBack: () => void
}

export function PersonnelDetail({ id, onBack }: PersonnelDetailProps) {
  const { personnel } = usePersonnelData()
  const [currentPersonnel, setCurrentPersonnel] = useState<Personnel | undefined>(undefined)
  const router = useRouter()

  useEffect(() => {
    const data = personnel.find(p => p.id === id)
    setCurrentPersonnel(data)
  }, [id, personnel])

  // 模拟绩效数据
  const performanceData: ChartData<'line'> = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [
      {
        label: '月度绩效',
        data: [85, 88, 92, 87, 90, 95],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4
      }
    ]
  }

  // 模拟技能评估数据
  const skillsData: ChartData<'radar'> = {
    labels: ['专业技能', '团队协作', '沟通能力', '问题解决', '创新能力', '执行力'],
    datasets: [
      {
        label: '能力评估',
        data: [4.5, 4.2, 4.8, 4.3, 3.9, 4.6],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgb(59, 130, 246)',
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(59, 130, 246)'
      }
    ]
  }

  // 模拟工作时长数据
  const workloadData: ChartData<'bar'> = {
    labels: ['周一', '周二', '周三', '周四', '周五'],
    datasets: [
      {
        label: '工作时长（小时）',
        data: [8.5, 9, 8, 8.5, 7.5],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderRadius: 4
      }
    ]
  }

  // 图表配置
  const lineOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  }

  const radarOptions: ChartOptions<'radar'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      r: {
        beginAtZero: true,
        max: 5,
        ticks: {
          stepSize: 1
        }
      }
    }
  }

  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 12
      }
    }
  }

  if (!currentPersonnel) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="hover:bg-slate-100 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回列表
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1 hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="pb-4">
            <div className="flex justify-center">
              <div className="rounded-full bg-gradient-to-r from-blue-100 to-blue-50 p-8 shadow-inner">
                <User className="h-16 w-16 text-blue-600" />
              </div>
            </div>
            <CardTitle className="text-center mt-6 text-2xl font-bold">{currentPersonnel.name}</CardTitle>
            <CardDescription className="text-center text-base mt-2">{currentPersonnel.position}</CardDescription>
            <div className="flex justify-center mt-4">
              <Badge 
                variant={currentPersonnel.status === "在职" ? "default" : "secondary"}
                className={currentPersonnel.status === "在职" 
                  ? "bg-green-100 text-green-700 hover:bg-green-200 px-4 py-1 text-sm" 
                  : "bg-slate-100 text-slate-700 hover:bg-slate-200 px-4 py-1 text-sm"}
              >
                {currentPersonnel.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 mt-4">
              <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                <Building className="h-5 w-5 mr-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">部门</p>
                  <p className="font-medium">{currentPersonnel.department}</p>
                </div>
              </div>
              <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                <Mail className="h-5 w-5 mr-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">邮箱</p>
                  <p className="font-medium">{currentPersonnel.email || "未设置"}</p>
                </div>
              </div>
              <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                <Phone className="h-5 w-5 mr-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">电话</p>
                  <p className="font-medium">{currentPersonnel.phone || "未设置"}</p>
                </div>
              </div>
              <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                <Clock className="h-5 w-5 mr-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">入职日期</p>
                  <p className="font-medium">{currentPersonnel.hireDate}</p>
                </div>
              </div>
              {currentPersonnel.education && (
                <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                  <GraduationCap className="h-5 w-5 mr-4 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">学历</p>
                    <p className="font-medium">{currentPersonnel.education}</p>
                  </div>
                </div>
              )}
              {currentPersonnel.address && (
                <div className="flex items-center p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-slate-200">
                  <MapPin className="h-5 w-5 mr-4 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">地址</p>
                    <p className="font-medium">{currentPersonnel.address}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="border-b bg-slate-50">
            <CardTitle className="text-lg font-semibold flex items-center">
              <Briefcase className="h-5 w-5 mr-2 text-blue-500" />
              详细信息
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger 
                  value="basic" 
                  className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 transition-all"
                >
                  基本信息
                </TabsTrigger>
                <TabsTrigger 
                  value="work" 
                  className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 transition-all"
                >
                  工作信息
                </TabsTrigger>
                <TabsTrigger 
                  value="contact" 
                  className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 transition-all"
                >
                  联系方式
                </TabsTrigger>
                <TabsTrigger 
                  value="performance" 
                  className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 transition-all"
                >
                  绩效分析
                </TabsTrigger>
              </TabsList>
              <TabsContent value="basic">
                <div className="grid grid-cols-2 gap-6">
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">姓名</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.name}</p>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">状态</h4>
                    </CardHeader>
                    <CardContent>
                      <Badge 
                        variant={currentPersonnel.status === "在职" ? "default" : "secondary"}
                        className={currentPersonnel.status === "在职" 
                          ? "bg-green-100 text-green-700" 
                          : "bg-slate-100 text-slate-700"}
                      >
                        {currentPersonnel.status}
                      </Badge>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">学历</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.education || "未设置"}</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              <TabsContent value="work">
                <div className="grid grid-cols-2 gap-6">
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">部门</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.department}</p>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">职位</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.position}</p>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">入职日期</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.hireDate}</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              <TabsContent value="contact">
                <div className="grid grid-cols-2 gap-6">
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">邮箱</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.email || "未设置"}</p>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">电话</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.phone || "未设置"}</p>
                    </CardContent>
                  </Card>
                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <h4 className="text-sm font-medium text-muted-foreground">地址</h4>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg font-medium">{currentPersonnel.address || "未设置"}</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              <TabsContent value="performance">
                <div className="grid grid-cols-2 gap-6">
                  <Card className="col-span-2 border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <ChartBar className="h-5 w-5 mr-2 text-blue-500" />
                          <h3 className="font-semibold">月度绩效趋势</h3>
                        </div>
                        <Badge className="bg-green-100 text-green-700">良好</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <Line 
                          data={performanceData}
                          options={lineOptions}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center">
                        <Target className="h-5 w-5 mr-2 text-blue-500" />
                        <h3 className="font-semibold">能力评估</h3>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <Radar 
                          data={skillsData}
                          options={radarOptions}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center">
                        <Award className="h-5 w-5 mr-2 text-blue-500" />
                        <h3 className="font-semibold">工作时长统计</h3>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <Bar 
                          data={workloadData}
                          options={barOptions}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 