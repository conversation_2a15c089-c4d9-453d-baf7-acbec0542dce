"use client"

import { useState } from 'react';
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, Row, Col, Statistic, Progress, message, Tooltip, Popconfirm, Badge, Descriptions, Divider } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  WarningOutlined,
  EnvironmentOutlined,
  ProjectOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  FileExcelOutlined,
  PrinterOutlined,
  BarChartOutlined,
  DashboardOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import moment from 'moment';

const { Option } = Select;

interface Material {
  id: string;
  name: string;
  type: string;
  status: string;
  quantity: number;
  unit: string;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
  minQuantity: number;
  maxQuantity: number;
  location: string;
  supplier: string;
  turnoverRate: number;
}

export function MaterialMaintenance() {
  const [materials, setMaterials] = useState<Material[]>([
    {
      id: '1',
      name: '螺丝',
      type: '标准件',
      status: '正常',
      quantity: 1000,
      unit: '个',
      lastMaintenanceDate: '2025-02-14',
      nextMaintenanceDate: '2025-04-14',
      minQuantity: 500,
      maxQuantity: 2000,
      location: 'A区-01-01',
      supplier: '某某五金有限公司',
      turnoverRate: 85,
    },
    {
      id: '2',
      name: '轴承',
      type: '机械件',
      status: '待维护',
      quantity: 50,
      unit: '个',
      lastMaintenanceDate: '2025-01-14',
      nextMaintenanceDate: '2025-03-14',
      minQuantity: 100,
      maxQuantity: 300,
      location: 'B区-02-03',
      supplier: '某某轴承厂',
      turnoverRate: 92,
    },
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // 添加表格loading状态
  const [tableLoading, setTableLoading] = useState(false);

  // 添加批量操作选中项的状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [currentMaterial, setCurrentMaterial] = useState<Material | null>(null);
  const [editForm] = Form.useForm();

  // 统计信息
  const stats = {
    total: materials.length,
    normal: materials.filter(m => m.status === '正常').length,
    pending: materials.filter(m => m.status === '待维护').length,
    repairing: materials.filter(m => m.status === '维修中').length,
  };

  // 添加导出Excel功能
  const handleExportExcel = () => {
    message.success('正在导出Excel文件...');
    // 这里添加导出Excel的具体实现
  };

  // 添加打印功能
  const handlePrint = () => {
    message.success('正在准备打印...');
    // 这里添加打印功能的具体实现
  };

  // 添加刷新数据功能
  const handleRefresh = () => {
    setTableLoading(true);
    message.loading('正在刷新数据...');
    setTimeout(() => {
      setTableLoading(false);
      message.success('数据已刷新');
    }, 1000);
  };

  // 添加批量删除功能
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 项物资吗？`,
      onOk: () => {
        setMaterials(materials.filter(item => !selectedRowKeys.includes(item.id)));
        setSelectedRowKeys([]);
        message.success('批量删除成功');
      }
    });
  };

  // 添加批量更新状态功能
  const handleBatchUpdateStatus = (status: string) => {
    setMaterials(materials.map(item =>
      selectedRowKeys.includes(item.id)
        ? { ...item, status }
        : item
    ));
    setSelectedRowKeys([]);
    message.success(`已将选中项状态更新为${status}`);
  };

  // 修改表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: Material[]) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 查看详情
  const handleView = (material: Material) => {
    setCurrentMaterial(material);
    setIsDetailVisible(true);
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      '正常': { color: 'success', icon: <CheckCircleOutlined /> },
      '待维护': { color: 'warning', icon: <ClockCircleOutlined /> },
      '维修中': { color: 'processing', icon: <SyncOutlined spin /> },
      '报废': { color: 'error', icon: <ExclamationCircleOutlined /> }
    };
    const config = statusConfig[status] || { color: 'default', icon: null };
    return (
      <Tag color={config.color} icon={config.icon}>
        {status}
      </Tag>
    );
  };

  // 获取周转率标签
  const getTurnoverRateTag = (rate: number) => {
    if (rate >= 90) return <Tag color="success">优秀</Tag>;
    if (rate >= 70) return <Tag color="processing">良好</Tag>;
    if (rate >= 50) return <Tag color="warning">一般</Tag>;
    return <Tag color="error">较差</Tag>;
  };

  // 详情模态框
  const renderDetailModal = () => (
    <Modal
      title={
        <div className="flex items-center text-lg">
          <FileTextOutlined className="mr-2 text-blue-500" />
          <span>物资详情</span>
        </div>
      }
      open={isDetailVisible}
      onCancel={() => setIsDetailVisible(false)}
      width={800}
      footer={[
        <Button key="edit" type="primary" onClick={() => {
          setIsDetailVisible(false);
          handleEdit(currentMaterial!);
        }}>
          编辑
        </Button>,
        <Button key="close" onClick={() => setIsDetailVisible(false)}>
          关闭
        </Button>
      ]}
      className="custom-modal"
    >
      {currentMaterial && (
        <div className="p-4">
          <Descriptions
            bordered
            column={2}
            labelStyle={{ fontWeight: 'bold', backgroundColor: '#fafafa' }}
            className="custom-descriptions"
          >
            <Descriptions.Item label="物资名称" span={2}>
              {currentMaterial.name}
            </Descriptions.Item>
            <Descriptions.Item label="物资类型">
              <Tag color="blue">{currentMaterial.type}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="当前状态">
              {getStatusTag(currentMaterial.status)}
            </Descriptions.Item>
            <Descriptions.Item label="库存数量">
              <div className="flex items-center">
                <span className="mr-2">{currentMaterial.quantity} {currentMaterial.unit}</span>
                {currentMaterial.quantity < currentMaterial.minQuantity && (
                  <Tag color="error" icon={<WarningOutlined />}>库存不足</Tag>
                )}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="库存范围">
              {currentMaterial.minQuantity} - {currentMaterial.maxQuantity} {currentMaterial.unit}
            </Descriptions.Item>
          </Descriptions>

          <Divider orientation="left">维护信息</Divider>

          <Descriptions
            bordered
            column={2}
            labelStyle={{ fontWeight: 'bold', backgroundColor: '#fafafa' }}
            className="custom-descriptions"
          >
            <Descriptions.Item label="上次维护日期">
              {currentMaterial.lastMaintenanceDate}
            </Descriptions.Item>
            <Descriptions.Item label="下次维护日期">
              {currentMaterial.nextMaintenanceDate}
            </Descriptions.Item>
            <Descriptions.Item label="存放位置">
              <Space>
                <EnvironmentOutlined className="text-blue-500" />
                {currentMaterial.location}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="供应商">
              {currentMaterial.supplier}
            </Descriptions.Item>
            <Descriptions.Item label="周转率" span={2}>
              <Space align="center">
                <Progress
                  percent={currentMaterial.turnoverRate}
                  size="small"
                  status={currentMaterial.turnoverRate >= 90 ? 'success' : currentMaterial.turnoverRate >= 70 ? 'normal' : 'exception'}
                />
                {getTurnoverRateTag(currentMaterial.turnoverRate)}
              </Space>
            </Descriptions.Item>
          </Descriptions>

          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-500">
              <InfoCircleOutlined className="mr-2" />
              系统提示：该物资{
                currentMaterial.quantity < currentMaterial.minQuantity
                  ? '当前库存低于最小库存限制，建议及时补充。'
                  : '库存状态正常，可以正常使用。'
              }
            </div>
          </div>
        </div>
      )}
    </Modal>
  );

  // 编辑模态框的确认处理
  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      const updatedMaterial = {
        ...currentMaterial,
        ...values,
      };

      setMaterials(materials.map(item =>
        item.id === updatedMaterial.id ? updatedMaterial : item
      ));

      message.success('更新成功');
      setIsModalVisible(false);
      editForm.resetFields();
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const columns = [
    {
      title: '物资名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Material) => (
        <Space>
          <span>{text}</span>
          <Tooltip title={record.quantity < record.minQuantity ? '库存不足，需要补充' : '库存充足'}>
            <Tag icon={record.quantity < record.minQuantity ? <WarningOutlined /> : <CheckCircleOutlined />}
                color={record.quantity < record.minQuantity ? 'red' : 'green'}>
              {record.quantity < record.minQuantity ? '库存不足' : '库存充足'}
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '物资类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === '标准件' ? 'blue' : 'green'}>{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === '正常' ? 'success' :
          status === '待维护' ? 'warning' :
          status === '维修中' ? 'processing' : 'error'
        }>{status}</Tag>
      ),
    },
    {
      title: '库存',
      key: 'quantity',
      render: (record: Material) => (
        <Space direction="vertical" size="small">
          <Progress
            percent={Math.round((record.quantity / record.maxQuantity) * 100)}
            status={record.quantity < record.minQuantity ? 'exception' : 'normal'}
            size="small"
          />
          <span>{record.quantity} {record.unit}</span>
        </Space>
      ),
    },
    {
      title: '周转率',
      dataIndex: 'turnoverRate',
      key: 'turnoverRate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate > 90 ? 'exception' : rate > 80 ? 'normal' : 'success'}
        />
      ),
    },
    {
      title: '存放位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
    },
    {
      title: '上次维护日期',
      dataIndex: 'lastMaintenanceDate',
      key: 'lastMaintenanceDate',
    },
    {
      title: '下次维护日期',
      dataIndex: 'nextMaintenanceDate',
      key: 'nextMaintenanceDate',
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, record: Material) => (
        <Space>
          <Button
            type="link"
            icon={<SearchOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingMaterial(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (material: Material) => {
    setEditingMaterial(material);
    form.setFieldsValue({
      ...material,
      lastMaintenanceDate: moment(material.lastMaintenanceDate, 'YYYY-MM-DD'),
      nextMaintenanceDate: moment(material.nextMaintenanceDate, 'YYYY-MM-DD'),
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个物资吗？',
      onOk: () => {
        setMaterials(materials.filter(material => material.id !== id));
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const formattedValues = {
        ...values,
        lastMaintenanceDate: values.lastMaintenanceDate ? values.lastMaintenanceDate.format('YYYY-MM-DD') : '',
        nextMaintenanceDate: values.nextMaintenanceDate ? values.nextMaintenanceDate.format('YYYY-MM-DD') : '',
      };

      if (editingMaterial) {
        // 编辑现有物资
        setMaterials(materials.map(material =>
          material.id === editingMaterial.id
            ? { ...material, ...formattedValues }
            : material
        ));
      } else {
        // 添加新物资
        const newMaterial: Material = {
          id: Date.now().toString(),
          ...formattedValues,
          turnoverRate: 0,
        };
        setMaterials([...materials, newMaterial]);
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  // 过滤数据
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = selectedType === 'all' || material.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || material.status === selectedStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  // 添加自定义样式常量
  const cardHeaderStyle = {
    background: 'linear-gradient(135deg, #1890ff 0%, #52c41a 100%)',
    padding: '16px 24px',
    borderRadius: '8px 8px 0 0',
    color: '#fff',
    fontWeight: 'bold',
    fontSize: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '-1px'
  };

  const statCardStyle = {
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0,0,0,0.12)'
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* 统计卡片行 */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <DashboardOutlined className="mr-2 text-blue-500" />
                  <span>物资总数</span>
                </div>
              }
              value={stats.total}
              prefix={<InfoCircleOutlined className="text-blue-500" />}
              valueStyle={{ color: '#1890ff', fontWeight: 'bold', fontSize: '24px' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              较上月{stats.total > 100 ? '增长' : '减少'} {Math.abs(stats.total - 100)}%
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <CheckCircleOutlined className="mr-2 text-green-500" />
                  <span>正常物资</span>
                </div>
              }
              value={stats.normal}
              prefix={<CheckCircleOutlined className="text-green-500" />}
              valueStyle={{ color: '#52c41a', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.normal / stats.total) * 100)} size="small" strokeColor="#52c41a" />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <ClockCircleOutlined className="mr-2 text-yellow-500" />
                  <span>待维护</span>
                </div>
              }
              value={stats.pending}
              prefix={<ExclamationCircleOutlined className="text-yellow-500" />}
              valueStyle={{ color: '#faad14', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.pending / stats.total) * 100)} size="small" strokeColor="#faad14" />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <ToolOutlined className="mr-2 text-purple-500" />
                  <span>维修中</span>
                </div>
              }
              value={stats.repairing}
              prefix={<ToolOutlined className="text-purple-500" />}
              valueStyle={{ color: '#722ed1', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.repairing / stats.total) * 100)} size="small" strokeColor="#722ed1" />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card className="rounded-lg shadow-lg">
        <div style={cardHeaderStyle}>
          <Space>
            <BarChartOutlined style={{ fontSize: '20px' }} />
            <span>物资维护管理</span>
          </Space>
          <Space size="middle">
            <Tooltip title="刷新数据">
              <Button
                type="text"
                icon={<ReloadOutlined spin={tableLoading} />}
                onClick={handleRefresh}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Tooltip title="导出Excel">
              <Button
                type="text"
                icon={<FileExcelOutlined />}
                onClick={handleExportExcel}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Tooltip title="打印">
              <Button
                type="text"
                icon={<PrinterOutlined />}
                onClick={handlePrint}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Button
              type="primary"
              ghost
              icon={<FilterOutlined />}
              style={{ borderColor: '#fff', color: '#fff' }}
            >
              筛选
            </Button>
            <Button
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ borderColor: '#fff', color: '#fff' }}
            >
              新增物资
            </Button>
          </Space>
        </div>

        <div className="p-6">
          <Space className="mb-6" direction="vertical" style={{ width: '100%' }}>
            <Row gutter={[16, 16]} justify="space-between" align="middle">
              <Col>
                <Space size="large">
                  <Input
                    placeholder="搜索物资名称"
                    prefix={<SearchOutlined className="text-gray-400" />}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    style={{ width: 240, borderRadius: '6px' }}
                    allowClear
                  />
                  <Select
                    value={selectedType}
                    onChange={setSelectedType}
                    style={{ width: 160, borderRadius: '6px' }}
                    placeholder="选择物资类型"
                  >
                    <Option value="all">所有类型</Option>
                    <Option value="标准件">标准件</Option>
                    <Option value="机械件">机械件</Option>
                    <Option value="电气件">电气件</Option>
                    <Option value="其他">其他</Option>
                  </Select>
                  <Select
                    value={selectedStatus}
                    onChange={setSelectedStatus}
                    style={{ width: 160, borderRadius: '6px' }}
                    placeholder="选择状态"
                  >
                    <Option value="all">所有状态</Option>
                    <Option value="正常">正常</Option>
                    <Option value="待维护">待维护</Option>
                    <Option value="维修中">维修中</Option>
                    <Option value="报废">报废</Option>
                  </Select>
                </Space>
              </Col>
              {selectedRowKeys.length > 0 && (
                <Col>
                  <Space>
                    <Badge count={selectedRowKeys.length}>
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleBatchDelete}
                      >
                        批量删除
                      </Button>
                    </Badge>
                    <Button
                      type="primary"
                      icon={<CheckCircleOutlined />}
                      onClick={() => handleBatchUpdateStatus('正常')}
                    >
                      设为正常
                    </Button>
                  </Space>
                </Col>
              )}
            </Row>
          </Space>

          <Table
            rowSelection={{
              selectedRowKeys,
              onChange: (keys) => setSelectedRowKeys(keys),
              selections: [
                Table.SELECTION_ALL,
                Table.SELECTION_INVERT,
                Table.SELECTION_NONE
              ]
            }}
            columns={columns}
            dataSource={filteredMaterials}
            rowKey="id"
            loading={tableLoading}
            className="custom-table"
            pagination={{
              total: filteredMaterials.length,
              pageSize: 10,
              showTotal: (total: number) => `共 ${total} 条记录`,
              showSizeChanger: true,
              showQuickJumper: true,
              size: 'default',
              className: 'custom-pagination'
            }}
          />
        </div>
      </Card>

      {/* 添加/编辑表单模态框 */}
      <Modal
        title={
          <div className="flex items-center">
            {editingMaterial ? (
              <EditOutlined className="mr-2 text-blue-500" />
            ) : (
              <PlusOutlined className="mr-2 text-green-500" />
            )}
            <span>{editingMaterial ? '编辑物资' : '新增物资'}</span>
          </div>
        }
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
        centered
        maskClosable={false}
        destroyOnClose
        className="custom-modal"
      >
        <Form
          form={form}
          layout="vertical"
          className="pt-4"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="物资名称"
                rules={[{ required: true, message: '请输入物资名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="物资类型"
                rules={[{ required: true, message: '请选择物资类型' }]}
              >
                <Select>
                  <Option value="标准件">标准件</Option>
                  <Option value="机械件">机械件</Option>
                  <Option value="电气件">电气件</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="当前数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请输入单位' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minQuantity"
                label="最低库存"
                rules={[{ required: true, message: '请输入最低库存' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxQuantity"
                label="最高库存"
                rules={[{ required: true, message: '请输入最高库存' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="location"
                label="存放位置"
                rules={[{ required: true, message: '请输入存放位置' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="supplier"
                label="供应商"
                rules={[{ required: true, message: '请输入供应商' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="正常">正常</Option>
                  <Option value="待维护">待维护</Option>
                  <Option value="维修中">维修中</Option>
                  <Option value="报废">报废</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastMaintenanceDate"
                label="上次维护日期"
                rules={[{ required: true, message: '请选择上次维护日期' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="nextMaintenanceDate"
            label="下次维护日期"
            rules={[{ required: true, message: '请选择下次维护日期' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 渲染详情模态框 */}
      {renderDetailModal()}

      <style jsx global>{`
        .custom-table .ant-table {
          border-radius: 8px;
          overflow: hidden;
        }

        .custom-table .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
        }

        .custom-table .ant-table-tbody > tr:hover > td {
          background: #f0f7ff !important;
        }

        .custom-pagination {
          margin-top: 16px;
          padding: 0 8px;
        }

        .custom-modal .ant-modal-content {
          border-radius: 8px;
          overflow: hidden;
        }

        .custom-modal .ant-modal-header {
          border-bottom: 1px solid #f0f0f0;
          padding: 16px 24px;
        }

        .custom-modal .ant-modal-body {
          padding: 24px;
        }

        .custom-modal .ant-modal-footer {
          border-top: 1px solid #f0f0f0;
          padding: 16px 24px;
        }

        .custom-descriptions .ant-descriptions-item-label {
          width: 120px;
        }

        .custom-descriptions .ant-descriptions-item-content {
          padding: 16px;
        }

        .custom-modal .ant-modal-body {
          padding: 0;
        }
      `}</style>
    </div>
  );
}
