/* 洞察容器样式 */
.insightsContainer {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid #eaeaea;
}

.insightsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.insightsHeader h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.insightsTabs {
  display: flex;
  gap: 0.5rem;
}

.insightTab {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #f5f5f5;
}

.insightTab:hover {
  background-color: #e5e5e5;
}

.activeTab {
  background-color: #1677ff;
  color: white;
}

.insightsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insightCard {
  display: flex;
  padding: 0.75rem;
  background-color: #fafafa;
  border-radius: 0.375rem;
  position: relative;
  transition: all 0.2s;
  cursor: pointer;
}

.insightCard:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
}

.insightIcon {
  margin-right: 0.75rem;
  margin-top: 0.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.insightContent {
  flex: 1;
}

.insightContent h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.insightContent p {
  font-size: 0.75rem;
  color: #666;
  margin: 0 0 0.5rem 0;
}

.insightMeta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #888;
}

.insightBadge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.highImportance {
  background-color: #ff4d4f;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
  font-size: 0.625rem;
}

.mediumImportance {
  background-color: #faad14;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
  font-size: 0.625rem;
}

.lowImportance {
  background-color: #1890ff;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
  font-size: 0.625rem;
}

.viewAllInsights {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #f5f5f5;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.viewAllInsights:hover {
  background-color: #e5e5e5;
}

.viewAllIcon {
  margin-left: 0.5rem;
  transition: transform 0.2s;
}

.viewAllInsights:hover .viewAllIcon {
  transform: translateX(3px);
}

/* 智能分析入口卡片样式 */
.analyticsCard {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  height: 100%;
  position: relative;
}

.analyticsCard:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(99, 102, 241, 0.3);
}

/* 适配暗色模式 */
:global(.dark) .analyticsCard {
  background-color: #2c2c2e;
  border-color: #3a3a3c;
}

:global(.dark) .analyticsCard:hover {
  border-color: rgba(129, 140, 248, 0.5);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.5);
}

.cardContent {
  display: flex;
  padding: 1.5rem;
  height: 100%;
}

.cardIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.cardInfo {
  flex: 1;
}

.cardInfo h3 {
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.cardInfo p {
  color: #6b7280;
  margin: 0;
}

/* 适配暗色模式 */
:global(.dark) .cardInfo h3 {
  color: #f3f4f6;
}

:global(.dark) .cardInfo p {
  color: #9ca3af;
}

.analyticsPreview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
  margin-top: 1rem;
}

.analyticsPreviewItem {
  font-size: 0.6875rem;
  font-weight: 500;
  background-color: #f9fafb;
  color: #4b5563;
  padding: 0.35rem 0.75rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  border: 1px solid #f3f4f6;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.analyticsPreviewItem:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

/* 适配暗色模式 */
:global(.dark) .analyticsPreviewItem {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

:global(.dark) .analyticsPreviewItem:hover {
  background-color: #4b5563;
} 