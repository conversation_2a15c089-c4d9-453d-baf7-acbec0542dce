-- 能源管理模块数据库初始化脚本
-- 创建于2025年1月1日
-- 包含能源消耗、能源类型、能源效率等能源管理相关数据

-- 创建能源类型表
CREATE TABLE IF NOT EXISTS `energy_types` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `type` VARCHAR(50) NOT NULL UNIQUE,
  `description` TEXT,
  `unit` VARCHAR(20) NOT NULL,
  `icon` VARCHAR(50),
  `color` VARCHAR(20),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建能源消耗记录表
CREATE TABLE IF NOT EXISTS `energy_consumption` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `date` DATE NOT NULL,
  `type_id` INT NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `location` VARCHAR(100) NOT NULL,
  `consumption` DECIMAL(12,2) NOT NULL,
  `unit` VARCHAR(20) NOT NULL,
  `cost` DECIMAL(12,2) NOT NULL,
  `status` ENUM('正常', '异常') DEFAULT '正常',
  `trend` ENUM('上升', '下降', '平稳') DEFAULT '平稳',
  `manager_id` INT,
  `manager` VARCHAR(50),
  `description` TEXT,
  `efficiency` INT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建能源月度统计表
CREATE TABLE IF NOT EXISTS `energy_monthly_stats` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `year` INT NOT NULL,
  `month` INT NOT NULL,
  `type_id` INT NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `consumption` DECIMAL(12,2) NOT NULL,
  `unit` VARCHAR(20) NOT NULL,
  `cost` DECIMAL(12,2) NOT NULL,
  `efficiency` INT,
  `anomaly` BOOLEAN DEFAULT FALSE,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `year_month_type` (`year`, `month`, `type_id`)
);

-- 创建能源效率评估表
CREATE TABLE IF NOT EXISTS `energy_efficiency` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `type_id` INT NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `location` VARCHAR(100) NOT NULL,
  `assessment_date` DATE NOT NULL,
  `score` INT NOT NULL,
  `baseline` DECIMAL(12,2),
  `actual` DECIMAL(12,2),
  `saving_potential` DECIMAL(12,2),
  `assessor_id` INT,
  `assessor` VARCHAR(50),
  `recommendations` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建能源优化建议表
CREATE TABLE IF NOT EXISTS `energy_optimization` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `title` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `type_id` INT,
  `type` VARCHAR(50),
  `impact` ENUM('low', 'medium', 'high') NOT NULL,
  `implementation_difficulty` ENUM('easy', 'medium', 'hard') NOT NULL,
  `estimated_savings` DECIMAL(12,2),
  `implementation_cost` DECIMAL(12,2),
  `payback_period` INT,
  `status` ENUM('proposed', 'approved', 'in_progress', 'implemented', 'rejected') DEFAULT 'proposed',
  `proposer_id` INT,
  `proposer` VARCHAR(50),
  `approval_date` DATE,
  `implementation_date` DATE,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建能源设备表
CREATE TABLE IF NOT EXISTS `energy_equipment` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `model` VARCHAR(50),
  `serial_number` VARCHAR(50),
  `location` VARCHAR(100) NOT NULL,
  `installation_date` DATE,
  `rated_power` DECIMAL(10,2),
  `power_unit` VARCHAR(10) DEFAULT 'kW',
  `efficiency_rating` VARCHAR(20),
  `status` ENUM('运行中', '停用', '维修中', '报废') DEFAULT '运行中',
  `last_maintenance_date` DATE,
  `next_maintenance_date` DATE,
  `responsible_id` INT,
  `responsible` VARCHAR(50),
  `description` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入能源类型数据
INSERT INTO `energy_types` (`type`, `description`, `unit`, `icon`, `color`) VALUES
('电力', '用于供电的电力资源', 'kWh', 'Zap', 'yellow'),
('天然气', '用于锅炉和加热的天然气资源', 'm³', 'Flame', 'orange'),
('水', '用于生产和生活的水资源', 'm³', 'Droplet', 'blue'),
('蒸汽', '用于加热和动力的蒸汽资源', 'ton', 'Cloud', 'gray'),
('柴油', '用于运输和备用发电的柴油资源', 'L', 'Fuel', 'red'),
('压缩空气', '用于气动工具和设备的压缩空气', 'm³', 'Wind', 'cyan');

-- 插入能源消耗记录数据
INSERT INTO `energy_consumption` (`date`, `type_id`, `type`, `location`, `consumption`, `unit`, `cost`, `status`, `trend`, `manager_id`, `manager`, `efficiency`) VALUES
('2025-03-15', 1, '电力', 'A区生产线', 12500.00, 'kWh', 8750.00, '正常', '平稳', 3, '李四', 85),
('2025-03-14', 1, '电力', 'B区锅炉房', 8500.00, 'kWh', 5950.00, '正常', '下降', 3, '李四', 82),
('2025-03-13', 1, '电力', 'C区加工车间', 15000.00, 'kWh', 10500.00, '正常', '上升', 3, '李四', 78),
('2025-03-15', 2, '天然气', 'B区锅炉房', 3200.00, 'm³', 5120.00, '正常', '平稳', 6, '孙八', 75),
('2025-03-14', 2, '天然气', 'D区食堂', 1500.00, 'm³', 2400.00, '正常', '下降', 6, '孙八', 80),
('2025-03-13', 2, '天然气', 'E区宿舍', 2000.00, 'm³', 3200.00, '异常', '上升', 6, '孙八', 70),
('2025-03-15', 3, '水', 'A区生产线', 500.00, 'm³', 1500.00, '正常', '平稳', 5, '钱七', 65),
('2025-03-14', 3, '水', 'D区办公楼', 200.00, 'm³', 600.00, '正常', '下降', 5, '钱七', 70),
('2025-03-13', 3, '水', 'E区绿化', 300.00, 'm³', 900.00, '正常', '上升', 5, '钱七', 60),
('2025-03-15', 4, '蒸汽', 'A区生产线', 50.00, 'ton', 10000.00, '正常', '平稳', 3, '李四', 80),
('2025-03-14', 4, '蒸汽', 'C区加工车间', 30.00, 'ton', 6000.00, '正常', '下降', 3, '李四', 82),
('2025-03-15', 5, '柴油', '运输车队', 500.00, 'L', 3500.00, '正常', '平稳', 8, '周九', 75),
('2025-03-14', 5, '柴油', '备用发电机', 200.00, 'L', 1400.00, '正常', '下降', 8, '周九', 78),
('2025-03-15', 6, '压缩空气', 'A区生产线', 1000.00, 'm³', 2000.00, '正常', '平稳', 3, '李四', 85),
('2025-03-14', 6, '压缩空气', 'C区加工车间', 800.00, 'm³', 1600.00, '正常', '下降', 3, '李四', 83);

-- 插入能源月度统计数据
INSERT INTO `energy_monthly_stats` (`year`, `month`, `type_id`, `type`, `consumption`, `unit`, `cost`, `efficiency`, `anomaly`) VALUES
(2025, 1, 1, '电力', 350000.00, 'kWh', 245000.00, 83, FALSE),
(2025, 2, 1, '电力', 320000.00, 'kWh', 224000.00, 85, FALSE),
(2025, 3, 1, '电力', 380000.00, 'kWh', 266000.00, 80, TRUE),
(2025, 1, 2, '天然气', 85000.00, 'm³', 136000.00, 78, FALSE),
(2025, 2, 2, '天然气', 90000.00, 'm³', 144000.00, 75, FALSE),
(2025, 3, 2, '天然气', 95000.00, 'm³', 152000.00, 73, TRUE),
(2025, 1, 3, '水', 12000.00, 'm³', 36000.00, 68, FALSE),
(2025, 2, 3, '水', 11500.00, 'm³', 34500.00, 70, FALSE),
(2025, 3, 3, '水', 13000.00, 'm³', 39000.00, 65, FALSE),
(2025, 1, 4, '蒸汽', 1200.00, 'ton', 240000.00, 82, FALSE),
(2025, 2, 4, '蒸汽', 1100.00, 'ton', 220000.00, 83, FALSE),
(2025, 3, 4, '蒸汽', 1300.00, 'ton', 260000.00, 80, FALSE),
(2025, 1, 5, '柴油', 15000.00, 'L', 105000.00, 75, FALSE),
(2025, 2, 5, '柴油', 14000.00, 'L', 98000.00, 77, FALSE),
(2025, 3, 5, '柴油', 16000.00, 'L', 112000.00, 73, FALSE),
(2025, 1, 6, '压缩空气', 25000.00, 'm³', 50000.00, 85, FALSE),
(2025, 2, 6, '压缩空气', 24000.00, 'm³', 48000.00, 86, FALSE),
(2025, 3, 6, '压缩空气', 26000.00, 'm³', 52000.00, 84, FALSE);

-- 插入能源效率评估数据
INSERT INTO `energy_efficiency` (`type_id`, `type`, `location`, `assessment_date`, `score`, `baseline`, `actual`, `saving_potential`, `assessor_id`, `assessor`, `recommendations`) VALUES
(1, '电力', 'A区生产线', '2025-03-01', 85, 14000.00, 12500.00, 1000.00, 3, '李四', '建议优化生产线设备运行时间，避免空载运行；更换高效节能灯具；加强设备维护保养'),
(1, '电力', 'B区锅炉房', '2025-03-01', 82, 9000.00, 8500.00, 500.00, 3, '李四', '建议优化锅炉运行参数；加强保温措施；定期清洗加热表面'),
(1, '电力', 'C区加工车间', '2025-03-01', 78, 16000.00, 15000.00, 1500.00, 3, '李四', '建议更换老旧电机；优化生产工艺；加强员工节能意识培训'),
(2, '天然气', 'B区锅炉房', '2025-03-02', 75, 3500.00, 3200.00, 300.00, 6, '孙八', '建议优化燃烧系统；加强锅炉保温；回收余热'),
(3, '水', 'A区生产线', '2025-03-03', 65, 600.00, 500.00, 100.00, 5, '钱七', '建议循环利用冷却水；修复泄漏点；优化清洗工艺'),
(4, '蒸汽', 'A区生产线', '2025-03-04', 80, 55.00, 50.00, 5.00, 3, '李四', '建议加强管道保温；修复泄漏点；优化蒸汽分配系统'),
(5, '柴油', '运输车队', '2025-03-05', 75, 550.00, 500.00, 50.00, 8, '周九', '建议优化运输路线；加强驾驶员培训；定期维护车辆'),
(6, '压缩空气', 'A区生产线', '2025-03-06', 85, 1100.00, 1000.00, 100.00, 3, '李四', '建议检查并修复泄漏点；优化压缩机运行参数；降低系统压力');

-- 插入能源优化建议数据
INSERT INTO `energy_optimization` (`title`, `description`, `type_id`, `type`, `impact`, `implementation_difficulty`, `estimated_savings`, `implementation_cost`, `payback_period`, `status`, `proposer_id`, `proposer`, `approval_date`, `implementation_date`) VALUES
('更换高效节能灯具', '将A区和C区的传统灯具更换为LED节能灯具，预计可节省30%的照明用电', 1, '电力', 'medium', 'easy', 50000.00, 150000.00, 36, 'approved', 3, '李四', '2025-02-15', NULL),
('优化锅炉运行参数', '调整锅炉运行参数，优化燃烧效率，预计可节省5%的天然气消耗', 2, '天然气', 'medium', 'medium', 80000.00, 20000.00, 3, 'implemented', 6, '孙八', '2025-01-20', '2025-02-10'),
('安装变频器', '为主要电机安装变频器，根据负载需求调整运行频率，预计可节省15%的电力消耗', 1, '电力', 'high', 'medium', 120000.00, 300000.00, 30, 'in_progress', 3, '李四', '2025-02-01', NULL),
('余热回收系统', '安装余热回收系统，利用锅炉排烟余热预热给水，预计可节省8%的天然气消耗', 2, '天然气', 'high', 'hard', 130000.00, 500000.00, 46, 'proposed', 6, '孙八', NULL, NULL),
('水循环利用系统', '建设水循环利用系统，将生产用水处理后循环使用，预计可节省40%的水资源消耗', 3, '水', 'high', 'hard', 150000.00, 600000.00, 48, 'proposed', 5, '钱七', NULL, NULL),
('压缩空气系统优化', '检测并修复压缩空气系统泄漏点，优化管网布局，预计可节省10%的压缩空气消耗', 6, '压缩空气', 'medium', 'medium', 60000.00, 100000.00, 20, 'approved', 3, '李四', '2025-03-01', NULL),
('运输车队管理优化', '优化运输路线，加强驾驶员培训，实施车辆定期维护计划，预计可节省8%的柴油消耗', 5, '柴油', 'medium', 'easy', 90000.00, 50000.00, 7, 'implemented', 8, '周九', '2025-01-15', '2025-02-01');

-- 插入能源设备数据
INSERT INTO `energy_equipment` (`name`, `type`, `model`, `serial_number`, `location`, `installation_date`, `rated_power`, `power_unit`, `efficiency_rating`, `status`, `last_maintenance_date`, `next_maintenance_date`, `responsible_id`, `responsible`) VALUES
('锅炉1号', '天然气锅炉', 'BL-500', 'BL20250001', 'B区锅炉房', '2025-01-10', 500.00, 'kW', 'A级', '运行中', '2025-02-15', '2025-05-15', 6, '孙八'),
('锅炉2号', '天然气锅炉', 'BL-500', 'BL20250002', 'B区锅炉房', '2025-01-10', 500.00, 'kW', 'A级', '运行中', '2025-02-15', '2025-05-15', 6, '孙八'),
('空压机1号', '螺杆式空压机', 'SC-100', 'SC20250001', 'A区空压机房', '2025-01-15', 100.00, 'kW', 'B级', '运行中', '2025-02-20', '2025-05-20', 3, '李四'),
('空压机2号', '螺杆式空压机', 'SC-100', 'SC20250002', 'A区空压机房', '2025-01-15', 100.00, 'kW', 'B级', '运行中', '2025-02-20', '2025-05-20', 3, '李四'),
('变压器1号', '干式变压器', 'DT-1000', 'DT20250001', 'A区配电室', '2025-01-20', 1000.00, 'kVA', 'S13级', '运行中', '2025-02-25', '2025-08-25', 3, '李四'),
('变压器2号', '干式变压器', 'DT-1000', 'DT20250002', 'C区配电室', '2025-01-20', 1000.00, 'kVA', 'S13级', '运行中', '2025-02-25', '2025-08-25', 3, '李四'),
('水泵1号', '离心泵', 'CP-50', 'CP20250001', 'A区水泵房', '2025-01-25', 50.00, 'kW', 'A级', '运行中', '2025-02-28', '2025-05-28', 5, '钱七'),
('水泵2号', '离心泵', 'CP-50', 'CP20250002', 'A区水泵房', '2025-01-25', 50.00, 'kW', 'A级', '运行中', '2025-02-28', '2025-05-28', 5, '钱七'),
('冷却塔1号', '逆流式冷却塔', 'CT-200', 'CT20250001', 'A区屋顶', '2025-02-01', 15.00, 'kW', 'B级', '运行中', '2025-03-01', '2025-06-01', 3, '李四'),
('发电机', '柴油发电机', 'DG-500', 'DG20250001', 'E区发电机房', '2025-02-05', 500.00, 'kW', 'B级', '停用', '2025-03-05', '2025-09-05', 8, '周九');
