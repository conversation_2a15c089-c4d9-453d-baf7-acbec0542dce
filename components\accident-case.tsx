"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  MoreHorizontal,
  Filter,
  FileText,
  AlertTriangle,
  Calendar,
  FileCheck,
  BookOpen,
  Eye,
  BarChart2,
  RefreshCcw,
  Printer
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { MainLayout } from "@/components/main-layout"
import * as XLSX from 'xlsx-js-style'
import { message, Modal, Space, Statistic, Progress, Row, Col, Drawer } from 'antd'

interface Accident {
  id: string;
  title: string;
  type: string;
  level: string;
  location: string;
  date: string;
  time: string;
  casualties: number;
  economicLoss: string;
  status: string;
  responsiblePerson: string;
  investigationStatus: string;
  description: string;
  measures: string;
  disabled: boolean;
}

interface SelectProps {
  value: string;
  onValueChange: (value: string) => void;
}

export function AccidentCase() {
  // 状态管理
  const [isAddAccidentOpen, setIsAddAccidentOpen] = useState(false)
  const [isViewModalVisible, setIsViewModalVisible] = useState(false)
  const [selectedAccident, setSelectedAccident] = useState<Accident | null>(null)
  const [searchText, setSearchText] = useState('')
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedLevels, setSelectedLevels] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [isStatsVisible, setIsStatsVisible] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingAccident, setEditingAccident] = useState<Accident | null>(null)
  const [formData, setFormData] = useState<Partial<Accident>>({})
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [newAccidentData, setNewAccidentData] = useState<Partial<Accident>>({
    title: '',
    type: '',
    level: '',
    location: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    casualties: 0,
    economicLoss: '',
    status: '调查中',
    responsiblePerson: '',
    investigationStatus: '进行中',
    description: '',
    measures: '',
    disabled: false
  })

  // 事故数据
  const [accidents, setAccidents] = useState<Accident[]>([
    {
      id: "1",
      title: "矿区A3瓦斯超限事故",
      type: "瓦斯事故",
      level: "一般事故",
      location: "矿区A3-东部巷道",
      date: "2025-03-15",
      time: "14:30",
      casualties: 0,
      economicLoss: "5万元",
      status: "已结案",
      responsiblePerson: "张三",
      investigationStatus: "已完成",
      description: "由于瓦斯检测不及时，导致局部瓦斯浓度超标。",
      measures: "加强瓦斯检测频率，完善预警机制。",
      disabled: false
    },
    {
      id: "2",
      title: "设备维修触电事故",
      type: "触电事故",
      level: "一般事故",
      location: "变电所",
      date: "2025-03-20",
      time: "09:45",
      casualties: 1,
      economicLoss: "3万元",
      status: "调查中",
      responsiblePerson: "李四",
      investigationStatus: "进行中",
      description: "维修人员在未完全断电的情况下进行设备维修。",
      measures: "强化安全操作规程培训，完善设备维修流程。",
      disabled: false
    },
    {
      id: "3",
      title: "矿区B2顶板冒落事故",
      type: "冒顶事故",
      level: "较大事故",
      location: "矿区B2-北部巷道",
      date: "2025-02-05",
      time: "11:20",
      casualties: 2,
      economicLoss: "20万元",
      status: "已结案",
      responsiblePerson: "王五",
      investigationStatus: "已完成",
      description: "支护不当导致顶板局部冒落。",
      measures: "加强巷道支护质量检查，完善顶板管理制度。",
      disabled: false
    },
    {
      id: "4",
      title: "运输车辆碰撞事故",
      type: "机械事故",
      level: "一般事故",
      location: "地面运输道路",
      date: "2025-03-12",
      time: "16:30",
      casualties: 0,
      economicLoss: "8万元",
      status: "调查中",
      responsiblePerson: "赵六",
      investigationStatus: "进行中",
      description: "车辆转弯时未注意观察，与停放车辆发生碰撞。",
      measures: "加强驾驶员安全教育，优化运输路线规划。",
      disabled: false
    },
    {
      id: "5",
      title: "爆破作业伤人事故",
      type: "爆破事故",
      level: "较大事故",
      location: "爆破区",
      date: "2025-01-18",
      time: "10:15",
      casualties: 1,
      economicLoss: "15万元",
      status: "已结案",
      responsiblePerson: "钱七",
      investigationStatus: "已完成",
      description: "爆破警戒不到位，人员未完全撤离。",
      measures: "严格执行爆破作业规程，强化现场警戒管理。",
      disabled: false
    }
  ])

  // 统计数据
  const statistics = {
    total: accidents.length,
    investigating: accidents.filter(a => a.status === "调查中").length,
    closed: accidents.filter(a => a.status === "已结案").length,
    byType: {
      gas: accidents.filter(a => a.type === "瓦斯事故").length,
      roof: accidents.filter(a => a.type === "冒顶事故").length,
      electric: accidents.filter(a => a.type === "触电事故").length,
      mechanical: accidents.filter(a => a.type === "机械事故").length,
    },
    byLevel: {
      minor: accidents.filter(a => a.level === "一般事故").length,
      major: accidents.filter(a => a.level === "较大事故").length,
      serious: accidents.filter(a => a.level === "重大事故").length,
    },
    casualties: accidents.reduce((sum, acc) => sum + acc.casualties, 0),
  }

  // 处理导出Excel
  const handleExportExcel = () => {
    try {
      const exportData = accidents.map(accident => ({
        '事故标题': accident.title,
        '事故类型': accident.type,
        '事故等级': accident.level,
        '发生地点': accident.location,
        '发生日期': accident.date,
        '发生时间': accident.time,
        '伤亡人数': accident.casualties,
        '经济损失': accident.economicLoss,
        '状态': accident.status,
        '责任人': accident.responsiblePerson,
        '调查状态': accident.investigationStatus,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData, { header: Object.keys(exportData[0]) })

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 事故标题
        { wch: 15 }, // 事故类型
        { wch: 15 }, // 事故等级
        { wch: 20 }, // 发生地点
        { wch: 15 }, // 发生日期
        { wch: 15 }, // 发生时间
        { wch: 10 }, // 伤亡人数
        { wch: 15 }, // 经济损失
        { wch: 10 }, // 状态
        { wch: 15 }, // 责任人
        { wch: 15 }, // 调查状态
      ]
      ws['!cols'] = colWidths

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      }

      // 为表头添加样式
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1"
        if (!ws[address]) continue
        ws[address].s = headerStyle
      }

      XLSX.utils.book_append_sheet(wb, ws, '事故记录')
      XLSX.writeFile(wb, `事故记录_${new Date().toLocaleDateString()}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 处理打印
  const handlePrint = () => {
    window.print()
  }

  // 处理刷新
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  // 处理查看详情
  const handleView = (record: Accident) => {
    setSelectedAccident(record)
    setIsViewModalVisible(true)
  }

  // 处理编辑
  const handleEdit = (record: Accident) => {
    setEditingAccident(record)
    setFormData(record)
    setIsEditModalOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = () => {
    if (!editingAccident || !formData) return

    const updatedAccidents = accidents.map(accident =>
      accident.id === editingAccident.id ? { ...accident, ...formData } : accident
    )
    setAccidents(updatedAccidents)
    setIsEditModalOpen(false)
    setEditingAccident(null)
    setFormData({})
    message.success('修改成功')
  }

  // 处理删除
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条事故记录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newAccidents = accidents.filter(accident => accident.id !== id)
        setAccidents(newAccidents)
        message.success('记录已删除')
      },
    })
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`,
      onOk: () => {
        setAccidents(accidents.filter(accident => !selectedRowKeys.includes(accident.id)))
        setSelectedRowKeys([])
        message.success('批量删除成功')
      },
    })
  }

  // 处理禁用/启用
  const handleToggleDisable = (id: string) => {
    setAccidents(accidents.map(accident =>
      accident.id === id ? { ...accident, disabled: !accident.disabled } : accident
    ))
    message.success(`${accidents.find(a => a.id === id)?.disabled ? '启用' : '禁用'}成功`)
  }

  // 处理添加新事故
  const handleAddAccident = () => {
    const newAccident: Accident = {
      ...newAccidentData as Accident,
      id: (accidents.length + 1).toString(),
    }
    setAccidents([...accidents, newAccident])
    setIsAddModalOpen(false)
    setNewAccidentData({})
    message.success('添加成功')
  }

  // 表格列配置
  interface Column {
    title: string;
    dataIndex?: keyof Accident;
    key: string;
    render?: (value: any, record: Accident) => React.ReactNode;
  }

  const columns: Column[] = [
    {
      title: '事故标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Accident) => (
        <Button variant="link" onClick={() => handleView(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: '事故类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Badge variant={type === '瓦斯事故' ? 'destructive' : 'secondary'}>
          {type}
        </Badge>
      ),
    },
    {
      title: '事故等级',
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: '发生时间',
      dataIndex: 'date',
      key: 'date',
      render: (date: string, record: Accident) => `${date} ${record.time}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge variant={status === '已结案' ? 'default' : 'secondary'}>
          {status}
        </Badge>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Accident) => (
        <Space>
          <Button variant="ghost" size="sm" onClick={() => handleView(record)}>
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => handleEdit(record)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => handleDelete(record.id)}>
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleDisable(record.id)}
          >
            {record.disabled ? '启用' : '禁用'}
          </Button>
        </Space>
      ),
    },
  ]

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: string[]) => {
      setSelectedRowKeys(selectedKeys)
    },
  }

  // 筛选逻辑
  const filteredAccidents = accidents.filter(accident => {
    const matchesSearch =
      accident.title.toLowerCase().includes(searchText.toLowerCase()) ||
      accident.description.toLowerCase().includes(searchText.toLowerCase()) ||
      accident.responsiblePerson.toLowerCase().includes(searchText.toLowerCase()) ||
      accident.location.toLowerCase().includes(searchText.toLowerCase());

    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(accident.type);
    const matchesLevel = selectedLevels.length === 0 || selectedLevels.includes(accident.level);

    return matchesSearch && matchesType && matchesLevel;
  });

  // 更新筛选UI部分
  const renderFilters = () => (
    <div className="flex flex-wrap items-center gap-4 mb-6">
      <div className="flex-1 min-w-[250px]">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索事故标题、描述、责任人、地点..."
            className="pl-8 w-full"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
      </div>

      <Select
        value={selectedTypes.length > 0 ? selectedTypes.join(',') : 'all'}
        onValueChange={(value) => setSelectedTypes(value === 'all' ? [] : value.split(','))}
      >
        <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="事故类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
          <SelectItem value="瓦斯事故">瓦斯事故</SelectItem>
          <SelectItem value="冒顶事故">冒顶事故</SelectItem>
          <SelectItem value="触电事故">触电事故</SelectItem>
          <SelectItem value="机械事故">机械事故</SelectItem>
          <SelectItem value="爆破事故">爆破事故</SelectItem>
                  </SelectContent>
                </Select>

      <Select
        value={selectedLevels.length > 0 ? selectedLevels.join(',') : 'all'}
        onValueChange={(value) => setSelectedLevels(value === 'all' ? [] : value.split(','))}
      >
        <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="事故等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有等级</SelectItem>
          <SelectItem value="一般事故">一般事故</SelectItem>
          <SelectItem value="较大事故">较大事故</SelectItem>
          <SelectItem value="重大事故">重大事故</SelectItem>
          <SelectItem value="特别重大事故">特别重大事故</SelectItem>
                  </SelectContent>
                </Select>

      <Button
        variant="outline"
        onClick={() => {
          setSearchText('');
          setSelectedTypes([]);
          setSelectedLevels([]);
        }}
      >
        <RefreshCcw className="h-4 w-4 mr-2" />
        重置筛选
                </Button>
              </div>
  );

  // 渲染统计抽屉
  const renderStatsDrawer = () => (
    <Drawer
      title={
        <Space>
          <BarChart2 className="h-5 w-5" />
          事故统计分析
        </Space>
      }
      placement="right"
      onClose={() => setIsStatsVisible(false)}
      open={isStatsVisible}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card>
          <Statistic
            title="事故处理率"
            value={((statistics.closed / statistics.total) * 100).toFixed(1)}
            suffix="%"
            prefix={<FileCheck className="h-5 w-5" />}
          />
          <Progress
            percent={Number(((statistics.closed / statistics.total) * 100).toFixed(1))}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </Card>

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <Statistic
                title="总事故数"
                value={statistics.total}
                prefix={<AlertTriangle className="h-5 w-5" />}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="调查中"
                value={statistics.investigating}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="已结案"
                value={statistics.closed}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        <Card title="事故类型分布">
          <Row gutter={[16, 16]}>
            {Object.entries(statistics.byType).map(([type, count]) => (
              <Col span={6} key={type}>
                <Statistic
                  title={type === 'gas' ? '瓦斯事故' :
                         type === 'roof' ? '冒顶事故' :
                         type === 'electric' ? '触电事故' : '机械事故'}
                  value={count}
                  suffix={`/ ${statistics.total}`}
                />
              </Col>
            ))}
          </Row>
        </Card>

        <Card title="事故等级分布">
          <Row gutter={[16, 16]}>
            {Object.entries(statistics.byLevel).map(([level, count]) => (
              <Col span={8} key={level}>
                <Statistic
                  title={level === 'minor' ? '一般事故' :
                         level === 'major' ? '较大事故' : '重大事故'}
                  value={count}
                  valueStyle={{
                    color: level === 'minor' ? '#52c41a' :
                           level === 'major' ? '#faad14' : '#f5222d'
                  }}
                />
              </Col>
            ))}
          </Row>
        </Card>

        <Card>
          <Statistic
            title="伤亡人数"
            value={statistics.casualties}
            valueStyle={{ color: statistics.casualties > 0 ? '#cf1322' : '#52c41a' }}
            prefix={<AlertTriangle className="h-5 w-5" />}
          />
        </Card>
      </Space>
    </Drawer>
  )

  // 编辑表单组件
  const renderEditForm = () => (
    <Drawer
      open={isEditModalOpen}
      onClose={() => setIsEditModalOpen(false)}
      placement="right"
      width={720}
      title={
              <div className="flex items-center gap-2">
          <Edit className="h-5 w-5" />
          <span>编辑事故记录</span>
        </div>
      }
      extra={
        <Space>
          <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
            取消
          </Button>
          <Button onClick={handleSaveEdit}>
            保存
                    </Button>
        </Space>
      }
    >
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
                          <div className="space-y-2">
            <Label>事故标题 <span className="text-red-500">*</span></Label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>事故类型 <span className="text-red-500">*</span></Label>
            <Select
              value={formData.type}
              onValueChange={(value) => setFormData({ ...formData, type: value })}
            >
              <SelectTrigger>
                                <SelectValue placeholder="选择事故类型" />
                              </SelectTrigger>
                              <SelectContent>
                <SelectItem value="瓦斯事故">瓦斯事故</SelectItem>
                <SelectItem value="冒顶事故">冒顶事故</SelectItem>
                <SelectItem value="触电事故">触电事故</SelectItem>
                <SelectItem value="机械事故">机械事故</SelectItem>
                <SelectItem value="爆破事故">爆破事故</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
            <Label>事故等级 <span className="text-red-500">*</span></Label>
            <Select
              value={formData.level}
              onValueChange={(value) => setFormData({ ...formData, level: value })}
            >
              <SelectTrigger>
                                <SelectValue placeholder="选择事故等级" />
                              </SelectTrigger>
                              <SelectContent>
                <SelectItem value="一般事故">一般事故</SelectItem>
                <SelectItem value="较大事故">较大事故</SelectItem>
                <SelectItem value="重大事故">重大事故</SelectItem>
                <SelectItem value="特别重大事故">特别重大事故</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
            <Label>发生地点 <span className="text-red-500">*</span></Label>
            <Input
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>发生日期 <span className="text-red-500">*</span></Label>
            <Input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>发生时间 <span className="text-red-500">*</span></Label>
            <Input
              type="time"
              value={formData.time}
              onChange={(e) => setFormData({ ...formData, time: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>伤亡人数 <span className="text-red-500">*</span></Label>
            <Input
              type="number"
              value={formData.casualties}
              onChange={(e) => setFormData({ ...formData, casualties: parseInt(e.target.value) })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>经济损失 <span className="text-red-500">*</span></Label>
            <Input
              value={formData.economicLoss}
              onChange={(e) => setFormData({ ...formData, economicLoss: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>责任人 <span className="text-red-500">*</span></Label>
            <Input
              value={formData.responsiblePerson}
              onChange={(e) => setFormData({ ...formData, responsiblePerson: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>状态 <span className="text-red-500">*</span></Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData({ ...formData, status: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="调查中">调查中</SelectItem>
                <SelectItem value="已结案">已结案</SelectItem>
              </SelectContent>
            </Select>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="space-y-2">
            <Label>事故描述 <span className="text-red-500">*</span></Label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={4}
              placeholder="请详细描述事故发生的经过..."
            />
          </div>
          <div className="space-y-2">
            <Label>处理措施 <span className="text-red-500">*</span></Label>
            <Textarea
              value={formData.measures}
              onChange={(e) => setFormData({ ...formData, measures: e.target.value })}
              rows={4}
              placeholder="请描述采取的处理措施..."
            />
          </div>
        </div>

        <div className="border-t pt-4">
          <p className="text-sm text-muted-foreground">
            注：带 <span className="text-red-500">*</span> 的字段为必填项
          </p>
        </div>
      </div>
    </Drawer>
  )

  // 添加事故表单组件
  const renderAddForm = () => (
    <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>添加事故记录</DialogTitle>
          <DialogDescription>
            请填写新事故的相关信息
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>事故标题 <span className="text-red-500">*</span></Label>
            <Input
              value={newAccidentData.title}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, title: e.target.value })}
              placeholder="请输入事故标题"
            />
          </div>
          <div className="space-y-2">
            <Label>事故类型 <span className="text-red-500">*</span></Label>
            <Select
              value={newAccidentData.type}
              onValueChange={(value) => setNewAccidentData({ ...newAccidentData, type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择事故类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="瓦斯事故">瓦斯事故</SelectItem>
                <SelectItem value="冒顶事故">冒顶事故</SelectItem>
                <SelectItem value="触电事故">触电事故</SelectItem>
                <SelectItem value="机械事故">机械事故</SelectItem>
                <SelectItem value="爆破事故">爆破事故</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>事故等级 <span className="text-red-500">*</span></Label>
            <Select
              value={newAccidentData.level}
              onValueChange={(value) => setNewAccidentData({ ...newAccidentData, level: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择事故等级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="一般事故">一般事故</SelectItem>
                <SelectItem value="较大事故">较大事故</SelectItem>
                <SelectItem value="重大事故">重大事故</SelectItem>
                <SelectItem value="特别重大事故">特别重大事故</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>发生地点 <span className="text-red-500">*</span></Label>
            <Input
              value={newAccidentData.location}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, location: e.target.value })}
              placeholder="请输入发生地点"
            />
          </div>
          <div className="space-y-2">
            <Label>发生日期 <span className="text-red-500">*</span></Label>
            <Input
              type="date"
              value={newAccidentData.date}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, date: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>发生时间 <span className="text-red-500">*</span></Label>
            <Input
              type="time"
              value={newAccidentData.time}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, time: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>伤亡人数 <span className="text-red-500">*</span></Label>
            <Input
              type="number"
              value={newAccidentData.casualties}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, casualties: parseInt(e.target.value) })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>经济损失 <span className="text-red-500">*</span></Label>
            <Input
              value={newAccidentData.economicLoss}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, economicLoss: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>责任人 <span className="text-red-500">*</span></Label>
            <Input
              value={newAccidentData.responsiblePerson}
              onChange={(e) => setNewAccidentData({ ...newAccidentData, responsiblePerson: e.target.value })}
            />
                          </div>
                          <div className="space-y-2">
            <Label>状态 <span className="text-red-500">*</span></Label>
            <Select
              value={newAccidentData.status}
              onValueChange={(value) => setNewAccidentData({ ...newAccidentData, status: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
                              </SelectTrigger>
                              <SelectContent>
                <SelectItem value="调查中">调查中</SelectItem>
                <SelectItem value="已结案">已结案</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
        <div className="space-y-2 mt-4">
          <Label>事故描述</Label>
          <Textarea
            value={newAccidentData.description}
            onChange={(e) => setNewAccidentData({ ...newAccidentData, description: e.target.value })}
            rows={4}
          />
        </div>
        <div className="space-y-2 mt-4">
          <Label>处理措施</Label>
          <Textarea
            value={newAccidentData.measures}
            onChange={(e) => setNewAccidentData({ ...newAccidentData, measures: e.target.value })}
            rows={4}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                        取消
                      </Button>
          <Button
            onClick={handleAddAccident}
            disabled={!newAccidentData.title || !newAccidentData.type || !newAccidentData.level}
          >
            添加
          </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
  )

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader className="border-b">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-bold">事故与案例</CardTitle>
              <CardDescription>管理和分析安全事故记录</CardDescription>
              </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsStatsVisible(true)}>
                <BarChart2 className="h-4 w-4 mr-2" />
                统计分析
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportExcel}>
                <Download className="h-4 w-4 mr-2" />
                导出Excel
              </Button>
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                打印
              </Button>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCcw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button onClick={() => setIsAddModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                添加事故
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* 统计卡片 - 使用网格布局 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总事故数</p>
                    <h3 className="text-2xl font-bold mt-1">{statistics.total}</h3>
                  </div>
                  <div className="rounded-full bg-red-100 p-3">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Progress
                    percent={100}
                    showInfo={false}
                    strokeColor={{
                      '0%': '#ff4d4f',
                      '100%': '#ff7875',
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">调查中</p>
                    <h3 className="text-2xl font-bold mt-1">{statistics.investigating}</h3>
                  </div>
                  <div className="rounded-full bg-yellow-100 p-3">
                    <FileText className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Progress
                    percent={(statistics.investigating / statistics.total) * 100}
                    showInfo={false}
                    strokeColor={{
                      '0%': '#faad14',
                      '100%': '#ffc53d',
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">已结案</p>
                    <h3 className="text-2xl font-bold mt-1">{statistics.closed}</h3>
                  </div>
                  <div className="rounded-full bg-green-100 p-3">
                    <FileCheck className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Progress
                    percent={(statistics.closed / statistics.total) * 100}
                    showInfo={false}
                    strokeColor={{
                      '0%': '#52c41a',
                      '100%': '#73d13d',
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">伤亡人数</p>
                    <h3 className="text-2xl font-bold mt-1">{statistics.casualties}</h3>
                  </div>
                  <div className="rounded-full bg-purple-100 p-3">
                    <AlertTriangle className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <Progress
                    percent={statistics.casualties > 0 ? 100 : 0}
                    showInfo={false}
                    strokeColor={{
                      '0%': '#722ed1',
                      '100%': '#b37feb',
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <div className="mb-6">
            {renderFilters()}
          </div>

          {/* 批量操作和表格 */}
          <div className="bg-white rounded-lg shadow">
            {selectedRowKeys.length > 0 && (
              <div className="p-4 border-b">
                <Space>
                  <Button variant="destructive" onClick={handleBatchDelete}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                </Space>
              </div>
            )}

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map((column) => (
                      <TableHead key={column.key} className="whitespace-nowrap">
                        {column.title}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAccidents.map((accident) => (
                    <TableRow
                      key={accident.id}
                      className={`
                        ${accident.disabled ? 'opacity-50' : ''}
                        hover:bg-gray-50 transition-colors
                      `}
                    >
                      {columns.map((column) => (
                        <TableCell key={`${accident.id}-${column.key}`} className="whitespace-nowrap">
                          {column.render ?
                            column.render(column.dataIndex ? accident[column.dataIndex] : null, accident) :
                            column.dataIndex ? accident[column.dataIndex] : null
                          }
                      </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 统计抽屉 */}
      {renderStatsDrawer()}

      {/* 查看详情对话框 */}
      <Dialog open={isViewModalVisible} onOpenChange={setIsViewModalVisible}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>事故详情</DialogTitle>
          </DialogHeader>
          {selectedAccident && (
          <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>事故标题</Label>
                  <p className="mt-1">{selectedAccident.title}</p>
                </div>
                <div>
                  <Label>事故类型</Label>
                  <p className="mt-1">{selectedAccident.type}</p>
                </div>
                <div>
                  <Label>事故等级</Label>
                  <p className="mt-1">{selectedAccident.level}</p>
                </div>
                <div>
                  <Label>发生地点</Label>
                  <p className="mt-1">{selectedAccident.location}</p>
                </div>
                <div>
                  <Label>发生时间</Label>
                  <p className="mt-1">{`${selectedAccident.date} ${selectedAccident.time}`}</p>
                </div>
                <div>
                  <Label>责任人</Label>
                  <p className="mt-1">{selectedAccident.responsiblePerson}</p>
                  </div>
                <div>
                  <Label>伤亡人数</Label>
                  <p className="mt-1">{selectedAccident.casualties}</p>
                  </div>
                <div>
                  <Label>经济损失</Label>
                  <p className="mt-1">{selectedAccident.economicLoss}</p>
                </div>
              </div>
              <div>
                <Label>事故描述</Label>
                <p className="mt-1">{selectedAccident.description}</p>
              </div>
              <div>
                <Label>处理措施</Label>
                <p className="mt-1">{selectedAccident.measures}</p>
              </div>
          </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 编辑表单 */}
      {renderEditForm()}

      {/* 添加表单 */}
      {renderAddForm()}
    </div>
  )
}

