"use client"

import { useState } from 'react';
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, Row, Col, Statistic, Progress, message, Tooltip, Popconfirm, Badge, Descriptions, Divider } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  WarningOutlined,
  EnvironmentOutlined,
  ProjectOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  FileExcelOutlined,
  PrinterOutlined,
  FileTextOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import moment from 'moment';

const { Option } = Select;

interface TurnoverMaterial {
  id: string;
  name: string;
  type: string;
  status: string;
  quantity: number;
  unit: string;
  location: string;
  lastCheckDate: string;
  nextCheckDate: string;
  minQuantity: number;
  maxQuantity: number;
  project: string;
  turnoverRate: number;
  maintenanceStatus: string;
}

export function TurnoverMaterialManagement() {
  const [materials, setMaterials] = useState<TurnoverMaterial[]>([
    {
      id: '1',
      name: '钢管',
      type: '周转材料',
      status: '正常',
      quantity: 1000,
      unit: '米',
      location: 'A区-01-01',
      lastCheckDate: '2025-02-14',
      nextCheckDate: '2025-04-14',
      minQuantity: 500,
      maxQuantity: 2000,
      project: '项目A',
      turnoverRate: 85,
      maintenanceStatus: '良好',
    },
    {
      id: '2',
      name: '模板',
      type: '周转材料',
      status: '待检查',
      quantity: 50,
      unit: '块',
      location: 'B区-02-03',
      lastCheckDate: '2025-01-14',
      nextCheckDate: '2025-03-14',
      minQuantity: 100,
      maxQuantity: 300,
      project: '项目B',
      turnoverRate: 92,
      maintenanceStatus: '需要维护',
    },
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingMaterial, setEditingMaterial] = useState<TurnoverMaterial | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedProject, setSelectedProject] = useState('all');

  // 修改选中项的类型
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 统计信息
  const stats = {
    total: materials.length,
    normal: materials.filter(m => m.status === '正常').length,
    pending: materials.filter(m => m.status === '待检查').length,
    repairing: materials.filter(m => m.status === '维修中').length,
  };

  // 添加导出Excel功能
  const handleExportExcel = () => {
    message.success('正在导出Excel文件...');
    // 这里添加导出Excel的具体实现
  };

  // 添加打印功能
  const handlePrint = () => {
    message.success('正在准备打印...');
    // 这里添加打印功能的具体实现
  };

  // 修改刷新数据功能
  const handleRefresh = () => {
    setTableLoading(true);
    message.loading('正在刷新数据...');
    // 模拟数据刷新
    setTimeout(() => {
      setTableLoading(false);
      message.success('数据已刷新');
    }, 1000);
  };

  // 添加批量删除功能
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 项材料吗？`,
      onOk: () => {
        setMaterials(materials.filter(item => !selectedRowKeys.includes(item.id)));
        setSelectedRowKeys([]);
        message.success('批量删除成功');
      }
    });
  };

  // 添加批量更新状态功能
  const handleBatchUpdateStatus = (status: string) => {
    setMaterials(materials.map(item =>
      selectedRowKeys.includes(item.id)
        ? { ...item, status }
        : item
    ));
    setSelectedRowKeys([]);
    message.success(`已将选中项状态更新为${status}`);
  };

  const columns = [
    {
      title: '材料名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TurnoverMaterial) => (
        <Space>
          <span>{text}</span>
          <Tooltip title={record.quantity < record.minQuantity ? '库存不足，需要补充' : '库存充足'}>
            <Tag icon={record.quantity < record.minQuantity ? <WarningOutlined /> : <CheckCircleOutlined />}
                color={record.quantity < record.minQuantity ? 'red' : 'green'}>
              {record.quantity < record.minQuantity ? '库存不足' : '库存充足'}
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === '正常' ? 'success' :
          status === '待检查' ? 'warning' :
          status === '维修中' ? 'processing' : 'error'
        }>{status}</Tag>
      ),
    },
    {
      title: '库存',
      key: 'quantity',
      render: (record: TurnoverMaterial) => (
        <Space direction="vertical" size="small">
          <Progress
            percent={Math.round((record.quantity / record.maxQuantity) * 100)}
            status={record.quantity < record.minQuantity ? 'exception' : 'normal'}
            size="small"
          />
          <span>{record.quantity} {record.unit}</span>
        </Space>
      ),
    },
    {
      title: '周转率',
      dataIndex: 'turnoverRate',
      key: 'turnoverRate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate > 90 ? 'exception' : rate > 80 ? 'normal' : 'success'}
        />
      ),
    },
    {
      title: '维护状态',
      dataIndex: 'maintenanceStatus',
      key: 'maintenanceStatus',
      render: (status: string) => (
        <Tag color={
          status === '良好' ? 'success' :
          status === '需要维护' ? 'warning' :
          status === '维修中' ? 'processing' : 'error'
        }>{status}</Tag>
      ),
    },
    {
      title: '所属项目',
      dataIndex: 'project',
      key: 'project',
    },
    {
      title: '存放位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '上次检查日期',
      dataIndex: 'lastCheckDate',
      key: 'lastCheckDate',
    },
    {
      title: '下次检查日期',
      dataIndex: 'nextCheckDate',
      key: 'nextCheckDate',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TurnoverMaterial) => (
        <Space>
          <Button
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingMaterial(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (material: TurnoverMaterial) => {
    setEditingMaterial(material);
    form.setFieldsValue({
      ...material,
      lastCheckDate: moment(material.lastCheckDate),
      nextCheckDate: moment(material.nextCheckDate)
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个周转材料吗？',
      onOk: () => {
        setMaterials(materials.filter(material => material.id !== id));
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const formattedValues = {
        ...values,
        lastCheckDate: values.lastCheckDate ? moment(values.lastCheckDate).format('YYYY-MM-DD') : '',
        nextCheckDate: values.nextCheckDate ? moment(values.nextCheckDate).format('YYYY-MM-DD') : ''
      };

      if (editingMaterial) {
        setMaterials(materials.map(material =>
          material.id === editingMaterial.id
            ? { ...material, ...formattedValues }
            : material
        ));
      } else {
        const newMaterial: TurnoverMaterial = {
          id: Date.now().toString(),
          ...formattedValues,
          turnoverRate: 0
        };
        setMaterials([...materials, newMaterial]);
      }
      setIsModalVisible(false);
      form.resetFields();
      message.success(editingMaterial ? '编辑成功' : '添加成功');
    });
  };

  // 过滤数据
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || material.status === selectedStatus;
    const matchesProject = selectedProject === 'all' || material.project === selectedProject;
    return matchesSearch && matchesStatus && matchesProject;
  });

  // 修改表格选择配置的类型
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: TurnoverMaterial[]) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 添加表格的loading状态
  const [tableLoading, setTableLoading] = useState(false);

  // 添加详情模态框状态
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [currentMaterial, setCurrentMaterial] = useState<TurnoverMaterial | null>(null);

  // 查看详情
  const handleView = (material: TurnoverMaterial) => {
    setCurrentMaterial(material);
    setIsDetailVisible(true);
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      '正常': { color: 'success', icon: <CheckCircleOutlined /> },
      '待检查': { color: 'warning', icon: <ClockCircleOutlined /> },
      '维修中': { color: 'processing', icon: <SyncOutlined spin /> },
      '报废': { color: 'error', icon: <ExclamationCircleOutlined /> }
    };
    const config = statusConfig[status] || { color: 'default', icon: null };
    return (
      <Tag color={config.color} icon={config.icon}>
        {status}
      </Tag>
    );
  };

  // 获取周转率标签
  const getTurnoverRateTag = (rate: number) => {
    if (rate >= 90) return <Tag color="success">优秀</Tag>;
    if (rate >= 70) return <Tag color="processing">良好</Tag>;
    if (rate >= 50) return <Tag color="warning">一般</Tag>;
    return <Tag color="error">较差</Tag>;
  };

  // 详情模态框
  const renderDetailModal = () => (
    <Modal
      title={
        <div className="flex items-center text-lg">
          <FileTextOutlined className="mr-2 text-blue-500" />
          <span>周转材料详情</span>
        </div>
      }
      open={isDetailVisible}
      onCancel={() => setIsDetailVisible(false)}
      width={800}
      footer={[
        <Button key="edit" type="primary" onClick={() => {
          setIsDetailVisible(false);
          handleEdit(currentMaterial!);
        }}>
          编辑
        </Button>,
        <Button key="close" onClick={() => setIsDetailVisible(false)}>
          关闭
        </Button>
      ]}
      className="custom-modal"
    >
      {currentMaterial && (
        <div className="p-4">
          <Descriptions
            bordered
            column={2}
            labelStyle={{ fontWeight: 'bold', backgroundColor: '#fafafa' }}
            className="custom-descriptions"
          >
            <Descriptions.Item label="材料名称" span={2}>
              {currentMaterial.name}
            </Descriptions.Item>
            <Descriptions.Item label="材料类型">
              <Tag color="blue">{currentMaterial.type}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="当前状态">
              {getStatusTag(currentMaterial.status)}
            </Descriptions.Item>
            <Descriptions.Item label="库存数量">
              <div className="flex items-center">
                <span className="mr-2">{currentMaterial.quantity} {currentMaterial.unit}</span>
                {currentMaterial.quantity < currentMaterial.minQuantity && (
                  <Tag color="error" icon={<WarningOutlined />}>库存不足</Tag>
                )}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="库存范围">
              {currentMaterial.minQuantity} - {currentMaterial.maxQuantity} {currentMaterial.unit}
            </Descriptions.Item>
          </Descriptions>

          <Divider orientation="left">维护信息</Divider>

          <Descriptions
            bordered
            column={2}
            labelStyle={{ fontWeight: 'bold', backgroundColor: '#fafafa' }}
            className="custom-descriptions"
          >
            <Descriptions.Item label="上次检查日期">
              {currentMaterial.lastCheckDate}
            </Descriptions.Item>
            <Descriptions.Item label="下次检查日期">
              {currentMaterial.nextCheckDate}
            </Descriptions.Item>
            <Descriptions.Item label="存放位置">
              <Space>
                <EnvironmentOutlined className="text-blue-500" />
                {currentMaterial.location}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="所属项目">
              {currentMaterial.project}
            </Descriptions.Item>
            <Descriptions.Item label="周转率" span={2}>
              <Space align="center">
                <Progress
                  percent={currentMaterial.turnoverRate}
                  size="small"
                  status={currentMaterial.turnoverRate >= 90 ? 'success' : currentMaterial.turnoverRate >= 70 ? 'normal' : 'exception'}
                />
                {getTurnoverRateTag(currentMaterial.turnoverRate)}
              </Space>
            </Descriptions.Item>
          </Descriptions>

          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-500">
              <InfoCircleOutlined className="mr-2" />
              系统提示：该材料{
                currentMaterial.quantity < currentMaterial.minQuantity
                  ? '当前库存低于最小库存限制，建议及时补充。'
                  : '库存状态正常，可以正常使用。'
              }
            </div>
          </div>
        </div>
      )}
    </Modal>
  );

  // 添加自定义样式常量
  const cardHeaderStyle = {
    background: 'linear-gradient(135deg, #1890ff 0%, #52c41a 100%)',
    padding: '16px 24px',
    borderRadius: '8px 8px 0 0',
    color: '#fff',
    fontWeight: 'bold',
    fontSize: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '-1px'
  };

  return (
    <div className="p-6 space-y-6">
      {/* 统计卡片行 */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <BarChartOutlined className="mr-2 text-blue-500" />
                  <span>材料总数</span>
                </div>
              }
              value={stats.total}
              prefix={<InfoCircleOutlined className="text-blue-500" />}
              valueStyle={{ color: '#1890ff', fontWeight: 'bold', fontSize: '24px' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              较上月{stats.total > 100 ? '增长' : '减少'} {Math.abs(stats.total - 100)}%
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <CheckCircleOutlined className="mr-2 text-green-500" />
                  <span>正常材料</span>
                </div>
              }
              value={stats.normal}
              prefix={<CheckCircleOutlined className="text-green-500" />}
              valueStyle={{ color: '#52c41a', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.normal / stats.total) * 100)} size="small" strokeColor="#52c41a" />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <ClockCircleOutlined className="mr-2 text-yellow-500" />
                  <span>待检查</span>
                </div>
              }
              value={stats.pending}
              prefix={<ExclamationCircleOutlined className="text-yellow-500" />}
              valueStyle={{ color: '#faad14', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.pending / stats.total) * 100)} size="small" strokeColor="#faad14" />
          </Card>
        </Col>
        <Col span={6}>
          <Card hoverable className="rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <Statistic
              title={
                <div className="flex items-center text-gray-600 mb-2">
                  <ToolOutlined className="mr-2 text-purple-500" />
                  <span>维修中</span>
                </div>
              }
              value={stats.repairing}
              prefix={<ToolOutlined className="text-purple-500" />}
              valueStyle={{ color: '#722ed1', fontWeight: 'bold', fontSize: '24px' }}
            />
            <Progress percent={Math.round((stats.repairing / stats.total) * 100)} size="small" strokeColor="#722ed1" />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card className="rounded-lg shadow-lg">
        <div style={cardHeaderStyle}>
          <Space>
            <BarChartOutlined style={{ fontSize: '20px' }} />
            <span>周转材料管理</span>
          </Space>
          <Space size="middle">
            <Tooltip title="刷新数据">
              <Button
                type="text"
                icon={<ReloadOutlined spin={tableLoading} />}
                onClick={handleRefresh}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Tooltip title="导出Excel">
              <Button
                type="text"
                icon={<FileExcelOutlined />}
                onClick={handleExportExcel}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Tooltip title="打印">
              <Button
                type="text"
                icon={<PrinterOutlined />}
                onClick={handlePrint}
                style={{ color: '#fff' }}
              />
            </Tooltip>
            <Button
              type="primary"
              ghost
              icon={<FilterOutlined />}
              style={{ borderColor: '#fff', color: '#fff' }}
            >
              筛选
            </Button>
            <Button
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ borderColor: '#fff', color: '#fff' }}
            >
              新增材料
            </Button>
          </Space>
        </div>

        <div className="p-6">
          <Space className="mb-6" direction="vertical" style={{ width: '100%' }}>
            <Row gutter={[16, 16]} justify="space-between" align="middle">
              <Col>
                <Space size="large">
                  <Input
                    placeholder="搜索材料名称"
                    prefix={<SearchOutlined className="text-gray-400" />}
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    style={{ width: 240, borderRadius: '6px' }}
                    allowClear
                  />
                  <Select
                    value={selectedStatus}
                    onChange={setSelectedStatus}
                    style={{ width: 160, borderRadius: '6px' }}
                    placeholder="选择状态"
                  >
                    <Option value="all">所有状态</Option>
                    <Option value="正常">正常</Option>
                    <Option value="待检查">待检查</Option>
                    <Option value="维修中">维修中</Option>
                    <Option value="报废">报废</Option>
                  </Select>
                  <Select
                    value={selectedProject}
                    onChange={setSelectedProject}
                    style={{ width: 160, borderRadius: '6px' }}
                    placeholder="选择项目"
                  >
                    <Option value="all">所有项目</Option>
                    <Option value="项目A">项目A</Option>
                    <Option value="项目B">项目B</Option>
                    <Option value="项目C">项目C</Option>
                  </Select>
                </Space>
              </Col>
              {selectedRowKeys.length > 0 && (
                <Col>
                  <Space>
                    <Badge count={selectedRowKeys.length}>
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleBatchDelete}
                      >
                        批量删除
                      </Button>
                    </Badge>
                    <Button
                      type="primary"
                      icon={<CheckCircleOutlined />}
                      onClick={() => handleBatchUpdateStatus('正常')}
                    >
                      设为正常
                    </Button>
                  </Space>
                </Col>
              )}
            </Row>
          </Space>

          <Table
            rowSelection={{
              selectedRowKeys,
              onChange: (keys) => setSelectedRowKeys(keys),
              selections: [
                Table.SELECTION_ALL,
                Table.SELECTION_INVERT,
                Table.SELECTION_NONE
              ]
            }}
            columns={columns}
            dataSource={filteredMaterials}
            rowKey="id"
            loading={tableLoading}
            className="custom-table"
            pagination={{
              total: filteredMaterials.length,
              pageSize: 10,
              showTotal: (total: number) => `共 ${total} 条记录`,
              showSizeChanger: true,
              showQuickJumper: true,
              size: 'default',
              className: 'custom-pagination'
            }}
          />
        </div>
      </Card>

      {/* 添加/编辑表单模态框 */}
      <Modal
        title={
          <div className="flex items-center">
            {editingMaterial ? (
              <EditOutlined className="mr-2 text-blue-500" />
            ) : (
              <PlusOutlined className="mr-2 text-green-500" />
            )}
            <span>{editingMaterial ? '编辑周转材料' : '新增周转材料'}</span>
          </div>
        }
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
        centered
        maskClosable={false}
        destroyOnClose
        className="custom-modal"
      >
        <Form
          form={form}
          layout="vertical"
          className="pt-4"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="材料名称"
                rules={[{ required: true, message: '请输入材料名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="材料类型"
                rules={[{ required: true, message: '请选择材料类型' }]}
              >
                <Select>
                  <Option value="周转材料">周转材料</Option>
                  <Option value="临时材料">临时材料</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="当前数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请输入单位' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minQuantity"
                label="最低库存"
                rules={[{ required: true, message: '请输入最低库存' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxQuantity"
                label="最高库存"
                rules={[{ required: true, message: '请输入最高库存' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="location"
                label="存放位置"
                rules={[{ required: true, message: '请输入存放位置' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="project"
                label="所属项目"
                rules={[{ required: true, message: '请选择所属项目' }]}
              >
                <Select>
                  <Option value="项目A">项目A</Option>
                  <Option value="项目B">项目B</Option>
                  <Option value="项目C">项目C</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="正常">正常</Option>
                  <Option value="待检查">待检查</Option>
                  <Option value="维修中">维修中</Option>
                  <Option value="报废">报废</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maintenanceStatus"
                label="维护状态"
                rules={[{ required: true, message: '请选择维护状态' }]}
              >
                <Select>
                  <Option value="良好">良好</Option>
                  <Option value="需要维护">需要维护</Option>
                  <Option value="维修中">维修中</Option>
                  <Option value="报废">报废</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="lastCheckDate"
                label="上次检查日期"
                rules={[{ required: true, message: '请选择上次检查日期' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nextCheckDate"
                label="下次检查日期"
                rules={[{ required: true, message: '请选择下次检查日期' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 渲染详情模态框 */}
      {renderDetailModal()}

      <style jsx global>{`
        .custom-table .ant-table {
          border-radius: 8px;
          overflow: hidden;
        }

        .custom-table .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
        }

        .custom-table .ant-table-tbody > tr:hover > td {
          background: #f0f7ff !important;
        }

        .custom-pagination {
          margin-top: 16px;
          padding: 0 8px;
        }

        .custom-modal .ant-modal-content {
          border-radius: 8px;
          overflow: hidden;
        }

        .custom-modal .ant-modal-header {
          border-bottom: 1px solid #f0f0f0;
          padding: 16px 24px;
        }

        .custom-modal .ant-modal-body {
          padding: 24px;
        }

        .custom-modal .ant-modal-footer {
          border-top: 1px solid #f0f0f0;
          padding: 16px 24px;
        }

        .custom-descriptions .ant-descriptions-item-label {
          width: 120px;
        }

        .custom-descriptions .ant-descriptions-item-content {
          padding: 16px;
        }

        .custom-modal .ant-modal-body {
          padding: 0;
        }
      `}</style>
    </div>
  );
}
