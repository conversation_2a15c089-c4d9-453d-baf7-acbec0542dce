"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, Calendar, User, Building, FileText, Edit, CheckCircle, AlertTriangle, MapPin } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SafetyCheck, useSafetyCheckData } from "@/hooks/use-safety-check-data"

interface SafetyCheckDetailProps {
  id: string
  onBack: () => void
}

export function SafetyCheckDetail({ id, onBack }: SafetyCheckDetailProps) {
  const { getSafetyCheck, updateSafetyCheck } = useSafetyCheckData()
  const [safetyCheck, setSafetyCheck] = useState<SafetyCheck | undefined>(undefined)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [formData, setFormData] = useState<Partial<SafetyCheck>>({})

  useEffect(() => {
    const data = getSafetyCheck(id)
    setSafetyCheck(data)
    if (data) {
      setFormData(data)
    }
  }, [id, getSafetyCheck])

  if (!safetyCheck) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>加载中...</p>
      </div>
    )
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id.replace('edit-', '')]: value
    }))
  }

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    if (safetyCheck) {
      // 处理检查项目，将文本转换为数组
      if (typeof formData.items === 'string') {
        formData.items = (formData.items as string).split('\n').filter(item => item.trim() !== '')
      }
      
      updateSafetyCheck(safetyCheck.id, formData)
      setSafetyCheck({...safetyCheck, ...formData})
      setIsEditDialogOpen(false)
    }
  }

  // 将检查项目数组转换为文本，用于编辑
  const itemsText = Array.isArray(safetyCheck.items) 
    ? safetyCheck.items.join('\n') 
    : safetyCheck.items || ''

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回列表
        </Button>
        <Button onClick={() => setIsEditDialogOpen(true)}>
          <Edit className="h-4 w-4 mr-2" />
          编辑信息
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>{safetyCheck.name}</CardTitle>
            <CardDescription>{safetyCheck.type}</CardDescription>
            <div className="mt-2">
              <Badge
                variant={
                  safetyCheck.status === "已完成" ? "default" : 
                  safetyCheck.status === "进行中" ? "secondary" : 
                  "outline"
                }
              >
                {safetyCheck.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-3 text-muted-foreground" />
                <span>检查日期: {safetyCheck.date}</span>
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 mr-3 text-muted-foreground" />
                <span>检查人员: {safetyCheck.inspector}</span>
              </div>
              <div className="flex items-center">
                <Building className="h-4 w-4 mr-3 text-muted-foreground" />
                <span>负责部门: {safetyCheck.department}</span>
              </div>
              {safetyCheck.location && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-3 text-muted-foreground" />
                  <span>检查地点: {safetyCheck.location}</span>
                </div>
              )}
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-3 text-muted-foreground" />
                <span>发现问题: {safetyCheck.issues} 项</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>检查详情</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="details">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">检查内容</TabsTrigger>
                <TabsTrigger value="issues">问题记录</TabsTrigger>
              </TabsList>
              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">检查项目</h4>
                    {safetyCheck.items && Array.isArray(safetyCheck.items) && safetyCheck.items.length > 0 ? (
                      <ul className="list-disc pl-5 space-y-1">
                        {safetyCheck.items.map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-muted-foreground">暂无检查项目记录</p>
                    )}
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">检查说明</h4>
                    <p>{safetyCheck.description || "暂无检查说明"}</p>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="issues" className="space-y-4 mt-4">
                {safetyCheck.issues > 0 ? (
                  <div className="space-y-4">
                    <div className="rounded-md border p-4">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium">安全隐患1</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            设备老化，存在安全隐患
                          </p>
                        </div>
                      </div>
                    </div>
                    {safetyCheck.issues > 1 && (
                      <div className="rounded-md border p-4">
                        <div className="flex items-start gap-3">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                          <div>
                            <h4 className="font-medium">安全隐患2</h4>
                            <p className="text-sm text-muted-foreground mt-1">
                              消防通道堵塞，需要立即清理
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                    <p className="text-muted-foreground">未发现安全隐患</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑安全检查</DialogTitle>
            <DialogDescription>修改安全检查的详细信息</DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="details">检查详情</TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">检查名称</Label>
                  <Input 
                    id="edit-name" 
                    value={formData.name || ''} 
                    onChange={handleInputChange} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-type">检查类型</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger id="edit-type">
                      <SelectValue placeholder="选择检查类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="例行检查">例行检查</SelectItem>
                      <SelectItem value="专项检查">专项检查</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-department">负责部门</Label>
                  <Select 
                    value={formData.department} 
                    onValueChange={(value) => handleSelectChange('department', value)}
                  >
                    <SelectTrigger id="edit-department">
                      <SelectValue placeholder="选择负责部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="设备管理部">设备管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-inspector">检查人员</Label>
                  <Input 
                    id="edit-inspector" 
                    value={formData.inspector || ''} 
                    onChange={handleInputChange} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-date">检查日期</Label>
                  <Input 
                    id="edit-date" 
                    type="date" 
                    value={formData.date || ''} 
                    onChange={handleInputChange} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-status">检查状态</Label>
                  <Select 
                    value={formData.status} 
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger id="edit-status">
                      <SelectValue placeholder="选择检查状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="计划中">计划中</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-issues">发现问题数</Label>
                  <Input 
                    id="edit-issues" 
                    type="number" 
                    value={formData.issues?.toString() || '0'} 
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0
                      setFormData(prev => ({...prev, issues: value}))
                    }} 
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="details" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-location">检查地点</Label>
                  <Input 
                    id="edit-location" 
                    value={formData.location || ''} 
                    onChange={handleInputChange} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-items">检查项目（每行一项）</Label>
                  <Textarea 
                    id="edit-items" 
                    value={typeof formData.items === 'string' ? formData.items : itemsText} 
                    onChange={handleInputChange} 
                    rows={4} 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description">检查说明</Label>
                  <Textarea 
                    id="edit-description" 
                    value={formData.description || ''} 
                    onChange={handleInputChange} 
                    rows={3} 
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 