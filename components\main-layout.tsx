"use client"

import { useState, useEffect, useRef } from "react"
import { Sidebar } from "./sidebar"
import { <PERSON><PERSON> } from "./header"
import { ScrollArea } from "./ui/scroll-area"
import { useTheme } from "@/contexts/theme-context"

interface MainLayoutProps {
  children: React.ReactNode
}

// 添加粒子背景组件
function ParticleBackground({ theme }: { theme: "light" | "dark" }) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    let particles: Particle[] = []
    let animationFrameId: number
    
    // 调整画布大小以匹配窗口
    const resizeCanvas = () => {
      if (canvas) {
        canvas.width = window.innerWidth
        canvas.height = window.innerHeight
      }
    }
    
    window.addEventListener('resize', resizeCanvas)
    resizeCanvas()
    
    // 粒子类
    class Particle {
      x: number
      y: number
      size: number
      speedX: number
      speedY: number
      color: string
      
      constructor() {
        this.x = Math.random() * (canvas ? canvas.width : window.innerWidth)
        this.y = Math.random() * (canvas ? canvas.height : window.innerHeight)
        this.size = Math.random() * 3 + 1
        this.speedX = Math.random() * 0.5 - 0.25
        this.speedY = Math.random() * 0.5 - 0.25
        this.color = theme === "dark" 
          ? `rgba(255, 255, 255, ${Math.random() * 0.1 + 0.05})` 
          : `rgba(0, 0, 0, ${Math.random() * 0.07 + 0.03})`
      }
      
      update() {
        this.x += this.speedX
        this.y += this.speedY
        
        // 边界处理
        const width = canvas ? canvas.width : window.innerWidth
        const height = canvas ? canvas.height : window.innerHeight
        
        if (this.x < 0 || this.x > width) this.speedX *= -1
        if (this.y < 0 || this.y > height) this.speedY *= -1
      }
      
      draw() {
        if (!ctx) return
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
      }
    }
    
    // 初始化粒子
    const initParticles = () => {
      particles = []
      const particleCount = Math.min(100, Math.floor((canvas.width * canvas.height) / 10000))
      
      for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle())
      }
    }
    
    initParticles()
    
    // 粒子动画
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      particles.forEach(particle => {
        particle.update()
        particle.draw()
      })
      
      // 绘制粒子之间的连线
      connectParticles()
      
      animationFrameId = requestAnimationFrame(animate)
    }
    
    // 连接附近的粒子
    const connectParticles = () => {
      if (!ctx) return
      
      const maxDistance = canvas.width > 1000 ? 100 : 70 
      
      for (let a = 0; a < particles.length; a++) {
        for (let b = a; b < particles.length; b++) {
          const dx = particles[a].x - particles[b].x
          const dy = particles[a].y - particles[b].y
          const distance = Math.sqrt(dx * dx + dy * dy)
          
          if (distance < maxDistance) {
            const opacity = theme === "dark" 
              ? 0.2 * (1 - distance / maxDistance)
              : 0.1 * (1 - distance / maxDistance)
              
            ctx.strokeStyle = theme === "dark"
              ? `rgba(255, 255, 255, ${opacity})`
              : `rgba(0, 0, 0, ${opacity})`
              
            ctx.lineWidth = 1
            ctx.beginPath()
            ctx.moveTo(particles[a].x, particles[a].y)
            ctx.lineTo(particles[b].x, particles[b].y)
            ctx.stroke()
          }
        }
      }
    }
    
    animate()
    
    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationFrameId)
    }
  }, [theme])
  
  return (
    <canvas 
      ref={canvasRef} 
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: 0.7 }}
    />
  )
}

export function MainLayout({ children }: MainLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)
  const { themeMode } = useTheme()

  // 获取侧边栏状态
  useEffect(() => {
    // 读取侧边栏状态
    const savedSidebarState = localStorage.getItem("sidebar-collapsed")
    if (savedSidebarState !== null) {
      setSidebarCollapsed(savedSidebarState === "true")
    }
    
    // 添加页面加载效果
    setTimeout(() => {
      setIsLoaded(true)
    }, 100)

    // 添加滚动检测
    const handleScroll = () => {
      if (contentRef.current) {
        setIsScrolled(contentRef.current.scrollTop > 10)
      }
    }

    const currentRef = contentRef.current
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  // 切换侧边栏
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed
    setSidebarCollapsed(newState)
    localStorage.setItem("sidebar-collapsed", String(newState))
  }

  return (
    <div className={`flex h-screen transition-all duration-500 ${
      themeMode === "dark" 
        ? "bg-gradient-to-br from-[#141414] via-[#1c1c1e] to-[#242428] text-white" 
        : "bg-gradient-to-br from-[#f0f0f5] via-[#f5f5f7] to-[#ffffff] text-[#1d1d1f]"
    } ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
      {/* 粒子背景 */}
      <ParticleBackground theme={themeMode as "light" | "dark"} />

      <Sidebar collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div className={`flex flex-col flex-1 w-0 transition-all duration-300 ease-in-out ${
        sidebarCollapsed ? 'ml-0' : 'ml-0 md:ml-4'
      }`}>
        <Header 
          toggleSidebar={toggleSidebar} 
          sidebarCollapsed={sidebarCollapsed}
        />
        <div className="flex-1 relative overflow-hidden">
          {/* 内容顶部阴影 - 仅在滚动时显示 */}
          <div className={`absolute top-0 left-0 right-0 h-4 z-10 transition-opacity duration-300 pointer-events-none ${
            isScrolled 
              ? 'opacity-70' 
              : 'opacity-0'
          } ${
            themeMode === "dark" 
              ? 'bg-gradient-to-b from-[#1a1a1c] to-transparent' 
              : 'bg-gradient-to-b from-[#f0f0f5] to-transparent'
          }`}></div>
          
          {/* 主容器背景 */}
          <div className={`transition-all duration-300 ${
            themeMode === "dark" 
              ? "bg-[#1a1a1c]/50" 
              : "bg-white/50"
          } backdrop-blur-sm absolute inset-0 -z-10 rounded-tl-2xl shadow-lg`}></div>
          
          {/* 内容区域 */}
          <div 
            ref={contentRef}
            className={`h-full overflow-auto scrollbar-thin ${
              themeMode === "dark" 
                ? "scrollbar-track-gray-800 scrollbar-thumb-gray-600" 
                : "scrollbar-track-gray-200 scrollbar-thumb-gray-400"
            }`}
          >
            <main className={`p-4 md:p-6 animate-fadeIn ${
              themeMode === "dark" ? "text-gray-100" : "text-gray-800"
            }`}>
              {children}
            </main>
          </div>
        </div>
      </div>
      
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out forwards;
        }
        
        /* 美化滚动条 */
        ::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        ::-webkit-scrollbar-track {
          background: ${themeMode === "dark" ? "#2d2d30" : "#f1f1f1"};
          border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
          background: ${themeMode === "dark" ? "#444" : "#c1c1c1"};
          border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: ${themeMode === "dark" ? "#555" : "#a1a1a1"};
        }
      `}</style>
    </div>
  )
}

