"use client";
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
exports.__esModule = true;
exports.Dashboard = void 0;
var react_1 = require("react");
var navigation_1 = require("next/navigation");
var lucide_react_1 = require("lucide-react");
var card_1 = require("@/components/ui/card");
var button_1 = require("@/components/ui/button");
var tabs_1 = require("@/components/ui/tabs");
var progress_1 = require("@/components/ui/progress");
var auth_context_1 = require("@/contexts/auth-context");
var use_toast_1 = require("@/components/ui/use-toast");
var alert_dialog_1 = require("@/components/ui/alert-dialog");
var theme_context_1 = require("@/contexts/theme-context");
var input_1 = require("@/components/ui/input");
var use_safety_check_data_1 = require("@/hooks/use-safety-check-data");
// 导入相关样式
var insights_module_css_1 = require("@/components/insights.module.css");
var RollingGallery_1 = require("@/components/RollingGallery");
var Silk_1 = require("@/components/Silk");
function Dashboard() {
    var router = navigation_1.useRouter();
    var user = auth_context_1.useAuth().user;
    var toast = use_toast_1.useToast().toast;
    var themeMode = theme_context_1.useTheme().themeMode;
    var _a = use_safety_check_data_1.useSafetyCheckData(), safetyChecks = _a.safetyChecks, getSafetyStatistics = _a.getStatistics;
    var _b = react_1.useState("all"), activeTab = _b[0], setActiveTab = _b[1];
    // 获取真实的安全检查数据
    var safetyStats = react_1.useMemo(function () { return getSafetyStatistics(); }, [safetyChecks]);
    // 添加数字跳动动画的引用
    var animatedCountersInitialized = react_1.useRef(false);
    // 添加智能洞察数据
    var _c = react_1.useState([
        {
            id: 1,
            title: '能源使用异常检测',
            description: '检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况',
            type: 'anomaly',
            timestamp: new Date(),
            module: '能源管理',
            importance: 'high',
            icon: React.createElement(lucide_react_1.AlertCircle, { className: "h-5 w-5 text-red-500" }),
            color: '#ff4d4f'
        },
        {
            id: 2,
            title: '财务趋势预测',
            description: '根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标',
            type: 'trend',
            timestamp: new Date(),
            module: '财务管理',
            importance: 'medium',
            icon: React.createElement(lucide_react_1.TrendingUp, { className: "h-5 w-5 text-green-500" }),
            color: '#52c41a'
        },
        {
            id: 3,
            title: '设备维护建议',
            description: '3号生产线设备预计在14天内需要进行预防性维护，建议提前安排',
            type: 'recommendation',
            timestamp: new Date(),
            module: '设备管理',
            importance: 'medium',
            icon: React.createElement(lucide_react_1.Lightbulb, { className: "h-5 w-5 text-yellow-500" }),
            color: '#faad14'
        },
        {
            id: 4,
            title: '项目风险预警',
            description: '主厂房建设项目进度已落后计划7.2%，建议增加资源投入',
            type: 'alert',
            timestamp: new Date(),
            module: '项目管理',
            importance: 'high',
            icon: React.createElement(lucide_react_1.AlertCircle, { className: "h-5 w-5 text-red-500" }),
            color: '#ff4d4f'
        },
        {
            id: 5,
            title: '物资库存优化',
            description: '分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本',
            type: 'recommendation',
            timestamp: new Date(),
            module: '物资管理',
            importance: 'low',
            icon: React.createElement(lucide_react_1.Lightbulb, { className: "h-5 w-5 text-blue-500" }),
            color: '#1890ff'
        },
    ]), insights = _c[0], setInsights = _c[1];
    // 添加智能分析功能状态
    var _d = react_1.useState('today'), analysisActiveTab = _d[0], setAnalysisActiveTab = _d[1];
    var _e = react_1.useState(false), showAnalyticsSidebar = _e[0], setShowAnalyticsSidebar = _e[1];
    // 渲染今日洞察组件
    var renderInsights = function () {
        return (React.createElement("div", { className: insights_module_css_1["default"].insightsContainer },
            React.createElement("div", { className: insights_module_css_1["default"].insightsHeader },
                React.createElement("h3", null, "\u4ECA\u65E5\u667A\u80FD\u6D1E\u5BDF"),
                React.createElement("div", { className: insights_module_css_1["default"].insightsTabs },
                    React.createElement("span", { className: insights_module_css_1["default"].insightTab + " " + (analysisActiveTab === 'today' ? insights_module_css_1["default"].activeTab : ''), onClick: function () { return setAnalysisActiveTab('today'); } }, "\u4ECA\u65E5"),
                    React.createElement("span", { className: insights_module_css_1["default"].insightTab + " " + (analysisActiveTab === 'week' ? insights_module_css_1["default"].activeTab : ''), onClick: function () { return setAnalysisActiveTab('week'); } }, "\u672C\u5468"),
                    React.createElement("span", { className: insights_module_css_1["default"].insightTab + " " + (analysisActiveTab === 'all' ? insights_module_css_1["default"].activeTab : ''), onClick: function () { return setAnalysisActiveTab('all'); } }, "\u5168\u90E8"))),
            React.createElement("div", { className: insights_module_css_1["default"].insightsList }, insights.map(function (insight) { return (React.createElement("div", { key: insight.id, className: insights_module_css_1["default"].insightCard, style: { borderLeft: "4px solid " + insight.color }, onClick: function () { return router.push("/" + insight.module.toLowerCase().replace(/\s+/g, '-')); } },
                React.createElement("div", { className: insights_module_css_1["default"].insightIcon }, insight.icon),
                React.createElement("div", { className: insights_module_css_1["default"].insightContent },
                    React.createElement("h4", null, insight.title),
                    React.createElement("p", null, insight.description),
                    React.createElement("div", { className: insights_module_css_1["default"].insightMeta },
                        React.createElement("span", null, insight.module),
                        React.createElement("span", null, insight.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })))),
                React.createElement("div", { className: insights_module_css_1["default"].insightBadge },
                    insight.importance === 'high' && React.createElement("span", { className: insights_module_css_1["default"].highImportance }, "\u9AD8"),
                    insight.importance === 'medium' && React.createElement("span", { className: insights_module_css_1["default"].mediumImportance }, "\u4E2D"),
                    insight.importance === 'low' && React.createElement("span", { className: insights_module_css_1["default"].lowImportance }, "\u4F4E")))); })),
            React.createElement("div", { className: insights_module_css_1["default"].viewAllInsights, onClick: function () { return router.push('/insights'); } },
                "\u67E5\u770B\u5168\u90E8",
                React.createElement("span", { className: insights_module_css_1["default"].viewAllIcon }, "\u2192"))));
    };
    // 实现数字跳动动画
    react_1.useEffect(function () {
        if (animatedCountersInitialized.current)
            return;
        var animateCounters = function () {
            var counters = document.querySelectorAll('.animate-counter');
            counters.forEach(function (counter) {
                var elem = counter;
                var target = parseInt(elem.dataset.target || '0');
                var duration = 1500; // 动画持续时间（毫秒）
                var steps = 60; // 动画步数
                var stepTime = duration / steps;
                var increment = target / steps;
                var current = 0;
                var step = 0;
                var updateCounter = function () {
                    step++;
                    current += increment;
                    elem.innerText = Math.round(Math.min(current, target)) + "%";
                    if (step < steps) {
                        setTimeout(updateCounter, stepTime);
                    }
                    else {
                        elem.innerText = target + "%";
                    }
                };
                updateCounter();
            });
        };
        // 页面加载完成后执行动画
        var timer = setTimeout(function () {
            animateCounters();
            animatedCountersInitialized.current = true;
        }, 500);
        return function () { return clearTimeout(timer); };
    }, []);
    var _f = react_1.useState({
        safetyChecks: 0,
        safetyChecksChange: 0,
        safetyIssues: 0,
        personnel: 247,
        personnelChange: 3,
        projects: 8,
        tasks: 24,
        tasksChange: -8
    }), stats = _f[0], setStats = _f[1];
    // 修改为从项目真实数据获取周报数据
    var _g = react_1.useState([
        { day: '周一', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周二', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周三', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周四', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周五', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周六', safetyChecks: 0, tasks: 0, issues: 0 },
        { day: '周日', safetyChecks: 0, tasks: 0, issues: 0 },
    ]), weeklyStats = _g[0], setWeeklyStats = _g[1];
    var alerts = react_1.useState([
        {
            id: 1,
            type: 'warning',
            title: '安全风险提醒',
            content: 'A3矿区检测到异常情况，请及时处理',
            time: '10分钟前',
            priority: 'high',
            isRead: false,
            route: '/safety-management/safety-hazard-management'
        },
        {
            id: 2,
            type: 'info',
            title: '系统更新通知',
            content: '系统将于今晚22:00进行例行维护',
            time: '1小时前',
            priority: 'medium',
            isRead: false,
            route: '/system-management/system-settings'
        },
        {
            id: 3,
            type: 'success',
            title: '安全检查完成',
            content: 'B2矿区安全检查已完成，无重大安全隐患',
            time: '2小时前',
            priority: 'normal',
            isRead: true,
            route: '/safety-management/safety-check'
        },
    ])[0];
    var _h = react_1.useState([]), recentModules = _h[0], setRecentModules = _h[1];
    var _j = react_1.useState([
        'safety-management',
        'personnel-management',
        'project-management'
    ]), frequentModules = _j[0], setFrequentModules = _j[1];
    var _k = react_1.useState(false), showWelcomeDialog = _k[0], setShowWelcomeDialog = _k[1];
    var _l = react_1.useState(""), welcomeUsername = _l[0], setWelcomeUsername = _l[1];
    // 添加通知详情弹窗相关状态
    var _m = react_1.useState(false), showNotificationDialog = _m[0], setShowNotificationDialog = _m[1];
    var _o = react_1.useState(null), selectedNotification = _o[0], setSelectedNotification = _o[1];
    var _p = react_1.useState("all"), notificationFilter = _p[0], setNotificationFilter = _p[1];
    // 添加模块搜索状态
    var _q = react_1.useState(""), moduleSearchTerm = _q[0], setModuleSearchTerm = _q[1];
    // 检查是否需要显示欢迎弹窗
    react_1.useEffect(function () {
        var shouldShowWelcome = sessionStorage.getItem("showWelcomePopup") === "true";
        var storedUsername = sessionStorage.getItem("username");
        if (shouldShowWelcome) {
            // 显示欢迎弹窗
            setWelcomeUsername(storedUsername || (user === null || user === void 0 ? void 0 : user.username) || "用户");
            setShowWelcomeDialog(true);
            // 清除标记，避免刷新页面后再次显示
            sessionStorage.removeItem("showWelcomePopup");
        }
    }, [user]);
    // 关闭欢迎弹窗
    var handleCloseWelcomeDialog = function () {
        setShowWelcomeDialog(false);
    };
    // 主要模块快速访问配置
    var mainModules = [
        {
            id: "system-management",
            title: "系统管理",
            description: "用户、角色和系统设置管理",
            icon: React.createElement(lucide_react_1.Settings, { className: "h-8 w-8" }),
            path: "/system-management/user-management"
        },
        {
            id: "safety-management",
            title: "安全管理",
            description: "安全检查、隐患管理和应急预案",
            icon: React.createElement(lucide_react_1.Shield, { className: "h-8 w-8" }),
            path: "/safety-management/safety-check"
        },
        {
            id: "project-management",
            title: "工程管理",
            description: "工程计划、进度和质量管理",
            icon: React.createElement(lucide_react_1.HardHat, { className: "h-8 w-8" }),
            path: "/project-management/project-homepage"
        },
        {
            id: "personnel-management",
            title: "人事管理",
            description: "人员信息、考核和培训管理",
            icon: React.createElement(lucide_react_1.Users, { className: "h-8 w-8" }),
            path: "/personnel-management/personnel-info-management"
        },
        {
            id: "financial-management",
            title: "财务管理",
            description: "财务状况和工资管理",
            icon: React.createElement(lucide_react_1.DollarSign, { className: "h-8 w-8" }),
            path: "/financial-management/financial-status-maintenance"
        },
        {
            id: "fixed-assets-management",
            title: "固定资产管理",
            description: "资产信息、维护和设备管理",
            icon: React.createElement(lucide_react_1.Database, { className: "h-8 w-8" }),
            path: "/fixed-assets-management/fixed-assets-info"
        },
        {
            id: "energy-management",
            title: "能源管理",
            description: "能源类型和使用情况管理",
            icon: React.createElement(lucide_react_1.Zap, { className: "h-8 w-8" }),
            path: "/energy-management/energy-type"
        },
        {
            id: "security-management",
            title: "保卫管理",
            description: "保卫巡检和人员定位管理",
            icon: React.createElement(lucide_react_1.Lock, { className: "h-8 w-8" }),
            path: "/security-management/security-dept-inspection"
        },
        {
            id: "office-admin-management",
            title: "办公与行政管理",
            description: "办公室、档案和邮件管理",
            icon: React.createElement(lucide_react_1.FileText, { className: "h-8 w-8" }),
            path: "/office-admin-management/office-management"
        },
        {
            id: "material-supply-chain",
            title: "物资与供应链管理",
            description: "供应、采购和仓库管理",
            icon: React.createElement(lucide_react_1.Package, { className: "h-8 w-8" }),
            path: "/material-supply-chain/supply-management"
        },
        {
            id: "task-process-management",
            title: "任务与流程管理",
            description: "任务管理和流程审批",
            icon: React.createElement(lucide_react_1.CheckSquare, { className: "h-8 w-8" }),
            path: "/task-process-management/task-management"
        },
        {
            id: "visual-dashboard",
            title: "可视化大屏",
            description: "数据可视化和综合展示",
            icon: React.createElement(lucide_react_1.BarChart2, { className: "h-8 w-8" }),
            path: "/visual-dashboard"
        },
        {
            id: "comprehensive-display",
            title: "综合展示与报表",
            description: "数据统计和报表展示",
            icon: React.createElement(lucide_react_1.BarChart, { className: "h-8 w-8" }),
            path: "/comprehensive-display"
        }
    ];
    // 在组件挂载时，从本地存储加载最近访问和常用模块
    react_1.useEffect(function () {
        var storedRecent = localStorage.getItem('recent-modules');
        if (storedRecent) {
            setRecentModules(JSON.parse(storedRecent));
        }
        var storedFrequent = localStorage.getItem('frequent-modules');
        if (storedFrequent) {
            setFrequentModules(JSON.parse(storedFrequent));
        }
    }, []);
    // 跳转到指定模块
    var navigateToModule = function (path, moduleId) {
        try {
            // 更新最近访问的模块
            var updatedRecent = __spreadArrays([moduleId], recentModules.filter(function (id) { return id !== moduleId; })).slice(0, 6);
            setRecentModules(updatedRecent);
            localStorage.setItem('recent-modules', JSON.stringify(updatedRecent));
            router.push(path);
        }
        catch (error) {
            console.error("导航错误:", error);
            toast({
                title: "导航错误",
                description: "无法跳转到指定页面，请稍后重试",
                variant: "destructive"
            });
        }
    };
    // 切换模块是否为常用模块
    var toggleFrequent = function (e, moduleId) {
        e.stopPropagation();
        var updatedFrequent;
        if (frequentModules.includes(moduleId)) {
            updatedFrequent = frequentModules.filter(function (id) { return id !== moduleId; });
        }
        else {
            updatedFrequent = __spreadArrays(frequentModules, [moduleId]);
        }
        setFrequentModules(updatedFrequent);
        localStorage.setItem('frequent-modules', JSON.stringify(updatedFrequent));
        toast({
            title: frequentModules.includes(moduleId) ? "已从常用移除" : "已添加到常用",
            description: frequentModules.includes(moduleId)
                ? "该模块已从常用模块中移除"
                : "该模块已添加到常用模块中",
            variant: "default"
        });
    };
    // 根据当前选择的标签过滤模块
    var filteredModules = react_1.useMemo(function () {
        var filtered = __spreadArrays(mainModules);
        // 首先应用标签过滤
        if (activeTab === "frequent") {
            filtered = filtered.filter(function (m) { return frequentModules.includes(m.id); });
        }
        if (activeTab === "recent") {
            filtered = filtered.filter(function (m) { return recentModules.includes(m.id); });
        }
        // 然后应用搜索过滤
        if (moduleSearchTerm.trim()) {
            var searchLower_1 = moduleSearchTerm.toLowerCase();
            filtered = filtered.filter(function (module) {
                return module.title.toLowerCase().includes(searchLower_1) ||
                    module.description.toLowerCase().includes(searchLower_1) ||
                    module.id.toLowerCase().includes(searchLower_1);
            });
        }
        return filtered;
    }, [activeTab, frequentModules, recentModules, mainModules, moduleSearchTerm]);
    // 修复百分比计算
    var calculateSafetyChecksPercentage = function () {
        return 85; // 假设是85%
    };
    var calculateTasksPercentage = function () {
        return 92; // 假设是92%
    };
    var calculateIssuesPercentage = function () {
        return 78; // 假设是78%
    };
    // 处理通知点击
    var handleNotificationClick = function (notification) {
        setSelectedNotification(notification);
        setShowNotificationDialog(true);
    };
    // 标记通知为已读
    var handleMarkAsRead = function (e, notificationId) {
        e.stopPropagation();
        alerts.forEach(function (alert) {
            if (alert.id === notificationId) {
                alert.isRead = true;
            }
        });
        toast({
            title: "已标记为已读",
            description: "通知已标记为已读状态"
        });
    };
    // 前往处理通知
    var handleProcessNotification = function () {
        if (selectedNotification && selectedNotification.route) {
            router.push(selectedNotification.route);
            setShowNotificationDialog(false);
        }
    };
    // 过滤通知
    var filteredAlerts = react_1.useMemo(function () {
        if (notificationFilter === "unread") {
            return alerts.filter(function (alert) { return !alert.isRead; });
        }
        return alerts;
    }, [alerts, notificationFilter]);
    // 加载项目数据
    react_1.useEffect(function () {
        // 模拟从localStorage获取项目数据
        var loadProjectData = function () {
            try {
                // 从localStorage获取项目数据
                var projectData = localStorage.getItem("project-data");
                if (projectData) {
                    var parsedData_1 = JSON.parse(projectData);
                    // 更新项目统计数据
                    if (parsedData_1.projects) {
                        setStats(function (prev) { return (__assign(__assign({}, prev), { projects: parsedData_1.projects.length || 8 })); });
                    }
                    // 更新项目进度数据
                    if (parsedData_1.projects) {
                        // 这里可以进一步处理项目数据，提取更多信息
                    }
                }
            }
            catch (error) {
                console.error("加载项目数据出错:", error);
            }
        };
        // 模拟从localStorage获取任务数据
        var loadTaskData = function () {
            try {
                // 从localStorage获取任务数据
                var taskData = localStorage.getItem("task-data");
                if (taskData) {
                    var parsedData_2 = JSON.parse(taskData);
                    // 更新任务统计数据
                    if (parsedData_2.tasks) {
                        setStats(function (prev) { return (__assign(__assign({}, prev), { tasks: parsedData_2.tasks.length || 24, tasksChange: parsedData_2.tasksChange || -8 })); });
                    }
                }
            }
            catch (error) {
                console.error("加载任务数据出错:", error);
            }
        };
        // 更新安全检查统计数据
        var updateSafetyStats = function () {
            // 更新安全检查统计数据
            setStats(function (prev) { return (__assign(__assign({}, prev), { safetyChecks: safetyStats.completed || 0, safetyIssues: safetyStats.totalIssues || 0 })); });
            // 更新周报数据
            setWeeklyStats([
                { day: '周一', safetyChecks: 85, tasks: 90, issues: 75 },
                { day: '周二', safetyChecks: 88, tasks: 85, issues: 80 },
                { day: '周三', safetyChecks: 92, tasks: 92, issues: 85 },
                { day: '周四', safetyChecks: 90, tasks: 95, issues: 90 },
                { day: '周五', safetyChecks: 95, tasks: 88, issues: 95 },
                { day: '周六', safetyChecks: 85, tasks: 80, issues: 85 },
                { day: '周日', safetyChecks: 80, tasks: 75, issues: 80 },
            ]);
        };
        // 加载所有数据
        loadProjectData();
        loadTaskData();
        updateSafetyStats();
    }, [safetyStats]);
    // 添加施工现场图片数组
    var constructionImages = [
        "/zhanshi/1.jpg",
        "/zhanshi/2.jpg",
        "/zhanshi/3.jpg",
        "/zhanshi/4.jpg",
        "/zhanshi/5.jpg",
        "/zhanshi/6.jpg",
        "/zhanshi/7.jpg",
        "/zhanshi/8.jpg",
        "/zhanshi/9.jpg",
        "/zhanshi/10.jpg",
    ];
    return (React.createElement("div", { className: "space-y-4 transition-colors duration-200 " + (themeMode === "dark" ? "text-white" : "text-[#1d1d1f]") },
        React.createElement(alert_dialog_1.AlertDialog, { open: showWelcomeDialog, onOpenChange: setShowWelcomeDialog },
            React.createElement(alert_dialog_1.AlertDialogContent, { className: (themeMode === "dark"
                    ? "bg-[#2c2c2e] border-[#3a3a3c] text-white"
                    : "bg-white") + " max-w-[400px] border shadow-xl animate-scaleUp" },
                React.createElement(alert_dialog_1.AlertDialogHeader, null,
                    React.createElement("div", { className: "flex justify-center mb-4" },
                        React.createElement("div", { className: "w-16 h-16 flex items-center justify-center rounded-full " + (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100") },
                            React.createElement("svg", { viewBox: "0 0 1024 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", width: "60%", height: "60%", className: "" + (themeMode === "dark" ? "text-white" : "text-[#1d1d1f]") },
                                React.createElement("path", { d: "M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-112-119-220.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z", fill: "currentColor" })))),
                    React.createElement(alert_dialog_1.AlertDialogTitle, { className: "text-center text-2xl" }, "\u767B\u5F55\u6210\u529F\uFF01"),
                    React.createElement(alert_dialog_1.AlertDialogDescription, { className: "text-center " + (themeMode === "dark" ? "text-gray-300" : "text-gray-600") },
                        React.createElement("p", { className: "text-lg mb-1" },
                            "\u6B22\u8FCE\u56DE\u6765\uFF0C",
                            React.createElement("span", { className: "font-medium " + (themeMode === "dark" ? "text-blue-300" : "text-blue-600") }, welcomeUsername)),
                        React.createElement("p", null, "\u795D\u60A8\u5728\u77FF\u4E1A\u516C\u53F8\u7EFC\u5408\u7BA1\u7406\u7CFB\u7EDF\u4E2D\u5DE5\u4F5C\u6109\u5FEB"))),
                React.createElement(alert_dialog_1.AlertDialogFooter, { className: "mt-4" },
                    React.createElement(alert_dialog_1.AlertDialogAction, { className: "w-full " + (themeMode === "dark"
                            ? "bg-white text-black hover:bg-gray-200"
                            : "bg-black text-white hover:bg-gray-800"), onClick: handleCloseWelcomeDialog }, "\u5F00\u59CB\u4F7F\u7528")))),
        React.createElement("div", { className: "relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600/90 to-indigo-600/90 p-6 mb-4 shadow-lg animate-float" },
            React.createElement(Silk_1["default"], { speed: 3, scale: 1.5, color: themeMode === "dark" ? "#4338CA" : "#6366F1", noiseIntensity: 1.2, rotation: 0.2, className: "opacity-50" }),
            React.createElement("div", { className: "absolute inset-0" },
                React.createElement("div", { className: "absolute inset-0 bg-cover bg-center z-0", style: { backgroundImage: 'url(/menu-bg/huanying.png)', opacity: 0.15 } }),
                React.createElement("div", { className: "absolute inset-0 bg-gradient-to-r from-blue-600/95 to-indigo-600/95 z-[1] opacity-60" })),
            React.createElement("div", { className: "absolute inset-0 overflow-hidden z-[2]" }, Array.from({ length: 20 }).map(function (_, i) { return (React.createElement("div", { key: i, className: "absolute rounded-full bg-white/10", style: {
                    top: Math.random() * 100 + "%",
                    left: Math.random() * 100 + "%",
                    width: Math.random() * 10 + 2 + "px",
                    height: Math.random() * 10 + 2 + "px",
                    animationDelay: Math.random() * 5 + "s",
                    animationDuration: Math.random() * 10 + 15 + "s"
                } })); })),
            React.createElement("div", { className: "absolute -top-10 -right-10 w-40 h-40 bg-blue-500/20 rounded-full filter blur-3xl animate-pulse z-[2]" }),
            React.createElement("div", { className: "absolute -bottom-8 -left-8 w-32 h-32 bg-indigo-500/20 rounded-full filter blur-3xl animate-pulse z-[2]", style: { animationDelay: '2s' } }),
            React.createElement("div", { className: "absolute top-0 left-0 w-full h-full opacity-20 z-[2]" },
                React.createElement("svg", { viewBox: "0 0 1000 1000", xmlns: "http://www.w3.org/2000/svg", className: "w-full h-full" },
                    React.createElement("defs", null,
                        React.createElement("linearGradient", { id: "b", gradientTransform: "rotate(45 0.5 0.5)" },
                            React.createElement("stop", { offset: "0%", stopColor: "#ffffff", stopOpacity: "0.3" }),
                            React.createElement("stop", { offset: "100%", stopColor: "#ffffff", stopOpacity: "0" }))),
                    React.createElement("path", { d: "M0,1000 C200,900 400,650 600,800 C750,900 800,800 1000,800 L1000,1000 L0,1000 Z", fill: "url(#b)", className: "animate-wave" }),
                    React.createElement("path", { d: "M0,1000 C150,850 350,700 500,800 C650,900 750,850 1000,700 L1000,1000 L0,1000 Z", fill: "url(#b)", opacity: "0.5", className: "animate-wave-slow" }))),
            React.createElement("div", { className: "relative z-[3] flex flex-col md:flex-row justify-between items-start md:items-center" },
                React.createElement("div", { className: "mb-3 md:mb-0 animate-fadeInUp", style: { animationDelay: '0.3s' } },
                    React.createElement("h2", { className: "text-2xl md:text-3xl font-bold tracking-tight text-white mb-1 flex items-center" },
                        "\u6B22\u8FCE\u56DE\u6765\uFF0C",
                        (user === null || user === void 0 ? void 0 : user.username) || "用户",
                        React.createElement("span", { className: "inline-block ml-2 animate-wave-hand" }, "\uD83D\uDC4B")),
                    React.createElement("p", { className: "text-blue-100 opacity-90 animate-fadeInUp", style: { animationDelay: '0.4s' } },
                        "\u4ECA\u5929\u662F ",
                        new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' })),
                    React.createElement("p", { className: "text-blue-100 opacity-75 mt-1 animate-fadeInUp", style: { animationDelay: '0.5s' } }, "\u8FD9\u662F\u60A8\u7684\u7BA1\u7406\u6982\u89C8\uFF0C\u4E00\u76EE\u4E86\u7136\u638C\u63E1\u7CFB\u7EDF\u52A8\u6001")),
                React.createElement("div", { className: "flex items-center gap-2 mb-4" },
                    React.createElement(button_1.Button, { onClick: function () { return router.push('/visual-dashboard'); }, className: "bg-white/10 hover:bg-white/20 text-white border-0 transition-all duration-300 hover:scale-105 backdrop-blur-sm" },
                        React.createElement(lucide_react_1.BarChart2, { className: "mr-2 h-4 w-4" }),
                        "\u53EF\u89C6\u5316\u5927\u5C4F"),
                    React.createElement(button_1.Button, { onClick: function () { return router.push('/task-process-management/task-management'); }, className: "bg-white hover:bg-blue-50 text-indigo-700 border-0 transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg" },
                        React.createElement(lucide_react_1.Clock, { className: "mr-2 h-4 w-4" }),
                        "\u4ECA\u65E5\u4EFB\u52A1"))),
            React.createElement("style", { jsx: true }, "\n          @keyframes float {\n            0%, 100% { transform: translateY(0); }\n            50% { transform: translateY(-10px); }\n          }\n\n          @keyframes wave {\n            0%, 100% { transform: translateX(0); }\n            50% { transform: translateX(-20px); }\n          }\n\n          @keyframes wave-slow {\n            0%, 100% { transform: translateX(0); }\n            50% { transform: translateX(15px); }\n          }\n\n          @keyframes fadeInUp {\n            from { opacity: 0; transform: translateY(10px); }\n            to { opacity: 1; transform: translateY(0); }\n          }\n\n          @keyframes wave-hand {\n            0%, 100% { transform: rotate(0deg); }\n            25% { transform: rotate(15deg); }\n            50% { transform: rotate(0deg); }\n            75% { transform: rotate(15deg); }\n          }\n\n          @keyframes float-particle {\n            0%, 100% { transform: translateY(0) translateX(0); }\n            25% { transform: translateY(-20px) translateX(10px); }\n            50% { transform: translateY(0) translateX(20px); }\n            75% { transform: translateY(20px) translateX(10px); }\n          }\n\n          @keyframes pulse {\n            0%, 100% { opacity: 0.2; }\n            50% { opacity: 0.3; }\n          }\n\n          .animate-float {\n            animation: float 6s ease-in-out infinite;\n          }\n\n          .animate-wave {\n            animation: wave 8s ease-in-out infinite;\n          }\n\n          .animate-wave-slow {\n            animation: wave-slow 12s ease-in-out infinite;\n          }\n\n          .animate-fadeInUp {\n            animation: fadeInUp 0.8s ease-out forwards;\n          }\n\n          .animate-wave-hand {\n            animation: wave-hand 2s ease-in-out infinite;\n            transform-origin: 70% 70%;\n            display: inline-block;\n          }\n\n          .animate-pulse {\n            animation: pulse 4s ease-in-out infinite;\n          }\n\n          .absolute {\n            animation: float-particle 20s ease-in-out infinite;\n          }\n        ")),
        React.createElement("div", { className: "rounded-xl overflow-hidden " + (themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-[#f5f5f7]") },
            React.createElement("div", { className: "p-6" },
                React.createElement("div", { className: "flex items-center justify-between mb-6" },
                    React.createElement("div", { className: "flex items-center gap-3" },
                        React.createElement("h3", { className: "text-2xl font-bold " + (themeMode === "dark" ? "text-white" : "text-[#1d1d1f]") }, "\u65BD\u5DE5\u73B0\u573A\u5B9E\u51B5"),
                        React.createElement("div", { className: "px-2 py-1 text-xs rounded-full " + (themeMode === "dark"
                                ? "bg-blue-500/10 text-blue-400"
                                : "bg-blue-100 text-blue-600") }, "\u5B9E\u65F6\u66F4\u65B0")),
                    React.createElement(button_1.Button, { variant: "ghost", size: "sm", className: "" + (themeMode === "dark"
                            ? "text-gray-400 hover:text-white hover:bg-[#2c2c2e]"
                            : "text-gray-500 hover:text-gray-900"), onClick: function () { return router.push('/visual-dashboard'); } },
                        "\u67E5\u770B\u66F4\u591A",
                        React.createElement(lucide_react_1.ChevronRight, { className: "ml-1 h-4 w-4" }))),
                React.createElement(RollingGallery_1["default"], { autoplay: true, pauseOnHover: true, images: constructionImages }))),
        React.createElement("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-2" },
            React.createElement("div", { className: "card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 " + (themeMode === "dark"
                    ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
                    : "bg-white border border-gray-100 card-glow-light") },
                React.createElement("div", { className: "relative px-6 py-5" },
                    React.createElement("div", { className: "absolute top-0 right-0 w-24 h-24 opacity-30" },
                        React.createElement("div", { className: "absolute top-6 right-10 w-2 h-2 rounded-full bg-blue-400/30 animate-ping-slow" }),
                        React.createElement("div", { className: "absolute top-14 right-6 w-1 h-1 rounded-full bg-blue-400/20 animate-ping-slow", style: { animationDelay: '1s' } }),
                        React.createElement("div", { className: "absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-blue-500/20 animate-ping-slow", style: { animationDelay: '2s' } })),
                    React.createElement("div", { className: "flex justify-between items-start" },
                        React.createElement("div", null,
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-blue-300" : "text-blue-600") }, "\u5B89\u5168\u68C0\u67E5\u5B8C\u6210\u7387"),
                            React.createElement("h3", { className: "text-2xl font-bold mt-1 " + (themeMode === "dark" ? "text-white" : "text-gray-900") + " animate-counter", "data-target": safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(function (check) { return check.status !== "已完成"; }).length)) * 100) : 0 },
                                safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(function (check) { return check.status !== "已完成"; }).length)) * 100) : 0,
                                "%"),
                            React.createElement("p", { className: "text-xs mt-1 " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") },
                                "\u5DF2\u5B8C\u6210 ",
                                safetyStats.completed,
                                "/",
                                safetyStats.completed + safetyChecks.filter(function (check) { return check.status !== "已完成"; }).length)),
                        React.createElement("div", { className: "p-2 rounded-lg icon-container " + (themeMode === "dark" ? "bg-blue-500/10" : "bg-blue-50") },
                            React.createElement(lucide_react_1.Shield, { className: "h-6 w-6 " + (themeMode === "dark" ? "text-blue-400" : "text-blue-500") }))),
                    React.createElement("div", { className: "mt-4" },
                        React.createElement("div", { className: "flex justify-between text-xs mb-1" },
                            React.createElement("span", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" }, "\u8FDB\u5EA6"),
                            React.createElement("span", { className: themeMode === "dark" ? "text-blue-300" : "text-blue-600" },
                                safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(function (check) { return check.status !== "已完成"; }).length)) * 100) : 0,
                                "%")),
                        React.createElement("div", { className: "relative h-2 rounded-full overflow-hidden" },
                            React.createElement("div", { className: "absolute inset-0 " + (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100") }),
                            React.createElement("div", { className: "absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate", style: {
                                    width: (safetyStats.completed > 0 ? (safetyStats.completed / (safetyStats.completed + safetyChecks.filter(function (check) { return check.status !== "已完成"; }).length)) * 100 : 0) + "%",
                                    background: themeMode === "dark"
                                        ? "linear-gradient(90deg, rgba(37, 99, 235, 0.8) 0%, rgba(59, 130, 246, 0.9) 100%)"
                                        : "linear-gradient(90deg, rgba(37, 99, 235, 0.7) 0%, rgba(59, 130, 246, 0.8) 100%)"
                                } },
                                React.createElement("div", { className: "absolute top-0 right-0 bottom-0 w-20 progress-bar-shine" })))),
                    React.createElement("div", { className: "mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs" },
                        React.createElement("div", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" },
                            "\u8F83\u4E0A\u5468",
                            React.createElement("span", { className: themeMode === "dark" ? "text-blue-300 ml-1" : "text-blue-600 ml-1" },
                                "+5%",
                                React.createElement(lucide_react_1.TrendingUp, { className: "h-3 w-3 inline ml-0.5" }))),
                        React.createElement(button_1.Button, { size: "sm", variant: "ghost", className: "text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 " + (themeMode === "dark"
                                ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                                : "text-gray-600 hover:text-gray-900"), onClick: function () { return router.push('/safety-management/safety-check'); } },
                            "\u67E5\u770B\u8BE6\u60C5",
                            React.createElement(lucide_react_1.ChevronRight, { className: "h-3 w-3 ml-0.5" }))))),
            React.createElement("div", { className: "card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 " + (themeMode === "dark"
                    ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
                    : "bg-white border border-gray-100 card-glow-light") },
                React.createElement("div", { className: "relative px-6 py-5" },
                    React.createElement("div", { className: "absolute top-0 right-0 w-24 h-24 opacity-30" },
                        React.createElement("div", { className: "absolute top-6 right-10 w-2 h-2 rounded-full bg-green-400/30 animate-ping-slow" }),
                        React.createElement("div", { className: "absolute top-14 right-6 w-1 h-1 rounded-full bg-green-400/20 animate-ping-slow", style: { animationDelay: '1.5s' } }),
                        React.createElement("div", { className: "absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-green-500/20 animate-ping-slow", style: { animationDelay: '0.5s' } })),
                    React.createElement("div", { className: "flex justify-between items-start" },
                        React.createElement("div", null,
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-green-300" : "text-green-600") }, "\u4EFB\u52A1\u5B8C\u6210\u7387"),
                            React.createElement("h3", { className: "text-2xl font-bold mt-1 " + (themeMode === "dark" ? "text-white" : "text-gray-900") + " animate-counter", "data-target": calculateTasksPercentage() },
                                calculateTasksPercentage(),
                                "%"),
                            React.createElement("p", { className: "text-xs mt-1 " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") },
                                "\u5DF2\u5B8C\u6210 ",
                                Math.round(stats.tasks * calculateTasksPercentage() / 100),
                                "/",
                                stats.tasks)),
                        React.createElement("div", { className: "p-2 rounded-lg icon-container " + (themeMode === "dark" ? "bg-green-500/10" : "bg-green-50") },
                            React.createElement(lucide_react_1.CheckSquare, { className: "h-6 w-6 " + (themeMode === "dark" ? "text-green-400" : "text-green-500") }))),
                    React.createElement("div", { className: "mt-4" },
                        React.createElement("div", { className: "flex justify-between text-xs mb-1" },
                            React.createElement("span", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" }, "\u8FDB\u5EA6"),
                            React.createElement("span", { className: themeMode === "dark" ? "text-green-300" : "text-green-600" },
                                calculateTasksPercentage(),
                                "%")),
                        React.createElement("div", { className: "relative h-2 rounded-full overflow-hidden" },
                            React.createElement("div", { className: "absolute inset-0 " + (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100") }),
                            React.createElement("div", { className: "absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate", style: {
                                    width: calculateTasksPercentage() + "%",
                                    background: themeMode === "dark"
                                        ? "linear-gradient(90deg, rgba(22, 163, 74, 0.8) 0%, rgba(34, 197, 94, 0.9) 100%)"
                                        : "linear-gradient(90deg, rgba(22, 163, 74, 0.7) 0%, rgba(34, 197, 94, 0.8) 100%)"
                                } },
                                React.createElement("div", { className: "absolute top-0 right-0 bottom-0 w-20 progress-bar-shine" })))),
                    React.createElement("div", { className: "mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs" },
                        React.createElement("div", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" },
                            "\u8F83\u4E0A\u5468",
                            React.createElement("span", { className: stats.tasksChange >= 0 ? (themeMode === "dark" ? "text-green-300 ml-1" : "text-green-600 ml-1") : (themeMode === "dark" ? "text-red-300 ml-1" : "text-red-600 ml-1") },
                                stats.tasksChange >= 0 ? "+" + stats.tasksChange + "%" : stats.tasksChange + "%",
                                stats.tasksChange >= 0 ? React.createElement(lucide_react_1.TrendingUp, { className: "h-3 w-3 inline ml-0.5" }) : React.createElement(lucide_react_1.TrendingDown, { className: "h-3 w-3 inline ml-0.5" }))),
                        React.createElement(button_1.Button, { size: "sm", variant: "ghost", className: "text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 " + (themeMode === "dark"
                                ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                                : "text-gray-600 hover:text-gray-900"), onClick: function () { return router.push('/task-process-management/task-management'); } },
                            "\u67E5\u770B\u8BE6\u60C5",
                            React.createElement(lucide_react_1.ChevronRight, { className: "h-3 w-3 ml-0.5" }))))),
            React.createElement("div", { className: "card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 " + (themeMode === "dark"
                    ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
                    : "bg-white border border-gray-100 card-glow-light") },
                React.createElement("div", { className: "relative px-6 py-5" },
                    React.createElement("div", { className: "absolute top-0 right-0 w-24 h-24 opacity-30" },
                        React.createElement("div", { className: "absolute top-6 right-10 w-2 h-2 rounded-full bg-amber-400/30 animate-ping-slow" }),
                        React.createElement("div", { className: "absolute top-14 right-6 w-1 h-1 rounded-full bg-amber-400/20 animate-ping-slow", style: { animationDelay: '0.7s' } }),
                        React.createElement("div", { className: "absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-amber-500/20 animate-ping-slow", style: { animationDelay: '1.7s' } })),
                    React.createElement("div", { className: "flex justify-between items-start" },
                        React.createElement("div", null,
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-amber-300" : "text-amber-600") }, "\u5B89\u5168\u9690\u60A3\u89E3\u51B3\u7387"),
                            React.createElement("h3", { className: "text-2xl font-bold mt-1 " + (themeMode === "dark" ? "text-white" : "text-gray-900") + " animate-counter", "data-target": calculateIssuesPercentage() },
                                calculateIssuesPercentage(),
                                "%"),
                            React.createElement("p", { className: "text-xs mt-1 " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") },
                                "\u5DF2\u89E3\u51B3 ",
                                Math.round(safetyStats.totalIssues * calculateIssuesPercentage() / 100),
                                "/",
                                safetyStats.totalIssues || 1)),
                        React.createElement("div", { className: "p-2 rounded-lg icon-container " + (themeMode === "dark" ? "bg-amber-500/10" : "bg-amber-50") },
                            React.createElement(lucide_react_1.AlertCircle, { className: "h-6 w-6 " + (themeMode === "dark" ? "text-amber-400" : "text-amber-500") }))),
                    React.createElement("div", { className: "mt-4" },
                        React.createElement("div", { className: "flex justify-between text-xs mb-1" },
                            React.createElement("span", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" }, "\u8FDB\u5EA6"),
                            React.createElement("span", { className: themeMode === "dark" ? "text-amber-300" : "text-amber-600" },
                                calculateIssuesPercentage(),
                                "%")),
                        React.createElement("div", { className: "relative h-2 rounded-full overflow-hidden" },
                            React.createElement("div", { className: "absolute inset-0 " + (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100") }),
                            React.createElement("div", { className: "absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate", style: {
                                    width: calculateIssuesPercentage() + "%",
                                    background: themeMode === "dark"
                                        ? "linear-gradient(90deg, rgba(217, 119, 6, 0.8) 0%, rgba(245, 158, 11, 0.9) 100%)"
                                        : "linear-gradient(90deg, rgba(217, 119, 6, 0.7) 0%, rgba(245, 158, 11, 0.8) 100%)"
                                } },
                                React.createElement("div", { className: "absolute top-0 right-0 bottom-0 w-20 progress-bar-shine" })))),
                    React.createElement("div", { className: "mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs" },
                        React.createElement("div", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" },
                            "\u4ECA\u65E5\u65B0\u589E",
                            React.createElement("span", { className: themeMode === "dark" ? "text-amber-300 ml-1" : "text-amber-600 ml-1" },
                                "3",
                                React.createElement(lucide_react_1.AlertTriangle, { className: "h-3 w-3 inline ml-0.5" }))),
                        React.createElement(button_1.Button, { size: "sm", variant: "ghost", className: "text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 " + (themeMode === "dark"
                                ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                                : "text-gray-600 hover:text-gray-900"), onClick: function () { return router.push('/safety-management/safety-hazard-management'); } },
                            "\u67E5\u770B\u8BE6\u60C5",
                            React.createElement(lucide_react_1.ChevronRight, { className: "h-3 w-3 ml-0.5" }))))),
            React.createElement("style", { jsx: true, global: true }, "\n          @keyframes ping-slow {\n            0% { transform: scale(1); opacity: 1; }\n            50% { transform: scale(2); opacity: 0.5; }\n            100% { transform: scale(1); opacity: 1; }\n          }\n\n          @keyframes progress-animation {\n            0% { width: 0%; }\n            100% { width: var(--target-width); }\n          }\n\n          @keyframes float-subtle {\n            0%, 100% { transform: translateY(0); }\n            50% { transform: translateY(-5px); }\n          }\n\n          @keyframes shine {\n            0% { transform: translateX(-100%) rotate(20deg); }\n            100% { transform: translateX(300%) rotate(20deg); }\n          }\n\n          @keyframes pulse-scale {\n            0%, 100% { transform: scaleX(1); }\n            50% { transform: scaleX(1.02); }\n          }\n\n          .animate-ping-slow {\n            animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;\n          }\n\n          .progress-bar-animate {\n            animation: pulse-scale 2s ease-in-out infinite;\n            transform-origin: left center;\n            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);\n            transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);\n          }\n\n          .progress-bar-shine {\n            background: linear-gradient(\n              90deg,\n              rgba(255, 255, 255, 0) 0%,\n              rgba(255, 255, 255, 0.4) 50%,\n              rgba(255, 255, 255, 0) 100%\n            );\n            transform: translateX(-100%) rotate(20deg);\n            animation: shine 3s ease-in-out infinite;\n            animation-delay: 1s;\n          }\n\n          .card-with-hover {\n            transform: translateY(0);\n            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n          }\n\n          .card-with-hover:hover {\n            transform: translateY(-8px);\n          }\n\n          .card-with-hover:hover .progress-bar-animate {\n            box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);\n          }\n\n          .icon-container {\n            transition: all 0.3s ease;\n          }\n\n          .card-with-hover:hover .icon-container {\n            transform: scale(1.1) rotate(5deg);\n          }\n\n          .card-glow-dark:hover {\n            box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);\n          }\n\n          .card-glow-light:hover {\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);\n          }\n        ")),
        React.createElement("div", { className: "rounded-xl p-5 mb-6 shadow-sm " + (themeMode === "dark"
                ? "bg-gradient-to-br from-amber-500/10 to-red-500/10 border border-amber-500/20"
                : "bg-gradient-to-r from-amber-50 to-amber-100/50 border border-amber-100") },
            React.createElement("div", { className: "flex" },
                React.createElement("div", { className: "rounded-full p-2 " + (themeMode === "dark" ? "bg-amber-500/20" : "bg-amber-200/50") },
                    React.createElement(lucide_react_1.AlertTriangle, { className: "h-5 w-5 " + (themeMode === "dark" ? "text-amber-400" : "text-amber-600") })),
                React.createElement("div", { className: "ml-4 flex-1" },
                    React.createElement("div", { className: "flex justify-between items-start" },
                        React.createElement("div", null,
                            React.createElement("h3", { className: "font-semibold " + (themeMode === "dark" ? "text-white" : "text-gray-900") }, "\u5B89\u5168\u63D0\u9192"),
                            React.createElement("p", { className: "mt-1 text-sm " + (themeMode === "dark" ? "text-gray-300" : "text-gray-600") },
                                "\u77FF\u533AB2\u5B89\u5168\u5DE1\u68C0\u8BA1\u5212\u5373\u5C06\u5230\u671F\uFF0C\u8DDD\u79BB\u622A\u6B62\u65E5\u671F\u8FD8\u6709 ",
                                React.createElement("span", { className: "font-medium " + (themeMode === "dark" ? "text-amber-300" : "text-amber-600") }, "2 \u5929"))),
                        React.createElement(button_1.Button, { size: "sm", variant: "outline", className: "ml-4 " + (themeMode === "dark"
                                ? "border-amber-500/30 bg-amber-500/10 hover:bg-amber-500/20 text-amber-400"
                                : "border-amber-200 bg-amber-100/50 hover:bg-amber-100 text-amber-700"), onClick: function () { return router.push('/safety-management/safety-check'); } }, "\u7ACB\u5373\u5904\u7406"))))),
        React.createElement("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4" },
            React.createElement("div", { className: "h-full lg:col-span-4" },
                React.createElement("div", { className: insights_module_css_1["default"].analyticsCard + " group relative overflow-hidden hover:shadow-lg transition-all duration-300 ease-in-out p-4", onClick: function () { return router.push('/analytics-center'); } },
                    React.createElement("div", { className: "absolute -right-6 -top-6 w-24 h-24 bg-blue-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-500" }),
                    React.createElement("div", { className: "absolute right-10 bottom-10 w-16 h-16 bg-indigo-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-700 delay-100" }),
                    React.createElement("div", { className: "absolute left-20 bottom-6 w-12 h-12 bg-purple-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-700 delay-200" }),
                    React.createElement("div", { className: insights_module_css_1["default"].cardContent + " z-10 relative py-3" },
                        React.createElement("div", { className: insights_module_css_1["default"].cardIconContainer + " w-14 h-14 rounded-2xl overflow-hidden relative group-hover:scale-110 transition-all", style: {
                                background: 'linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%)',
                                boxShadow: '0 10px 15px -3px rgba(79, 70, 229, 0.3)'
                            } },
                            React.createElement("div", { className: "absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300" }),
                            React.createElement(lucide_react_1.ChartBar, { className: "h-7 w-7 text-white" })),
                        React.createElement("div", { className: insights_module_css_1["default"].cardInfo + " ml-4 z-10" },
                            React.createElement("h3", { className: "text-lg font-semibold text-gray-800 dark:text-white mb-1 group-hover:translate-x-1 transition-transform" }, "\u667A\u80FD\u5206\u6790\u4E2D\u5FC3"),
                            React.createElement("p", { className: "text-sm text-gray-500 dark:text-gray-300 mb-3 group-hover:translate-x-1 transition-transform duration-300 delay-75" }, "\u6570\u636E\u9A71\u52A8\u6D1E\u5BDF\u548C\u667A\u80FD\u5206\u6790\u5E73\u53F0"),
                            React.createElement("div", { className: "grid grid-cols-2 gap-2 mt-2" },
                                React.createElement("div", { className: "bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-2 transform group-hover:translate-y-[-3px] transition-all duration-300" },
                                    React.createElement("div", { className: "flex items-center space-x-1.5 mb-1" },
                                        React.createElement(lucide_react_1.LineChart, { className: "h-3.5 w-3.5 text-indigo-500 dark:text-indigo-400" }),
                                        React.createElement("span", { className: "text-[11px] font-medium text-indigo-700 dark:text-indigo-300" }, "\u8D8B\u52BF\u5206\u6790")),
                                    React.createElement("div", { className: "text-xs text-indigo-800 dark:text-indigo-200 font-semibold" }, "\u65B0\u589E 5 \u9879")),
                                React.createElement("div", { className: "bg-purple-50 dark:bg-purple-900/30 rounded-lg p-2 transform group-hover:translate-y-[-3px] transition-all duration-300 delay-75" },
                                    React.createElement("div", { className: "flex items-center space-x-1.5 mb-1" },
                                        React.createElement(lucide_react_1.AlertTriangle, { className: "h-3.5 w-3.5 text-purple-500 dark:text-purple-400" }),
                                        React.createElement("span", { className: "text-[11px] font-medium text-purple-700 dark:text-purple-300" }, "\u5F02\u5E38\u68C0\u6D4B")),
                                    React.createElement("div", { className: "text-xs text-purple-800 dark:text-purple-200 font-semibold" }, "3 \u4E2A\u5F85\u5904\u7406"))),
                            React.createElement("div", { className: "mt-2 flex items-center text-indigo-600 dark:text-indigo-400 group-hover:translate-x-1 transition-transform duration-500" },
                                React.createElement("span", { className: "text-xs font-medium" }, "\u67E5\u770B\u5206\u6790\u4E2D\u5FC3"),
                                React.createElement(lucide_react_1.ArrowRight, { className: "h-3.5 w-3.5 ml-1.5 transform group-hover:translate-x-1 transition-transform duration-300" })))),
                    React.createElement("div", { className: "absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500" })))),
        React.createElement("div", { className: "grid gap-4 md:grid-cols-2 lg:grid-cols-3" },
            React.createElement("div", { className: "lg:col-span-2 border " + (themeMode === "dark"
                    ? "bg-[#2c2c2e] border-[#3a3a3c]"
                    : "bg-white border-gray-100") + " rounded-xl overflow-hidden" },
                React.createElement("div", { className: "flex border-b items-center px-4 py-2 justify-between" },
                    React.createElement("div", { className: "flex items-center gap-2" },
                        React.createElement(lucide_react_1.Bell, { className: "h-4 w-4 " + (themeMode === "dark" ? "text-blue-400" : "text-blue-500") }),
                        React.createElement("h3", { className: "font-medium " + (themeMode === "dark" ? "text-white" : "text-gray-900") }, "\u901A\u77E5\u4E0E\u6D3B\u52A8")),
                    React.createElement("div", { className: "flex gap-1" },
                        React.createElement(button_1.Button, { variant: "ghost", size: "sm", className: "text-xs px-2 h-7 rounded " + (notificationFilter === "all"
                                ? (themeMode === "dark" ? "bg-[#3a3a3c] text-white" : "bg-gray-100")
                                : (themeMode === "dark" ? "hover:bg-[#3a3a3c] text-white" : "hover:bg-gray-100")), onClick: function () { return setNotificationFilter("all"); } }, "\u5168\u90E8"),
                        React.createElement(button_1.Button, { variant: "ghost", size: "sm", className: "text-xs px-2 h-7 rounded " + (notificationFilter === "unread"
                                ? (themeMode === "dark" ? "bg-[#3a3a3c] text-white" : "bg-gray-100")
                                : (themeMode === "dark" ? "hover:bg-[#3a3a3c] text-white" : "hover:bg-gray-100")), onClick: function () { return setNotificationFilter("unread"); } }, "\u672A\u8BFB"))),
                React.createElement("div", { className: "max-h-[350px] overflow-y-auto " + (themeMode === "dark" ? "scrollbar-thin scrollbar-thumb-gray-600" : "scrollbar-thin scrollbar-thumb-gray-300") }, filteredAlerts.concat([
                    {
                        id: 4,
                        type: 'task',
                        title: '新的任务分配',
                        content: '您被指派为"月度安全检查报告"的负责人',
                        time: '昨天',
                        priority: 'normal',
                        isRead: false,
                        route: '/task-process-management/task-management'
                    },
                    {
                        id: 5,
                        type: 'approval',
                        title: '审批请求',
                        content: '设备维护计划需要您的审核',
                        time: '2天前',
                        priority: 'medium',
                        isRead: true,
                        route: '/task-process-management/process-approval'
                    },
                    {
                        id: 6,
                        type: 'meeting',
                        title: '会议提醒',
                        content: '安全生产月度会议将于明天上午10:00举行',
                        time: '昨天',
                        priority: 'normal',
                        isRead: false,
                        route: '/office-admin-management/office-management'
                    }
                ]).map(function (alert) { return (React.createElement("div", { key: alert.id, className: "px-4 py-3 border-b " + (themeMode === "dark" ? "border-[#3a3a3c] hover:bg-[#3a3a3c]/50" : "border-gray-100 hover:bg-gray-50") + " transition-colors cursor-pointer " + (!alert.isRead ? (themeMode === "dark" ? "bg-blue-900/10" : "bg-blue-50/50") : ""), onClick: function () { return handleNotificationClick(alert); } },
                    React.createElement("div", { className: "flex items-start gap-3" },
                        React.createElement("div", { className: "p-2 rounded-full flex-shrink-0 " + (alert.type === 'warning'
                                ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-500"
                                : alert.type === 'info'
                                    ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-500"
                                    : alert.type === 'success'
                                        ? themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-500"
                                        : alert.type === 'task'
                                            ? themeMode === "dark" ? "bg-purple-500/20 text-purple-400" : "bg-purple-100 text-purple-500"
                                            : alert.type === 'approval'
                                                ? themeMode === "dark" ? "bg-indigo-500/20 text-indigo-400" : "bg-indigo-100 text-indigo-500"
                                                : themeMode === "dark" ? "bg-gray-500/20 text-gray-400" : "bg-gray-100 text-gray-500") },
                            alert.type === 'warning' && React.createElement(lucide_react_1.AlertTriangle, { className: "h-5 w-5" }),
                            alert.type === 'info' && React.createElement(lucide_react_1.Info, { className: "h-5 w-5" }),
                            alert.type === 'success' && React.createElement(lucide_react_1.CheckCircle, { className: "h-5 w-5" }),
                            alert.type === 'task' && React.createElement(lucide_react_1.Clipboard, { className: "h-5 w-5" }),
                            alert.type === 'approval' && React.createElement(lucide_react_1.FileText, { className: "h-5 w-5" }),
                            alert.type === 'meeting' && React.createElement(lucide_react_1.Calendar, { className: "h-5 w-5" })),
                        React.createElement("div", { className: "flex-1 min-w-0" },
                            React.createElement("div", { className: "flex items-center justify-between mb-1" },
                                React.createElement("h4", { className: "font-medium truncate " + (themeMode === "dark" ? "text-white" : "text-gray-900") },
                                    alert.title,
                                    !alert.isRead && (React.createElement("span", { className: "ml-2 inline-block w-2 h-2 rounded-full " + (themeMode === "dark" ? "bg-blue-400" : "bg-blue-500") }))),
                                React.createElement("div", { className: "flex items-center text-xs " + (themeMode === "dark" ? "text-gray-500" : "text-gray-500") },
                                    React.createElement(lucide_react_1.Clock, { className: "h-3 w-3 mr-1" }),
                                    alert.time)),
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-gray-400" : "text-gray-600") }, alert.content),
                            !alert.isRead && (React.createElement("div", { className: "flex justify-end mt-2" },
                                React.createElement(button_1.Button, { variant: "ghost", size: "sm", className: "text-xs h-6 rounded-full " + (themeMode === "dark"
                                        ? "hover:bg-[#3a3a3c] text-gray-300"
                                        : "hover:bg-gray-100 text-gray-600"), onClick: function (e) { return handleMarkAsRead(e, alert.id); } }, "\u6807\u8BB0\u4E3A\u5DF2\u8BFB"))))))); })),
                React.createElement("div", { className: "p-3 flex justify-center" },
                    React.createElement(button_1.Button, { variant: "outline", size: "sm", className: "rounded-full shadow-sm text-xs " + (themeMode === "dark"
                            ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                            : "bg-gray-50 text-[#1d1d1f] border-gray-200") },
                        "\u67E5\u770B\u5168\u90E8\u901A\u77E5",
                        React.createElement(lucide_react_1.ChevronRight, { className: "ml-1 h-3.5 w-3.5" })))),
            React.createElement(card_1.Card, { className: "border " + (themeMode === "dark"
                    ? "bg-[#2c2c2e] border-[#3a3a3c]"
                    : "bg-white border-gray-100") },
                React.createElement(card_1.CardHeader, { className: "flex flex-row items-center justify-between py-3" },
                    React.createElement(card_1.CardTitle, { className: themeMode === "dark" ? "text-white" : "" }, "\u5DE5\u7A0B\u8FDB\u5EA6"),
                    React.createElement(button_1.Button, { variant: "ghost", size: "icon", className: "h-8 w-8 rounded-full " + (themeMode === "dark" ? "hover:bg-[#3a3a3c]" : "hover:bg-gray-100") },
                        React.createElement(lucide_react_1.Layers, { className: "h-4 w-4 " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") }),
                        React.createElement("span", { className: "sr-only" }, "\u67E5\u770B\u9009\u9879"))),
                React.createElement(card_1.CardContent, null,
                    React.createElement("div", { className: "space-y-4" }, [
                        { name: "矿区A3开发项目", progress: 80, status: "正常" },
                        { name: "设备更新计划", progress: 45, status: "延迟" },
                        { name: "安全系统升级", progress: 90, status: "超前" },
                        { name: "风险评估计划", progress: 65, status: "正常" },
                    ].map(function (project, i) { return (React.createElement("div", { key: i, className: "space-y-2 " + (i < 3 ? "pb-4 mb-1 border-b " + (themeMode === "dark" ? "border-[#3a3a3c]" : "border-gray-100") : "") },
                        React.createElement("div", { className: "flex items-center justify-between" },
                            React.createElement("div", { className: "text-sm font-medium " + (themeMode === "dark" ? "text-white" : "") }, project.name),
                            React.createElement("div", { className: "text-xs px-2 py-0.5 rounded-full " + (project.status === "正常"
                                    ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-600"
                                    : project.status === "延迟"
                                        ? themeMode === "dark" ? "bg-red-500/20 text-red-400" : "bg-red-100 text-red-600"
                                        : themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-600") }, project.status)),
                        React.createElement("div", { className: "flex items-center gap-2" },
                            React.createElement(progress_1.Progress, { value: project.progress, className: "h-1.5 flex-1 " + (themeMode === "dark" ? "bg-[#48484a]" : "bg-gray-100"), indicatorClassName: project.status === "正常"
                                    ? "bg-blue-500"
                                    : project.status === "延迟"
                                        ? "bg-red-500"
                                        : "bg-green-500" }),
                            React.createElement("div", { className: "text-xs font-medium " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") },
                                project.progress,
                                "%")))); }))),
                React.createElement(card_1.CardFooter, null,
                    React.createElement(button_1.Button, { variant: "outline", size: "sm", className: "w-full rounded-full " + (themeMode === "dark"
                            ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                            : "bg-gray-50 text-[#1d1d1f] border-gray-200"), onClick: function () { return navigateToModule("/project-management/project-homepage", "project-management"); } },
                        "\u67E5\u770B\u5168\u90E8\u9879\u76EE",
                        React.createElement(lucide_react_1.ChevronRight, { className: "ml-1 h-4 w-4" }))))),
        React.createElement("div", { className: "rounded-xl " + (themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-[#f5f5f7]") },
            React.createElement("div", { className: "p-6" },
                React.createElement("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3" },
                    React.createElement("div", { className: "flex items-center" },
                        React.createElement("h3", { className: "text-xl font-bold mr-2 " + (themeMode === "dark" ? "text-white" : "text-[#1d1d1f]") }, "\u5FEB\u901F\u8BBF\u95EE"),
                        React.createElement("div", { className: "text-xs px-2 py-1 rounded-full " + (themeMode === "dark"
                                ? "bg-[#2c2c2e] text-gray-400"
                                : "bg-white/80 text-gray-500 border border-gray-200") },
                            mainModules.length,
                            " \u4E2A\u6A21\u5757")),
                    React.createElement("div", { className: "flex items-center gap-3" },
                        React.createElement("div", { className: "relative" },
                            React.createElement(lucide_react_1.Search, { className: "absolute left-3 top-2.5 h-4 w-4 " + (themeMode === "dark" ? "text-gray-500" : "text-gray-400") }),
                            React.createElement(input_1.Input, { placeholder: "\u641C\u7D22\u6A21\u5757...", className: "w-full rounded-full pl-9 " + (themeMode === "dark"
                                    ? "bg-[#2c2c2e] border-[#3a3a3c] text-white placeholder:text-gray-500"
                                    : "bg-white border-gray-200 placeholder:text-gray-400"), value: moduleSearchTerm, onChange: function (e) { return setModuleSearchTerm(e.target.value); } })),
                        React.createElement(tabs_1.Tabs, { value: activeTab, onValueChange: setActiveTab, className: "w-auto" },
                            React.createElement(tabs_1.TabsList, { className: themeMode === "dark"
                                    ? "bg-[#2c2c2e]"
                                    : "bg-white/90 border border-gray-200 shadow-sm" },
                                React.createElement(tabs_1.TabsTrigger, { value: "all", className: themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : "" }, "\u5168\u90E8"),
                                React.createElement(tabs_1.TabsTrigger, { value: "frequent", className: themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : "" }, "\u5E38\u7528"),
                                React.createElement(tabs_1.TabsTrigger, { value: "recent", className: themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : "" }, "\u6700\u8FD1"))))),
                React.createElement("div", { className: "grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3 mb-4" }, mainModules
                    .filter(function (module) { return moduleSearchTerm === "" ||
                    module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
                    module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase()); })
                    .map(function (module) { return (React.createElement("div", { key: module.id, className: "relative overflow-hidden " + (themeMode === "dark"
                        ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] shadow-lg shadow-[#000]/10"
                        : "bg-white border border-gray-100 shadow-md shadow-gray-100/50") + " rounded-xl transition-all duration-300 transform hover:translate-y-[-4px] hover:shadow-xl " + (themeMode === "dark" ? "hover:shadow-[#000]/20" : "hover:shadow-gray-200/70") + " group cursor-pointer", onClick: function () { return navigateToModule(module.path, module.id); } },
                    React.createElement("div", { className: "absolute -right-8 -bottom-8 w-32 h-32 rounded-full opacity-10 transition-all duration-300 group-hover:scale-125 group-hover:opacity-20", style: {
                            background: module.id === "safety-management" ? "radial-gradient(circle, #3b82f6 0%, #1d4ed8 100%)" :
                                module.id === "project-management" ? "radial-gradient(circle, #f59e0b 0%, #d97706 100%)" :
                                    module.id === "personnel-management" ? "radial-gradient(circle, #10b981 0%, #059669 100%)" :
                                        module.id === "financial-management" ? "radial-gradient(circle, #6366f1 0%, #4f46e5 100%)" :
                                            module.id === "fixed-assets-management" ? "radial-gradient(circle, #8b5cf6 0%, #7c3aed 100%)" :
                                                module.id === "energy-management" ? "radial-gradient(circle, #ec4899 0%, #db2777 100%)" :
                                                    module.id === "security-management" ? "radial-gradient(circle, #f43f5e 0%, #e11d48 100%)" :
                                                        module.id === "office-admin-management" ? "radial-gradient(circle, #0ea5e9 0%, #0284c7 100%)" :
                                                            module.id === "material-supply-chain" ? "radial-gradient(circle, #84cc16 0%, #65a30d 100%)" :
                                                                module.id === "task-process-management" ? "radial-gradient(circle, #14b8a6 0%, #0f766e 100%)" :
                                                                    module.id === "comprehensive-display" ? "radial-gradient(circle, #a855f7 0%, #9333ea 100%)" :
                                                                        "radial-gradient(circle, #475569 0%, #334155 100%)"
                        } }),
                    React.createElement("div", { className: "p-6 relative z-10" },
                        React.createElement("div", { className: "flex justify-between items-start" },
                            React.createElement("div", { className: "p-3 rounded-lg mb-4 transition-all duration-300 group-hover:scale-110 " + (themeMode === "dark" ? "bg-[#000]/20" : "bg-gray-100"), style: {
                                    color: module.id === "safety-management" ? "#3b82f6" :
                                        module.id === "project-management" ? "#f59e0b" :
                                            module.id === "personnel-management" ? "#10b981" :
                                                module.id === "financial-management" ? "#6366f1" :
                                                    module.id === "fixed-assets-management" ? "#8b5cf6" :
                                                        module.id === "energy-management" ? "#ec4899" :
                                                            module.id === "security-management" ? "#f43f5e" :
                                                                module.id === "office-admin-management" ? "#0ea5e9" :
                                                                    module.id === "material-supply-chain" ? "#84cc16" :
                                                                        module.id === "task-process-management" ? "#14b8a6" :
                                                                            module.id === "comprehensive-display" ? "#a855f7" :
                                                                                "#475569"
                                } }, module.icon),
                            React.createElement("div", { className: (frequentModules.includes(module.id) ? "visible" : "invisible group-hover:visible") + " transition-all duration-200" },
                                React.createElement(lucide_react_1.Star, { className: "h-5 w-5 cursor-pointer transition-transform hover:scale-110 " + (frequentModules.includes(module.id)
                                        ? "text-yellow-400 fill-yellow-400"
                                        : "" + (themeMode === "dark" ? "text-gray-500" : "text-gray-300")), onClick: function (e) { return toggleFrequent(e, module.id); } }))),
                        React.createElement("div", null,
                            React.createElement("h3", { className: "text-lg font-bold mb-1 group-hover:underline decoration-2 underline-offset-4 " + (themeMode === "dark" ? "text-white" : "text-gray-900"), style: {
                                    textDecorationColor: module.id === "safety-management" ? "#3b82f6" :
                                        module.id === "project-management" ? "#f59e0b" :
                                            module.id === "personnel-management" ? "#10b981" :
                                                module.id === "financial-management" ? "#6366f1" :
                                                    module.id === "fixed-assets-management" ? "#8b5cf6" :
                                                        module.id === "energy-management" ? "#ec4899" :
                                                            module.id === "security-management" ? "#f43f5e" :
                                                                module.id === "office-admin-management" ? "#0ea5e9" :
                                                                    module.id === "material-supply-chain" ? "#84cc16" :
                                                                        module.id === "task-process-management" ? "#14b8a6" :
                                                                            module.id === "comprehensive-display" ? "#a855f7" :
                                                                                "#475569"
                                } }, module.title),
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") }, module.description))),
                    React.createElement("div", { className: "absolute bottom-0 left-0 w-full h-1 transition-all duration-300 group-hover:h-1.5 " + (themeMode === "dark" ? "opacity-70 group-hover:opacity-100" : "opacity-90 group-hover:opacity-100"), style: {
                            background: module.id === "safety-management" ? "#3b82f6" :
                                module.id === "project-management" ? "#f59e0b" :
                                    module.id === "personnel-management" ? "#10b981" :
                                        module.id === "financial-management" ? "#6366f1" :
                                            module.id === "fixed-assets-management" ? "#8b5cf6" :
                                                module.id === "energy-management" ? "#ec4899" :
                                                    module.id === "security-management" ? "#f43f5e" :
                                                        module.id === "office-admin-management" ? "#0ea5e9" :
                                                            module.id === "material-supply-chain" ? "#84cc16" :
                                                                module.id === "task-process-management" ? "#14b8a6" :
                                                                    module.id === "comprehensive-display" ? "#a855f7" :
                                                                        "#475569"
                        } }))); })))),
        moduleSearchTerm !== "" && (React.createElement("div", { className: "fixed inset-0 bg-opacity-90 z-50 flex items-center justify-center p-6 " + (themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-white") },
            React.createElement("div", { className: "w-full max-w-4xl" },
                React.createElement("div", { className: "flex justify-between items-center mb-6" },
                    React.createElement("h2", { className: "text-2xl font-bold " + (themeMode === "dark" ? "text-white" : "text-gray-900") },
                        "\u641C\u7D22\u6A21\u5757: ",
                        React.createElement("span", { className: "text-blue-500" },
                            "\"",
                            moduleSearchTerm,
                            "\"")),
                    React.createElement(button_1.Button, { variant: "ghost", size: "icon", onClick: function () { return setModuleSearchTerm(""); }, className: themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-600 hover:text-gray-900" },
                        React.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", className: "lucide lucide-x" },
                            React.createElement("path", { d: "M18 6 6 18" }),
                            React.createElement("path", { d: "m6 6 12 12" })))),
                mainModules.filter(function (module) {
                    return module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
                        module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase());
                }).length > 0 ? (React.createElement("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" }, mainModules
                    .filter(function (module) {
                    return module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
                        module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase());
                })
                    .map(function (module) { return (React.createElement("div", { key: module.id, className: "p-4 rounded-lg cursor-pointer transition-all duration-200 hover:translate-y-[-2px] " + (themeMode === "dark"
                        ? "bg-[#2c2c2e] hover:bg-[#3a3a3c]"
                        : "bg-gray-50 hover:bg-gray-100"), onClick: function () {
                        navigateToModule(module.path, module.id);
                        setModuleSearchTerm("");
                    } },
                    React.createElement("div", { className: "flex items-center" },
                        React.createElement("div", { className: "p-2 rounded-md mr-3", style: {
                                color: module.id === "safety-management" ? "#3b82f6" :
                                    module.id === "project-management" ? "#f59e0b" :
                                        module.id === "personnel-management" ? "#10b981" :
                                            module.id === "financial-management" ? "#6366f1" :
                                                module.id === "fixed-assets-management" ? "#8b5cf6" :
                                                    module.id === "energy-management" ? "#ec4899" :
                                                        module.id === "security-management" ? "#f43f5e" :
                                                            module.id === "office-admin-management" ? "#0ea5e9" :
                                                                module.id === "material-supply-chain" ? "#84cc16" :
                                                                    module.id === "task-process-management" ? "#14b8a6" :
                                                                        module.id === "comprehensive-display" ? "#a855f7" :
                                                                            "#475569",
                                background: themeMode === "dark" ? "rgba(0,0,0,0.2)" : "rgba(0,0,0,0.05)"
                            } }, module.icon),
                        React.createElement("div", null,
                            React.createElement("h3", { className: "font-semibold " + (themeMode === "dark" ? "text-white" : "text-gray-900") }, module.title),
                            React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") }, module.description))))); }))) : (React.createElement("div", { className: "text-center py-12 " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") },
                    React.createElement("div", { className: "flex justify-center mb-4" },
                        React.createElement(lucide_react_1.Search, { className: "h-12 w-12 opacity-30" })),
                    React.createElement("h3", { className: "text-lg font-medium mb-2" }, "\u672A\u627E\u5230\u5339\u914D\u7684\u6A21\u5757"),
                    React.createElement("p", null, "\u8BF7\u5C1D\u8BD5\u5176\u4ED6\u641C\u7D22\u5173\u952E\u8BCD")))))),
        React.createElement(alert_dialog_1.AlertDialog, { open: showNotificationDialog, onOpenChange: setShowNotificationDialog },
            React.createElement(alert_dialog_1.AlertDialogContent, { className: (themeMode === "dark"
                    ? "bg-[#2c2c2e] border-[#3a3a3c] text-white"
                    : "bg-white") + " max-w-[500px] border shadow-xl" },
                React.createElement(alert_dialog_1.AlertDialogHeader, null,
                    React.createElement("div", { className: "flex items-center gap-3 mb-4" },
                        selectedNotification && (React.createElement("div", { className: "p-2 rounded-full " + (selectedNotification.type === 'warning'
                                ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-500"
                                : selectedNotification.type === 'info'
                                    ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-500"
                                    : selectedNotification.type === 'success'
                                        ? themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-500"
                                        : selectedNotification.type === 'task'
                                            ? themeMode === "dark" ? "bg-purple-500/20 text-purple-400" : "bg-purple-100 text-purple-500"
                                            : selectedNotification.type === 'approval'
                                                ? themeMode === "dark" ? "bg-indigo-500/20 text-indigo-400" : "bg-indigo-100 text-indigo-500"
                                                : themeMode === "dark" ? "bg-gray-500/20 text-gray-400" : "bg-gray-100 text-gray-500") },
                            selectedNotification.type === 'warning' && React.createElement(lucide_react_1.AlertTriangle, { className: "h-5 w-5" }),
                            selectedNotification.type === 'info' && React.createElement(lucide_react_1.Info, { className: "h-5 w-5" }),
                            selectedNotification.type === 'success' && React.createElement(lucide_react_1.CheckCircle, { className: "h-5 w-5" }),
                            selectedNotification.type === 'task' && React.createElement(lucide_react_1.Clipboard, { className: "h-5 w-5" }),
                            selectedNotification.type === 'approval' && React.createElement(lucide_react_1.FileText, { className: "h-5 w-5" }),
                            selectedNotification.type === 'meeting' && React.createElement(lucide_react_1.Calendar, { className: "h-5 w-5" }))),
                        React.createElement(alert_dialog_1.AlertDialogTitle, { className: "text-xl" }, selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.title)),
                    React.createElement("div", { className: "flex justify-between items-center text-sm mb-4" },
                        React.createElement("span", { className: themeMode === "dark" ? "text-gray-400" : "text-gray-500" }, selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.time),
                        React.createElement("span", { className: "px-2 py-0.5 rounded-full text-xs " + ((selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.priority) === 'high'
                                ? themeMode === "dark" ? "bg-red-500/20 text-red-400" : "bg-red-100 text-red-600"
                                : (selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.priority) === 'medium'
                                    ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-600"
                                    : themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-600") }, (selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.priority) === 'high' ? '紧急' :
                            (selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.priority) === 'medium' ? '重要' : '一般')),
                    React.createElement(alert_dialog_1.AlertDialogDescription, { className: "text-base " + (themeMode === "dark" ? "text-gray-300" : "text-gray-600") }, selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification.content)),
                React.createElement(alert_dialog_1.AlertDialogFooter, { className: "flex gap-3 mt-6" },
                    React.createElement(button_1.Button, { variant: "outline", onClick: function () { return setShowNotificationDialog(false); }, className: "" + (themeMode === "dark"
                            ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                            : "bg-gray-50 text-[#1d1d1f] border-gray-200") }, "\u5173\u95ED"),
                    React.createElement(button_1.Button, { onClick: handleProcessNotification, className: "" + (themeMode === "dark"
                            ? "bg-white text-black hover:bg-gray-200"
                            : "bg-black text-white hover:bg-gray-800") }, "\u53BB\u5904\u7406"))))));
}
exports.Dashboard = Dashboard;
