"use client"

import { useState, useEffect, useMemo } from "react"
import {
  BarChart2,
  Calendar,
  Filter,
  HardHat,
  Plus,
  Search,
  Settings,
  Users,
  CheckCircle,
  AlertTriangle,
  ChevronRight,
  Download,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  Clock,
  FileText,
  Ban,
  Bookmark,
  CalendarClock,
  ChevronsUpDown,
  Clipboard,
  ClipboardCheck,
  FileBarChart,
  Hammer,
  HardDrive,
  LineChart,
  Loader2,
  PieChart,
  Tag,
  Workflow,
  Wrench,
  ArrowUpDown,
  Share2,
  SlidersHorizontal,
  Star,
  LayoutGrid,
  Banknote,
  X,
  AlignLeft,
  Target,
  Upload,
  Pencil,
  Activity,
  Waves,
  Pickaxe,
  ServerCrash,
  Shield,
  FileSearch,
  Leaf,
  ChevronDown,
  ChevronLeft,
  CalendarRange,
  MapPin,
  FileSpreadsheet,
  ListFilter,
  FilterIcon,
  ChevronUp
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetClose,
  SheetFooter
} from "@/components/ui/sheet"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import {
  RadioGroup,
  RadioGroupItem,
} from "@/components/ui/radio-group"
import * as XLSX from "xlsx"

// 定义工程项目接口
interface Project {
  id: string
  name: string
  manager: string
  startDate: string
  endDate: string
  progress: number
  status: string
  budget: string
  spent: string
  workers: number
  safetyIssues: number
  type: string
  priority: string
  description?: string
  location?: string
  objectives?: string
  risksAndIssues?: string
  milestones?: {
    name: string
    date: string
    completed: boolean
  }[]
  tags?: string[]
  attachments?: string[]
  createdAt?: string
  updatedAt?: string
  starred?: boolean
}

// 定义里程碑接口
interface Milestone {
  name: string
  date: string
  completed: boolean
}

// 日期格式化函数
const formatDateToLocale = (dateString: string | undefined) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "short",
    day: "numeric"
  });
};

// 更新统计数据结构
interface Statistics {
  totalProjects: number;
  ongoingProjects: number;
  completedProjects: number;
  initialProjects: number;
  totalWorkers: number;
  totalSafetyIssues: number;
  totalBudget: number;
  totalSpent: number;
  spendingPercentage: number;
  completionRate: number;
  onTimeProjects: number;
  delayedProjects: number;
  projectsByType: Record<string, number>;
  projectsByPriority: Record<string, number>;
  avgWorkersPerProject: number;
  avgSafetyIssuesPerProject: number;
  avgBudgetPerProject: number;
  resourceUtilization: number;
}

export function ProjectHomepage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [currentTime, setCurrentTime] = useState(new Date("2025-03-25T10:30:00"));
  const [selectedType, setSelectedType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBatchDeleteDialogOpen, setIsBatchDeleteDialogOpen] = useState(false);
  const [isProjectDrawerOpen, setIsProjectDrawerOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedViewMode, setSelectedViewMode] = useState<"table" | "card" | "gantt">("table");
  const [currentTab, setCurrentTab] = useState("all");
  const [exportFormat, setExportFormat] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [sortConfig, setSortConfig] = useState<{column: string, direction: 'asc' | 'desc'} | null>(null);
  const [formData, setFormData] = useState<Partial<Project>>({});
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false);
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);

  // 初始化项目数据
  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      name: "A矿区扩建工程",
      manager: "张工",
      startDate: "2025-03-01",
      endDate: "2025-04-30",
      progress: 35,
      status: "进行中",
      budget: "¥12,500,000",
      spent: "¥4,375,000",
      workers: 48,
      safetyIssues: 2,
      type: "扩建",
      priority: "高",
      description: "A矿区产能提升30%的扩建工程，包括新增设备采购和基础设施建设",
      location: "A矿区",
      objectives: "1. 新增两条生产线\n2. 改造现有通风系统\n3. 建设新的安全通道",
      risksAndIssues: "1. 施工期间可能影响现有生产\n2. 地质条件复杂，可能需要额外加固",
      milestones: [
        { name: "设计方案确认", date: "2025-03-15", completed: true },
        { name: "设备采购完成", date: "2025-03-30", completed: false },
        { name: "主体工程完工", date: "2025-04-15", completed: false },
        { name: "系统调试完成", date: "2025-04-25", completed: false }
      ],
      tags: ["扩建", "A矿区", "大型项目"],
      createdAt: "2025-02-28",
      updatedAt: "2025-03-15",
      starred: true
    },
    {
      id: "2",
      name: "B矿区安全改造",
      manager: "李工",
      startDate: "2025-03-10",
      endDate: "2025-04-20",
      progress: 15,
      status: "进行中",
      budget: "¥5,800,000",
      spent: "¥870,000",
      workers: 22,
      safetyIssues: 0,
      type: "改造",
      priority: "高",
      description: "B矿区安全系统全面升级改造，提高矿区安全等级",
      location: "B矿区",
      objectives: "1. 更新监控系统\n2. 增设紧急疏散通道\n3. 改造通风系统",
      risksAndIssues: "施工期间需要部分区域停工",
      milestones: [
        { name: "安全评估完成", date: "2025-03-20", completed: true },
        { name: "设备到货", date: "2025-03-25", completed: false },
        { name: "系统安装完成", date: "2025-04-10", completed: false },
        { name: "验收测试", date: "2025-04-15", completed: false }
      ],
      tags: ["安全", "改造", "B矿区"],
      createdAt: "2025-03-05",
      updatedAt: "2025-03-12"
    },
    {
      id: "3",
      name: "选矿厂工艺优化",
      manager: "王工",
      startDate: "2025-03-01",
      endDate: "2025-04-28",
      progress: 0,
      status: "未开始",
      budget: "¥8,200,000",
      spent: "¥0",
      workers: 0,
      safetyIssues: 0,
      type: "技改",
      priority: "中",
      description: "选矿厂工艺流程优化，提高精矿品位和回收率",
      location: "选矿厂",
      objectives: "1. 提高精矿品位2个百分点\n2. 提高回收率3个百分点\n3. 降低药剂消耗10%",
      milestones: [
        { name: "工艺方案确定", date: "2025-03-15", completed: false },
        { name: "设备改造完成", date: "2025-03-25", completed: false },
        { name: "调试运行", date: "2025-04-10", completed: false },
        { name: "验收", date: "2025-04-20", completed: false }
      ],
      tags: ["工艺优化", "选矿", "技改"],
      createdAt: "2025-02-20",
      updatedAt: "2025-02-20"
    },
    {
      id: "4",
      name: "尾矿库扩容工程",
      manager: "刘工",
      startDate: "2025-03-15",
      endDate: "2025-04-30",
      progress: 100,
      status: "已完成",
      budget: "¥15,300,000",
      spent: "¥14,850,000",
      workers: 0,
      safetyIssues: 3,
      type: "扩建",
      priority: "高",
      description: "尾矿库扩容工程，增加库容500万立方米",
      location: "尾矿库",
      objectives: "1. 增加库容500万立方\n2. 加高坝体15米\n3. 改造排水系统",
      milestones: [
        { name: "设计方案批复", date: "2025-03-20", completed: true },
        { name: "坝体加高", date: "2025-06-30", completed: true },
        { name: "排水系统改造", date: "2025-08-30", completed: true },
        { name: "验收", date: "2025-09-25", completed: true }
      ],
      tags: ["尾矿库", "扩容", "环保"],
      createdAt: "2025-02-28",
      updatedAt: "2025-04-30"
    },
    {
      id: "5",
      name: "矿区道路维修",
      manager: "赵工",
      startDate: "2025-03-20",
      endDate: "2025-04-15",
      progress: 10,
      status: "进行中",
      budget: "¥2,300,000",
      spent: "¥230,000",
      workers: 15,
      safetyIssues: 0,
      type: "维修",
      priority: "中",
      description: "矿区主干道路面维修及排水系统改造",
      location: "矿区道路",
      objectives: "1. 修复破损路面\n2. 改造排水沟\n3. 增设安全标识",
      milestones: [
        { name: "勘测完成", date: "2025-03-25", completed: true },
        { name: "路面修复", date: "2025-04-05", completed: false },
        { name: "排水沟改造", date: "2025-04-10", completed: false },
        { name: "验收", date: "2025-04-15", completed: false }
      ],
      tags: ["道路", "维修", "基础设施"],
      createdAt: "2025-03-10",
      updatedAt: "2025-03-22"
    },
    {
      id: "6",
      name: "矿山智能化改造",
      manager: "陈工",
      startDate: "2025-03-01",
      endDate: "2025-04-30",
      progress: 0,
      status: "未开始",
      budget: "¥18,500,000",
      spent: "¥0",
      workers: 0,
      safetyIssues: 0,
      type: "技改",
      priority: "高",
      description: "矿山开采智能化改造，引入自动化设备和远程控制系统",
      location: "采矿区",
      objectives: "1. 建设井下5G网络\n2. 引入无人采矿设备\n3. 建设远程控制中心",
      milestones: [
        { name: "方案设计", date: "2025-03-10", completed: false },
        { name: "设备采购", date: "2025-03-25", completed: false },
        { name: "系统安装", date: "2025-04-15", completed: false },
        { name: "调试验收", date: "2025-04-25", completed: false }
      ],
      tags: ["智能化", "自动化", "技改"],
      createdAt: "2025-02-15",
      updatedAt: "2025-02-15"
    },
    {
      id: "7",
      name: "矿区环保设施升级",
      manager: "杨工",
      startDate: "2025-03-15",
      endDate: "2025-04-15",
      progress: 0,
      status: "未开始",
      budget: "¥6,800,000",
      spent: "¥0",
      workers: 0,
      safetyIssues: 0,
      type: "环保",
      priority: "中",
      description: "矿区环保设施升级改造，满足新环保标准要求",
      location: "全矿区",
      objectives: "1. 升级废水处理系统\n2. 改造除尘设施\n3. 增设噪声监测点",
      milestones: [
        { name: "环评完成", date: "2025-03-20", completed: false },
        { name: "设备采购", date: "2025-03-30", completed: false },
        { name: "系统安装", date: "2025-04-10", completed: false },
        { name: "验收", date: "2025-04-15", completed: false }
      ],
      tags: ["环保", "升级", "合规"],
      createdAt: "2025-02-25",
      updatedAt: "2025-02-25"
    }
  ]);

  // 维护统计数据
  const [statistics, setStatistics] = useState<Statistics>({
    totalProjects: 7,
    ongoingProjects: 3,
    completedProjects: 1,
    initialProjects: 3,
    totalWorkers: 85,
    totalSafetyIssues: 5,
    totalBudget: 69400000,
    totalSpent: 20325000,
    spendingPercentage: 29.3,
    completionRate: 22.9,
    onTimeProjects: 4,
    delayedProjects: 0,
    projectsByType: {
      "扩建": 2,
      "改造": 1,
      "技改": 2,
      "维修": 1,
      "环保": 1
    },
    projectsByPriority: {
      "高": 4,
      "中": 3,
      "低": 0
    },
    avgWorkersPerProject: 12.1,
    avgSafetyIssuesPerProject: 0.7,
    avgBudgetPerProject: 9914285.7,
    resourceUtilization: 78.5
  });

  // 计算统计数据的函数
  const calculateStatistics = (projects: Project[]): Statistics => {
    const totalProjects = projects.length;
    const ongoingProjects = projects.filter(p => p.status === "进行中" || p.status === "即将完成").length;
    const completedProjects = projects.filter(p => p.status === "已完成").length;
    const initialProjects = projects.filter(p => p.status === "初期阶段").length;

    let totalWorkers = 0;
    let totalSafetyIssues = 0;
    let totalBudget = 0;
    let totalSpent = 0;
    let onTimeProjects = 0;
    let delayedProjects = 0;

    const projectsByType: Record<string, number> = {};
    const projectsByPriority: Record<string, number> = {};

    projects.forEach(project => {
      // 工人数量统计
      totalWorkers += project.workers;

      // 安全问题统计
      totalSafetyIssues += project.safetyIssues;

      // 预算统计
      const budget = typeof project.budget === 'string'
        ? parseInt(project.budget.replace(/,/g, ''), 10)
        : project.budget;
      const spent = typeof project.spent === 'string'
        ? parseInt(project.spent.replace(/,/g, ''), 10)
        : project.spent;

      totalBudget += budget;
      totalSpent += spent;

      // 按时/延期项目统计
      if (project.status === "已完成") {
        const endDate = new Date(project.endDate);
        const today = new Date();
        if (endDate <= today) {
          onTimeProjects++;
        } else {
          delayedProjects++;
        }
      }

      // 按类型分组
      if (project.type) {
        projectsByType[project.type] = (projectsByType[project.type] || 0) + 1;
      }

      // 按优先级分组
      if (project.priority) {
        projectsByPriority[project.priority] = (projectsByPriority[project.priority] || 0) + 1;
      }
    });

    // 计算平均值和百分比
    const spendingPercentage = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0;
    const completionRate = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0;
    const avgWorkersPerProject = totalProjects > 0 ? Math.round(totalWorkers / totalProjects) : 0;
    const avgSafetyIssuesPerProject = totalProjects > 0 ? Math.round(totalSafetyIssues / totalProjects * 10) / 10 : 0;
    const avgBudgetPerProject = totalProjects > 0 ? Math.round(totalBudget / totalProjects) : 0;

    // 模拟资源利用率 (示例计算)
    const resourceUtilization = Math.min(95, Math.round(totalWorkers * 100 / (totalProjects * 10 + 5)));

    return {
      totalProjects,
      ongoingProjects,
      completedProjects,
      initialProjects,
      totalWorkers,
      totalSafetyIssues,
      totalBudget,
      totalSpent,
      spendingPercentage,
      completionRate,
      onTimeProjects,
      delayedProjects,
      projectsByType,
      projectsByPriority,
      avgWorkersPerProject,
      avgSafetyIssuesPerProject,
      avgBudgetPerProject,
      resourceUtilization
    };
  };

  // 更新统计数据
  useEffect(() => {
    // 统计计算
    const stats = calculateStatistics(projects);
    setStatistics(stats);

    // 设置定时器每分钟更新当前时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, [projects]);

  // 添加useEffect初始化过滤项目
  useEffect(() => {
    // 统计计算
    const stats = calculateStatistics(projects);
    setStatistics(stats);

    // 设置定时器每分钟更新当前时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, [projects]);

  // 消息提示
  const showMessage = (type: "success" | "error", content: string) => {
    toast({
      variant: type === "success" ? "default" : "destructive",
      title: content,
    })
  }

  // 导出Excel
  const handleExportExcel = () => {
    setIsExportDialogOpen(true);
  }

  // 确认导出
  const confirmExport = (format: string) => {
    setLoading(true);
    try {
      // 准备导出数据
      const exportData = projects.map(project => ({
        '项目名称': project.name,
        '负责人': project.manager,
        '项目类型': project.type,
        '开始日期': project.startDate,
        '结束日期': project.endDate,
        '进度': `${project.progress}%`,
        '状态': project.status,
        '预算': project.budget,
        '已花费': project.spent,
        '工作人员数量': project.workers,
        '安全问题': project.safetyIssues,
        '优先级': project.priority,
        '位置': project.location || '',
        '描述': project.description || '',
        '目标': project.objectives || '',
        '风险和问题': project.risksAndIssues || '',
        '标签': project.tags ? project.tags.join(', ') : '',
        '创建时间': project.createdAt || '',
        '更新时间': project.updatedAt || '',
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 25 },  // 项目名称
        { wch: 15 },  // 负责人
        { wch: 15 },  // 项目类型
        { wch: 15 },  // 开始日期
        { wch: 15 },  // 结束日期
        { wch: 10 },  // 进度
        { wch: 15 },  // 状态
        { wch: 15 },  // 预算
        { wch: 15 },  // 已花费
        { wch: 15 },  // 工作人员数量
        { wch: 15 },  // 安全问题
        { wch: 10 },  // 优先级
        { wch: 20 },  // 位置
        { wch: 50 },  // 描述
        { wch: 50 },  // 目标
        { wch: 50 },  // 风险和问题
        { wch: 30 },  // 标签
        { wch: 20 },  // 创建时间
        { wch: 20 },  // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '项目列表');

      // 根据格式导出文件
      if (format === 'excel') {
        XLSX.writeFile(wb, `项目列表_${new Date().toISOString().split('T')[0]}.xlsx`);
      } else if (format === 'csv') {
        XLSX.writeFile(wb, `项目列表_${new Date().toISOString().split('T')[0]}.csv`);
      } else {
        // 默认为Excel
        XLSX.writeFile(wb, `项目列表_${new Date().toISOString().split('T')[0]}.xlsx`);
      }

      toast({
        title: "导出成功",
        description: `数据已导出为${format === 'excel' ? 'Excel' : format === 'csv' ? 'CSV' : 'PDF'}格式`,
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        variant: "destructive",
        title: "导出失败",
        description: "导出过程中出现错误",
      });
    } finally {
      setLoading(false);
      setIsExportDialogOpen(false);
    }
  }

  // 导入数据
  const handleImportData = () => {
    setIsImportDialogOpen(true)
  }

  // 确认导入
  const confirmImport = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      setIsImportDialogOpen(false)
      showMessage("success", "数据已导入")
    }, 1000)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "数据已刷新",
        description: "项目数据已更新至最新状态",
      });
    }, 800)
  }

  // 标记星标
  const handleToggleStar = (id: string) => {
    setProjects(prev =>
      prev.map(p => p.id === id ? { ...p, starred: !p.starred } : p)
    );

    const project = projects.find(p => p.id === id);
    if (project) {
      toast({
        title: project.starred ? "已取消标记" : "已标记为重要",
        description: `项目 "${project.name}" ${project.starred ? "已取消标记" : "已标记为重要项目"}`,
      });
    }
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedProjects.length === 0) return;
    setIsBatchDeleteDialogOpen(true);
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    if (selectedProjects.length === 0) return;

    setLoading(true);
    setTimeout(() => {
      // 在真实环境下这里会是API调用
      setProjects(prev => prev.filter(project => !selectedProjects.includes(project.id)));
      setSelectedProjects([]);
      setIsBatchDeleteDialogOpen(false);
      setLoading(false);
      toast({
        title: "批量删除成功",
        description: `已成功删除 ${selectedProjects.length} 个项目`,
      });
    }, 800);
  }

  // 处理行选择
  const handleRowSelect = (id: string) => {
    setSelectedProjects(prev =>
      prev.includes(id)
        ? prev.filter(key => key !== id)
        : [...prev, id]
    );
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProjects(filteredProjects.map(p => p.id));
    } else {
      setSelectedProjects([]);
    }
  }

  // 过滤项目数据
  useEffect(() => {
    // 过滤项目数据
    const filtered = projects.filter(project => {
      // 根据当前标签筛选
      if (currentTab === "inProgress" && project.status !== "进行中") return false
      if (currentTab === "completed" && project.status !== "已完成") return false
      if (currentTab === "initial" && project.status !== "初期阶段") return false

      // 根据搜索文本筛选
      const searchLower = searchText.toLowerCase()
      const nameMatch = project.name.toLowerCase().includes(searchLower)
      const managerMatch = project.manager.toLowerCase().includes(searchLower)
      const locationMatch = project.location?.toLowerCase().includes(searchLower) || false
      const descriptionMatch = project.description?.toLowerCase().includes(searchLower) || false
      const tagsMatch = project.tags?.some(tag => tag.toLowerCase().includes(searchLower)) || false

      if (searchText && !nameMatch && !managerMatch && !locationMatch && !descriptionMatch && !tagsMatch) {
        return false
      }

      // 根据类型筛选
      if (selectedType !== "all" && project.type !== selectedType) {
        return false
      }

      // 根据状态筛选
      if (selectedStatus !== "all" && project.status !== selectedStatus) {
        return false
      }

      // 根据优先级筛选
      if (selectedPriority !== "all" && project.priority !== selectedPriority) {
        return false
      }

      return true
    });

    // 应用排序
    if (sortConfig) {
      filtered.sort((a, b) => {
        if (sortConfig.column === 'name') {
          return sortConfig.direction === 'asc'
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        }

        if (sortConfig.column === 'progress') {
          return sortConfig.direction === 'asc'
            ? a.progress - b.progress
            : b.progress - a.progress;
        }

        // 默认按名称排序
        return a.name.localeCompare(b.name);
      });
    }

    // 更新过滤后的项目列表
    setFilteredProjects(filtered);
  }, [projects, currentTab, searchText, selectedType, selectedStatus, selectedPriority, sortConfig]);

  // 处理查看项目详情
  const handleViewProject = (project: Project) => {
    setSelectedProject(project);
    setIsProjectDrawerOpen(true);
  }

  // 处理添加项目
  const handleAddProject = () => {
    setIsAddDialogOpen(true);
  };

  // 处理编辑项目
  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setFormData({...project});
    setIsEditDialogOpen(true);
  }

  // 处理删除项目
  const handleDeleteProject = (id: string) => {
    setSelectedProjects([id]);
    setIsDeleteDialogOpen(true);
  }

  // 确认删除项目
  const confirmDeleteProject = () => {
    setLoading(true);
    setTimeout(() => {
      // 在真实环境下这里会是API调用
      setProjects(prev => prev.filter(project => !selectedProjects.includes(project.id)));
      setSelectedProjects([]);
      setIsDeleteDialogOpen(false);
      setLoading(false);
      toast({
        title: "项目已删除",
        description: "项目已成功删除",
      });
    }, 800);
  }

  // 保存项目（添加或编辑）
  const handleSaveProject = (isEdit: boolean) => {
    if (!formData.name || !formData.manager || !formData.startDate || !formData.endDate) {
      showMessage("error", "请填写必填项")
      return
    }

    if (isEdit && selectedProject) {
      // 编辑现有项目
      setProjects(prev =>
        prev.map(p => p.id === selectedProject.id ? { ...p, ...formData } : p)
      )
      setIsEditDialogOpen(false)
      showMessage("success", "项目已更新")
    } else {
      // 添加新项目
      const newProject: Project = {
        id: `${projects.length + 1}`,
        name: formData.name || "",
        manager: formData.manager || "",
        startDate: formData.startDate || "",
        endDate: formData.endDate || "",
        progress: formData.progress || 0,
        status: formData.status || "初期阶段",
        budget: formData.budget || "¥0",
        spent: formData.spent || "¥0",
        workers: formData.workers || 0,
        safetyIssues: formData.safetyIssues || 0,
        type: formData.type || "采矿工程",
        priority: formData.priority || "中",
        description: formData.description,
        location: formData.location,
        objectives: formData.objectives,
        risksAndIssues: formData.risksAndIssues,
        tags: formData.tags,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0],
      }
      setProjects(prev => [...prev, newProject])
      setIsAddDialogOpen(false)
      showMessage("success", "项目已添加")
    }
  }

  // 表格视图
  const renderProjectsTable = () => {
    if (filteredProjects.length === 0) {
  return (
        <div className="border rounded-md p-8 text-center">
          <p className="text-muted-foreground">没有找到符合条件的项目</p>
        </div>
      );
    }

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={selectedProjects.length === filteredProjects.length && filteredProjects.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('name')}>
                <div className="flex items-center">
                  项目名称
                  {sortConfig?.column === 'name' && (
                    <ChevronUp className={`ml-2 h-4 w-4 ${sortConfig.direction === 'desc' ? 'transform rotate-180' : ''}`} />
                  )}
                </div>
              </TableHead>
              <TableHead>负责人</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>开始时间</TableHead>
              <TableHead>结束时间</TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('progress')}>
                <div className="flex items-center">
                  进度
                  {sortConfig?.column === 'progress' && (
                    <ChevronUp className={`ml-2 h-4 w-4 ${sortConfig.direction === 'desc' ? 'transform rotate-180' : ''}`} />
                  )}
                </div>
              </TableHead>
              <TableHead>状态</TableHead>
              <TableHead>优先级</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProjects.map((project) => (
              <TableRow key={project.id} className={selectedProjects.includes(project.id) ? "bg-muted/50" : undefined}>
                <TableCell>
                  <Checkbox
                    checked={selectedProjects.includes(project.id)}
                    onCheckedChange={() => handleRowSelect(project.id)}
                  />
                </TableCell>
                <TableCell className="font-medium cursor-pointer hover:underline" onClick={() => handleViewProject(project)}>
                  {project.name}
                </TableCell>
                <TableCell>{project.manager}</TableCell>
                <TableCell className="hidden md:table-cell">{project.type}</TableCell>
                <TableCell className="hidden md:table-cell">{project.startDate}</TableCell>
                <TableCell className="hidden md:table-cell">{project.endDate}</TableCell>
                <TableCell>
        <div className="flex items-center gap-2">
                      <div className="w-full max-w-[100px]">
                        <Progress value={project.progress} className="h-2" />
                      </div>
                      <span className="text-xs">{project.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      project.status === "已完成" ? "default" :
                      project.status === "进行中" ? "secondary" :
                      project.status === "即将完成" ? "outline" : "secondary"
                    }>
                      {project.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <Badge variant={
                      project.priority === "高" ? "destructive" :
                      project.priority === "中" ? "default" : "secondary"
                    }>
                      {project.priority}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
          </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewProject(project)}>
                          <Eye className="h-4 w-4 mr-2" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditProject(project)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑项目
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => handleDeleteProject(project.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除项目
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
            ))}
          </TableBody>
        </Table>
        </div>
    );
  };

  // 处理排序函数
  const handleSort = (column: string) => {
    // 如果点击同一列，则切换排序方向
    if (sortConfig && sortConfig.column === column) {
      setSortConfig(
        sortConfig.direction === 'asc'
          ? { column, direction: 'desc' }
          : { column, direction: 'asc' }
      );
    } else {
      // 默认以升序开始
      setSortConfig({ column, direction: 'asc' });
    }
  };

  // 渲染项目卡片视图
  const renderProjectsCards = (projects: Project[]) => {
    if (projects.length === 0) {
    return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <FileSearch className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">无匹配项目</h3>
          <p className="text-sm text-muted-foreground">
            尝试调整筛选条件或搜索其他关键词
          </p>
      </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {projects.map((project) => (
          <Card key={project.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <CardTitle className="flex items-center">
                    {project.name}
                    {project.starred && (
                      <Star
                        className="h-4 w-4 ml-2 text-yellow-400 fill-yellow-400"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleStar(project.id);
                    }}
                      />
                    )}
                  </CardTitle>
                  <CardDescription>{project.manager}</CardDescription>
            </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <span className="sr-only">打开菜单</span>
                      <MoreHorizontal className="h-4 w-4" />
                  </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewProject(project)}>
                      <Eye className="h-4 w-4 mr-2" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleEditProject(project)}>
                      <Edit className="h-4 w-4 mr-2" />
                      编辑项目
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => handleDeleteProject(project.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      删除项目
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
            </div>
            </CardHeader>
            <CardContent className="pb-3">
              <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                <div className="flex items-center">
                  <CalendarRange className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{project.startDate} ~ {project.endDate}</span>
            </div>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{project.location}</span>
            </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>进度</span>
                  <span>{project.progress}%</span>
                </div>
                <Progress value={project.progress} className="h-2" />
              </div>
              <div className="mt-4 flex flex-wrap gap-1">
                    <Badge variant={
                  project.status === "已完成" ? "default" :
                  project.status === "进行中" ? "secondary" :
                  project.status === "即将完成" ? "outline" : "secondary"
                    }>
                      {project.status}
                    </Badge>
                <Badge variant="outline">{project.type}</Badge>
                {project.priority && (
                    <Badge variant={
                      project.priority === "高" ? "destructive" :
                    project.priority === "中" ? "default" : "secondary"
                    }>
                    {project.priority}
                    </Badge>
                )}
                {project.tags && project.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="bg-muted">
                        {tag}
                      </Badge>
                    ))}
              </div>
          </CardContent>
        </Card>
        ))}
      </div>
    );
  };

  // 渲染甘特图视图
  const renderProjectsGantt = (projects: Project[]) => {
    if (projects.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <FileSearch className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">无匹配项目</h3>
          <p className="text-sm text-muted-foreground">
            尝试调整筛选条件或搜索其他关键词
          </p>
        </div>
      );
    }

    // 找出所有项目的最早开始日期和最晚结束日期
    const today = new Date();
    const startDates = projects.map(p => new Date(p.startDate));
    const endDates = projects.map(p => new Date(p.endDate));
    const earliestDate = new Date(Math.min(...startDates.map(d => d.getTime())));
    const latestDate = new Date(Math.max(...endDates.map(d => d.getTime())));

    // 计算时间轴的总天数
    const totalDays = Math.ceil((latestDate.getTime() - earliestDate.getTime()) / (1000 * 60 * 60 * 24)) + 10;

    // 计算每个项目在时间轴上的位置
    const projectTimeline = projects.map(project => {
      const start = new Date(project.startDate);
      const end = new Date(project.endDate);
      const offsetDays = Math.floor((start.getTime() - earliestDate.getTime()) / (1000 * 60 * 60 * 24));
      const durationDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      return {
        ...project,
        offsetDays,
        durationDays,
        isDelayed: end < today && project.status !== "已完成",
        isCompleted: project.status === "已完成"
      };
    });

    return (
      <div className="relative overflow-x-auto">
        <div className="min-w-[1000px]">
          {/* 时间轴头部 */}
          <div className="flex border-b h-10">
            <div className="w-[200px] shrink-0 font-medium p-2 border-r">项目名称</div>
            <div className="flex-1 flex">
              {Array.from({ length: 10 }).map((_, i) => {
                const date = new Date(earliestDate);
                date.setDate(date.getDate() + i * Math.ceil(totalDays / 10));
                return (
                  <div
                    key={i}
                    className="flex-1 text-xs text-center border-r p-2"
                  >
                    {date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
            </div>
                );
              })}
            </div>
        </div>

          {/* 项目甘特图 */}
          {projectTimeline.map(project => (
            <div
              key={project.id}
              className="flex border-b hover:bg-muted/20 cursor-pointer"
              onClick={() => handleViewProject(project)}
            >
              <div className="w-[200px] shrink-0 p-2 border-r truncate flex items-center">
            <div>
                  <div className="font-medium">{project.name}</div>
                  <div className="text-xs text-muted-foreground">{project.manager}</div>
            </div>
          </div>
              <div className="flex-1 relative h-16">
                  <div
                    className={cn(
                    "absolute top-2 h-8 rounded-md flex items-center justify-center text-xs font-medium text-white shadow-sm",
                    project.isCompleted ? "bg-green-500" :
                    project.isDelayed ? "bg-red-500" :
                    project.priority === "高" ? "bg-orange-500" :
                    project.priority === "中" ? "bg-blue-500" :
                      "bg-slate-500"
                    )}
                    style={{
                    left: `${(project.offsetDays / totalDays) * 100}%`,
                    width: `${(project.durationDays / totalDays) * 100}%`,
                    maxWidth: 'calc(100% - 8px)'
                    }}
                  >
                  {project.name} ({project.progress}%)
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 项目详情抽屉组件
  const ProjectDetailsDrawer = () => {
    // 确保有选中的项目
    if (!selectedProject) return null;

    return (
      <Sheet open={isProjectDrawerOpen} onOpenChange={setIsProjectDrawerOpen}>
        <SheetContent className="sm:max-w-xl w-full overflow-y-auto">
          <SheetHeader className="border-b pb-4">
            <SheetTitle className="flex items-center">
              {selectedProject.name}
                <Button
                variant="ghost"
                size="icon"
                className="ml-2"
                onClick={() => handleToggleStar(selectedProject.id)}
              >
                <Star
                  className={cn(
                    "h-5 w-5",
                    selectedProject.starred ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
                  )}
                />
                </Button>
            </SheetTitle>
            <SheetDescription>
              项目详细信息
            </SheetDescription>
          </SheetHeader>

          <div className="py-4 space-y-6">
            {/* 基本信息 */}
                      <div>
              <h3 className="text-sm font-medium mb-2">基本信息</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <div className="text-muted-foreground">项目经理</div>
                  <div>{selectedProject.manager}</div>
                        </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">项目类型</div>
                  <div>{selectedProject.type}</div>
                      </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">项目状态</div>
                <div>
                    <Badge variant={
                      selectedProject.status === "已完成" ? "default" :
                      selectedProject.status === "进行中" ? "secondary" :
                      selectedProject.status === "即将完成" ? "outline" : "secondary"
                    }>
                      {selectedProject.status}
                      </Badge>
                </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">优先级</div>
                <div>
                  <Badge variant={
                      selectedProject.priority === "高" ? "destructive" :
                      selectedProject.priority === "中" ? "default" : "secondary"
                  }>
                    {selectedProject.priority}
                  </Badge>
                </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">开始日期</div>
                  <div>{selectedProject.startDate}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">结束日期</div>
                  <div>{selectedProject.endDate}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">工作人员</div>
                  <div>{selectedProject.workers} 人</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">安全问题</div>
                  <div>{selectedProject.safetyIssues} 个</div>
                </div>
              </div>
                    </div>

            {/* 项目进度 */}
                <div>
              <h3 className="text-sm font-medium mb-2">项目进度</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>完成度</span>
                  <span>{selectedProject.progress}%</span>
                      </div>
                <Progress value={selectedProject.progress} className="h-2" />
                      </div>
                      </div>

            {/* 项目预算 */}
                <div>
              <h3 className="text-sm font-medium mb-2">项目预算</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <div className="text-muted-foreground">总预算</div>
                  <div>¥{selectedProject.budget.toLocaleString()}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">已花费</div>
                  <div>¥{selectedProject.spent.toLocaleString()}</div>
                </div>
                  </div>
              <div className="mt-2 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>支出进度</span>
                  <span>
                    {Math.round((Number(selectedProject.spent) / Number(selectedProject.budget)) * 100)}%
                        </span>
                </div>
                <Progress
                  value={Math.min(100, (Number(selectedProject.spent) / Number(selectedProject.budget)) * 100)}
                  className="h-2"
                />
                      </div>
                    </div>

            {/* 项目位置 */}
            <div>
              <h3 className="text-sm font-medium mb-2">项目位置</h3>
              <div className="text-sm">{selectedProject.location}</div>
                      </div>

            {/* 项目描述 */}
                  <div>
              <h3 className="text-sm font-medium mb-2">项目描述</h3>
              <div className="text-sm">{selectedProject.description}</div>
                    </div>

            {/* 项目目标 */}
                <div>
              <h3 className="text-sm font-medium mb-2">项目目标</h3>
              <div className="text-sm">{selectedProject.objectives}</div>
                    </div>

            {/* 风险与问题 */}
                <div>
              <h3 className="text-sm font-medium mb-2">风险与问题</h3>
              <div className="text-sm">{selectedProject.risksAndIssues}</div>
                  </div>

            {/* 里程碑 */}
              {selectedProject.milestones && selectedProject.milestones.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">项目里程碑</h3>
                <div className="space-y-2">
                    {selectedProject.milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center justify-between text-sm border-l-2 pl-2 border-primary">
                      <div>
                        <div>{milestone.name}</div>
                        <div className="text-xs text-muted-foreground">{milestone.date}</div>
                        </div>
                      <Badge variant={milestone.completed ? "default" : "outline"}>
                        {milestone.completed ? "已完成" : "进行中"}
                      </Badge>
                    </div>
              ))}
            </div>
          </div>
            )}

            {/* 标签 */}
            {selectedProject.tags && selectedProject.tags.length > 0 && (
            <div>
                <h3 className="text-sm font-medium mb-2">项目标签</h3>
                <div className="flex flex-wrap gap-1">
                  {selectedProject.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
            </div>
              </div>
            )}

            {/* 时间信息 */}
            <div className="pt-4 border-t text-xs text-muted-foreground flex justify-between">
              <div>创建时间: {selectedProject.createdAt}</div>
              <div>更新时间: {selectedProject.updatedAt}</div>
              </div>
          </div>

          <SheetFooter className="pt-4 border-t flex space-x-2">
            <Button onClick={() => handleEditProject(selectedProject)}>
              <Edit className="h-4 w-4 mr-2" />
              编辑项目
            </Button>
            <Button variant="outline" onClick={() => setIsProjectDrawerOpen(false)}>
              关闭
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    );
  };

  // 编辑项目对话框组件
  const EditProjectDialog = () => {
    return (
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑项目</DialogTitle>
            <DialogDescription>
              编辑项目信息，完成后点击保存
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">项目名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="输入项目名称"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="manager">负责人 <span className="text-red-500">*</span></Label>
                <Input
                  id="manager"
                  placeholder="输入负责人姓名"
                  value={formData.manager || ''}
                  onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">项目类型 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.type || '采矿工程'}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择项目类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程">采矿工程</SelectItem>
                    <SelectItem value="设备工程">设备工程</SelectItem>
                    <SelectItem value="安全工程">安全工程</SelectItem>
                    <SelectItem value="勘探工程">勘探工程</SelectItem>
                    <SelectItem value="环保工程">环保工程</SelectItem>
                    <SelectItem value="建筑工程">建筑工程</SelectItem>
                    <SelectItem value="道路工程">道路工程</SelectItem>
                    <SelectItem value="桥梁工程">桥梁工程</SelectItem>
                    <SelectItem value="隧道工程">隧道工程</SelectItem>
                    <SelectItem value="水利工程">水利工程</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">优先级 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.priority || '中'}
                  onValueChange={(value) => setFormData({ ...formData, priority: value })}
                >
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="startDate">开始日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate || ''}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">结束日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate || ''}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">项目状态 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.status || '初期阶段'}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择项目状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="初期阶段">初期阶段</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="即将完成">即将完成</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="progress">完成进度 <span className="text-red-500">*</span></Label>
              <div className="flex items-center gap-2">
                  <Input
                    id="progress"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.progress || 0}
                    onChange={(e) => setFormData({ ...formData, progress: parseInt(e.target.value) })}
                  />
                  <span>%</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="budget">项目预算 <span className="text-red-500">*</span></Label>
                <Input
                  id="budget"
                  placeholder="输入项目预算"
                  value={formData.budget || ''}
                  onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="spent">已花费 <span className="text-red-500">*</span></Label>
                <Input
                  id="spent"
                  placeholder="输入已花费金额"
                  value={formData.spent || ''}
                  onChange={(e) => setFormData({ ...formData, spent: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="workers">工作人员数量 <span className="text-red-500">*</span></Label>
                <Input
                  id="workers"
                  type="number"
                  min="0"
                  value={formData.workers || 0}
                  onChange={(e) => setFormData({ ...formData, workers: parseInt(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="safetyIssues">安全问题数量 <span className="text-red-500">*</span></Label>
                <Input
                  id="safetyIssues"
                  type="number"
                  min="0"
                  value={formData.safetyIssues || 0}
                  onChange={(e) => setFormData({ ...formData, safetyIssues: parseInt(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">项目位置</Label>
                <Input
                  id="location"
                  placeholder="输入项目位置"
                  value={formData.location || ''}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">标签 (用逗号分隔)</Label>
                <Input
                  id="tags"
                  placeholder="输入标签，多个标签用逗号分隔"
                  value={formData.tags ? formData.tags.join(',') : ''}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag) })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">项目描述</Label>
              <Textarea
                id="description"
                placeholder="输入项目描述"
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="objectives">项目目标</Label>
              <Textarea
                id="objectives"
                placeholder="输入项目目标"
                value={formData.objectives || ''}
                onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="risksAndIssues">风险与问题</Label>
              <Textarea
                id="risksAndIssues"
                placeholder="输入项目面临的风险与问题"
                value={formData.risksAndIssues || ''}
                onChange={(e) => setFormData({ ...formData, risksAndIssues: e.target.value })}
                rows={3}
              />
            </div>

            {formData.milestones && formData.milestones.length > 0 && (
              <div className="space-y-2">
                <Label>项目里程碑</Label>
                <div className="border rounded-md p-3 space-y-3">
                  {formData.milestones.map((milestone, index) => (
                    <div key={index} className="grid grid-cols-3 gap-2 items-center">
                      <Input
                        placeholder="里程碑名称"
                        value={milestone.name}
                        onChange={(e) => {
                          const newMilestones = [...formData.milestones!];
                          newMilestones[index].name = e.target.value;
                          setFormData({ ...formData, milestones: newMilestones });
                        }}
                      />
                      <Input
                        type="date"
                        value={milestone.date}
                        onChange={(e) => {
                          const newMilestones = [...formData.milestones!];
                          newMilestones[index].date = e.target.value;
                          setFormData({ ...formData, milestones: newMilestones });
                        }}
                      />
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`milestone-${index}`}
                          checked={milestone.completed}
                          onCheckedChange={(checked) => {
                            const newMilestones = [...formData.milestones!];
                            newMilestones[index].completed = checked as boolean;
                            setFormData({ ...formData, milestones: newMilestones });
                          }}
                        />
                        <Label htmlFor={`milestone-${index}`}>已完成</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="ml-auto"
                          onClick={() => {
                            const newMilestones = [...formData.milestones!];
                            newMilestones.splice(index, 1);
                            setFormData({ ...formData, milestones: newMilestones });
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newMilestones = [...(formData.milestones || [])];
                      newMilestones.push({
                        name: '',
                        date: new Date().toISOString().split('T')[0],
                        completed: false
                      });
                      setFormData({ ...formData, milestones: newMilestones });
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    添加里程碑
                  </Button>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditDialogOpen(false);
              setFormData({});
            }}>
              取消
            </Button>
            {loading ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </Button>
            ) : (
            <Button onClick={() => handleSaveProject(true)}>保存项目</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // 添加项目对话框组件
  const AddProjectDialog = () => {
    return (
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        setIsAddDialogOpen(open);
        if (!open) setFormData({});
      }}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加新项目</DialogTitle>
            <DialogDescription>
              填写新项目信息，完成后点击创建
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-name">项目名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-name"
                  placeholder="输入项目名称"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-manager">负责人 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-manager"
                  placeholder="输入负责人姓名"
                  value={formData.manager || ''}
                  onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-type">项目类型 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.type || '采矿工程'}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger id="add-type">
                    <SelectValue placeholder="选择项目类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程">采矿工程</SelectItem>
                    <SelectItem value="设备工程">设备工程</SelectItem>
                    <SelectItem value="安全工程">安全工程</SelectItem>
                    <SelectItem value="勘探工程">勘探工程</SelectItem>
                    <SelectItem value="环保工程">环保工程</SelectItem>
                    <SelectItem value="建筑工程">建筑工程</SelectItem>
                    <SelectItem value="道路工程">道路工程</SelectItem>
                    <SelectItem value="桥梁工程">桥梁工程</SelectItem>
                    <SelectItem value="隧道工程">隧道工程</SelectItem>
                    <SelectItem value="水利工程">水利工程</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-priority">优先级 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.priority || '中'}
                  onValueChange={(value) => setFormData({ ...formData, priority: value })}
                >
                  <SelectTrigger id="add-priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-startDate">开始日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-startDate"
                  type="date"
                  value={formData.startDate || ''}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-endDate">结束日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-endDate"
                  type="date"
                  value={formData.endDate || ''}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-status">项目状态 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.status || '初期阶段'}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger id="add-status">
                    <SelectValue placeholder="选择项目状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="初期阶段">初期阶段</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="即将完成">即将完成</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-progress">完成进度 <span className="text-red-500">*</span></Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="add-progress"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.progress || 0}
                    onChange={(e) => setFormData({ ...formData, progress: parseInt(e.target.value) })}
                  />
                  <span>%</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-budget">项目预算 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-budget"
                  placeholder="输入项目预算"
                  value={formData.budget || ''}
                  onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-spent">已花费 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-spent"
                  placeholder="输入已花费金额"
                  value={formData.spent || ''}
                  onChange={(e) => setFormData({ ...formData, spent: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-workers">工作人员数量 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-workers"
                  type="number"
                  min="0"
                  value={formData.workers || 0}
                  onChange={(e) => setFormData({ ...formData, workers: parseInt(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-safetyIssues">安全问题数量 <span className="text-red-500">*</span></Label>
                <Input
                  id="add-safetyIssues"
                  type="number"
                  min="0"
                  value={formData.safetyIssues || 0}
                  onChange={(e) => setFormData({ ...formData, safetyIssues: parseInt(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-location">项目位置</Label>
                <Input
                  id="add-location"
                  placeholder="输入项目位置"
                  value={formData.location || ''}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-tags">标签 (用逗号分隔)</Label>
                <Input
                  id="add-tags"
                  placeholder="输入标签，多个标签用逗号分隔"
                  value={formData.tags ? formData.tags.join(',') : ''}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag) })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-description">项目描述</Label>
              <Textarea
                id="add-description"
                placeholder="输入项目描述"
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-objectives">项目目标</Label>
              <Textarea
                id="add-objectives"
                placeholder="输入项目目标"
                value={formData.objectives || ''}
                onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-risksAndIssues">风险与问题</Label>
              <Textarea
                id="add-risksAndIssues"
                placeholder="输入项目面临的风险与问题"
                value={formData.risksAndIssues || ''}
                onChange={(e) => setFormData({ ...formData, risksAndIssues: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>项目里程碑</Label>
              <div className="border rounded-md p-3 space-y-3">
                {(formData.milestones || []).map((milestone, index) => (
                  <div key={index} className="grid grid-cols-3 gap-2 items-center">
                    <Input
                      placeholder="里程碑名称"
                      value={milestone.name}
                      onChange={(e) => {
                        const newMilestones = [...(formData.milestones || [])];
                        newMilestones[index].name = e.target.value;
                        setFormData({ ...formData, milestones: newMilestones });
                      }}
                    />
                    <Input
                      type="date"
                      value={milestone.date}
                      onChange={(e) => {
                        const newMilestones = [...(formData.milestones || [])];
                        newMilestones[index].date = e.target.value;
                        setFormData({ ...formData, milestones: newMilestones });
                      }}
                    />
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id={`add-milestone-${index}`}
                        checked={milestone.completed}
                        onCheckedChange={(checked) => {
                          const newMilestones = [...(formData.milestones || [])];
                          newMilestones[index].completed = checked as boolean;
                          setFormData({ ...formData, milestones: newMilestones });
                        }}
                      />
                      <Label htmlFor={`add-milestone-${index}`}>已完成</Label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="ml-auto"
                        onClick={() => {
                          const newMilestones = [...(formData.milestones || [])];
                          newMilestones.splice(index, 1);
                          setFormData({ ...formData, milestones: newMilestones });
                        }}
                      >
                        <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newMilestones = [...(formData.milestones || [])];
                    newMilestones.push({
                      name: '',
                      date: new Date().toISOString().split('T')[0],
                      completed: false
                    });
                    setFormData({ ...formData, milestones: newMilestones });
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加里程碑
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddDialogOpen(false);
              setFormData({});
            }}>
              取消
            </Button>
            {loading ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                创建中...
              </Button>
            ) : (
            <Button onClick={() => handleSaveProject(false)}>创建项目</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // 添加批量编辑函数
  const handleBatchEdit = () => {
    toast({
      title: "批量编辑",
      description: "批量编辑功能正在开发中",
    });
  };

  // 高级筛选对话框组件
  const AdvancedFilterDialog = () => {
    const [localFilters, setLocalFilters] = useState<{
      status: string[];
      type: string[];
      priority: string[];
      startDate: string;
      endDate: string;
      budgetMin: string;
      budgetMax: string;
      spentMin: string;
      spentMax: string;
      progressMin: string;
      progressMax: string;
    }>({...filters});

    const handleApplyFilters = () => {
      setFilters(localFilters);
      setIsFilterDialogOpen(false);
      toast({
        title: "筛选条件已应用",
        description: "项目列表已根据筛选条件更新",
      });
    };

    const handleResetFilters = () => {
      const defaultFilters = {
        status: [],
        type: [],
        priority: [],
        startDate: '',
        endDate: '',
        budgetMin: '',
        budgetMax: '',
        spentMin: '',
        spentMax: '',
        progressMin: '',
        progressMax: '',
      };
      setLocalFilters(defaultFilters);
    };

    return (
      <Dialog open={isFilterDialogOpen} onOpenChange={setIsFilterDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>高级筛选</DialogTitle>
            <DialogDescription>
              设置筛选条件，精确查找项目
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>项目状态</Label>
                <div className="flex flex-wrap gap-2">
                  {['初期阶段', '进行中', '即将完成', '已完成'].map((status) => (
                    <Badge
                      key={status}
                      variant={localFilters.status.includes(status) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newStatus = localFilters.status.includes(status)
                          ? localFilters.status.filter(s => s !== status)
                          : [...localFilters.status, status];
                        setLocalFilters({...localFilters, status: newStatus});
                      }}
                    >
                      {status}
                    </Badge>
                  ))}
                        </div>
                      </div>
              <div className="space-y-2">
                <Label>项目类型</Label>
                <div className="flex flex-wrap gap-2">
                  {['采矿工程', '设备工程', '安全工程', '勘探工程', '环保工程', '建筑工程', '道路工程', '桥梁工程', '隧道工程', '水利工程'].map((type) => (
                      <Badge
                      key={type}
                      variant={localFilters.type.includes(type) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newTypes = localFilters.type.includes(type)
                          ? localFilters.type.filter(t => t !== type)
                          : [...localFilters.type, type];
                        setLocalFilters({...localFilters, type: newTypes});
                      }}
                    >
                      {type}
                      </Badge>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <Label>优先级</Label>
                <div className="flex gap-2">
                  {['高', '中', '低'].map((priority) => (
                    <Badge
                      key={priority}
                      variant={localFilters.priority.includes(priority) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newPriorities = localFilters.priority.includes(priority)
                          ? localFilters.priority.filter(p => p !== priority)
                          : [...localFilters.priority, priority];
                        setLocalFilters({...localFilters, priority: newPriorities});
                      }}
                    >
                      {priority}
                    </Badge>
                  ))}
                </div>
              </div>
                    </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="filter-startDate">开始日期范围（从）</Label>
                <Input
                  id="filter-startDate"
                  type="date"
                  value={localFilters.startDate}
                  onChange={(e) => setLocalFilters({...localFilters, startDate: e.target.value})}
                />
                      </div>
              <div className="space-y-2">
                <Label htmlFor="filter-endDate">开始日期范围（至）</Label>
                <Input
                  id="filter-endDate"
                  type="date"
                  value={localFilters.endDate}
                  onChange={(e) => setLocalFilters({...localFilters, endDate: e.target.value})}
                />
                      </div>
                      </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="filter-budgetMin">预算范围（最小值）</Label>
                <Input
                  id="filter-budgetMin"
                  type="number"
                  placeholder="输入最小预算金额"
                  value={localFilters.budgetMin}
                  onChange={(e) => setLocalFilters({...localFilters, budgetMin: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="filter-budgetMax">预算范围（最大值）</Label>
                <Input
                  id="filter-budgetMax"
                  type="number"
                  placeholder="输入最大预算金额"
                  value={localFilters.budgetMax}
                  onChange={(e) => setLocalFilters({...localFilters, budgetMax: e.target.value})}
                />
                      </div>
                    </div>

            <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                <Label htmlFor="filter-spentMin">已花费范围（最小值）</Label>
                <Input
                  id="filter-spentMin"
                  type="number"
                  placeholder="输入最小已花费金额"
                  value={localFilters.spentMin}
                  onChange={(e) => setLocalFilters({...localFilters, spentMin: e.target.value})}
                />
                      </div>
              <div className="space-y-2">
                <Label htmlFor="filter-spentMax">已花费范围（最大值）</Label>
                <Input
                  id="filter-spentMax"
                  type="number"
                  placeholder="输入最大已花费金额"
                  value={localFilters.spentMax}
                  onChange={(e) => setLocalFilters({...localFilters, spentMax: e.target.value})}
                />
              </div>
                    </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="filter-progressMin">进度范围（最小值）</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="filter-progressMin"
                    type="number"
                    min="0"
                    max="100"
                    placeholder="输入最小进度"
                    value={localFilters.progressMin}
                    onChange={(e) => setLocalFilters({...localFilters, progressMin: e.target.value})}
                  />
                  <span>%</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="filter-progressMax">进度范围（最大值）</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="filter-progressMax"
                    type="number"
                    min="0"
                    max="100"
                    placeholder="输入最大进度"
                    value={localFilters.progressMax}
                    onChange={(e) => setLocalFilters({...localFilters, progressMax: e.target.value})}
                  />
                  <span>%</span>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleResetFilters}>
              重置筛选
                      </Button>
            <Button onClick={handleApplyFilters}>应用筛选</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // 在组件开始处附近添加状态
  const [filters, setFilters] = useState<{
    status: string[];
    type: string[];
    priority: string[];
    startDate: string;
    endDate: string;
    budgetMin: string;
    budgetMax: string;
    spentMin: string;
    spentMax: string;
    progressMin: string;
    progressMax: string;
  }>({
    status: [],
    type: [],
    priority: [],
    startDate: '',
    endDate: '',
    budgetMin: '',
    budgetMax: '',
    spentMin: '',
    spentMax: '',
    progressMin: '',
    progressMax: '',
  });

  // 添加统计分析抽屉状态
  // 已在组件顶部声明，此处删除重复声明
  // const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false);

  // 添加统计抽屉组件
  const StatsDrawer = () => {
    return (
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-full md:max-w-[680px] sm:max-w-md p-0">
          <SheetHeader className="p-6 pb-2">
            <SheetTitle className="text-xl">项目统计分析</SheetTitle>
            <SheetDescription>查看项目数据统计和趋势分析</SheetDescription>
          </SheetHeader>
          <ScrollArea className="h-[calc(100vh-80px)] px-6">
            <div className="py-4 space-y-8">
              <div className="space-y-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium">基本统计</h3>
                  <div className="ml-2 p-1 rounded-full bg-blue-100">
                    <BarChart2 className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <Card className="overflow-hidden border-l-4 border-l-blue-500">
                    <CardHeader className="pb-2 flex flex-row items-center justify-between">
                      <CardTitle className="text-sm font-medium">项目总数</CardTitle>
                      <div className="rounded-full p-1.5 bg-blue-100">
                        <Clipboard className="h-4 w-4 text-blue-600" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.totalProjects}</div>
                      <p className="text-xs text-muted-foreground mt-1 flex items-center">
                        <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                        {statistics.completionRate}% 完成率
                      </p>
                    </CardContent>
                </Card>

                  <Card className="overflow-hidden border-l-4 border-l-green-500">
                    <CardHeader className="pb-2 flex flex-row items-center justify-between">
                      <CardTitle className="text-sm font-medium">进行中项目</CardTitle>
                      <div className="rounded-full p-1.5 bg-green-100">
                        <Activity className="h-4 w-4 text-green-600" />
            </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.ongoingProjects}</div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {Math.round((statistics.ongoingProjects / statistics.totalProjects) * 100) || 0}% 占比
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="overflow-hidden border-l-4 border-l-purple-500">
                    <CardHeader className="pb-2 flex flex-row items-center justify-between">
                      <CardTitle className="text-sm font-medium">已完成项目</CardTitle>
                      <div className="rounded-full p-1.5 bg-purple-100">
                        <CheckCircle className="h-4 w-4 text-purple-600" />
          </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.completedProjects}</div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {Math.round((statistics.completedProjects / statistics.totalProjects) * 100) || 0}% 占比
                      </p>
        </CardContent>
                  </Card>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium">项目类型分布</h3>
                  <div className="ml-2 p-1 rounded-full bg-orange-100">
                    <PieChart className="h-4 w-4 text-orange-600" />
                  </div>
                </div>
                <Card>
                  <CardContent className="p-4">
                    <div className="w-full overflow-x-auto">
                      <div className="flex flex-col items-center min-w-[500px]">
                        {/* 饼状图 */}
                        <div className="relative h-[220px] w-[220px] mb-6">
                          {Object.entries(statistics.projectsByType).length > 0 ? (
                            Object.entries(statistics.projectsByType).map(([type, count], index, arr) => {
                              // 计算总和
                              const total = arr.reduce((sum, [_, val]) => sum + val, 0);
                              // 计算当前类型和之前所有类型的占比，用于确定扇形位置
                              const prevTotal = arr.slice(0, index).reduce((sum, [_, val]) => sum + val, 0);
                              const startAngle = (prevTotal / total) * 360;
                              const angle = (count / total) * 360;

                              // 生动的颜色数组
                              const colors = [
                                "url(#blue-gradient)",
                                "url(#green-gradient)",
                                "url(#orange-gradient)",
                                "url(#purple-gradient)",
                                "url(#pink-gradient)",
                                "url(#teal-gradient)",
                                "url(#red-gradient)",
                                "url(#indigo-gradient)"
                              ];

                              // 计算扇形路径
                              const startRad = (startAngle - 90) * (Math.PI / 180);
                              const endRad = (startAngle + angle - 90) * (Math.PI / 180);
                              const radius = 100;
                              const x1 = radius * Math.cos(startRad) + radius;
                              const y1 = radius * Math.sin(startRad) + radius;
                              const x2 = radius * Math.cos(endRad) + radius;
                              const y2 = radius * Math.sin(endRad) + radius;

                              // 大弧标志（如果角度超过180度）
                              const largeArcFlag = angle > 180 ? 1 : 0;

                              // 生成路径
                              const path = `M${radius},${radius} L${x1},${y1} A${radius},${radius} 0 ${largeArcFlag},1 ${x2},${y2} Z`;

                              // 显示项目类型名称在扇形中央
                              const labelRad = (startAngle + angle/2 - 90) * (Math.PI / 180);
                              const labelRadius = radius * 0.65; // 标签放在半径65%的位置
                              const labelX = labelRadius * Math.cos(labelRad) + radius;
                              const labelY = labelRadius * Math.sin(labelRad) + radius;

                              // 只有当扇形足够大时才显示标签
                              const showLabel = angle > 25;

                              return (
                                <svg key={type} className="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 200">
                                  <defs>
                                    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#3b82f6", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#1d4ed8", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="green-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#22c55e", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#16a34a", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="orange-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#f97316", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#ea580c", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#a855f7", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#7e22ce", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="pink-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#ec4899", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#be185d", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="teal-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#14b8a6", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#0f766e", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="red-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#ef4444", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#b91c1c", stopOpacity: 1 }} />
                                    </linearGradient>
                                    <linearGradient id="indigo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                      <stop offset="0%" style={{ stopColor: "#6366f1", stopOpacity: 1 }} />
                                      <stop offset="100%" style={{ stopColor: "#4338ca", stopOpacity: 1 }} />
                                    </linearGradient>
                                  </defs>
                                  <path
                                    d={path}
                                    fill={colors[index % colors.length]}
                                    stroke="white"
                                    strokeWidth="1.5"
                                    className="drop-shadow-md hover:opacity-95 transition-opacity cursor-pointer"
                                  />
                                  {showLabel && (
                                    <text
                                      x={labelX}
                                      y={labelY}
                                      textAnchor="middle"
                                      dominantBaseline="middle"
                                      fill="white"
                                      fontSize="12"
                                      fontWeight="bold"
                                      className="drop-shadow-sm"
                                    >
                                      {count}
                                    </text>
                                  )}
                                </svg>
                              );
                            })
                          ) : (
                            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
                              没有项目类型数据
                            </div>
                          )}
                        </div>

                        {/* 图例 */}
                        <div className="grid grid-cols-2 gap-4 w-full">
                          {Object.entries(statistics.projectsByType).map(([type, count], index) => {
                            const bgColors = ["bg-blue-500", "bg-green-500", "bg-orange-500", "bg-purple-500",
                                          "bg-pink-500", "bg-teal-500", "bg-red-500", "bg-indigo-500"];
                            const percentage = Math.round((count / statistics.totalProjects) * 100) || 0;

                            return (
                              <div key={type} className="flex items-center gap-2">
                                <div className={`${bgColors[index % bgColors.length]} w-4 h-4 rounded-full shadow-sm flex items-center justify-center`}>
                                  <span className="text-white text-[8px] font-bold">{index + 1}</span>
                                </div>
                                <div className="text-sm">
                                  <span className="font-medium">{type}</span>
                                  <span className="text-muted-foreground ml-2">
                                    {count}个 ({percentage}%)
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium">优先级分布</h3>
                  <div className="ml-2 p-1 rounded-full bg-red-100">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  {Object.entries(statistics.projectsByPriority).map(([priority, count]) => {
                    const colors = {
                      "高": {bg: "bg-gradient-to-r from-red-500 to-rose-600", text: "text-red-500", light: "bg-red-100"},
                      "中": {bg: "bg-gradient-to-r from-amber-500 to-orange-600", text: "text-amber-500", light: "bg-amber-100"},
                      "低": {bg: "bg-gradient-to-r from-green-500 to-emerald-600", text: "text-green-500", light: "bg-green-100"}
                    };
                    const icons = {
                      "高": <AlertTriangle className="h-5 w-5 text-white" />,
                      "中": <AlertTriangle className="h-5 w-5 text-white" />,
                      "低": <CheckCircle className="h-5 w-5 text-white" />
                    };
                    return (
                      <Card key={priority} className="overflow-hidden">
                        <CardContent className="p-0">
                          <div className={`${colors[priority as keyof typeof colors].bg} text-white p-3 flex items-center justify-between`}>
                            <div className="font-medium">{priority}优先级</div>
                            <div className="rounded-full p-1">
                              {icons[priority as keyof typeof icons]}
                            </div>
                          </div>
                          <div className="p-4 flex items-center justify-between">
                            <p className="text-2xl font-bold">{count}</p>
                            <div className={`text-sm py-1 px-2 rounded-full ${colors[priority as keyof typeof colors].light} ${colors[priority as keyof typeof colors].text} font-medium`}>
                              {Math.round((count / statistics.totalProjects) * 100) || 0}%
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium">资源与预算</h3>
                  <div className="ml-2 p-1 rounded-full bg-indigo-100">
                    <Users className="h-4 w-4 text-indigo-600" />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Card className="overflow-hidden border-t-4 border-t-indigo-500">
                    <CardHeader className="pb-2 flex flex-row items-center justify-between">
                      <CardTitle className="text-sm font-medium">平均每项目工作人员</CardTitle>
                      <div className="rounded-full p-1.5 bg-indigo-100">
                        <Users className="h-4 w-4 text-indigo-600" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.avgWorkersPerProject}</div>
                      <p className="text-xs text-muted-foreground mt-1">
                        总计 {statistics.totalWorkers} 人
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="overflow-hidden border-t-4 border-t-cyan-500">
                    <CardHeader className="pb-2 flex flex-row items-center justify-between">
                      <CardTitle className="text-sm font-medium">预算使用率</CardTitle>
                      <div className="rounded-full p-1.5 bg-cyan-100">
                        <LineChart className="h-4 w-4 text-cyan-600" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.spendingPercentage}%</div>
                      <div className="w-full h-3 bg-gray-100 rounded-full mt-2 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full"
                          style={{ width: `${statistics.spendingPercentage}%` }}
                        ></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium">项目按时情况</h3>
                  <div className="ml-2 p-1 rounded-full bg-amber-100">
                    <Clock className="h-4 w-4 text-amber-600" />
                  </div>
                </div>
                <Card className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">按时完成率</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="flex flex-col items-center p-4 rounded-lg bg-gradient-to-b from-green-50 to-green-100">
                        <CheckCircle className="h-5 w-5 text-green-500 mb-1" />
                        <p className="text-sm font-medium text-green-600">按时完成</p>
                        <p className="text-2xl font-bold">{statistics.onTimeProjects}</p>
                      </div>
                      <div className="flex flex-col items-center p-4 rounded-lg bg-gradient-to-b from-red-50 to-red-100">
                        <Clock className="h-5 w-5 text-red-500 mb-1" />
                        <p className="text-sm font-medium text-red-600">延期项目</p>
                        <p className="text-2xl font-bold">{statistics.delayedProjects}</p>
                      </div>
                      <div className="flex flex-col items-center p-4 rounded-lg bg-gradient-to-b from-blue-50 to-blue-100">
                        <LineChart className="h-5 w-5 text-blue-500 mb-1" />
                        <p className="text-sm font-medium text-blue-600">按时率</p>
                        <p className="text-2xl font-bold">
                          {statistics.onTimeProjects + statistics.delayedProjects > 0
                            ? Math.round((statistics.onTimeProjects / (statistics.onTimeProjects + statistics.delayedProjects)) * 100)
                            : 0}%
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="h-6"></div> {/* 底部间距 */}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    );
  };

  // 添加导入对话框组件
  const ImportDialog = () => {
    const [file, setFile] = useState<File | null>(null);
    const [importType, setImportType] = useState<"excel" | "csv">("excel");

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        setFile(e.target.files[0]);
      }
    };

    const handleImportTypeChange = (value: "excel" | "csv") => {
      setImportType(value);
    };

    return (
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>导入项目数据</DialogTitle>
            <DialogDescription>
              从Excel或CSV文件导入项目数据
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>文件格式</Label>
              <div className="flex space-x-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="excel"
                    checked={importType === "excel"}
                    onChange={() => handleImportTypeChange("excel")}
                  />
                  <Label htmlFor="excel">Excel</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="csv"
                    checked={importType === "csv"}
                    onChange={() => handleImportTypeChange("csv")}
                  />
                  <Label htmlFor="csv">CSV</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="file-upload">上传文件</Label>
              <Input
                id="file-upload"
                type="file"
                accept={importType === "excel" ? ".xlsx,.xls" : ".csv"}
                onChange={handleFileChange}
              />
            </div>

            <div className="bg-muted p-3 rounded-md">
              <h4 className="text-sm font-medium mb-2">数据导入说明：</h4>
              <ul className="text-xs text-muted-foreground space-y-1 list-disc pl-4">
                <li>文件第一行应为表头，字段名与系统一致</li>
                <li>导入数据将与现有数据合并，重复ID将被覆盖</li>
                <li>日期格式应为YYYY-MM-DD</li>
                <li>数字字段不应包含特殊字符</li>
                <li>标签字段应使用英文逗号分隔</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={confirmImport}
              disabled={!file || loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  导入中...
                </>
              ) : "开始导入"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="space-y-6">
      {/* 头部区域 */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">项目概览</h2>
          <p className="text-muted-foreground">
            当前时间: {currentTime.toLocaleString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              weekday: 'long'
            })}
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => confirmExport("excel")}>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                导出为Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => confirmExport("pdf")}>
                <FileText className="mr-2 h-4 w-4" />
                导出为PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => confirmExport("csv")}>
                <FileText className="mr-2 h-4 w-4" />
                导出为CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" onClick={handleImportData}>
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>

          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>

          <Button
            variant="outline"
            onClick={() => setIsStatsDrawerOpen(true)}
          >
            <BarChart2 className="mr-2 h-4 w-4" />
            统计分析
          </Button>

          <Button onClick={handleAddProject}>
            <Plus className="mr-2 h-4 w-4" />
            新建项目
            </Button>
          </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">项目总数</CardTitle>
            <div className="rounded-full bg-blue-100 p-2">
              <Clipboard className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalProjects}</div>
            <p className="text-xs text-muted-foreground mt-1">
              进行中: {statistics.ongoingProjects} | 已完成: {statistics.completedProjects} | 初期: {statistics.initialProjects}
            </p>
            <div className="mt-3">
              <div className="flex text-xs mb-1">
                <span className="flex-1">按类型分布</span>
                <span>项目数</span>
              </div>
              <div className="space-y-1">
                {Object.entries(statistics.projectsByType).map(([type, count]) => (
                  <div key={type} className="flex items-center text-xs">
                    <span className="flex-1 truncate">{type}</span>
                    <span>{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
      </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成率</CardTitle>
            <div className="rounded-full bg-green-100 p-2">
              <FileBarChart className="h-5 w-5 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.completionRate}%</div>
            <p className="text-xs text-muted-foreground mt-1">
              按时完成: {statistics.onTimeProjects} | 延期完成: {statistics.delayedProjects}
            </p>
            <div className="mt-3">
              <div className="flex text-xs mb-1">
                <span className="flex-1">按优先级分布</span>
                <span>项目数</span>
              </div>
              <div className="space-y-1">
                {Object.entries(statistics.projectsByPriority).map(([priority, count]) => (
                  <div key={priority} className="flex items-center text-xs">
                    <Badge
                      variant={
                        priority === "高" ? "destructive" :
                        priority === "中" ? "default" :
                        "secondary"
                      }
                      className="mr-1"
                    >
                      {priority}
                    </Badge>
                    <span className="flex-1">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">项目人员</CardTitle>
            <div className="rounded-full bg-amber-100 p-2">
              <Users className="h-5 w-5 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalWorkers}</div>
            <p className="text-xs text-muted-foreground mt-1">
              平均每个项目: {statistics.avgWorkersPerProject} 人
            </p>
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs">
                <span>人员利用率</span>
                <span>{statistics.resourceUtilization}%</span>
              </div>
              <Progress value={statistics.resourceUtilization} className="h-2 mt-1" />
            </div>
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs">
                <span>安全问题</span>
                <span>{statistics.totalSafetyIssues}</span>
              </div>
              <div className="flex items-center justify-between text-xs mt-1">
                <span>平均每个项目</span>
                <span>{statistics.avgSafetyIssuesPerProject}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 项目管理部分 */}
      <div className="space-y-4">
        <div className="flex justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant={selectedViewMode === "table" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedViewMode("table")}
            >
              <LayoutGrid className="h-4 w-4 mr-2" />
              表格视图
            </Button>
            <Button
              variant={selectedViewMode === "card" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedViewMode("card")}
            >
              <LayoutGrid className="h-4 w-4 mr-2" />
              卡片视图
            </Button>
            <Button
              variant={selectedViewMode === "gantt" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedViewMode("gantt")}
            >
              <BarChart2 className="h-4 w-4 mr-2" />
              甘特图
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索项目名称、负责人、位置、描述或标签..."
                className="pl-8 w-[250px] lg:w-[350px] h-9"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-9 gap-1">
                  <ListFilter className="h-4 w-4" />
                  <span>{selectedStatus === "all" ? "所有状态" : selectedStatus}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setSelectedStatus("all")}>
                  所有状态
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedStatus("初期阶段")}>
                  初期阶段
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedStatus("进行中")}>
                  进行中
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedStatus("即将完成")}>
                  即将完成
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedStatus("已完成")}>
                  已完成
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              onClick={() => setIsFilterDialogOpen(true)}
              variant="outline"
              className="h-9 gap-1"
            >
              <FilterIcon className="h-4 w-4" />
              <span>高级筛选</span>
            </Button>
          </div>
        </div>

        {/* 批量操作 */}
        {selectedProjects.length > 0 && (
          <div className="bg-muted/50 rounded-lg p-2 flex justify-between items-center">
            <span className="text-sm">已选择 {selectedProjects.length} 个项目</span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleBatchEdit}>
                <Edit className="h-4 w-4 mr-2" />
                批量编辑
              </Button>
              <Button variant="destructive" size="sm" onClick={handleBatchDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                批量删除
              </Button>
            </div>
          </div>
        )}

        <Tabs defaultValue="all" value={currentTab} onValueChange={setCurrentTab}>
          <TabsList>
            <TabsTrigger value="all">全部项目</TabsTrigger>
            <TabsTrigger value="inProgress">进行中</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
            <TabsTrigger value="initial">初期阶段</TabsTrigger>
          </TabsList>
          <TabsContent value={currentTab} className="mt-4">
            {selectedViewMode === "table" && renderProjectsTable()}
            {selectedViewMode === "card" && renderProjectsCards(filteredProjects)}
            {selectedViewMode === "gantt" && renderProjectsGantt(filteredProjects)}
          </TabsContent>
        </Tabs>

        {filteredProjects.length > 0 && (
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <div>
              显示 {filteredProjects.length} 个项目中的 {Math.min(10, filteredProjects.length)} 个
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" disabled>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 项目详情抽屉 */}
      <ProjectDetailsDrawer />

      {/* 编辑项目对话框 */}
      <EditProjectDialog />

      {/* 添加项目对话框 */}
      <AddProjectDialog />

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定要删除此项目吗？</AlertDialogTitle>
            <AlertDialogDescription>
              此操作无法撤销，项目将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteProject}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              确定删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量删除确认对话框 */}
      <AlertDialog open={isBatchDeleteDialogOpen} onOpenChange={setIsBatchDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定要删除这些项目吗？</AlertDialogTitle>
            <AlertDialogDescription>
              此操作无法撤销，所选项目将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBatchDelete}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              确定删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 导出确认对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出项目</DialogTitle>
            <DialogDescription>
              选择要导出的项目数据格式。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <RadioGroup value={exportFormat} onValueChange={setExportFormat}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="all" />
                <Label htmlFor="all">所有项目数据</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="filtered" id="filtered" />
                <Label htmlFor="filtered">筛选后的项目数据</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="selected" id="selected" />
                <Label htmlFor="selected">已选择的项目</Label>
              </div>
            </RadioGroup>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => confirmExport(exportFormat)}>
              导出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入确认对话框 */}
      <ImportDialog />

      {/* 高级筛选对话框 */}
      <AdvancedFilterDialog />

      {/* 统计抽屉 */}
      <StatsDrawer />
    </div>
  )
}

