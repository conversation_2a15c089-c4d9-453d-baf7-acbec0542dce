"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Bomb,
  Shield
} from "lucide-react"

interface BlastingRecord {
  id: string
  date: string
  location: string
  responsible: string
  type: string
  status: string
  explosiveAmount: string
  safetyMeasures: string
  approvalStatus: string
}

export function BlastingManagement() {
  // 初始爆破记录数据
  const initialBlastingRecords: BlastingRecord[] = [
    {
      id: "1",
      date: "2025-03-01",
      location: "A区矿山",
      responsible: "张三",
      type: "露天爆破",
      status: "已完成",
      explosiveAmount: "200kg",
      safetyMeasures: "已设置警戒线，疏散周边人员",
      approvalStatus: "已批准"
    },
    {
      id: "2",
      date: "2025-03-05",
      location: "B区隧道",
      responsible: "李四",
      type: "隧道爆破",
      status: "计划中",
      explosiveAmount: "150kg",
      safetyMeasures: "制定安全疏散方案，准备警戒设备",
      approvalStatus: "审批中"
    },
    {
      id: "3",
      date: "2025-03-10",
      location: "C区采石场",
      responsible: "王五",
      type: "定向爆破",
      status: "计划中",
      explosiveAmount: "300kg",
      safetyMeasures: "已制定详细安全方案，准备警戒设备",
      approvalStatus: "已批准"
    },
    {
      id: "4",
      date: "2025-02-25",
      location: "D区矿井",
      responsible: "赵六",
      type: "井下爆破",
      status: "已完成",
      explosiveAmount: "100kg",
      safetyMeasures: "已按规定疏散人员，设置安全区域",
      approvalStatus: "已批准"
    },
  ]

  // 状态管理
  const [blastingRecords, setBlastingRecords] = useState<BlastingRecord[]>(initialBlastingRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<BlastingRecord>({
    id: "",
    date: "",
    location: "",
    responsible: "",
    type: "",
    status: "",
    explosiveAmount: "",
    safetyMeasures: "",
    approvalStatus: ""
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤爆破记录
  const filteredRecords = blastingRecords.filter(
    (record) => {
      const matchesSearch =
        record.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.responsible.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.type.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "pending") return matchesSearch && record.status === "计划中";
      if (activeTab === "completed") return matchesSearch && record.status === "已完成";

      return matchesSearch;
    }
  )

  // 添加爆破记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (blastingRecords.length + 1).toString()
    }
    setBlastingRecords([...blastingRecords, newRecord])
    setCurrentRecord({
      id: "",
      date: "",
      location: "",
      responsible: "",
      type: "",
      status: "",
      explosiveAmount: "",
      safetyMeasures: "",
      approvalStatus: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑爆破记录
  const handleEditRecord = () => {
    const updatedRecords = blastingRecords.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    )
    setBlastingRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      date: "",
      location: "",
      responsible: "",
      type: "",
      status: "",
      explosiveAmount: "",
      safetyMeasures: "",
      approvalStatus: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除爆破记录
  const handleDeleteRecord = () => {
    const updatedRecords = blastingRecords.filter(
      (record) => record.id !== currentRecord.id
    )
    setBlastingRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      date: "",
      location: "",
      responsible: "",
      type: "",
      status: "",
      explosiveAmount: "",
      safetyMeasures: "",
      approvalStatus: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (record: BlastingRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: BlastingRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "计划中":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">计划中</Badge>
      case "进行中":
        return <Badge variant="secondary">进行中</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取审批状态对应的徽章样式
  const getApprovalBadge = (status: string) => {
    switch (status) {
      case "已批准":
        return <Badge className="bg-green-500">已批准</Badge>
      case "审批中":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">审批中</Badge>
      case "未批准":
        return <Badge variant="destructive">未批准</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">爆破管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加爆破记录
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加新爆破记录</DialogTitle>
              <DialogDescription>
                请填写新爆破作业的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">计划日期</Label>
                  <Input
                    id="date"
                    type="date"
                    value={currentRecord.date}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, date: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">爆破地点</Label>
                  <Input
                    id="location"
                    value={currentRecord.location}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="responsible">负责人</Label>
                  <Input
                    id="responsible"
                    value={currentRecord.responsible}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, responsible: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">爆破类型</Label>
                  <Select
                    value={currentRecord.type}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择爆破类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="露天爆破">露天爆破</SelectItem>
                      <SelectItem value="隧道爆破">隧道爆破</SelectItem>
                      <SelectItem value="定向爆破">定向爆破</SelectItem>
                      <SelectItem value="井下爆破">井下爆破</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={currentRecord.status}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, status: value })}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="计划中">计划中</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="explosiveAmount">炸药用量</Label>
                  <Input
                    id="explosiveAmount"
                    value={currentRecord.explosiveAmount}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, explosiveAmount: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="safetyMeasures">安全措施</Label>
                <Textarea
                  id="safetyMeasures"
                  value={currentRecord.safetyMeasures}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, safetyMeasures: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="approvalStatus">审批状态</Label>
                <Select
                  value={currentRecord.approvalStatus}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, approvalStatus: value })}
                >
                  <SelectTrigger id="approvalStatus">
                    <SelectValue placeholder="选择审批状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="审批中">审批中</SelectItem>
                    <SelectItem value="已批准">已批准</SelectItem>
                    <SelectItem value="未批准">未批准</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索爆破记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="pending">计划中</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{record.location}</CardTitle>
              {getStatusBadge(record.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  {record.date}
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  {record.responsible}
                </div>
                <div className="flex items-center text-sm">
                  <Bomb className="h-4 w-4 mr-2 text-gray-500" />
                  {record.type} - {record.explosiveAmount}
                </div>
                <div className="flex items-center text-sm">
                  <Shield className="h-4 w-4 mr-2 text-gray-500" />
                  审批状态: {getApprovalBadge(record.approvalStatus)}
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>爆破作业记录</CardTitle>
          <CardDescription>管理所有爆破作业计划和记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>爆破地点</TableHead>
                <TableHead>负责人</TableHead>
                <TableHead>爆破类型</TableHead>
                <TableHead>炸药用量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>审批状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.date}</TableCell>
                  <TableCell>{record.location}</TableCell>
                  <TableCell>{record.responsible}</TableCell>
                  <TableCell>{record.type}</TableCell>
                  <TableCell>{record.explosiveAmount}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell>{getApprovalBadge(record.approvalStatus)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑爆破记录</DialogTitle>
            <DialogDescription>
              修改爆破作业的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-date">计划日期</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={currentRecord.date}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">爆破地点</Label>
                <Input
                  id="edit-location"
                  value={currentRecord.location}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-responsible">负责人</Label>
                <Input
                  id="edit-responsible"
                  value={currentRecord.responsible}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, responsible: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">爆破类型</Label>
                <Select
                  value={currentRecord.type}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择爆破类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="露天爆破">露天爆破</SelectItem>
                    <SelectItem value="隧道爆破">隧道爆破</SelectItem>
                    <SelectItem value="定向爆破">定向爆破</SelectItem>
                    <SelectItem value="井下爆破">井下爆破</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select
                  value={currentRecord.status}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, status: value })}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="计划中">计划中</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-explosiveAmount">炸药用量</Label>
                <Input
                  id="edit-explosiveAmount"
                  value={currentRecord.explosiveAmount}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, explosiveAmount: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-safetyMeasures">安全措施</Label>
              <Textarea
                id="edit-safetyMeasures"
                value={currentRecord.safetyMeasures}
                onChange={(e) => setCurrentRecord({ ...currentRecord, safetyMeasures: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-approvalStatus">审批状态</Label>
              <Select
                value={currentRecord.approvalStatus}
                onValueChange={(value) => setCurrentRecord({ ...currentRecord, approvalStatus: value })}
              >
                <SelectTrigger id="edit-approvalStatus">
                  <SelectValue placeholder="选择审批状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="审批中">审批中</SelectItem>
                  <SelectItem value="已批准">已批准</SelectItem>
                  <SelectItem value="未批准">未批准</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.location}" 的爆破记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
