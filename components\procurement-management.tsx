"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  ShoppingCart,
  Package,
  AlertCircle,
  CheckCircle2,
  XCircle,
  BarChart2,
  Printer,
  FileText,
  Clock,
  DollarSign,
  Truck,
  Calendar,
  Users,
  Building2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'

interface ProcurementOrder {
  id: string
  orderNumber: string
  title: string
  type: string
  status: string
  priority: string
  supplier: string
  department: string
  requestor: string
  approver: string
  totalAmount: number
  currency: string
  createdAt: string
  expectedDeliveryDate: string
  items: ProcurementItem[]
  attachments?: string[]
  notes?: string
}

interface ProcurementItem {
  id: string
  name: string
  code: string
  specification: string
  unit: string
  quantity: number
  unitPrice: number
  totalPrice: number
  supplier: string
  category: string
  status: string
}

export function ProcurementManagement() {
  const [isAddOrderOpen, setIsAddOrderOpen] = useState(false)
  const [isAddItemOpen, setIsAddItemOpen] = useState(false)
  const [currentTab, setCurrentTab] = useState("all")
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [selectedOrder, setSelectedOrder] = useState<ProcurementOrder | null>(null)
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [isDeleteOpen, setIsDeleteOpen] = useState(false)
  const [editForm, setEditForm] = useState<ProcurementOrder | null>(null)

  // 示例数据
  const [orders, setOrders] = useState<ProcurementOrder[]>([
    {
      id: "1",
      orderNumber: "PO2024001",
      title: "2024年第一季度办公用品采购",
      type: "常规采购",
      status: "待审批",
      priority: "普通",
      supplier: "办公用品供应商A",
      department: "行政部",
      requestor: "张三",
      approver: "李四",
      totalAmount: 50000,
      currency: "CNY",
      createdAt: "2025-03-15",
      expectedDeliveryDate: "2025-03-30",
      items: [
        {
          id: "1-1",
          name: "A4纸",
          code: "P001",
          specification: "70g 500张/包",
          unit: "包",
          quantity: 100,
          unitPrice: 25,
          totalPrice: 2500,
          supplier: "办公用品供应商A",
          category: "办公用品",
          status: "待采购"
        },
        {
          id: "1-2",
          name: "签字笔",
          code: "P002",
          specification: "0.5mm 黑色",
          unit: "支",
          quantity: 200,
          unitPrice: 5,
          totalPrice: 1000,
          supplier: "办公用品供应商A",
          category: "办公用品",
          status: "待采购"
        }
      ]
    },
    {
      id: "2",
      orderNumber: "PO2024002",
      title: "工程设备采购",
      type: "设备采购",
      status: "已完成",
      priority: "紧急",
      supplier: "工程设备供应商B",
      department: "工程部",
      requestor: "王五",
      approver: "赵六",
      totalAmount: 200000,
      currency: "CNY",
      createdAt: "2025-03-10",
      expectedDeliveryDate: "2025-03-25",
      items: [
        {
          id: "2-1",
          name: "工程车",
          code: "E001",
          specification: "5吨",
          unit: "台",
          quantity: 1,
          unitPrice: 150000,
          totalPrice: 150000,
          supplier: "工程设备供应商B",
          category: "工程设备",
          status: "已完成"
        }
      ]
    }
  ])

  // 采购统计数据
  const procurementStats = [
    {
      title: "总采购单数",
      value: orders.length,
      icon: <ShoppingCart className="h-6 w-6 text-blue-600" />,
      description: "所有采购单数量"
    },
    {
      title: "待处理采购",
      value: orders.filter(o => o.status === "待审批" || o.status === "待采购").length,
      icon: <Clock className="h-6 w-6 text-yellow-600" />,
      description: "需要处理的采购单"
    },
    {
      title: "本月采购金额",
      value: orders
        .filter(o => dayjs(o.createdAt).isAfter(dayjs().startOf('month')))
        .reduce((sum, o) => sum + o.totalAmount, 0),
      icon: <DollarSign className="h-6 w-6 text-green-600" />,
      description: "本月采购总额",
      prefix: "¥"
    },
    {
      title: "供应商数量",
      value: new Set(orders.map(o => o.supplier)).size,
      icon: <Building2 className="h-6 w-6 text-purple-600" />,
      description: "合作供应商数量"
    }
  ]

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待审批":
        return <Badge className="bg-yellow-500">待审批</Badge>
      case "待采购":
        return <Badge className="bg-blue-500">待采购</Badge>
      case "采购中":
        return <Badge className="bg-purple-500">采购中</Badge>
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "已取消":
        return <Badge className="bg-gray-500">已取消</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取优先级对应的徽章样式
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "紧急":
        return <Badge className="bg-red-500">紧急</Badge>
      case "高":
        return <Badge className="bg-orange-500">高</Badge>
      case "普通":
        return <Badge className="bg-blue-500">普通</Badge>
      case "低":
        return <Badge className="bg-gray-500">低</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  // 处理导出
  const handleExport = () => {
    try {
      const exportData = orders.map(order => ({
        '采购单号': order.orderNumber,
        '标题': order.title,
        '类型': order.type,
        '状态': order.status,
        '优先级': order.priority,
        '供应商': order.supplier,
        '申请部门': order.department,
        '申请人': order.requestor,
        '审批人': order.approver,
        '总金额': order.totalAmount,
        '创建时间': order.createdAt,
        '预计交付日期': order.expectedDeliveryDate,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)
      XLSX.utils.book_append_sheet(wb, ws, '采购单列表')
      // 使用2025年的固定日期而不是当前日期
      XLSX.writeFile(wb, `采购单列表_2025-03-15.xlsx`)
    } catch (error) {
      console.error('导出失败:', error)
    }
  }

  // 处理打印
  const handlePrint = () => {
    const printContent = document.createElement('div')
    printContent.innerHTML = `
      <style>
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .header { margin-bottom: 20px; }
        .footer { margin-top: 20px; text-align: right; }
        @media print {
          button { display: none; }
        }
      </style>
      <div class="header">
        <h2>采购单列表</h2>
        <p>打印时间：2025-03-15 10:30:45</p>
      </div>
      <table>
        <thead>
          <tr>
            <th>采购单号</th>
            <th>标题</th>
            <th>类型</th>
            <th>状态</th>
            <th>供应商</th>
            <th>总金额</th>
            <th>创建时间</th>
          </tr>
        </thead>
        <tbody>
          ${orders.map(order => `
            <tr>
              <td>${order.orderNumber}</td>
              <td>${order.title}</td>
              <td>${order.type}</td>
              <td>${order.status}</td>
              <td>${order.supplier}</td>
              <td>¥${order.totalAmount}</td>
              <td>${order.createdAt}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      <div class="footer">
        <p>总计：${orders.length} 条记录</p>
      </div>
    `

    const printWindow = window.open('', '_blank')
    printWindow?.document.write(printContent.innerHTML)
    printWindow?.document.close()
    printWindow?.print()
  }

  // 过滤数据
  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      order.title.toLowerCase().includes(searchText.toLowerCase()) ||
      order.supplier.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || order.type === selectedType
    const matchesStatus = selectedStatus === "all" || order.status === selectedStatus
    const matchesPriority = selectedPriority === "all" || order.priority === selectedPriority
    const matchesTab = currentTab === "all" ||
      (currentTab === "pending" && (order.status === "待审批" || order.status === "待采购")) ||
      (currentTab === "urgent" && order.priority === "紧急")
    return matchesSearch && matchesType && matchesStatus && matchesPriority && matchesTab
  })

  // 处理编辑
  const handleEdit = (order: ProcurementOrder) => {
    setEditForm(order)
    setIsEditOpen(true)
  }

  // 处理删除
  const handleDelete = (order: ProcurementOrder) => {
    setSelectedOrder(order)
    setIsDeleteOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (selectedOrder) {
      setOrders(orders.filter(o => o.id !== selectedOrder.id))
      setIsDeleteOpen(false)
      setSelectedOrder(null)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">采购管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            打印
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
          <Dialog open={isAddOrderOpen} onOpenChange={setIsAddOrderOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                新建采购
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>新建采购单</DialogTitle>
                <DialogDescription>创建新的采购申请单</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>采购标题</Label>
                    <Input placeholder="请输入采购标题" />
                  </div>
                  <div className="space-y-2">
                    <Label>采购类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择采购类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="常规采购">常规采购</SelectItem>
                        <SelectItem value="设备采购">设备采购</SelectItem>
                        <SelectItem value="原材料采购">原材料采购</SelectItem>
                        <SelectItem value="服务采购">服务采购</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>供应商</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择供应商" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="办公用品供应商A">办公用品供应商A</SelectItem>
                        <SelectItem value="工程设备供应商B">工程设备供应商B</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>申请部门</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择申请部门" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="行政部">行政部</SelectItem>
                        <SelectItem value="工程部">工程部</SelectItem>
                        <SelectItem value="采购部">采购部</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>优先级</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="紧急">紧急</SelectItem>
                        <SelectItem value="高">高</SelectItem>
                        <SelectItem value="普通">普通</SelectItem>
                        <SelectItem value="低">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>预计交付日期</Label>
                    <Input type="date" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>采购说明</Label>
                  <Textarea placeholder="请输入采购说明" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddOrderOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsAddOrderOpen(false)}>确认创建</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {procurementStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4">
                  <div className="rounded-full bg-gray-100 p-3">
                    {stat.icon}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                    <h3 className="text-2xl font-bold">
                      {stat.prefix}{stat.value}
                    </h3>
                    <p className="text-sm text-muted-foreground">{stat.description}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>采购单列表</CardTitle>
              <CardDescription>查看和管理所有采购单</CardDescription>
            </div>
            <Tabs defaultValue="all" value={currentTab} onValueChange={setCurrentTab}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="pending">待处理</TabsTrigger>
                <TabsTrigger value="urgent">紧急</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索采购单..."
                  className="pl-8 w-[250px]"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="采购类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="常规采购">常规采购</SelectItem>
                  <SelectItem value="设备采购">设备采购</SelectItem>
                  <SelectItem value="原材料采购">原材料采购</SelectItem>
                  <SelectItem value="服务采购">服务采购</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="待审批">待审批</SelectItem>
                  <SelectItem value="待采购">待采购</SelectItem>
                  <SelectItem value="采购中">采购中</SelectItem>
                  <SelectItem value="已完成">已完成</SelectItem>
                  <SelectItem value="已取消">已取消</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="优先级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有优先级</SelectItem>
                  <SelectItem value="紧急">紧急</SelectItem>
                  <SelectItem value="高">高</SelectItem>
                  <SelectItem value="普通">普通</SelectItem>
                  <SelectItem value="低">低</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">采购信息</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>供应商</TableHead>
                  <TableHead>申请信息</TableHead>
                  <TableHead>金额</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>优先级</TableHead>
                  <TableHead>预计交付</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{order.title}</span>
                        <span className="text-sm text-muted-foreground">{order.orderNumber}</span>
                      </div>
                    </TableCell>
                    <TableCell>{order.type}</TableCell>
                    <TableCell>{order.supplier}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{order.department}</span>
                        <span className="text-sm text-muted-foreground">{order.requestor}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span>{order.totalAmount}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>{getPriorityBadge(order.priority)}</TableCell>
                    <TableCell>{order.expectedDeliveryDate}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setSelectedOrder(order)
                            setIsDetailsOpen(true)
                          }}>
                            <FileText className="h-4 w-4 mr-2" />
                            详情
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setIsAddItemOpen(true)}>
                            <Plus className="h-4 w-4 mr-2" />
                            添加物品
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEdit(order)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDelete(order)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 采购单详情对话框 */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>采购单详情</DialogTitle>
            <DialogDescription>
              采购单号：{selectedOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">基本信息</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">采购标题：</span>
                      <span>{selectedOrder.title}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">采购类型：</span>
                      <span>{selectedOrder.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">状态：</span>
                      <span>{getStatusBadge(selectedOrder.status)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">优先级：</span>
                      <span>{getPriorityBadge(selectedOrder.priority)}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">申请信息</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">申请部门：</span>
                      <span>{selectedOrder.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">申请人：</span>
                      <span>{selectedOrder.requestor}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">审批人：</span>
                      <span>{selectedOrder.approver}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">创建时间：</span>
                      <span>{selectedOrder.createdAt}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">采购物品</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>物品名称</TableHead>
                      <TableHead>规格型号</TableHead>
                      <TableHead>单位</TableHead>
                      <TableHead>数量</TableHead>
                      <TableHead>单价</TableHead>
                      <TableHead>总价</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedOrder.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{item.name}</span>
                            <span className="text-sm text-muted-foreground">{item.code}</span>
                          </div>
                        </TableCell>
                        <TableCell>{item.specification}</TableCell>
                        <TableCell>{item.unit}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.unitPrice}</TableCell>
                        <TableCell>{item.totalPrice}</TableCell>
                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">供应商：</span>
                  <span>{selectedOrder.supplier}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">总金额：</span>
                  <span className="text-xl font-bold">¥{selectedOrder.totalAmount}</span>
                </div>
              </div>

              {selectedOrder.notes && (
                <div>
                  <h4 className="font-medium mb-2">备注</h4>
                  <p className="text-muted-foreground">{selectedOrder.notes}</p>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑采购单对话框 */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑采购单</DialogTitle>
            <DialogDescription>
              修改采购单信息
            </DialogDescription>
          </DialogHeader>
          {editForm && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>采购标题</Label>
                  <Input
                    value={editForm.title}
                    onChange={(e) => setEditForm({ ...editForm, title: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>采购类型</Label>
                  <Select
                    value={editForm.type}
                    onValueChange={(value) => setEditForm({ ...editForm, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="常规采购">常规采购</SelectItem>
                      <SelectItem value="设备采购">设备采购</SelectItem>
                      <SelectItem value="原材料采购">原材料采购</SelectItem>
                      <SelectItem value="服务采购">服务采购</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>供应商</Label>
                  <Select
                    value={editForm.supplier}
                    onValueChange={(value) => setEditForm({ ...editForm, supplier: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="办公用品供应商A">办公用品供应商A</SelectItem>
                      <SelectItem value="工程设备供应商B">工程设备供应商B</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>申请部门</Label>
                  <Select
                    value={editForm.department}
                    onValueChange={(value) => setEditForm({ ...editForm, department: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="行政部">行政部</SelectItem>
                      <SelectItem value="工程部">工程部</SelectItem>
                      <SelectItem value="采购部">采购部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>优先级</Label>
                  <Select
                    value={editForm.priority}
                    onValueChange={(value) => setEditForm({ ...editForm, priority: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="紧急">紧急</SelectItem>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="普通">普通</SelectItem>
                      <SelectItem value="低">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>预计交付日期</Label>
                  <Input
                    type="date"
                    value={editForm.expectedDeliveryDate}
                    onChange={(e) => setEditForm({ ...editForm, expectedDeliveryDate: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>采购说明</Label>
                <Textarea
                  value={editForm.notes || ""}
                  onChange={(e) => setEditForm({ ...editForm, notes: e.target.value })}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (editForm) {
                setOrders(orders.map(o => o.id === editForm.id ? editForm : o))
                setIsEditOpen(false)
                setEditForm(null)
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个采购单吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加物品对话框 */}
      <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加采购物品</DialogTitle>
            <DialogDescription>
              为采购单 {selectedOrder?.orderNumber} 添加物品
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>物品名称</Label>
                <Input placeholder="请输入物品名称" />
              </div>
              <div className="space-y-2">
                <Label>物品编码</Label>
                <Input placeholder="请输入物品编码" />
              </div>
              <div className="space-y-2">
                <Label>规格型号</Label>
                <Input placeholder="请输入规格型号" />
              </div>
              <div className="space-y-2">
                <Label>单位</Label>
                <Input placeholder="请输入单位" />
              </div>
              <div className="space-y-2">
                <Label>数量</Label>
                <Input type="number" placeholder="请输入数量" />
              </div>
              <div className="space-y-2">
                <Label>单价</Label>
                <Input type="number" placeholder="请输入单价" />
              </div>
              <div className="space-y-2">
                <Label>物品类别</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择物品类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="办公用品">办公用品</SelectItem>
                    <SelectItem value="工程设备">工程设备</SelectItem>
                    <SelectItem value="原材料">原材料</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>备注</Label>
              <Textarea placeholder="请输入备注信息" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsAddItemOpen(false)}>确认</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}