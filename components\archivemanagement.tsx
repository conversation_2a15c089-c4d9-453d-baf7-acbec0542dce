"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  Building2,
  Briefcase,
  CheckCircle2,
  XCircle,
  AlertCircle,
  BookOpen,
  Archive,
  History
} from "lucide-react"

interface ArchiveRecord {
  id: string
  title: string
  category: string
  creator: string
  department: string
  createDate: string
  updateDate: string
  status: string
  description: string
  location: string
  borrower?: string
  borrowDate?: string
  returnDate?: string
}

export function ArchiveManagement() {
  // 初始档案记录数据
  const initialRecords: ArchiveRecord[] = [
    {
      id: "1",
      title: "2025年第一季度工作总结",
      category: "工作总结",
      creator: "张三",
      department: "行政部",
      createDate: "2025-03-15",
      updateDate: "2025-03-15",
      status: "在库",
      description: "包含部门工作完成情况、存在问题及改进计划",
      location: "档案室A区-01-01"
    },
    {
      id: "2",
      title: "安全生产管理制度",
      category: "规章制度",
      creator: "李四",
      department: "安全部",
      createDate: "2025-03-10",
      updateDate: "2025-03-12",
      status: "借出",
      description: "最新版安全生产管理制度文件",
      location: "档案室B区-02-03",
      borrower: "王五",
      borrowDate: "2025-03-14",
      returnDate: "2025-03-21"
    },
    {
      id: "3",
      title: "设备维护记录",
      category: "技术文档",
      creator: "赵六",
      department: "设备部",
      createDate: "2025-03-01",
      updateDate: "2025-03-15",
      status: "在库",
      description: "主要设备日常维护和检修记录",
      location: "档案室C区-03-02"
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<ArchiveRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<ArchiveRecord>({
    id: "",
    title: "",
    category: "",
    creator: "",
    department: "",
    createDate: "",
    updateDate: "",
    status: "",
    description: "",
    location: ""
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch =
        record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.creator.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.department.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "inStock") return matchesSearch && record.status === "在库";
      if (activeTab === "borrowed") return matchesSearch && record.status === "借出";

      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      createDate: new Date().toISOString().split('T')[0],
      updateDate: new Date().toISOString().split('T')[0],
      status: "在库"
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      title: "",
      category: "",
      creator: "",
      department: "",
      createDate: "",
      updateDate: "",
      status: "",
      description: "",
      location: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? {
        ...currentRecord,
        updateDate: new Date().toISOString().split('T')[0]
      } : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      title: "",
      category: "",
      creator: "",
      department: "",
      createDate: "",
      updateDate: "",
      status: "",
      description: "",
      location: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      title: "",
      category: "",
      creator: "",
      department: "",
      createDate: "",
      updateDate: "",
      status: "",
      description: "",
      location: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 借阅档案
  const handleBorrowRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? {
        ...currentRecord,
        status: "借出",
        borrower: currentRecord.borrower,
        borrowDate: new Date().toISOString().split('T')[0],
        returnDate: currentRecord.returnDate,
        updateDate: new Date().toISOString().split('T')[0]
      } : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      title: "",
      category: "",
      creator: "",
      department: "",
      createDate: "",
      updateDate: "",
      status: "",
      description: "",
      location: ""
    })
    setIsBorrowDialogOpen(false)
  }

  // 归还档案
  const handleReturnRecord = (record: ArchiveRecord) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? {
        ...r,
        status: "在库",
        borrower: undefined,
        borrowDate: undefined,
        returnDate: undefined,
        updateDate: new Date().toISOString().split('T')[0]
      } : r
    )
    setRecords(updatedRecords)
  }

  // 打开编辑对话框
  const openEditDialog = (record: ArchiveRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: ArchiveRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 打开借阅对话框
  const openBorrowDialog = (record: ArchiveRecord) => {
    setCurrentRecord(record)
    setIsBorrowDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "在库":
        return <Badge className="bg-green-500">在库</Badge>
      case "借出":
        return <Badge variant="secondary">借出</Badge>
      case "待归还":
        return <Badge variant="destructive">待归还</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">档案管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新建档案
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>新建档案</DialogTitle>
              <DialogDescription>
                请填写档案的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">档案标题</Label>
                <Input
                  id="title"
                  value={currentRecord.title}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">档案类别</Label>
                  <Select
                    value={currentRecord.category}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="选择档案类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="工作总结">工作总结</SelectItem>
                      <SelectItem value="规章制度">规章制度</SelectItem>
                      <SelectItem value="技术文档">技术文档</SelectItem>
                      <SelectItem value="合同文件">合同文件</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">存放位置</Label>
                  <Input
                    id="location"
                    value={currentRecord.location}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="creator">创建人</Label>
                  <Input
                    id="creator"
                    value={currentRecord.creator}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, creator: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">所属部门</Label>
                  <Input
                    id="department"
                    value={currentRecord.department}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">档案描述</Label>
                <Textarea
                  id="description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认提交</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索档案记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[300px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="inStock">在库</TabsTrigger>
            <TabsTrigger value="borrowed">借出</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <Archive className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{record.title}</CardTitle>
              </div>
              {getStatusBadge(record.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    {record.category}
                  </div>
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                    {record.location}
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  创建人: {record.creator} ({record.department})
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  创建日期: {record.createDate}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">档案描述:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.description}</div>
                </div>
                {record.status === "借出" && (
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-500" />
                      借阅人: {record.borrower}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      借阅日期: {record.borrowDate}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-500" />
                      应还日期: {record.returnDate}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {record.status === "在库" && (
                    <DropdownMenuItem onClick={() => openBorrowDialog(record)}>
                      <BookOpen className="h-4 w-4 mr-2" />
                      借阅
                    </DropdownMenuItem>
                  )}
                  {record.status === "借出" && (
                    <DropdownMenuItem onClick={() => handleReturnRecord(record)}>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      归还
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>档案记录列表</CardTitle>
          <CardDescription>管理所有档案记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>档案标题</TableHead>
                <TableHead>类别</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>创建日期</TableHead>
                <TableHead>存放位置</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.title}</TableCell>
                  <TableCell>{record.category}</TableCell>
                  <TableCell>{record.creator}</TableCell>
                  <TableCell>{record.department}</TableCell>
                  <TableCell>{record.createDate}</TableCell>
                  <TableCell>{record.location}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell className="text-right">
                    {record.status === "在库" && (
                      <Button variant="ghost" size="sm" onClick={() => openBorrowDialog(record)}>
                        <BookOpen className="h-4 w-4" />
                      </Button>
                    )}
                    {record.status === "借出" && (
                      <Button variant="ghost" size="sm" onClick={() => handleReturnRecord(record)}>
                        <CheckCircle2 className="h-4 w-4" />
                      </Button>
                    )}
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑档案记录</DialogTitle>
            <DialogDescription>
              修改档案记录的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">档案标题</Label>
              <Input
                id="edit-title"
                value={currentRecord.title}
                onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-category">档案类别</Label>
                <Select
                  value={currentRecord.category}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                >
                  <SelectTrigger id="edit-category">
                    <SelectValue placeholder="选择档案类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="工作总结">工作总结</SelectItem>
                    <SelectItem value="规章制度">规章制度</SelectItem>
                    <SelectItem value="技术文档">技术文档</SelectItem>
                    <SelectItem value="合同文件">合同文件</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">存放位置</Label>
                <Input
                  id="edit-location"
                  value={currentRecord.location}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-creator">创建人</Label>
                <Input
                  id="edit-creator"
                  value={currentRecord.creator}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, creator: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-department">所属部门</Label>
                <Input
                  id="edit-department"
                  value={currentRecord.department}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">档案描述</Label>
              <Textarea
                id="edit-description"
                value={currentRecord.description}
                onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 借阅对话框 */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>借阅档案</DialogTitle>
            <DialogDescription>
              请填写借阅信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="borrow-title">档案标题</Label>
              <Input
                id="borrow-title"
                value={currentRecord.title}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="borrower">借阅人</Label>
              <Input
                id="borrower"
                value={currentRecord.borrower}
                onChange={(e) => setCurrentRecord({ ...currentRecord, borrower: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="returnDate">应还日期</Label>
              <Input
                id="returnDate"
                type="date"
                value={currentRecord.returnDate}
                onChange={(e) => setCurrentRecord({ ...currentRecord, returnDate: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBorrowDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleBorrowRecord}>确认借阅</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.title}" 的档案记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
