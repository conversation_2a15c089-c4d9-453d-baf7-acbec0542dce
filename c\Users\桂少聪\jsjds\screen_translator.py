import tkinter as tk
from PIL import Image
import pytesseract
from mss import mss
from googletrans import Translator
import time

class TranslationApp:
    def __init__(self):
        self.translator = Translator()
        self.monitor = {"top": 0, "left": 0, "width": 1920, "height": 1080}  # 修改为您的屏幕尺寸
        self.last_text = ""
        
        # 创建GUI窗口
        self.root = tk.Tk()
        self.root.overrideredirect(True)
        self.root.attributes("-topmost", True)
        self.text_label = tk.Label(self.root, font=("微软雅黑", 12), bg="lightyellow")
        self.text_label.pack()
        
    def capture_screen(self):
        with mss() as sct:
            sct_img = sct.grab(self.monitor)
            return Image.frombytes("RGB", sct_img.size, sct_img.bgra, "raw", "BGRX")

    def update_translation(self):
        try:
            # OCR识别
            img = self.capture_screen()
            text = pytesseract.image_to_string(img, lang='eng').strip()
            
            if text and text != self.last_text:
                # 翻译成中文
                translated = self.translator.translate(text, src='en', dest='zh-cn').text
                
                # 更新显示
                self.text_label.config(text=f"原文：{text}\n译文：{translated}")
                self.root.geometry(f"+{self.monitor['left']+50}+{self.monitor['top']+50}")
                self.last_text = text
                
        except Exception as e:
            print(f"Error: {e}")
            
        self.root.after(1000, self.update_translation)

    def run(self):
        self.root.after(0, self.update_translation)
        self.root.mainloop()

if __name__ == "__main__":
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # 确认路径正确
    app = TranslationApp()
    app.run()