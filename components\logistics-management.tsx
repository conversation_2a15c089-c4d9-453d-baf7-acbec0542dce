"use client"

import { useState } from 'react';
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, Row, Col, Statistic, Progress, message, Tooltip, Popconfirm } from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined, 
  FilterOutlined, 
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  WarningOutlined,
  CarOutlined,
  TruckOutlined,
  EnvironmentOutlined,
  DollarOutlined,
  FileExcelOutlined,
  PrinterOutlined,
  BarChartOutlined,
  LoadingOutlined,
  BoxPlotOutlined,
  GlobalOutlined
} from '@ant-design/icons';

const { Option } = Select;

interface LogisticsOrder {
  id: string;
  orderNumber: string;
  type: string;
  status: string;
  priority: string;
  origin: string;
  destination: string;
  goods: string;
  quantity: number;
  unit: string;
  transportMethod: string;
  expectedDeliveryDate: string;
  actualDeliveryDate: string;
  cost: number;
  carrier: string;
  trackingNumber: string;
  completionRate: number;
}

export function LogisticsManagement() {
  const [orders, setOrders] = useState<LogisticsOrder[]>([
    {
      id: '1',
      orderNumber: 'LG20240318001',
      type: '原材料运输',
      status: '运输中',
      priority: '高',
      origin: '供应商A',
      destination: '仓库B',
      goods: '钢材',
      quantity: 1000,
      unit: '吨',
      transportMethod: '公路运输',
      expectedDeliveryDate: '2024-03-20',
      actualDeliveryDate: '',
      cost: 50000,
      carrier: '某某物流公司',
      trackingNumber: 'TN123456',
      completionRate: 65,
    },
    {
      id: '2',
      orderNumber: 'LG20240318002',
      type: '成品配送',
      status: '待发货',
      priority: '中',
      origin: '工厂A',
      destination: '客户B',
      goods: '机械设备',
      quantity: 5,
      unit: '台',
      transportMethod: '铁路运输',
      expectedDeliveryDate: '2024-03-25',
      actualDeliveryDate: '',
      cost: 30000,
      carrier: '铁路物流',
      trackingNumber: 'TN234567',
      completionRate: 0,
    },
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingOrder, setEditingOrder] = useState<LogisticsOrder | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [tableLoading, setTableLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 统计信息
  const stats = {
    total: orders.length,
    inTransit: orders.filter(o => o.status === '运输中').length,
    pending: orders.filter(o => o.status === '待发货').length,
    completed: orders.filter(o => o.status === '已完成').length,
    totalCost: orders.reduce((sum, order) => sum + order.cost, 0),
  };

  // 导出Excel功能
  const handleExportExcel = () => {
    message.success('正在导出Excel文件...');
    // 这里添加导出Excel的具体实现
  };

  // 打印功能
  const handlePrint = () => {
    message.success('正在准备打印...');
    // 这里添加打印功能的具体实现
  };

  // 刷新数据功能
  const handleRefresh = () => {
    setTableLoading(true);
    message.loading('正在刷新数据...');
    setTimeout(() => {
      setTableLoading(false);
      message.success('数据已刷新');
    }, 1000);
  };

  // 批量删除功能
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 个物流订单吗？`,
      onOk: () => {
        setOrders(orders.filter(item => !selectedRowKeys.includes(item.id)));
        setSelectedRowKeys([]);
        message.success('批量删除成功');
      }
    });
  };

  // 批量更新状态功能
  const handleBatchUpdateStatus = (status: string) => {
    setOrders(orders.map(item => 
      selectedRowKeys.includes(item.id) 
        ? { ...item, status } 
        : item
    ));
    setSelectedRowKeys([]);
    message.success(`已将选中项状态更新为${status}`);
  };

  const columns = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text: string, record: LogisticsOrder) => (
        <Space>
          <span>{text}</span>
          <Tooltip title={record.priority === '高' ? '优先处理' : '正常处理'}>
            <Tag icon={record.priority === '高' ? <WarningOutlined /> : <CheckCircleOutlined />}
                color={record.priority === '高' ? 'red' : 'green'}>
              {record.priority}优先级
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '运输类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag icon={<TruckOutlined />} color="blue">{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === '运输中' ? 'processing' :
          status === '待发货' ? 'warning' :
          status === '已完成' ? 'success' : 'default'
        } icon={
          status === '运输中' ? <LoadingOutlined /> :
          status === '待发货' ? <ClockCircleOutlined /> :
          status === '已完成' ? <CheckCircleOutlined /> : <WarningOutlined />
        }>{status}</Tag>
      ),
    },
    {
      title: '完成进度',
      dataIndex: 'completionRate',
      key: 'completionRate',
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small"
          status={rate === 100 ? 'success' : 'active'}
        />
      ),
    },
    {
      title: '起始地',
      dataIndex: 'origin',
      key: 'origin',
      render: (text: string) => (
        <Space>
          <EnvironmentOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '目的地',
      dataIndex: 'destination',
      key: 'destination',
      render: (text: string) => (
        <Space>
          <EnvironmentOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '货物信息',
      key: 'goods',
      render: (record: LogisticsOrder) => (
        <Space direction="vertical" size="small">
          <span>{record.goods}</span>
          <span className="text-gray-500">{record.quantity} {record.unit}</span>
        </Space>
      ),
    },
    {
      title: '预计送达',
      dataIndex: 'expectedDeliveryDate',
      key: 'expectedDeliveryDate',
    },
    {
      title: '运输费用',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost: number) => (
        <Space>
          <DollarOutlined />
          {cost.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' })}
        </Space>
      ),
    },
    {
      title: '承运商',
      dataIndex: 'carrier',
      key: 'carrier',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: LogisticsOrder) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingOrder(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (order: LogisticsOrder) => {
    setEditingOrder(order);
    form.setFieldsValue(order);
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个物流订单吗？',
      onOk: () => {
        setOrders(orders.filter(order => order.id !== id));
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingOrder) {
        setOrders(orders.map(order =>
          order.id === editingOrder.id
            ? { ...order, ...values }
            : order
        ));
      } else {
        const newOrder: LogisticsOrder = {
          id: Date.now().toString(),
          ...values,
          completionRate: 0,
        };
        setOrders([...orders, newOrder]);
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  // 过滤数据
  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
                         order.goods.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = selectedType === 'all' || order.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: LogisticsOrder[]) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  return (
    <div className="p-6">
      <Row gutter={[16, 16]} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic 
              title={<Space><TruckOutlined />物流订单总数</Space>}
              value={stats.total}
              prefix={<BoxPlotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title={<Space><LoadingOutlined />运输中</Space>}
              value={stats.inTransit}
              valueStyle={{ color: '#1890ff' }}
              prefix={<CarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title={<Space><ClockCircleOutlined />待发货</Space>}
              value={stats.pending}
              valueStyle={{ color: '#faad14' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title={<Space><DollarOutlined />总运输费用</Space>}
              value={stats.totalCost}
              valueStyle={{ color: '#52c41a' }}
              prefix="¥"
              precision={2}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title={<Space><GlobalOutlined />物流管理</Space>}
        extra={
          <Space>
            <Tooltip title="刷新数据">
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} />
            </Tooltip>
            <Tooltip title="导出Excel">
              <Button icon={<FileExcelOutlined />} onClick={handleExportExcel} />
            </Tooltip>
            <Tooltip title="打印">
              <Button icon={<PrinterOutlined />} onClick={handlePrint} />
            </Tooltip>
            <Button icon={<FilterOutlined />}>筛选</Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增订单
            </Button>
          </Space>
        }
      >
        <Space className="mb-4" direction="vertical" style={{ width: '100%' }}>
          <Row gutter={[16, 16]} justify="space-between">
            <Col>
              <Space>
                <Input
                  placeholder="搜索订单编号/货物..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  style={{ width: 200 }}
                />
                <Select
                  value={selectedType}
                  onChange={setSelectedType}
                  style={{ width: 120 }}
                >
                  <Option value="all">所有类型</Option>
                  <Option value="原材料运输">原材料运输</Option>
                  <Option value="成品配送">成品配送</Option>
                  <Option value="设备运输">设备运输</Option>
                  <Option value="其他">其他</Option>
                </Select>
                <Select
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                  style={{ width: 120 }}
                >
                  <Option value="all">所有状态</Option>
                  <Option value="待发货">待发货</Option>
                  <Option value="运输中">运输中</Option>
                  <Option value="已完成">已完成</Option>
                  <Option value="已取消">已取消</Option>
                </Select>
              </Space>
            </Col>
            {selectedRowKeys.length > 0 && (
              <Col>
                <Space>
                  <Popconfirm
                    title="确定要删除选中项吗？"
                    onConfirm={handleBatchDelete}
                  >
                    <Button danger icon={<DeleteOutlined />}>
                      批量删除
                    </Button>
                  </Popconfirm>
                  <Button 
                    icon={<CarOutlined />}
                    onClick={() => handleBatchUpdateStatus('运输中')}
                  >
                    设为运输中
                  </Button>
                  <Button
                    icon={<CheckCircleOutlined />}
                    onClick={() => handleBatchUpdateStatus('已完成')}
                  >
                    设为已完成
                  </Button>
                </Space>
              </Col>
            )}
          </Row>
        </Space>

        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={tableLoading}
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showTotal: (total: number) => `共 ${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      <Modal
        title={editingOrder ? '编辑物流订单' : '新增物流订单'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="orderNumber"
                label="订单编号"
                rules={[{ required: true, message: '请输入订单编号' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="运输类型"
                rules={[{ required: true, message: '请选择运输类型' }]}
              >
                <Select>
                  <Option value="原材料运输">原材料运输</Option>
                  <Option value="成品配送">成品配送</Option>
                  <Option value="设备运输">设备运输</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="origin"
                label="起始地"
                rules={[{ required: true, message: '请输入起始地' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="destination"
                label="目的地"
                rules={[{ required: true, message: '请输入目的地' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="goods"
                label="货物名称"
                rules={[{ required: true, message: '请输入货物名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="quantity"
                label="数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请输入单位' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transportMethod"
                label="运输方式"
                rules={[{ required: true, message: '请选择运输方式' }]}
              >
                <Select>
                  <Option value="公路运输">公路运输</Option>
                  <Option value="铁路运输">铁路运输</Option>
                  <Option value="航空运输">航空运输</Option>
                  <Option value="水路运输">水路运输</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="carrier"
                label="承运商"
                rules={[{ required: true, message: '请输入承运商' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="expectedDeliveryDate"
                label="预计送达日期"
                rules={[{ required: true, message: '请选择预计送达日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="cost"
                label="运输费用"
                rules={[{ required: true, message: '请输入运输费用' }]}
              >
                <Input type="number" prefix="¥" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="待发货">待发货</Option>
                  <Option value="运输中">运输中</Option>
                  <Option value="已完成">已完成</Option>
                  <Option value="已取消">已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select>
                  <Option value="高">高</Option>
                  <Option value="中">中</Option>
                  <Option value="低">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="trackingNumber"
            label="物流单号"
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
