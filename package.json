{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "node scripts/export-static.js"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.18.0", "@types/chart.js": "^2.9.41", "@types/d3": "^7.4.3", "@types/three": "^0.160.0", "antd": "^5.24.3", "autoprefixer": "^10.4.20", "axios": "^1.8.3", "buffer": "^6.0.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "d3": "^7.9.0", "date-fns": "^2.30.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "embla-carousel-react": "8.5.1", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "^14.1.0", "next-themes": "latest", "ogl": "^1.0.11", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.2", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.160.1", "vaul": "^0.9.6", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}