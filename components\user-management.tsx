"use client"

import { useState, useEffect } from "react"
import {
  Search,
  Edit,
  Trash2,
  UserPlus,
  UserMinus,
  Lock,
  Download,
  Upload,
  MoreHorizontal,
  Check,
  X,
  Users,
  UserCheck,
  AlertTriangle,
  RefreshCw,
  FileText,
  Filter,
  Clock,
  Shield,
  User,
  Mail,
  Phone,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

interface User {
  id: string
  username: string
  name: string
  department: string
  role: string
  status: string
  lastLogin: string
  email?: string
  phone?: string
  createdAt?: string
  avatar?: string
  loginCount?: number
  passwordLastChanged?: string
  lastIp?: string
  permissions?: string[]
}

export function UserManagement() {
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [isEditUserOpen, setIsEditUserOpen] = useState(false)
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)
  const [isResetPasswordOpen, setIsResetPasswordOpen] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [userToEdit, setUserToEdit] = useState<User | null>(null)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [userToResetPassword, setUserToResetPassword] = useState<string | null>(null)

  const [searchText, setSearchText] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [roleFilter, setRoleFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [loading, setLoading] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)

  // 添加表单状态
  const [formData, setFormData] = useState({
    username: "",
    name: "",
    password: "",
    confirmPassword: "",
    department: "",
    role: "",
    email: "",
    phone: "",
    enabled: true,
    permissions: {
      viewUser: false,
      addUser: false,
      editUser: false,
      deleteUser: false,
      viewData: false,
      exportData: false,
      importData: false,
    }
  })

  // 添加表单验证状态
  const [formErrors, setFormErrors] = useState({
    username: "",
    name: "",
    password: "",
    confirmPassword: "",
    department: "",
    role: "",
    email: "",
    phone: "",
  })

  const [users, setUsers] = useState<User[]>([
    {
      id: "1",
      username: "admin",
      name: "系统管理员",
      department: "信息技术部",
      role: "超级管理员",
      status: "启用",
      lastLogin: "2025-03-15 08:30",
      email: "<EMAIL>",
      phone: "13800138000",
      createdAt: "2025-01-01",
      avatar: "",
      loginCount: 256,
      passwordLastChanged: "2025-02-15",
      lastIp: "*************",
      permissions: ["all"],
    },
    {
      id: "2",
      username: "zhangsan",
      name: "张三",
      department: "安全管理部",
      role: "部门主管",
      status: "启用",
      lastLogin: "2025-03-20 09:15",
      email: "<EMAIL>",
      phone: "13800138001",
      createdAt: "2025-01-10",
      avatar: "",
      loginCount: 125,
      passwordLastChanged: "2025-02-01",
      lastIp: "*************",
      permissions: ["viewUser", "viewData"],
    },
    {
      id: "3",
      username: "lisi",
      name: "李四",
      department: "工程管理部",
      role: "工程师",
      status: "启用",
      lastLogin: "2025-03-25 16:45",
      email: "<EMAIL>",
      phone: "13800138002",
      createdAt: "2025-01-15",
      avatar: "",
      loginCount: 98,
      passwordLastChanged: "2025-03-10",
      lastIp: "*************",
    },
    {
      id: "4",
      username: "wangwu",
      name: "王五",
      department: "人事管理部",
      role: "人事专员",
      status: "启用",
      lastLogin: "2025-03-29 14:20",
      email: "<EMAIL>",
      phone: "13800138003",
      createdAt: "2025-01-20",
      avatar: "",
      loginCount: 78,
      passwordLastChanged: "2025-03-05",
      lastIp: "*************",
    },
    {
      id: "5",
      username: "zhaoliu",
      name: "赵六",
      department: "财务管理部",
      role: "财务主管",
      status: "禁用",
      lastLogin: "2025-02-25 10:30",
      email: "<EMAIL>",
      phone: "13800138004",
      createdAt: "2025-01-05",
      avatar: "",
      loginCount: 45,
      passwordLastChanged: "2025-02-15",
      lastIp: "*************",
    },
    {
      id: "6",
      username: "sunqi",
      name: "孙七",
      department: "信息技术部",
      role: "系统工程师",
      status: "启用",
      lastLogin: "2025-04-02 11:20",
      email: "<EMAIL>",
      phone: "13800138005",
      createdAt: "2025-01-25",
      avatar: "",
      loginCount: 102,
      passwordLastChanged: "2025-03-10",
      lastIp: "*************",
    },
    {
      id: "7",
      username: "zhouba",
      name: "周八",
      department: "安全管理部",
      role: "安全员",
      status: "启用",
      lastLogin: "2025-04-01 15:40",
      email: "<EMAIL>",
      phone: "13800138006",
      createdAt: "2025-02-01",
      avatar: "",
      loginCount: 65,
      passwordLastChanged: "2025-03-05",
      lastIp: "*************",
    },
    {
      id: "8",
      username: "wujiu",
      name: "吴九",
      department: "工程管理部",
      role: "工程师",
      status: "禁用",
      lastLogin: "2025-02-15 09:10",
      email: "<EMAIL>",
      phone: "13800138007",
      createdAt: "2025-01-15",
      avatar: "",
      loginCount: 30,
      passwordLastChanged: "2025-02-20",
      lastIp: "*************",
    },
    {
      id: "9",
      username: "zhengshi",
      name: "郑十",
      department: "人事管理部",
      role: "人事专员",
      status: "启用",
      lastLogin: "2025-04-02 08:50",
      email: "<EMAIL>",
      phone: "13800138008",
      createdAt: "2025-02-10",
      avatar: "",
      loginCount: 86,
      passwordLastChanged: "2025-03-15",
      lastIp: "*************",
    },
    {
      id: "10",
      username: "liuyiyi",
      name: "刘一一",
      department: "财务管理部",
      role: "财务专员",
      status: "启用",
      lastLogin: "2025-03-01 13:25",
      email: "<EMAIL>",
      phone: "13800138009",
      createdAt: "2025-01-15",
      avatar: "",
      loginCount: 54,
      passwordLastChanged: "2025-02-25",
      lastIp: "*************",
    },
  ])

  // 统计信息
  const stats = {
    total: users.length,
    active: users.filter(user => user.status === "启用").length,
    disabled: users.filter(user => user.status === "禁用").length,
    departments: new Set(users.map(user => user.department)).size,
    roles: new Set(users.map(user => user.role)).size,
    recentLogin: users.filter(user => {
      const lastLoginDate = new Date(user.lastLogin);
      const now = new Date();
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(now.getDate() - 7);
      return lastLoginDate >= sevenDaysAgo && user.lastLogin !== "-";
    }).length,
    adminCount: users.filter(user => user.role === "超级管理员").length,
  }

  // 部门列表
  const departments = Array.from(new Set(users.map(user => user.department)));

  // 角色列表
  const roles = Array.from(new Set(users.map(user => user.role)));

  const toggleSelectUser = (userId: string) => {
    setSelectedUsers((prev) => (prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]))
  }

  const toggleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id))
    }
  }

  // 处理用户状态更新
  const handleUpdateStatus = (userId: string, newStatus: string) => {
    setUsers(users.map(user =>
      user.id === userId ? { ...user, status: newStatus } : user
    ))
    toast({
      title: "状态已更新",
      description: `用户状态已更新为: ${newStatus}`,
      variant: newStatus === "启用" ? "default" : "destructive",
    })
  }

  // 批量更新状态
  const handleBatchUpdateStatus = (status: string) => {
    setUsers(users.map(user =>
      selectedUsers.includes(user.id) ? { ...user, status } : user
    ))
    setSelectedUsers([])
    toast({
      title: "批量操作成功",
      description: `已将 ${selectedUsers.length} 个用户的状态更新为: ${status}`,
      variant: status === "启用" ? "default" : "destructive",
    })
  }

  // 删除用户
  const handleDelete = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId))
    toast({
      title: "用户已删除",
      description: "用户记录已从系统中移除",
      variant: "destructive",
    })
  }

  // 批量删除
  const handleBatchDelete = () => {
    const count = selectedUsers.length;
    setUsers(users.filter(user => !selectedUsers.includes(user.id)))
    setSelectedUsers([])
    toast({
      title: "批量删除成功",
      description: `已删除 ${count} 个用户`,
      variant: "destructive",
    })
  }

  // 导出Excel
  const handleExport = () => {
    try {
      setIsExporting(true);

      setTimeout(() => {
        const exportData = users.map(user => ({
          '用户名': user.username,
          '姓名': user.name,
          '部门': user.department,
          '角色': user.role,
          '状态': user.status,
          '最后登录': user.lastLogin,
          '邮箱': user.email || '',
          '电话': user.phone || '',
          '创建时间': user.createdAt || '',
          '登录次数': user.loginCount || 0,
          '最后密码修改': user.passwordLastChanged || '',
          '最后登录IP': user.lastIp || '',
        }))

        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)

        // 设置列宽
        const colWidths = [
          { wch: 15 }, // 用户名
          { wch: 10 }, // 姓名
          { wch: 15 }, // 部门
          { wch: 15 }, // 角色
          { wch: 10 }, // 状态
          { wch: 20 }, // 最后登录
          { wch: 25 }, // 邮箱
          { wch: 15 }, // 电话
          { wch: 20 }, // 创建时间
          { wch: 10 }, // 登录次数
          { wch: 20 }, // 最后密码修改
          { wch: 15 }, // 最后登录IP
        ]
        ws['!cols'] = colWidths

        // 添加样式
        const headerStyle = {
          font: { bold: true, color: { rgb: "FFFFFF" } },
          fill: { fgColor: { rgb: "4472C4" } },
          alignment: { horizontal: "center", vertical: "center" }
        }

        // 为表头添加样式
        const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const address = XLSX.utils.encode_col(C) + "1"
          if (!ws[address]) continue
          ws[address].s = headerStyle
        }

        XLSX.utils.book_append_sheet(wb, ws, '用户列表')
        XLSX.writeFile(wb, `用户列表_${new Date().toLocaleDateString()}.xlsx`)

        setIsExporting(false);
        toast({
          title: "导出成功",
          description: `成功导出 ${users.length} 条用户数据`,
        })
      }, 1000);
    } catch (error) {
      console.error('导出失败:', error)
      setIsExporting(false);
      toast({
        title: "导出失败",
        description: "导出过程中发生错误，请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 导入Excel
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true);
    setImportProgress(0);

    const simulateProgress = () => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        setImportProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
        }
      }, 100);
      return interval;
    };

    const progressInterval = simulateProgress();

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        setTimeout(() => {
          clearInterval(progressInterval);
          setImportProgress(100);

          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const worksheet = workbook.Sheets[workbook.SheetNames[0]]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)

          // 转换导入的数据为用户格式
          const importedUsers: User[] = jsonData.map((row: any, index) => ({
            id: (Date.now() + index).toString(),
            username: row['用户名'] || '',
            name: row['姓名'] || '',
            department: row['部门'] || '',
            role: row['角色'] || '',
            status: row['状态'] || '启用',
            lastLogin: '-',
            email: row['邮箱'] || '',
            phone: row['电话'] || '',
            createdAt: new Date().toLocaleDateString(),
            avatar: "",
            loginCount: 0,
            passwordLastChanged: "",
            lastIp: "",
            permissions: [],
          }))

          setUsers([...users, ...importedUsers])

          setTimeout(() => {
            setIsImporting(false);
            toast({
              title: "导入成功",
              description: `成功导入 ${importedUsers.length} 个用户`,
            })
          }, 500);
        }, 1500);
      } catch (error) {
        console.error('导入失败:', error)
        setIsImporting(false);
        setImportProgress(0);
        toast({
          title: "导入失败",
          description: "导入过程中发生错误，请检查文件格式",
          variant: "destructive",
        })
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 筛选用户
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.username.toLowerCase().includes(searchText.toLowerCase()) ||
      user.name.toLowerCase().includes(searchText.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      user.phone?.toLowerCase().includes(searchText.toLowerCase())

    const matchesDepartment = departmentFilter === "all" || user.department === departmentFilter
    const matchesStatus = statusFilter === "all" || user.status === statusFilter
    const matchesRole = roleFilter === "all" || user.role === roleFilter

    return matchesSearch && matchesDepartment && matchesStatus && matchesRole
  })

  // 分页处理
  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const totalPages = Math.ceil(filteredUsers.length / pageSize)

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "数据已刷新",
        description: "用户列表数据已更新",
      })
    }, 1000)
  }

  // 处理表单输入变化
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // 清除对应字段的错误信息
    setFormErrors(prev => ({
      ...prev,
      [field]: ""
    }))
  }

  // 处理权限复选框变化
  const handlePermissionChange = (field: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [field]: checked
      }
    }))
  }

  // 验证表单
  const validateForm = () => {
    const errors = {
      username: "",
      name: "",
      password: "",
      confirmPassword: "",
      department: "",
      role: "",
      email: "",
      phone: "",
    }
    let isValid = true

    // 用户名验证
    if (!formData.username) {
      errors.username = "用户名不能为空"
      isValid = false
    } else if (formData.username.length < 3) {
      errors.username = "用户名长度不能小于3个字符"
      isValid = false
    } else if (users.some(user => user.username === formData.username)) {
      errors.username = "用户名已存在"
      isValid = false
    }

    // 姓名验证
    if (!formData.name) {
      errors.name = "姓名不能为空"
      isValid = false
    }

    // 密码验证
    if (!formData.password) {
      errors.password = "密码不能为空"
      isValid = false
    } else if (formData.password.length < 6) {
      errors.password = "密码长度不能小于6个字符"
      isValid = false
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = "密码必须包含大小写字母和数字"
      isValid = false
    }

    // 确认密码验证
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "两次输入的密码不一致"
      isValid = false
    }

    // 部门验证
    if (!formData.department) {
      errors.department = "请选择部门"
      isValid = false
    }

    // 角色验证
    if (!formData.role) {
      errors.role = "请选择角色"
      isValid = false
    }

    // 邮箱验证
    if (!formData.email) {
      errors.email = "请输入邮箱"
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "请输入有效的邮箱地址"
      isValid = false
    }

    // 电话验证
    if (!formData.phone) {
      errors.phone = "请输入电话"
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.phone = "请输入有效的手机号码"
      isValid = false
    }

    setFormErrors(errors)
    return isValid
  }

  // 处理添加用户
  const handleAddUser = () => {
    if (!validateForm()) {
      toast({
        title: "表单验证失败",
        description: "请检查并填写所有必填项",
        variant: "destructive",
      })
      return
    }

    const newUser: User = {
      id: Date.now().toString(),
      username: formData.username,
      name: formData.name,
      department: formData.department,
      role: formData.role,
      status: formData.enabled ? "启用" : "禁用",
      lastLogin: "-",
      email: formData.email,
      phone: formData.phone,
      createdAt: new Date().toLocaleString(),
      avatar: "",
      loginCount: 0,
      passwordLastChanged: new Date().toLocaleString(),
      lastIp: "",
      permissions: Object.entries(formData.permissions)
        .filter(([_, value]) => value)
        .map(([key]) => key),
    }

    setUsers(prev => [...prev, newUser])
    toast({
      title: "添加成功",
      description: "新用户已成功创建",
    })
    setIsAddUserOpen(false)

    // 重置表单
    setFormData({
      username: "",
      name: "",
      password: "",
      confirmPassword: "",
      department: "",
      role: "",
      email: "",
      phone: "",
      enabled: true,
      permissions: {
        viewUser: false,
        addUser: false,
        editUser: false,
        deleteUser: false,
        viewData: false,
        exportData: false,
        importData: false,
      }
    })

    // 重置错误信息
    setFormErrors({
      username: "",
      name: "",
      password: "",
      confirmPassword: "",
      department: "",
      role: "",
      email: "",
      phone: "",
    })
  }

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setUserToEdit(user)
    setFormData({
      username: user.username,
      name: user.name,
      password: "",
      confirmPassword: "",
      department: user.department,
      role: user.role,
      email: user.email || "",
      phone: user.phone || "",
      enabled: user.status === "启用",
      permissions: {
        viewUser: user.permissions?.includes("viewUser") || false,
        addUser: user.permissions?.includes("addUser") || false,
        editUser: user.permissions?.includes("editUser") || false,
        deleteUser: user.permissions?.includes("deleteUser") || false,
        viewData: user.permissions?.includes("viewData") || false,
        exportData: user.permissions?.includes("exportData") || false,
        importData: user.permissions?.includes("importData") || false,
      }
    })
    setIsEditUserOpen(true)
  }

  // 处理更新用户
  const handleUpdateUser = () => {
    if (!userToEdit) return

    // 验证必填字段
    const errors = {
      username: "",
      name: "",
      password: "",
      confirmPassword: "",
      department: "",
      role: "",
      email: "",
      phone: "",
    }
    let isValid = true

    if (!formData.name) {
      errors.name = "姓名不能为空"
      isValid = false
    }

    if (!formData.department) {
      errors.department = "请选择部门"
      isValid = false
    }

    if (!formData.role) {
      errors.role = "请选择角色"
      isValid = false
    }

    if (!formData.email) {
      errors.email = "请输入邮箱"
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "请输入有效的邮箱地址"
      isValid = false
    }

    if (!formData.phone) {
      errors.phone = "请输入电话"
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.phone = "请输入有效的手机号码"
      isValid = false
    }

    if (!isValid) {
      setFormErrors(errors)
      toast({
        title: "表单验证失败",
        description: "请检查并填写所有必填项",
        variant: "destructive",
      })
      return
    }

    const updatedUser = {
      ...userToEdit,
      name: formData.name,
      department: formData.department,
      role: formData.role,
      status: formData.enabled ? "启用" : "禁用",
      email: formData.email,
      phone: formData.phone,
      permissions: Object.entries(formData.permissions)
        .filter(([_, value]) => value)
        .map(([key]) => key),
    }

    setUsers(users.map(user =>
      user.id === userToEdit.id ? updatedUser : user
    ))

    toast({
      title: "更新成功",
      description: "用户信息已成功更新",
    })

    setIsEditUserOpen(false)
    setUserToEdit(null)
    setFormErrors({
      username: "",
      name: "",
      password: "",
      confirmPassword: "",
      department: "",
      role: "",
      email: "",
      phone: "",
    })
  }

  // 处理重置密码
  const handleResetPassword = (userId: string) => {
    setUserToResetPassword(userId)
    setIsResetPasswordOpen(true)
  }

  // 确认重置密码
  const handleConfirmResetPassword = () => {
    if (!userToResetPassword) return

    // 这里应该调用API重置密码
    toast({
      title: "密码已重置",
      description: "新密码已发送至用户邮箱",
    })

    setIsResetPasswordOpen(false)
    setUserToResetPassword(null)
  }

  // 处理确认删除
  const handleConfirmDelete = (userId: string) => {
    setUserToDelete(userId)
    setIsDeleteConfirmOpen(true)
  }

  // 确认删除用户
  const handleConfirmDeleteUser = () => {
    if (!userToDelete) return

    handleDelete(userToDelete)
    setIsDeleteConfirmOpen(false)
    setUserToDelete(null)
  }

  // 在return语句前添加
  const [isUserDetailOpen, setIsUserDetailOpen] = useState(false)
  const [selectedUserDetail, setSelectedUserDetail] = useState<User | null>(null)

  // 处理查看用户详情
  const handleViewUserDetail = (user: User) => {
    setSelectedUserDetail(user)
    setIsUserDetailOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">用户管理</h2>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleExport} disabled={isExporting}>
                  <Download className="h-4 w-4 mr-2" />
                  {isExporting ? "导出中..." : "导出"}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>导出用户数据到Excel</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <label>
                  <Button variant="outline" size="sm" asChild disabled={isImporting}>
                    <span>
                      <Upload className="h-4 w-4 mr-2" />
                      {isImporting ? "导入中..." : "导入"}
                    </span>
                  </Button>
                  <input
                    type="file"
                    accept=".xlsx,.xls"
                    className="hidden"
                    onChange={handleImport}
                    disabled={isImporting}
                  />
                </label>
              </TooltipTrigger>
              <TooltipContent>
                <p>从Excel导入用户数据</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
                  <RefreshCw className={cn("h-4 w-4 mr-2", { "animate-spin": loading })} />
                  {loading ? "刷新中..." : "刷新"}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>刷新用户数据</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总用户数</p>
                  <h3 className="text-2xl font-bold">{stats.total}</h3>
                </div>
              </div>
              <Progress value={100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <UserCheck className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
                  <h3 className="text-2xl font-bold">{stats.recentLogin}</h3>
                </div>
              </div>
              <Progress value={(stats.recentLogin / stats.total) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-yellow-100 p-3">
                  <Shield className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">管理员</p>
                  <h3 className="text-2xl font-bold">{stats.adminCount}</h3>
                </div>
              </div>
              <Progress value={(stats.adminCount / stats.total) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-purple-100 p-3">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">部门数</p>
                  <h3 className="text-2xl font-bold">{stats.departments}</h3>
                </div>
              </div>
              <Progress value={(stats.departments / 10) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>管理系统用户账户和权限</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索用户..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="启用">启用</SelectItem>
                    <SelectItem value="禁用">禁用</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有角色</SelectItem>
                    {roles.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                {selectedUsers.length > 0 ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBatchUpdateStatus("禁用")}
                    >
                      <UserMinus className="h-4 w-4 mr-2" />
                      批量禁用
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsDeleteConfirmOpen(true)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      批量删除
                    </Button>
                  </>
                ) : (
                  <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <UserPlus className="h-4 w-4 mr-2" />
                        添加用户
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px]">
                      <DialogHeader>
                        <DialogTitle>添加新用户</DialogTitle>
                        <DialogDescription>创建新用户账户并分配权限</DialogDescription>
                      </DialogHeader>
                      <Tabs defaultValue="basic" className="mt-4">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="basic">基本信息</TabsTrigger>
                          <TabsTrigger value="permissions">权限设置</TabsTrigger>
                        </TabsList>
                        <TabsContent value="basic" className="space-y-4 mt-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="username">用户名</Label>
                              <Input
                                id="username"
                                placeholder="请输入用户名"
                                value={formData.username}
                                onChange={(e) => handleInputChange("username", e.target.value)}
                              />
                              {formErrors.username && (
                                <p className="text-sm text-red-500">{formErrors.username}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="name">姓名</Label>
                              <Input
                                id="name"
                                placeholder="请输入姓名"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                              />
                              {formErrors.name && (
                                <p className="text-sm text-red-500">{formErrors.name}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="password">密码</Label>
                              <Input
                                id="password"
                                type="password"
                                placeholder="请输入密码"
                                value={formData.password}
                                onChange={(e) => handleInputChange("password", e.target.value)}
                              />
                              {formErrors.password && (
                                <p className="text-sm text-red-500">{formErrors.password}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="confirm-password">确认密码</Label>
                              <Input
                                id="confirm-password"
                                type="password"
                                placeholder="请再次输入密码"
                                value={formData.confirmPassword}
                                onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                              />
                              {formErrors.confirmPassword && (
                                <p className="text-sm text-red-500">{formErrors.confirmPassword}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="department">部门</Label>
                              <Select
                                value={formData.department}
                                onValueChange={(value) => handleInputChange("department", value)}
                              >
                                <SelectTrigger id="department">
                                  <SelectValue placeholder="选择部门" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="信息技术部">信息技术部</SelectItem>
                                  <SelectItem value="安全管理部">安全管理部</SelectItem>
                                  <SelectItem value="工程管理部">工程管理部</SelectItem>
                                  <SelectItem value="人事管理部">人事管理部</SelectItem>
                                  <SelectItem value="财务管理部">财务管理部</SelectItem>
                                </SelectContent>
                              </Select>
                              {formErrors.department && (
                                <p className="text-sm text-red-500">{formErrors.department}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="role">角色</Label>
                              <Select
                                value={formData.role}
                                onValueChange={(value) => handleInputChange("role", value)}
                              >
                                <SelectTrigger id="role">
                                  <SelectValue placeholder="选择角色" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="超级管理员">系统管理员</SelectItem>
                                  <SelectItem value="部门主管">部门主管</SelectItem>
                                  <SelectItem value="普通员工">普通员工</SelectItem>
                                </SelectContent>
                              </Select>
                              {formErrors.role && (
                                <p className="text-sm text-red-500">{formErrors.role}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="enabled"
                              checked={formData.enabled}
                              onCheckedChange={(checked) => handleInputChange("enabled", !!checked)}
                            />
                            <Label htmlFor="enabled">启用账户</Label>
                          </div>
                        </TabsContent>
                        <TabsContent value="permissions" className="space-y-4 mt-4">
                          <div className="space-y-4">
                            <h4 className="text-sm font-medium">系统权限</h4>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-user-view"
                                  checked={formData.permissions.viewUser}
                                  onCheckedChange={(checked) => handlePermissionChange("viewUser", !!checked)}
                                />
                                <Label htmlFor="perm-user-view">查看用户</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-user-add"
                                  checked={formData.permissions.addUser}
                                  onCheckedChange={(checked) => handlePermissionChange("addUser", !!checked)}
                                />
                                <Label htmlFor="perm-user-add">添加用户</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-user-edit"
                                  checked={formData.permissions.editUser}
                                  onCheckedChange={(checked) => handlePermissionChange("editUser", !!checked)}
                                />
                                <Label htmlFor="perm-user-edit">编辑用户</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-user-delete"
                                  checked={formData.permissions.deleteUser}
                                  onCheckedChange={(checked) => handlePermissionChange("deleteUser", !!checked)}
                                />
                                <Label htmlFor="perm-user-delete">删除用户</Label>
                              </div>
                            </div>

                            <h4 className="text-sm font-medium">数据权限</h4>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-data-view"
                                  checked={formData.permissions.viewData}
                                  onCheckedChange={(checked) => handlePermissionChange("viewData", !!checked)}
                                />
                                <Label htmlFor="perm-data-view">查看数据</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-data-export"
                                  checked={formData.permissions.exportData}
                                  onCheckedChange={(checked) => handlePermissionChange("exportData", !!checked)}
                                />
                                <Label htmlFor="perm-data-export">导出数据</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="perm-data-import"
                                  checked={formData.permissions.importData}
                                  onCheckedChange={(checked) => handlePermissionChange("importData", !!checked)}
                                />
                                <Label htmlFor="perm-data-import">导入数据</Label>
                              </div>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                      <DialogFooter className="mt-6">
                        <Button variant="outline" onClick={() => {
                          setIsAddUserOpen(false)
                          setFormErrors({
                            username: "",
                            name: "",
                            password: "",
                            confirmPassword: "",
                            department: "",
                            role: "",
                            email: "",
                            phone: "",
                          })
                        }}>
                          取消
                        </Button>
                        <Button onClick={handleAddUser}>保存</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedUsers.length === paginatedUsers.length && paginatedUsers.length > 0}
                        onCheckedChange={toggleSelectAll}
                      />
                    </TableHead>
                    <TableHead>用户名</TableHead>
                    <TableHead>姓名</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>最后登录</TableHead>
                    <TableHead className="w-[180px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex items-center justify-center">
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : paginatedUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        没有找到匹配的用户
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={() => toggleSelectUser(user.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar || ""} />
                              <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="font-medium">{user.username}</span>
                              <span className="text-sm text-muted-foreground">{user.email}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.name}</TableCell>
                        <TableCell>{user.department}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.role}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={user.status === "启用" ? "default" : "destructive"}>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{user.lastLogin}</span>
                            <span className="text-sm text-muted-foreground">{user.lastIp}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleViewUserDetail(user)}
                                  >
                                    <FileText className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>查看详情</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEditUser(user)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>编辑用户</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleResetPassword(user.id)}
                                  >
                                    <Lock className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>重置密码</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleConfirmDelete(user.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>删除用户</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() =>
                                  handleUpdateStatus(user.id, user.status === "启用" ? "禁用" : "启用")
                                }>
                                  {user.status === "启用" ? (
                                    <>
                                      <X className="h-4 w-4 mr-2" />
                                      禁用账户
                                    </>
                                  ) : (
                                    <>
                                      <Check className="h-4 w-4 mr-2" />
                                      启用账户
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredUsers.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 用户详情对话框 */}
      <Dialog open={isUserDetailOpen} onOpenChange={setIsUserDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>用户详情</DialogTitle>
            <DialogDescription>查看用户的详细信息</DialogDescription>
          </DialogHeader>
          <div className="relative">
            <ScrollArea className="h-[calc(80vh-8rem)] w-full rounded-md border p-4">
              {selectedUserDetail && (
                <div className="grid gap-4">
                  <div className="flex items-center justify-center mb-4">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={selectedUserDetail.avatar || ""} />
                      <AvatarFallback className="text-lg">
                        {selectedUserDetail.name.slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>用户名</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedUserDetail.username}</span>
                      </div>
                    </div>
                    <div>
                      <Label>姓名</Label>
                      <div className="mt-1">{selectedUserDetail.name}</div>
                    </div>
                    <div>
                      <Label>部门</Label>
                      <div className="mt-1">{selectedUserDetail.department}</div>
                    </div>
                    <div>
                      <Label>角色</Label>
                      <div className="mt-1">{selectedUserDetail.role}</div>
                    </div>
                    <div>
                      <Label>邮箱</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedUserDetail.email}</span>
                      </div>
                    </div>
                    <div>
                      <Label>电话</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedUserDetail.phone}</span>
                      </div>
                    </div>
                    <div>
                      <Label>状态</Label>
                      <div className="mt-1">
                        <Badge variant={selectedUserDetail.status === "启用" ? "default" : "destructive"}>
                          {selectedUserDetail.status}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <Label>登录次数</Label>
                      <div className="mt-1">{selectedUserDetail.loginCount || 0}</div>
                    </div>
                    <div>
                      <Label>最后登录</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedUserDetail.lastLogin}</span>
                      </div>
                    </div>
                    <div>
                      <Label>最后登录IP</Label>
                      <div className="mt-1">{selectedUserDetail.lastIp || "-"}</div>
                    </div>
                    <div>
                      <Label>创建时间</Label>
                      <div className="mt-1">{selectedUserDetail.createdAt}</div>
                    </div>
                    <div>
                      <Label>密码修改时间</Label>
                      <div className="mt-1">{selectedUserDetail.passwordLastChanged || "-"}</div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Label>权限</Label>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {selectedUserDetail.permissions?.map((permission) => (
                        <Badge key={permission} variant="outline">
                          {permission}
                        </Badge>
                      )) || "无特殊权限"}
                    </div>
                  </div>
                </div>
              )}
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              {selectedUsers.length > 0
                ? `确定要删除选中的 ${selectedUsers.length} 个用户吗？`
                : "确定要删除该用户吗？"}
              此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (selectedUsers.length > 0) {
                  handleBatchDelete();
                } else if (userToDelete) {
                  handleConfirmDeleteUser();
                }
                setIsDeleteConfirmOpen(false);
              }}
            >
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重置密码确认对话框 */}
      <Dialog open={isResetPasswordOpen} onOpenChange={setIsResetPasswordOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重置密码</DialogTitle>
            <DialogDescription>
              确定要重置该用户的密码吗？新密码将发送至用户邮箱。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResetPasswordOpen(false)}>
              取消
            </Button>
            <Button onClick={handleConfirmResetPassword}>
              确认重置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入进度对话框 */}
      {isImporting && (
        <Dialog open={true}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>导入用户数据</DialogTitle>
              <DialogDescription>
                正在导入数据，请稍候...
              </DialogDescription>
            </DialogHeader>
            <div className="py-6">
              <Progress value={importProgress} className="w-full" />
              <p className="text-center mt-2">{importProgress}%</p>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* 编辑用户对话框 */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑用户</DialogTitle>
            <DialogDescription>修改用户信息和权限设置</DialogDescription>
          </DialogHeader>
          <div className="relative">
            <ScrollArea className="h-[calc(80vh-8rem)] w-full rounded-md border p-4">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="permissions">权限设置</TabsTrigger>
                </TabsList>
                <TabsContent value="basic" className="space-y-4 mt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-username">用户名</Label>
                      <Input
                        id="edit-username"
                        value={formData.username}
                        disabled
                        className="bg-muted"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-name">姓名</Label>
                      <Input
                        id="edit-name"
                        placeholder="请输入姓名"
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                      />
                      {formErrors.name && (
                        <p className="text-sm text-red-500">{formErrors.name}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-email">邮箱</Label>
                      <Input
                        id="edit-email"
                        type="email"
                        placeholder="请输入邮箱"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                      />
                      {formErrors.email && (
                        <p className="text-sm text-red-500">{formErrors.email}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-phone">电话</Label>
                      <Input
                        id="edit-phone"
                        placeholder="请输入电话"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                      />
                      {formErrors.phone && (
                        <p className="text-sm text-red-500">{formErrors.phone}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-department">部门</Label>
                      <Select
                        value={formData.department}
                        onValueChange={(value) => handleInputChange("department", value)}
                      >
                        <SelectTrigger id="edit-department">
                          <SelectValue placeholder="选择部门" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept} value={dept}>
                              {dept}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.department && (
                        <p className="text-sm text-red-500">{formErrors.department}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-role">角色</Label>
                      <Select
                        value={formData.role}
                        onValueChange={(value) => handleInputChange("role", value)}
                      >
                        <SelectTrigger id="edit-role">
                          <SelectValue placeholder="选择角色" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role} value={role}>
                              {role}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.role && (
                        <p className="text-sm text-red-500">{formErrors.role}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="edit-enabled"
                      checked={formData.enabled}
                      onCheckedChange={(checked) => handleInputChange("enabled", checked)}
                    />
                    <Label htmlFor="edit-enabled">启用账户</Label>
                  </div>
                </TabsContent>
                <TabsContent value="permissions" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">系统权限</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-user-view"
                          checked={formData.permissions.viewUser}
                          onCheckedChange={(checked) => handlePermissionChange("viewUser", !!checked)}
                        />
                        <Label htmlFor="edit-perm-user-view">查看用户</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-user-add"
                          checked={formData.permissions.addUser}
                          onCheckedChange={(checked) => handlePermissionChange("addUser", !!checked)}
                        />
                        <Label htmlFor="edit-perm-user-add">添加用户</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-user-edit"
                          checked={formData.permissions.editUser}
                          onCheckedChange={(checked) => handlePermissionChange("editUser", !!checked)}
                        />
                        <Label htmlFor="edit-perm-user-edit">编辑用户</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-user-delete"
                          checked={formData.permissions.deleteUser}
                          onCheckedChange={(checked) => handlePermissionChange("deleteUser", !!checked)}
                        />
                        <Label htmlFor="edit-perm-user-delete">删除用户</Label>
                      </div>
                    </div>

                    <h4 className="text-sm font-medium">数据权限</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-data-view"
                          checked={formData.permissions.viewData}
                          onCheckedChange={(checked) => handlePermissionChange("viewData", !!checked)}
                        />
                        <Label htmlFor="edit-perm-data-view">查看数据</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-data-export"
                          checked={formData.permissions.exportData}
                          onCheckedChange={(checked) => handlePermissionChange("exportData", !!checked)}
                        />
                        <Label htmlFor="edit-perm-data-export">导出数据</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit-perm-data-import"
                          checked={formData.permissions.importData}
                          onCheckedChange={(checked) => handlePermissionChange("importData", !!checked)}
                        />
                        <Label htmlFor="edit-perm-data-import">导入数据</Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </ScrollArea>
          </div>
          <DialogFooter className="mt-4">
            <Button onClick={handleUpdateUser}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

