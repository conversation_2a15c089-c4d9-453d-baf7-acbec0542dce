"use client"

import { useState } from "react"
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash, 
  Search, 
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  Building2,
  Briefcase,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Package,
  Truck,
  Store,
  ClipboardList,
  DollarSign,
  BarChart,
  FileCheck,
  FileX,
  FileClock,
  FilePlus
} from "lucide-react"

interface ProcurementRecord {
  id: string
  type: string
  title: string
  applicant: string
  department: string
  applyDate: string
  status: string
  priority: string
  budget: number
  items: {
    name: string
    specification: string
    unit: string
    quantity: number
    estimatedPrice: number
    supplier: string
  }[]
  description: string
  approvalStatus: string
  approvalDate?: string
  approvalComments?: string
  totalAmount: number
}

export function ProcurementManagement() {
  // 初始采购记录数据
  const initialRecords: ProcurementRecord[] = [
    {
      id: "1",
      type: "采购申请",
      title: "安全防护用品采购申请",
      applicant: "张三",
      department: "安全部",
      applyDate: "2024-03-01",
      status: "待审批",
      priority: "高",
      budget: 50000,
      items: [
        {
          name: "安全帽",
          specification: "标准型",
          unit: "个",
          quantity: 200,
          estimatedPrice: 50,
          supplier: "安全防护用品有限公司"
        },
        {
          name: "工作手套",
          specification: "防割型",
          unit: "双",
          quantity: 500,
          estimatedPrice: 25,
          supplier: "安全防护用品有限公司"
        }
      ],
      description: "用于日常安全防护工作",
      approvalStatus: "待审批",
      totalAmount: 22500
    },
    {
      id: "2",
      type: "采购计划",
      title: "2024年Q1季度办公用品采购计划",
      applicant: "李四",
      department: "行政部",
      applyDate: "2024-02-15",
      status: "已审批",
      priority: "中",
      budget: 30000,
      items: [
        {
          name: "办公用纸",
          specification: "A4",
          unit: "包",
          quantity: 100,
          estimatedPrice: 25,
          supplier: "办公用品有限公司"
        },
        {
          name: "签字笔",
          specification: "黑色",
          unit: "支",
          quantity: 200,
          estimatedPrice: 2,
          supplier: "办公用品有限公司"
        }
      ],
      description: "用于日常办公使用",
      approvalStatus: "已通过",
      approvalDate: "2024-02-20",
      approvalComments: "同意采购",
      totalAmount: 2900
    },
    {
      id: "3",
      type: "采购订单",
      title: "照明设备采购订单",
      applicant: "王五",
      department: "设备部",
      applyDate: "2024-03-10",
      status: "执行中",
      priority: "高",
      budget: 100000,
      items: [
        {
          name: "矿灯",
          specification: "LED型",
          unit: "个",
          quantity: 100,
          estimatedPrice: 200,
          supplier: "矿山设备有限公司"
        }
      ],
      description: "用于井下照明",
      approvalStatus: "已通过",
      approvalDate: "2024-03-12",
      approvalComments: "同意采购，注意质量",
      totalAmount: 20000
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<ProcurementRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<ProcurementRecord>({
    id: "",
    type: "",
    title: "",
    applicant: "",
    department: "",
    applyDate: "",
    status: "",
    priority: "",
    budget: 0,
    items: [],
    description: "",
    approvalStatus: "",
    totalAmount: 0
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch = 
        record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.department.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (activeTab === "all") return matchesSearch;
      if (activeTab === "pending") return matchesSearch && record.status === "待审批";
      if (activeTab === "approved") return matchesSearch && record.status === "已审批";
      if (activeTab === "executing") return matchesSearch && record.status === "执行中";
      
      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      applyDate: new Date().toISOString().split('T')[0],
      status: "待审批",
      approvalStatus: "待审批",
      totalAmount: currentRecord.items.reduce((sum, item) => sum + item.quantity * item.estimatedPrice, 0)
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      type: "",
      title: "",
      applicant: "",
      department: "",
      applyDate: "",
      status: "",
      priority: "",
      budget: 0,
      items: [],
      description: "",
      approvalStatus: "",
      totalAmount: 0
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? {
        ...currentRecord,
        totalAmount: currentRecord.items.reduce((sum, item) => sum + item.quantity * item.estimatedPrice, 0)
      } : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      type: "",
      title: "",
      applicant: "",
      department: "",
      applyDate: "",
      status: "",
      priority: "",
      budget: 0,
      items: [],
      description: "",
      approvalStatus: "",
      totalAmount: 0
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      type: "",
      title: "",
      applicant: "",
      department: "",
      applyDate: "",
      status: "",
      priority: "",
      budget: 0,
      items: [],
      description: "",
      approvalStatus: "",
      totalAmount: 0
    })
    setIsDeleteDialogOpen(false)
  }

  // 审批记录
  const handleApproveRecord = (record: ProcurementRecord, approved: boolean) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? {
        ...r,
        status: approved ? "已审批" : "已驳回",
        approvalStatus: approved ? "已通过" : "已驳回",
        approvalDate: new Date().toISOString().split('T')[0],
        approvalComments: approved ? "同意采购" : "采购申请不符合要求"
      } : r
    )
    setRecords(updatedRecords)
  }

  // 打开编辑对话框
  const openEditDialog = (record: ProcurementRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: ProcurementRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待审批":
        return <Badge className="bg-yellow-500">待审批</Badge>
      case "已审批":
        return <Badge className="bg-green-500">已审批</Badge>
      case "执行中":
        return <Badge className="bg-blue-500">执行中</Badge>
      case "已完成":
        return <Badge className="bg-gray-500">已完成</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取优先级对应的徽章样式
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "高":
        return <Badge variant="destructive">高</Badge>
      case "中":
        return <Badge className="bg-yellow-500">中</Badge>
      case "低":
        return <Badge className="bg-green-500">低</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">采购管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增采购
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>新增采购</DialogTitle>
              <DialogDescription>
                请填写采购的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">采购类型</Label>
                  <Select
                    value={currentRecord.type}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择采购类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="采购申请">采购申请</SelectItem>
                      <SelectItem value="采购计划">采购计划</SelectItem>
                      <SelectItem value="采购订单">采购订单</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="title">标题</Label>
                  <Input
                    id="title"
                    value={currentRecord.title}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="applicant">申请人</Label>
                  <Input
                    id="applicant"
                    value={currentRecord.applicant}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, applicant: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">部门</Label>
                  <Input
                    id="department"
                    value={currentRecord.department}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">优先级</Label>
                  <Select
                    value={currentRecord.priority}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, priority: value })}
                  >
                    <SelectTrigger id="priority">
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="中">中</SelectItem>
                      <SelectItem value="低">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">预算金额</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={currentRecord.budget}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, budget: Number(e.target.value) })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>采购项目</Label>
                <div className="space-y-4">
                  {currentRecord.items.map((item, index) => (
                    <div key={index} className="grid grid-cols-6 gap-4 p-4 border rounded-lg">
                      <div className="col-span-2">
                        <Label>物资名称</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, name: e.target.value }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                      <div>
                        <Label>规格型号</Label>
                        <Input
                          value={item.specification}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, specification: e.target.value }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                      <div>
                        <Label>单位</Label>
                        <Input
                          value={item.unit}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, unit: e.target.value }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                      <div>
                        <Label>数量</Label>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, quantity: Number(e.target.value) }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                      <div>
                        <Label>预估单价</Label>
                        <Input
                          type="number"
                          value={item.estimatedPrice}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, estimatedPrice: Number(e.target.value) }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                      <div>
                        <Label>供应商</Label>
                        <Input
                          value={item.supplier}
                          onChange={(e) => {
                            const newItems = [...currentRecord.items]
                            newItems[index] = { ...item, supplier: e.target.value }
                            setCurrentRecord({ ...currentRecord, items: newItems })
                          }}
                        />
                      </div>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setCurrentRecord({
                        ...currentRecord,
                        items: [
                          ...currentRecord.items,
                          {
                            name: "",
                            specification: "",
                            unit: "",
                            quantity: 0,
                            estimatedPrice: 0,
                            supplier: ""
                          }
                        ]
                      })
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    添加项目
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">采购说明</Label>
                <Textarea
                  id="description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索采购记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="pending">待审批</TabsTrigger>
            <TabsTrigger value="approved">已审批</TabsTrigger>
            <TabsTrigger value="executing">执行中</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                {record.type === "采购申请" ? (
                  <FilePlus className="h-5 w-5 mr-2 text-blue-500" />
                ) : record.type === "采购计划" ? (
                  <FileText className="h-5 w-5 mr-2 text-green-500" />
                ) : (
                  <ClipboardList className="h-5 w-5 mr-2 text-purple-500" />
                )}
                <CardTitle className="text-sm font-medium">{record.title}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                {getPriorityBadge(record.priority)}
                {getStatusBadge(record.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    {record.applicant}
                  </div>
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                    {record.department}
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  申请日期: {record.applyDate}
                </div>
                <div className="flex items-center text-sm">
                  <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                  预算金额: {record.budget}元
                </div>
                <div className="flex items-center text-sm">
                  <Package className="h-4 w-4 mr-2 text-gray-500" />
                  采购项目: {record.items.length}项
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">采购说明:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.description}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {record.status === "待审批" && (
                    <>
                      <DropdownMenuItem onClick={() => handleApproveRecord(record, true)}>
                        <FileCheck className="h-4 w-4 mr-2" />
                        通过审批
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleApproveRecord(record, false)}>
                        <FileX className="h-4 w-4 mr-2" />
                        驳回申请
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>采购记录列表</CardTitle>
          <CardDescription>管理所有采购记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>类型</TableHead>
                <TableHead>标题</TableHead>
                <TableHead>申请人</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>申请日期</TableHead>
                <TableHead>预算金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.type}</TableCell>
                  <TableCell>{record.title}</TableCell>
                  <TableCell>{record.applicant}</TableCell>
                  <TableCell>{record.department}</TableCell>
                  <TableCell>{record.applyDate}</TableCell>
                  <TableCell>{record.budget}元</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getPriorityBadge(record.priority)}
                      {getStatusBadge(record.status)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {record.status === "待审批" && (
                      <>
                        <Button variant="ghost" size="sm" onClick={() => handleApproveRecord(record, true)}>
                          <FileCheck className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleApproveRecord(record, false)}>
                          <FileX className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>编辑采购记录</DialogTitle>
            <DialogDescription>
              修改采购的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">采购类型</Label>
                <Select
                  value={currentRecord.type}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择采购类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采购申请">采购申请</SelectItem>
                    <SelectItem value="采购计划">采购计划</SelectItem>
                    <SelectItem value="采购订单">采购订单</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-title">标题</Label>
                <Input
                  id="edit-title"
                  value={currentRecord.title}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-applicant">申请人</Label>
                <Input
                  id="edit-applicant"
                  value={currentRecord.applicant}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, applicant: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-department">部门</Label>
                <Input
                  id="edit-department"
                  value={currentRecord.department}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-priority">优先级</Label>
                <Select
                  value={currentRecord.priority}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, priority: value })}
                >
                  <SelectTrigger id="edit-priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-budget">预算金额</Label>
                <Input
                  id="edit-budget"
                  type="number"
                  value={currentRecord.budget}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, budget: Number(e.target.value) })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>采购项目</Label>
              <div className="space-y-4">
                {currentRecord.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-6 gap-4 p-4 border rounded-lg">
                    <div className="col-span-2">
                      <Label>物资名称</Label>
                      <Input
                        value={item.name}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, name: e.target.value }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                    <div>
                      <Label>规格型号</Label>
                      <Input
                        value={item.specification}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, specification: e.target.value }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                    <div>
                      <Label>单位</Label>
                      <Input
                        value={item.unit}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, unit: e.target.value }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                    <div>
                      <Label>数量</Label>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, quantity: Number(e.target.value) }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                    <div>
                      <Label>预估单价</Label>
                      <Input
                        type="number"
                        value={item.estimatedPrice}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, estimatedPrice: Number(e.target.value) }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                    <div>
                      <Label>供应商</Label>
                      <Input
                        value={item.supplier}
                        onChange={(e) => {
                          const newItems = [...currentRecord.items]
                          newItems[index] = { ...item, supplier: e.target.value }
                          setCurrentRecord({ ...currentRecord, items: newItems })
                        }}
                      />
                    </div>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setCurrentRecord({
                      ...currentRecord,
                      items: [
                        ...currentRecord.items,
                        {
                          name: "",
                          specification: "",
                          unit: "",
                          quantity: 0,
                          estimatedPrice: 0,
                          supplier: ""
                        }
                      ]
                    })
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加项目
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">采购说明</Label>
              <Textarea
                id="edit-description"
                value={currentRecord.description}
                onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.title}" 的采购记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
