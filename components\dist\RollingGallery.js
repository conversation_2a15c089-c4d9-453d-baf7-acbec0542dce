"use client";
"use strict";
exports.__esModule = true;
var react_1 = require("react");
var framer_motion_1 = require("framer-motion");
var RollingGallery = function (_a) {
    var _b = _a.autoplay, autoplay = _b === void 0 ? false : _b, _c = _a.pauseOnHover, pauseOnHover = _c === void 0 ? false : _c, _d = _a.images, images = _d === void 0 ? [] : _d;
    var _e = react_1.useState(false), isScreenSizeSm = _e[0], setIsScreenSizeSm = _e[1];
    // 在组件挂载时初始化屏幕尺寸状态
    react_1.useEffect(function () {
        setIsScreenSizeSm(window.innerWidth <= 640);
        var handleResize = function () { return setIsScreenSizeSm(window.innerWidth <= 640); };
        window.addEventListener("resize", handleResize);
        return function () { return window.removeEventListener("resize", handleResize); };
    }, []);
    // 调整圆柱体宽度以适应更大的图片和更大的半径
    var cylinderWidth = isScreenSizeSm ? 2000 : 3600; // 增加圆柱体宽度
    var faceCount = images.length;
    var faceWidth = (cylinderWidth / faceCount) * 1.5;
    var dragFactor = 0.05;
    var radius = cylinderWidth / (2 * Math.PI) * 1.2; // 增加半径系数
    var rotation = framer_motion_1.useMotionValue(0);
    var controls = framer_motion_1.useAnimation();
    var autoplayRef = react_1.useRef();
    var handleDrag = function (_, info) {
        rotation.set(rotation.get() + info.offset.x * dragFactor);
    };
    var handleDragEnd = function (_, info) {
        controls.start({
            rotateY: rotation.get() + info.velocity.x * dragFactor,
            transition: { type: "spring", stiffness: 60, damping: 20, mass: 0.1, ease: "easeOut" }
        });
    };
    var transform = framer_motion_1.useTransform(rotation, function (value) {
        return "rotate3d(0, 1, 0, " + value + "deg)";
    });
    react_1.useEffect(function () {
        if (autoplay) {
            autoplayRef.current = setInterval(function () {
                controls.start({
                    rotateY: rotation.get() - (360 / faceCount),
                    transition: { duration: 2, ease: "linear" }
                });
                rotation.set(rotation.get() - (360 / faceCount));
            }, 2000);
            return function () {
                if (autoplayRef.current) {
                    clearInterval(autoplayRef.current);
                }
            };
        }
    }, [autoplay, rotation, controls, faceCount]);
    var handleMouseEnter = function () {
        if (autoplay && pauseOnHover && autoplayRef.current) {
            clearInterval(autoplayRef.current);
            controls.stop();
        }
    };
    var handleMouseLeave = function () {
        if (autoplay && pauseOnHover) {
            controls.start({
                rotateY: rotation.get() - (360 / faceCount),
                transition: { duration: 2, ease: "linear" }
            });
            rotation.set(rotation.get() - (360 / faceCount));
            autoplayRef.current = setInterval(function () {
                controls.start({
                    rotateY: rotation.get() - (360 / faceCount),
                    transition: { duration: 2, ease: "linear" }
                });
                rotation.set(rotation.get() - (360 / faceCount));
            }, 2000);
        }
    };
    return (React.createElement("div", { className: "relative h-[450px] w-full overflow-hidden" },
        " ",
        React.createElement("div", { className: "flex h-full items-center justify-center perspective-[2000px] transform-style-preserve-3d" },
            " ",
            React.createElement(framer_motion_1.motion.div, { drag: "x", className: "flex h-auto min-h-[200px] w-full cursor-grab items-center justify-center transform-style-preserve-3d", onMouseEnter: handleMouseEnter, onMouseLeave: handleMouseLeave, style: {
                    transform: transform,
                    rotateY: rotation,
                    width: cylinderWidth,
                    transformStyle: "preserve-3d"
                }, onDrag: handleDrag, onDragEnd: handleDragEnd, animate: controls }, images.map(function (url, i) { return (React.createElement("div", { key: i, className: "absolute flex h-fit items-center justify-center p-[5%] backface-hidden", style: {
                    width: faceWidth + "px",
                    transform: "rotateY(" + i * (360 / faceCount) + "deg) translateZ(" + radius + "px)"
                } },
                React.createElement("img", { src: url, alt: "\u65BD\u5DE5\u73B0\u573A\u56FE\u7247 " + (i + 1), className: "pointer-events-none rounded-xl border-2 border-white/10 object-cover transition-transform duration-300 ease-in-out hover:scale-105 shadow-lg", style: {
                        width: isScreenSizeSm ? '400px' : '600px',
                        height: isScreenSizeSm ? '200px' : '300px',
                        backgroundColor: 'transparent'
                    } }))); })))));
};
exports["default"] = RollingGallery;
