"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { 
  RefreshCw, 
  Download, 
  Search, 
  FileText, 
  User, 
  Calendar, 
  Building2, 
  GraduationCap,
  Award,
  Briefcase,
  UserPlus,
  Edit,
  Trash2,
  Eye,
  Flag,
  Star,
  Users,
  BookO<PERSON>,
  Medal
} from "lucide-react"
import { cn } from "@/lib/utils"

interface CadreRecord {
  id: string
  name: string
  employeeId: string
  position: string
  department: string
  level: string
  appointmentDate: string
  politicalStatus: string
  education: string
  specialties: string
  status: string
}

interface CadreEvaluation {
  id: string
  cadreName: string
  employeeId: string
  evaluationYear: string
  evaluationPeriod: string
  performanceScore: number
  leadershipScore: number
  innovationScore: number
  teamworkScore: number
  overallRating: string
  evaluator: string
  comments: string
}

export function CadreManagement() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [levelFilter, setLevelFilter] = useState("all")
  const [politicalStatusFilter, setPoliticalStatusFilter] = useState("all")
  const [isAddCadreOpen, setIsAddCadreOpen] = useState(false)
  const [isEditCadreOpen, setIsEditCadreOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedCadre, setSelectedCadre] = useState<CadreRecord | null>(null)
  const [loading, setLoading] = useState(false)
  const [cadres, setCadres] = useState<CadreRecord[]>([
    {
      id: "CD001",
      name: "张三",
      employeeId: "EMP001",
      position: "采矿部主任",
      department: "采矿部",
      level: "中层",
      appointmentDate: "2020-05-15",
      politicalStatus: "党员",
      education: "本科",
      specialties: "采矿工程",
      status: "在职",
    },
    {
      id: "CD002",
      name: "李四",
      employeeId: "EMP045",
      position: "安全部副主任",
      department: "安全部",
      level: "中层",
      appointmentDate: "2019-08-20",
      politicalStatus: "党员",
      education: "硕士",
      specialties: "安全工程",
      status: "在职",
    },
    {
      id: "CD003",
      name: "王五",
      employeeId: "EMP078",
      position: "机电部主任",
      department: "机电部",
      level: "中层",
      appointmentDate: "2021-03-10",
      politicalStatus: "群众",
      education: "本科",
      specialties: "机械工程",
      status: "在职",
    },
    {
      id: "CD004",
      name: "赵六",
      employeeId: "EMP102",
      position: "总工程师",
      department: "技术部",
      level: "高层",
      appointmentDate: "2018-11-05",
      politicalStatus: "党员",
      education: "博士",
      specialties: "矿山工程",
      status: "在职",
    },
    {
      id: "CD005",
      name: "钱七",
      employeeId: "EMP156",
      position: "人力资源部主任",
      department: "人力资源部",
      level: "中层",
      appointmentDate: "2022-01-15",
      politicalStatus: "党员",
      education: "硕士",
      specialties: "人力资源管理",
      status: "在职",
    },
  ])

  const [evaluations, setEvaluations] = useState<CadreEvaluation[]>([
    {
      id: "EV001",
      cadreName: "张三",
      employeeId: "EMP001",
      evaluationYear: "2023",
      evaluationPeriod: "年度",
      performanceScore: 85,
      leadershipScore: 82,
      innovationScore: 78,
      teamworkScore: 88,
      overallRating: "优秀",
      evaluator: "矿长",
      comments: "工作认真负责，团队管理能力强，创新意识有待提高",
    },
    {
      id: "EV002",
      cadreName: "李四",
      employeeId: "EMP045",
      evaluationYear: "2023",
      evaluationPeriod: "年度",
      performanceScore: 90,
      leadershipScore: 85,
      innovationScore: 92,
      teamworkScore: 88,
      overallRating: "优秀",
      evaluator: "安全部主任",
      comments: "安全管理工作出色，创新能力强，是部门的中坚力量",
    },
    {
      id: "EV003",
      cadreName: "王五",
      employeeId: "EMP078",
      evaluationYear: "2023",
      evaluationPeriod: "年度",
      performanceScore: 75,
      leadershipScore: 72,
      innovationScore: 70,
      teamworkScore: 80,
      overallRating: "合格",
      evaluator: "矿长",
      comments: "基本完成工作任务，领导能力和创新意识需要加强",
    },
    {
      id: "EV004",
      cadreName: "赵六",
      employeeId: "EMP102",
      evaluationYear: "2023",
      evaluationPeriod: "年度",
      performanceScore: 95,
      leadershipScore: 92,
      innovationScore: 96,
      teamworkScore: 90,
      overallRating: "优秀",
      evaluator: "矿长",
      comments: "技术能力突出，创新成果显著，是企业的技术带头人",
    },
  ])

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "在职":
        return <Badge variant="default" className="bg-green-100 text-green-700">在职</Badge>
      case "离职":
        return <Badge variant="secondary" className="bg-gray-100 text-gray-700">离职</Badge>
      case "退休":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">退休</Badge>
      default:
        return <Badge variant="default">{status}</Badge>
    }
  }

  // 获取政治面貌对应的徽章样式
  const getPoliticalStatusBadge = (status: string) => {
    switch (status) {
      case "党员":
        return <Badge variant="default" className="bg-red-100 text-red-700">党员</Badge>
      case "预备党员":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700">预备党员</Badge>
      case "群众":
        return <Badge variant="secondary" className="bg-slate-100 text-slate-700">群众</Badge>
      default:
        return <Badge variant="default">{status}</Badge>
    }
  }

  // 获取评估等级对应的徽章样式
  const getEvaluationBadge = (rating: string) => {
    switch (rating) {
      case "优秀":
        return <Badge variant="default" className="bg-green-100 text-green-700">优秀</Badge>
      case "良好":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">良好</Badge>
      case "合格":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">合格</Badge>
      case "不合格":
        return <Badge variant="destructive" className="bg-red-100 text-red-700">不合格</Badge>
      default:
        return <Badge variant="default">{rating}</Badge>
    }
  }

  // 处理查看干部详情
  const handleViewCadreDetail = (cadre: CadreRecord) => {
    setSelectedCadre(cadre)
    setIsViewDetailOpen(true)
  }

  // 处理编辑干部
  const handleEditCadre = (cadre: CadreRecord) => {
    setSelectedCadre(cadre)
    setIsEditCadreOpen(true)
  }

  // 处理删除干部
  const handleDeleteCadre = (cadre: CadreRecord) => {
    setSelectedCadre(cadre)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除干部
  const confirmDeleteCadre = () => {
    if (selectedCadre) {
      setCadres(prev => prev.filter(c => c.id !== selectedCadre.id))
      toast({
        title: "删除成功",
        description: "已成功删除干部信息",
      })
      setIsDeleteDialogOpen(false)
      setSelectedCadre(null)
    }
  }

  // 处理添加干部
  const handleAddCadre = (formData: Partial<CadreRecord>) => {
    const newCadre: CadreRecord = {
      id: `CD${cadres.length + 1}`.padStart(5, '0'),
      name: formData.name || "",
      employeeId: formData.employeeId || "",
      position: formData.position || "",
      department: formData.department || "",
      level: formData.level || "",
      appointmentDate: formData.appointmentDate || "",
      politicalStatus: formData.politicalStatus || "",
      education: formData.education || "",
      specialties: formData.specialties || "",
      status: "在职"
    }
    
    setCadres(prev => [...prev, newCadre])
    toast({
      title: "添加成功",
      description: "已成功添加新的干部信息",
    })
    setIsAddCadreOpen(false)
  }

  // 处理更新干部信息
  const handleUpdateCadre = (formData: Partial<CadreRecord>) => {
    if (!selectedCadre) return

    setCadres(prev => prev.map(cadre => 
      cadre.id === selectedCadre.id 
        ? { ...cadre, ...formData }
        : cadre
    ))

    toast({
      title: "更新成功",
      description: "已成功更新干部信息",
    })
    setIsEditCadreOpen(false)
    setSelectedCadre(null)
  }

  // 处理刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "刷新成功",
        description: "数据已更新",
      })
    }, 1000)
  }

  // 处理导出数据
  const handleExport = () => {
    try {
      const headers = ["干部编号", "姓名", "员工编号", "职位", "部门", "级别", "任职日期", "政治面貌", "学历", "专业特长", "状态"]
      const rows = cadres.map(cadre => [
        cadre.id,
        cadre.name,
        cadre.employeeId,
        cadre.position,
        cadre.department,
        cadre.level,
        cadre.appointmentDate,
        cadre.politicalStatus,
        cadre.education,
        cadre.specialties,
        cadre.status
      ])
      
      const csvContent = [headers, ...rows].map(row => row.join(",")).join("\n")
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")
      const url = URL.createObjectURL(blob)
      
      link.setAttribute("href", url)
      link.setAttribute("download", `干部信息_${new Date().toLocaleDateString()}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      })
    } catch (error) {
      console.error("导出失败:", error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 筛选干部
  const filteredCadres = cadres.filter(cadre => {
    const matchesSearch = !searchTerm || 
      cadre.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cadre.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cadre.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cadre.department.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesDepartment = departmentFilter === "all" || cadre.department === departmentFilter
    const matchesLevel = levelFilter === "all" || cadre.level === levelFilter
    const matchesPoliticalStatus = politicalStatusFilter === "all" || cadre.politicalStatus === politicalStatusFilter
    
    return matchesSearch && matchesDepartment && matchesLevel && matchesPoliticalStatus
  })

  const filteredEvaluations = evaluations.filter(
    (evaluation) =>
      evaluation.cadreName.includes(searchTerm) ||
      evaluation.employeeId.includes(searchTerm) ||
      evaluation.overallRating.includes(searchTerm),
  )

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Users className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold">干部管理</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={handleRefresh} variant="outline" size="icon" disabled={loading}>
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          </Button>
          <Button onClick={handleExport} variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-50 p-3 mb-4">
              <Star className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-blue-600">{cadres.length}</h3>
            <p className="text-sm text-gray-600">干部总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-50 p-3 mb-4">
              <Flag className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-green-600">
              {cadres.filter(c => c.politicalStatus === "党员").length}
            </h3>
            <p className="text-sm text-gray-600">党员干部</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-50 p-3 mb-4">
              <Award className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-2xl font-bold text-yellow-600">
              {cadres.filter(c => c.level === "高层").length}
            </h3>
            <p className="text-sm text-gray-600">高层干部</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-purple-50 p-3 mb-4">
              <Medal className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-2xl font-bold text-purple-600">
              {cadres.filter(c => c.education === "博士" || c.education === "硕士").length}
            </h3>
            <p className="text-sm text-gray-600">高学历干部</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-wrap gap-4">
        <div className="flex items-center space-x-2">
          <Search className="w-4 h-4 text-gray-500" />
          <Input
            placeholder="搜索姓名、职位..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </div>

        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-[180px]">
            <Building2 className="w-4 h-4 mr-2" />
            <SelectValue placeholder="部门" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部部门</SelectItem>
            <SelectItem value="采矿部">采矿部</SelectItem>
            <SelectItem value="安全部">安全部</SelectItem>
            <SelectItem value="机电部">机电部</SelectItem>
            <SelectItem value="技术部">技术部</SelectItem>
            <SelectItem value="人力资源部">人力资源部</SelectItem>
          </SelectContent>
        </Select>

        <Select value={levelFilter} onValueChange={setLevelFilter}>
          <SelectTrigger className="w-[180px]">
            <Briefcase className="w-4 h-4 mr-2" />
            <SelectValue placeholder="级别" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部级别</SelectItem>
            <SelectItem value="高层">高层</SelectItem>
            <SelectItem value="中层">中层</SelectItem>
          </SelectContent>
        </Select>

        <Select value={politicalStatusFilter} onValueChange={setPoliticalStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Flag className="w-4 h-4 mr-2" />
            <SelectValue placeholder="政治面貌" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="党员">党员</SelectItem>
            <SelectItem value="群众">群众</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="cadres">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="cadres" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            干部信息
          </TabsTrigger>
          <TabsTrigger value="evaluations" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            干部考核
          </TabsTrigger>
        </TabsList>

        <TabsContent value="cadres" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <CardTitle>干部信息管理</CardTitle>
                </div>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>
                      <UserPlus className="h-4 w-4 mr-2" />
                      新增干部
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增干部信息</DialogTitle>
                      <DialogDescription>请填写干部基本信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">姓名</Label>
                          <Input id="name" placeholder="请输入姓名" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employeeId">员工编号</Label>
                          <Input id="employeeId" placeholder="请输入员工编号" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="position">职位</Label>
                          <Input id="position" placeholder="请输入职位" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="department">部门</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="选择部门" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="采矿部">采矿部</SelectItem>
                              <SelectItem value="安全部">安全部</SelectItem>
                              <SelectItem value="机电部">机电部</SelectItem>
                              <SelectItem value="技术部">技术部</SelectItem>
                              <SelectItem value="人力资源部">人力资源部</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="level">级别</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="选择级别" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="高层">高层</SelectItem>
                              <SelectItem value="中层">中层</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="appointmentDate">任职日期</Label>
                          <Input id="appointmentDate" type="date" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="politicalStatus">政治面貌</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="选择政治面貌" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="党员">党员</SelectItem>
                              <SelectItem value="群众">群众</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="education">学历</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="选择学历" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="博士">博士</SelectItem>
                              <SelectItem value="硕士">硕士</SelectItem>
                              <SelectItem value="本科">本科</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="specialties">专业特长</Label>
                        <Input id="specialties" placeholder="请输入专业特长" />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddCadreOpen(false)}>
                        取消
                      </Button>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理企业干部信息，包括基本信息、职位、任职时间等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>干部编号</TableHead>
                    <TableHead>姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>职位</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>级别</TableHead>
                    <TableHead>任职日期</TableHead>
                    <TableHead>政治面貌</TableHead>
                    <TableHead>学历</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCadres.map((cadre) => (
                    <TableRow key={cadre.id}>
                      <TableCell>{cadre.id}</TableCell>
                      <TableCell>{cadre.name}</TableCell>
                      <TableCell>{cadre.employeeId}</TableCell>
                      <TableCell>{cadre.position}</TableCell>
                      <TableCell>{cadre.department}</TableCell>
                      <TableCell>{cadre.level}</TableCell>
                      <TableCell>{cadre.appointmentDate}</TableCell>
                      <TableCell>{cadre.politicalStatus}</TableCell>
                      <TableCell>{cadre.education}</TableCell>
                      <TableCell>
                        {getStatusBadge(cadre.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" onClick={() => handleViewCadreDetail(cadre)}>
                            <Eye className="h-4 w-4 mr-2" />
                            查看
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEditCadre(cadre)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteCadre(cadre)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="evaluations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>干部考核管理</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>新增考核</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增干部考核</DialogTitle>
                      <DialogDescription>请填写干部考核信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="cadreName">干部姓名</Label>
                          <Input id="cadreName" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employeeId">员工编号</Label>
                          <Input id="employeeId" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="evaluationYear">考核年度</Label>
                          <Input id="evaluationYear" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="evaluationPeriod">考核周期</Label>
                          <Input id="evaluationPeriod" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="performanceScore">业绩得分</Label>
                          <Input id="performanceScore" type="number" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="leadershipScore">领导力得分</Label>
                          <Input id="leadershipScore" type="number" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="innovationScore">创新得分</Label>
                          <Input id="innovationScore" type="number" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="teamworkScore">团队协作得分</Label>
                          <Input id="teamworkScore" type="number" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="overallRating">综合评级</Label>
                          <Input id="overallRating" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="evaluator">评价人</Label>
                          <Input id="evaluator" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="comments">评价意见</Label>
                        <Input id="comments" />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理干部考核信息，包括业绩考核、能力评估等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>考核编号</TableHead>
                    <TableHead>干部姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>考核年度</TableHead>
                    <TableHead>考核周期</TableHead>
                    <TableHead>业绩得分</TableHead>
                    <TableHead>领导力得分</TableHead>
                    <TableHead>创新得分</TableHead>
                    <TableHead>团队协作得分</TableHead>
                    <TableHead>综合评级</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvaluations.map((evaluation) => (
                    <TableRow key={evaluation.id}>
                      <TableCell>{evaluation.id}</TableCell>
                      <TableCell>{evaluation.cadreName}</TableCell>
                      <TableCell>{evaluation.employeeId}</TableCell>
                      <TableCell>{evaluation.evaluationYear}</TableCell>
                      <TableCell>{evaluation.evaluationPeriod}</TableCell>
                      <TableCell>{evaluation.performanceScore}</TableCell>
                      <TableCell>{evaluation.leadershipScore}</TableCell>
                      <TableCell>{evaluation.innovationScore}</TableCell>
                      <TableCell>{evaluation.teamworkScore}</TableCell>
                      <TableCell>
                        {getEvaluationBadge(evaluation.overallRating)}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            查看
                          </Button>
                          <Button variant="outline" size="sm">
                            编辑
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>干部详细信息</DialogTitle>
          </DialogHeader>
          {selectedCadre && (
            <div className="space-y-6">
              <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                <div className="rounded-full bg-blue-100 p-3">
                  <User className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedCadre.name}</h3>
                  <p className="text-sm text-muted-foreground">员工编号：{selectedCadre.employeeId}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>职位</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCadre.position}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>部门</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCadre.department}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>级别</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant={selectedCadre.level === "高层" ? "default" : "secondary"}>
                      {selectedCadre.level}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>任职日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCadre.appointmentDate}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>政治面貌</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {getPoliticalStatusBadge(selectedCadre.politicalStatus)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>学历</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <span>{selectedCadre.education}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>专业特长</Label>
                <div className="p-4 bg-slate-50 rounded-lg">
                  <p>{selectedCadre.specialties}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsViewDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewDetailOpen(false)
              if (selectedCadre) {
                handleEditCadre(selectedCadre)
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditCadreOpen} onOpenChange={setIsEditCadreOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑干部信息</DialogTitle>
            <DialogDescription>修改干部基本信息</DialogDescription>
          </DialogHeader>
          {selectedCadre && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">姓名</Label>
                  <Input
                    id="edit-name"
                    defaultValue={selectedCadre.name}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          name: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-employeeId">员工编号</Label>
                  <Input
                    id="edit-employeeId"
                    defaultValue={selectedCadre.employeeId}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          employeeId: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-position">职位</Label>
                  <Input
                    id="edit-position"
                    defaultValue={selectedCadre.position}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          position: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-department">部门</Label>
                  <Input
                    id="edit-department"
                    defaultValue={selectedCadre.department}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          department: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-level">级别</Label>
                  <Input
                    id="edit-level"
                    defaultValue={selectedCadre.level}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          level: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-appointmentDate">任职日期</Label>
                  <Input
                    id="edit-appointmentDate"
                    defaultValue={selectedCadre.appointmentDate}
                    type="date"
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          appointmentDate: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-politicalStatus">政治面貌</Label>
                  <Input
                    id="edit-politicalStatus"
                    defaultValue={selectedCadre.politicalStatus}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          politicalStatus: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-education">学历</Label>
                  <Input
                    id="edit-education"
                    defaultValue={selectedCadre.education}
                    onChange={(e) => {
                      if (selectedCadre) {
                        setSelectedCadre({
                          ...selectedCadre,
                          education: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-specialties">专业特长</Label>
                <Input
                  id="edit-specialties"
                  defaultValue={selectedCadre.specialties}
                  onChange={(e) => {
                    if (selectedCadre) {
                      setSelectedCadre({
                        ...selectedCadre,
                        specialties: e.target.value
                      })
                    }
                  }}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditCadreOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (selectedCadre) {
                handleUpdateCadre(selectedCadre)
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除该干部信息吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCadre}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

