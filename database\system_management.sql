-- 系统管理模块数据库初始化脚本
-- 创建于2025年1月1日
-- 包含用户、角色、权限等系统管理相关数据

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL,
  `real_name` VARCHAR(50) NOT NULL,
  `email` VARCHAR(100),
  `phone` VARCHAR(20),
  `department` VARCHAR(50),
  `position` VARCHAR(50),
  `role_id` INT NOT NULL,
  `status` ENUM('active', 'inactive', 'locked') DEFAULT 'active',
  `avatar` VARCHAR(255),
  `last_login` DATETIME,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE,
  `description` VARCHAR(255),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE,
  `code` VARCHAR(50) NOT NULL UNIQUE,
  `description` VARCHAR(255),
  `module` VARCHAR(50),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `role_id` INT NOT NULL,
  `permission_id` INT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `role_permission_unique` (`role_id`, `permission_id`)
);

-- 创建系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `setting_key` VARCHAR(50) NOT NULL UNIQUE,
  `setting_value` TEXT,
  `description` VARCHAR(255),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT,
  `username` VARCHAR(50),
  `module` VARCHAR(50),
  `operation` VARCHAR(50),
  `description` TEXT,
  `ip_address` VARCHAR(50),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建公共文件柜表
CREATE TABLE IF NOT EXISTS `public_files` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `folder_id` INT,
  `name` VARCHAR(255) NOT NULL,
  `file_type` VARCHAR(50),
  `file_size` INT,
  `file_path` VARCHAR(255),
  `owner_id` INT,
  `owner_name` VARCHAR(50),
  `shared` BOOLEAN DEFAULT FALSE,
  `downloads` INT DEFAULT 0,
  `status` VARCHAR(20) DEFAULT '正常',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建文件夹表
CREATE TABLE IF NOT EXISTS `file_folders` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `parent_id` INT DEFAULT NULL,
  `owner_id` INT,
  `owner_name` VARCHAR(50),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入角色数据
INSERT INTO `roles` (`name`, `description`) VALUES
('超级管理员', '系统最高权限，可以管理所有功能'),
('系统管理员', '管理系统用户、角色和权限'),
('安全管理员', '负责安全管理相关功能'),
('工程管理员', '负责工程管理相关功能'),
('人事管理员', '负责人事管理相关功能'),
('财务管理员', '负责财务管理相关功能'),
('普通用户', '基本功能访问权限');

-- 插入用户数据
INSERT INTO `users` (`username`, `password`, `real_name`, `email`, `phone`, `department`, `position`, `role_id`, `status`, `last_login`) VALUES
('admin', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '系统管理员', '<EMAIL>', '13800138000', '信息技术部', '系统管理员', 1, 'active', '2025-03-15 09:30:00'),
('zhangsan', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '张三', '<EMAIL>', '13800138001', '安全管理部', '安全主管', 3, 'active', '2025-03-14 14:20:00'),
('lisi', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '李四', '<EMAIL>', '13800138002', '工程部', '工程主管', 4, 'active', '2025-03-12 11:45:00'),
('wangwu', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '王五', '<EMAIL>', '13800138003', '人事部', '人事主管', 5, 'active', '2025-03-10 16:30:00'),
('zhaoliu', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '赵六', '<EMAIL>', '13800138004', '财务部', '财务主管', 6, 'active', '2025-03-08 10:15:00'),
('qianqi', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '钱七', '<EMAIL>', '13800138005', '生产部', '生产主管', 7, 'active', '2025-03-05 09:00:00'),
('sunba', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '孙八', '<EMAIL>', '13800138006', '质量部', '质量主管', 7, 'active', '2025-03-01 13:45:00'),
('zhoujiu', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '周九', '<EMAIL>', '13800138007', '设备部', '设备主管', 7, 'active', '2025-02-28 15:20:00'),
('wushi', '$2a$10$NxUfJUh4wVmjeJKYvF/YLOcYiSs7.8yH9.nxRBCJvjxQokjzCp/Oi', '吴十', '<EMAIL>', '13800138008', '后勤部', '后勤主管', 7, 'active', '2025-02-25 11:10:00');

-- 插入权限数据
INSERT INTO `permissions` (`name`, `code`, `description`, `module`) VALUES
('用户管理-查看', 'user:view', '查看用户列表和详情', '系统管理'),
('用户管理-添加', 'user:add', '添加新用户', '系统管理'),
('用户管理-编辑', 'user:edit', '编辑用户信息', '系统管理'),
('用户管理-删除', 'user:delete', '删除用户', '系统管理'),
('角色管理-查看', 'role:view', '查看角色列表和详情', '系统管理'),
('角色管理-添加', 'role:add', '添加新角色', '系统管理'),
('角色管理-编辑', 'role:edit', '编辑角色信息', '系统管理'),
('角色管理-删除', 'role:delete', '删除角色', '系统管理'),
('权限管理-查看', 'permission:view', '查看权限列表和详情', '系统管理'),
('权限管理-分配', 'permission:assign', '分配权限给角色', '系统管理'),
('系统设置-查看', 'setting:view', '查看系统设置', '系统管理'),
('系统设置-编辑', 'setting:edit', '编辑系统设置', '系统管理'),
('日志管理-查看', 'log:view', '查看操作日志', '系统管理'),
('文件管理-查看', 'file:view', '查看文件列表', '系统管理'),
('文件管理-上传', 'file:upload', '上传文件', '系统管理'),
('文件管理-下载', 'file:download', '下载文件', '系统管理'),
('文件管理-删除', 'file:delete', '删除文件', '系统管理');

-- 插入角色权限关联数据
-- 超级管理员拥有所有权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), (1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17);

-- 系统管理员拥有系统管理相关权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(2, 1), (2, 2), (2, 3), (2, 4), (2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10), (2, 11), (2, 12), (2, 13), (2, 14), (2, 15), (2, 16), (2, 17);

-- 其他角色拥有查看权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(3, 1), (3, 5), (3, 9), (3, 11), (3, 13), (3, 14), (3, 16),
(4, 1), (4, 5), (4, 9), (4, 11), (4, 13), (4, 14), (4, 16),
(5, 1), (5, 5), (5, 9), (5, 11), (5, 13), (5, 14), (5, 16),
(6, 1), (6, 5), (6, 9), (6, 11), (6, 13), (6, 14), (6, 16),
(7, 1), (7, 5), (7, 9), (7, 11), (7, 14), (7, 16);

-- 插入系统设置数据
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('system_name', '2025 矿业公司综合管理系统', '系统名称'),
('company_name', '矿业公司', '公司名称'),
('logo_path', '/images/logo.png', '系统Logo路径'),
('theme_color', 'blue', '系统主题颜色'),
('allow_register', 'false', '是否允许自助注册'),
('password_policy', 'medium', '密码策略强度'),
('session_timeout', '30', '会话超时时间(分钟)'),
('backup_path', '/backup', '系统备份路径'),
('backup_schedule', 'daily', '系统备份频率'),
('mail_server', 'smtp.example.com', '邮件服务器地址'),
('mail_port', '587', '邮件服务器端口'),
('mail_username', '<EMAIL>', '邮件服务器用户名'),
('mail_password', 'encrypted_password', '邮件服务器密码(加密)'),
('mail_from', '<EMAIL>', '系统发件人地址');

-- 插入操作日志示例数据
INSERT INTO `operation_logs` (`user_id`, `username`, `module`, `operation`, `description`, `ip_address`, `created_at`) VALUES
(1, 'admin', '系统管理', '登录', '用户登录系统', '*************', '2025-03-15 09:30:00'),
(1, 'admin', '系统管理', '添加用户', '添加用户: test_user', '*************', '2025-03-15 10:15:00'),
(1, 'admin', '系统管理', '修改设置', '修改系统设置: 会话超时时间', '*************', '2025-03-15 11:20:00'),
(2, 'zhangsan', '系统管理', '登录', '用户登录系统', '*************', '2025-03-14 14:20:00'),
(2, 'zhangsan', '安全管理', '添加记录', '添加安全检查记录', '*************', '2025-03-14 15:30:00'),
(3, 'lisi', '系统管理', '登录', '用户登录系统', '*************', '2025-03-12 11:45:00'),
(3, 'lisi', '工程管理', '更新进度', '更新工程进度: 项目A', '*************', '2025-03-12 13:10:00'),
(4, 'wangwu', '系统管理', '登录', '用户登录系统', '*************', '2025-03-10 16:30:00'),
(4, 'wangwu', '人事管理', '添加员工', '添加新员工: 张小明', '*************', '2025-03-10 17:00:00'),
(5, 'zhaoliu', '系统管理', '登录', '用户登录系统', '192.168.1.104', '2025-03-08 10:15:00'),
(5, 'zhaoliu', '财务管理', '添加记录', '添加财务记录: 2025年3月办公费用', '192.168.1.104', '2025-03-08 11:30:00');

-- 插入文件夹数据
INSERT INTO `file_folders` (`name`, `parent_id`, `owner_id`, `owner_name`) VALUES
('安全管理文档', NULL, 2, '张三'),
('人事管理文件', NULL, 4, '王五'),
('工程项目资料', NULL, 3, '李四'),
('财务报表', NULL, 5, '赵六'),
('规章制度', NULL, 1, '系统管理员'),
('培训资料', NULL, 4, '王五'),
('会议记录', NULL, 1, '系统管理员'),
('技术文档', NULL, 3, '李四');

-- 插入文件数据
INSERT INTO `public_files` (`folder_id`, `name`, `file_type`, `file_size`, `file_path`, `owner_id`, `owner_name`, `shared`, `downloads`, `status`) VALUES
(1, '2025年安全生产责任制.docx', 'docx', 2500, '/files/safety/responsibility.docx', 2, '张三', TRUE, 15, '正常'),
(1, '安全检查表格模板.xlsx', 'xlsx', 1800, '/files/safety/check_template.xlsx', 2, '张三', TRUE, 28, '正常'),
(1, '应急预案管理制度.pdf', 'pdf', 3200, '/files/safety/emergency_plan.pdf', 2, '张三', TRUE, 12, '正常'),
(2, '员工手册2025版.pdf', 'pdf', 4500, '/files/hr/employee_handbook.pdf', 4, '王五', TRUE, 45, '正常'),
(2, '绩效考核标准.docx', 'docx', 1900, '/files/hr/performance.docx', 4, '王五', TRUE, 32, '正常'),
(3, 'A区采矿工程设计图.dwg', 'dwg', 8500, '/files/project/design_a.dwg', 3, '李四', FALSE, 8, '正常'),
(3, 'B区基础设施建设计划.pdf', 'pdf', 5200, '/files/project/plan_b.pdf', 3, '李四', TRUE, 17, '正常'),
(4, '2025年Q1财务报表.xlsx', 'xlsx', 2800, '/files/finance/q1_report.xlsx', 5, '赵六', FALSE, 6, '正常'),
(5, '公司管理制度汇编.pdf', 'pdf', 7500, '/files/rules/compilation.pdf', 1, '系统管理员', TRUE, 56, '正常'),
(6, '新员工入职培训.pptx', 'pptx', 6800, '/files/training/new_employee.pptx', 4, '王五', TRUE, 38, '正常'),
(7, '2025年3月管理层会议纪要.docx', 'docx', 1600, '/files/meeting/march_management.docx', 1, '系统管理员', FALSE, 12, '正常'),
(8, '设备维护手册.pdf', 'pdf', 9200, '/files/technical/maintenance.pdf', 3, '李四', TRUE, 25, '正常');
