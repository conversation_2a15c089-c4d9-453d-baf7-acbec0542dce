"use client"

import { useState } from "react"
import { Search, Plus, Edit, Trash2, Download, MoreHorizontal, FileText, Star, BarChart2, Filter, Settings2, UserCheck, AlertCircle, UserPlus, AlertTriangle, LineChart, PieChart, TrendingUp, Calendar, ClipboardCheck, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart as RechartsPieChart, Pie, Cell } from 'recharts'
import * as XLSX from 'xlsx'

// 考核记录类型定义
interface Assessment {
  id: string
  employeeName: string
  employeeId: string
  department: string
  position: string
  assessmentPeriod: string
  performanceScore: number
  rating: string
  assessor: string
  status: string
  comments: string
  improvementPlan: string
  createdAt: string
  updatedAt: string
}

// 表单数据类型
interface AssessmentFormData {
  employeeName: string
  employeeId: string
  department: string
  position: string
  assessmentPeriod: string
  performanceScore: number
  rating: string
  assessor: string
  comments: string
  improvementPlan: string
}

export function PersonnelAssessmentEnhanced() {
  const { toast } = useToast()
  const [isAddAssessmentOpen, setIsAddAssessmentOpen] = useState(false)
  const [isEditAssessmentOpen, setIsEditAssessmentOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [periodFilter, setPeriodFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)
  const [assessments, setAssessments] = useState<Assessment[]>([
    {
      id: "1",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "安全管理部",
      position: "安全员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 85,
      rating: "优秀",
      assessor: "李经理",
      status: "已完成",
      comments: "工作认真负责，善于团队协作",
      improvementPlan: "建议加强专业知识学习，提升管理能力",
      createdAt: "2023-01-15",
      updatedAt: "2023-03-20"
    },
    {
      id: "2",
      employeeName: "李四",
      employeeId: "EMP002",
      department: "工程管理部",
      position: "工程师",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 92,
      rating: "卓越",
      assessor: "王主管",
      status: "已完成",
      comments: "技术能力突出，项目管理有序",
      improvementPlan: "可以进一步提升团队协作能力",
      createdAt: "2023-01-16",
      updatedAt: "2023-03-21"
    },
    {
      id: "3",
      employeeName: "王五",
      employeeId: "EMP003",
      department: "人事管理部",
      position: "人事专员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 78,
      rating: "良好",
      assessor: "赵经理",
      status: "已完成",
      comments: "工作态度积极，执行力强",
      improvementPlan: "需要提高专业知识储备",
      createdAt: "2023-01-17",
      updatedAt: "2023-03-22"
    },
    {
      id: "4",
      employeeName: "赵六",
      employeeId: "EMP004",
      department: "财务管理部",
      position: "财务主管",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 65,
      rating: "一般",
      assessor: "钱总监",
      status: "已完成",
      comments: "基本完成工作任务，但创新性不足",
      improvementPlan: "建议参加进阶培训，提升专业水平",
      createdAt: "2023-01-18",
      updatedAt: "2023-03-23"
    },
    {
      id: "5",
      employeeName: "钱七",
      employeeId: "EMP005",
      department: "工程管理部",
      position: "技术员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 0,
      rating: "-",
      assessor: "孙经理",
      status: "进行中",
      comments: "",
      improvementPlan: "",
      createdAt: "2023-01-19",
      updatedAt: "2023-03-24"
    },
    {
      id: "6",
      employeeName: "孙八",
      employeeId: "EMP006",
      department: "安全管理部",
      position: "安全主管",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 88,
      rating: "优秀",
      assessor: "李经理",
      status: "已完成",
      comments: "安全意识强，管理有方",
      improvementPlan: "可以考虑承担更多管理职责",
      createdAt: "2023-01-20",
      updatedAt: "2023-03-25"
    },
    {
      id: "7",
      employeeName: "周九",
      employeeId: "EMP007",
      department: "人事管理部",
      position: "培训主管",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 90,
      rating: "卓越",
      assessor: "赵经理",
      status: "已完成",
      comments: "培训效果显著，员工反馈良好",
      improvementPlan: "建议开发更多培训课程",
      createdAt: "2023-01-21",
      updatedAt: "2023-03-26"
    },
    {
      id: "8",
      employeeName: "吴十",
      employeeId: "EMP008",
      department: "工程管理部",
      position: "项目经理",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 86,
      rating: "优秀",
      assessor: "王主管",
      status: "已完成",
      comments: "项目进度把控准确，质量管理到位",
      improvementPlan: "可以加强成本控制意识",
      createdAt: "2023-01-22",
      updatedAt: "2023-03-27"
    }
  ])

  // 计算图表数据
  const calculateChartData = () => {
    // 计算评级分布
    const ratingCounts = assessments.reduce((acc, curr) => {
      if (curr.rating !== "-") {
        acc[curr.rating] = (acc[curr.rating] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const ratingColors = {
      "卓越": "#facc15",
      "优秀": "#22c55e",
      "良好": "#3b82f6",
      "一般": "#6b7280"
    }

    const ratingDistribution = Object.entries(ratingCounts).map(([name, value]) => ({
      name,
      value,
      color: ratingColors[name as keyof typeof ratingColors]
    }))

    // 计算部门平均分
    const departmentScores = assessments.reduce((acc, curr) => {
      if (curr.status === "已完成") {
        if (!acc[curr.department]) {
          acc[curr.department] = { total: 0, count: 0 }
        }
        acc[curr.department].total += curr.performanceScore
        acc[curr.department].count += 1
      }
      return acc
    }, {} as Record<string, { total: number; count: number }>)

    const performanceByDepartment = Object.entries(departmentScores).map(([dept, data]) => ({
      department: dept,
      score: Math.round(data.total / data.count)
    }))

    return {
      ratingDistribution,
      performanceByDepartment
    }
  }

  const chartData = calculateChartData()

  // 筛选后的考核数据
  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = !searchTerm || 
      assessment.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.employeeId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment = departmentFilter === "all" || assessment.department === departmentFilter;
    const matchesPeriod = periodFilter === "all" || assessment.assessmentPeriod === periodFilter;
    const matchesStatus = statusFilter === "all" || assessment.status === statusFilter;

    return matchesSearch && matchesDepartment && matchesPeriod && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredAssessments.length / pageSize)
  const paginatedAssessments = filteredAssessments.slice(
    (page - 1) * pageSize,
    page * pageSize
  )

  // 处理查看详情
  const handleViewDetail = (assessment: any) => {
    setSelectedAssessment(assessment)
    setIsViewDetailOpen(true)
  }

  // 处理编辑考核
  const handleEditAssessment = (assessment: any) => {
    setSelectedAssessment(assessment)
    setIsEditAssessmentOpen(true)
  }

  // 处理删除考核
  const handleDeleteAssessment = (assessment: any) => {
    setSelectedAssessment(assessment)
    setIsDeleteDialogOpen(true)
  }

  // 处理删除
  const handleDelete = (assessment: Assessment) => {
    setSelectedAssessment(assessment)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const handleConfirmDelete = () => {
    if (selectedAssessment) {
      setAssessments(prev => prev.filter(a => a.id !== selectedAssessment.id))
      toast({
        title: "删除成功",
        description: "已成功删除考核记录",
      })
      setIsDeleteDialogOpen(false)
      setSelectedAssessment(null)
    }
  }

  // 更新考核记录
  const updateAssessment = (formData: any) => {
    // 在实际应用中，这里应该调用API更新数据
    toast({
      title: "更新成功",
      description: "已成功更新考核记录",
    })
    setIsEditAssessmentOpen(false)
    setSelectedAssessment(null)
  }

  // 处理导出
  const handleExport = () => {
    try {
      const exportData = assessments.map(assessment => ({
        '员工姓名': assessment.employeeName,
        '员工编号': assessment.employeeId,
        '部门': assessment.department,
        '职位': assessment.position,
        '考核周期': assessment.assessmentPeriod,
        '绩效得分': assessment.performanceScore,
        '评级': assessment.rating,
        '考核人': assessment.assessor,
        '状态': assessment.status,
        '评语': assessment.comments,
        '改进计划': assessment.improvementPlan,
        '创建时间': assessment.createdAt,
        '更新时间': assessment.updatedAt
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 10 }, // 员工姓名
        { wch: 10 }, // 员工编号
        { wch: 12 }, // 部门
        { wch: 10 }, // 职位
        { wch: 15 }, // 考核周期
        { wch: 10 }, // 绩效得分
        { wch: 8 },  // 评级
        { wch: 10 }, // 考核人
        { wch: 8 },  // 状态
        { wch: 30 }, // 评语
        { wch: 30 }, // 改进计划
        { wch: 20 }, // 创建时间
        { wch: 20 }  // 更新时间
      ]
      ws['!cols'] = colWidths

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      }

      // 为表头添加样式
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1"
        if (!ws[address]) continue
        ws[address].s = headerStyle
      }

      XLSX.utils.book_append_sheet(wb, ws, '考核记录')
      XLSX.writeFile(wb, `考核记录_${new Date().toLocaleDateString()}.xlsx`)
      
      toast({
        title: "导出成功",
        description: `已导出 ${assessments.length} 条记录`,
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 处理导入
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 转换导入的数据为考核记录格式
        const importedAssessments: Assessment[] = jsonData.map((row: any) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          employeeName: row['员工姓名'] || '',
          employeeId: row['员工编号'] || '',
          department: row['部门'] || '',
          position: row['职位'] || '',
          assessmentPeriod: row['考核周期'] || '',
          performanceScore: Number(row['绩效得分']) || 0,
          rating: row['评级'] || '-',
          assessor: row['考核人'] || '',
          status: row['状态'] || '进行中',
          comments: row['评语'] || '',
          improvementPlan: row['改进计划'] || '',
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0]
        }))

        setAssessments(prev => [...prev, ...importedAssessments])
        
        toast({
          title: "导入成功",
          description: `已导入 ${importedAssessments.length} 条记录`,
        })
      } catch (error) {
        console.error('导入失败:', error)
        toast({
          title: "导入失败",
          description: "请检查文件格式是否正确",
          variant: "destructive"
        })
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 处理添加考核
  const handleAddAssessment = (formData: AssessmentFormData) => {
    const newAssessment: Assessment = {
      id: Date.now().toString(),
      ...formData,
      status: "进行中",
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    }

    setAssessments(prev => [...prev, newAssessment])
    toast({
      title: "添加成功",
      description: "已成功添加新的考核记录",
    })
    setIsAddAssessmentOpen(false)
  }

  // 处理更新考核
  const handleUpdateAssessment = (formData: Partial<AssessmentFormData>) => {
    if (!selectedAssessment) return

    setAssessments(prev => prev.map(assessment => 
      assessment.id === selectedAssessment.id
        ? {
            ...assessment,
            ...formData,
            updatedAt: new Date().toISOString().split('T')[0]
          }
        : assessment
    ))

    toast({
      title: "更新成功",
      description: "已成功更新考核记录",
    })
    setIsEditAssessmentOpen(false)
    setSelectedAssessment(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">人事考核</h2>
        <div className="flex items-center gap-2">
          <input
            type="file"
            id="import-file"
            className="hidden"
            accept=".xlsx,.xls"
            onChange={handleImport}
          />
          <Button 
            variant="outline" 
            size="sm" 
            className="hover:bg-green-50 hover:text-green-600"
            onClick={() => document.getElementById('import-file')?.click()}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            导入
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="hover:bg-blue-50 hover:text-blue-600"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="hover:bg-amber-50 hover:text-amber-600"
          >
            <LineChart className="h-4 w-4 mr-2" />
            生成报告
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="col-span-2">
          <CardHeader>
            <CardTitle className="text-lg font-medium">部门绩效</CardTitle>
            <CardDescription>各部门平均绩效得分</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <RechartsLineChart
                width={600}
                height={300}
                data={chartData.performanceByDepartment}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="department" />
                <YAxis domain={[60, 100]} />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="score"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6' }}
                />
              </RechartsLineChart>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-medium">评级分布</CardTitle>
            <CardDescription>当前考核评级分布情况</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full flex items-center justify-center">
              <RechartsPieChart width={250} height={250}>
                <Pie
                  data={chartData.ratingDistribution}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {chartData.ratingDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </RechartsPieChart>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              本季度考核完成率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-blue-100 p-3">
                <UserCheck className="h-6 w-6 text-blue-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round((assessments.filter(a => a.status === "已完成").length / assessments.length) * 100)}%
                </div>
                <Progress 
                  value={Math.round((assessments.filter(a => a.status === "已完成").length / assessments.length) * 100)} 
                  className="h-2 w-[60px]" 
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              平均绩效分
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-green-100 p-3">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(assessments.reduce((acc, curr) => acc + (curr.status === "已完成" ? curr.performanceScore : 0), 0) / 
                    assessments.filter(a => a.status === "已完成").length * 10) / 10}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              优秀率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-yellow-100 p-3">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-yellow-600">
                  {Math.round((assessments.filter(a => a.rating === "卓越" || a.rating === "优秀").length / 
                    assessments.filter(a => a.status === "已完成").length) * 100)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              待考核人数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-orange-100 p-3">
                <AlertCircle className="h-6 w-6 text-orange-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-orange-600">
                  {assessments.filter(a => a.status === "进行中").length}
                </div>
                <p className="text-xs text-muted-foreground">
                  剩余 {Math.round((assessments.filter(a => a.status === "进行中").length / assessments.length) * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader>
          <CardTitle>考核记录</CardTitle>
          <CardDescription>管理员工的绩效考核记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    type="search" 
                    placeholder="搜索员工..." 
                    className="pl-8 w-[250px] border-slate-200 focus-visible:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="工程管理部">工程管理部</SelectItem>
                    <SelectItem value="人事管理部">人事管理部</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={periodFilter} onValueChange={setPeriodFilter}>
                  <SelectTrigger className="w-[180px] border-slate-200">
                    <SelectValue placeholder="考核周期" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有周期</SelectItem>
                    <SelectItem value="2023Q1">2023年第一季度</SelectItem>
                    <SelectItem value="2022Q4">2022年第四季度</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={() => setIsAddAssessmentOpen(true)} 
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加考核
              </Button>
            </div>

            <div className="rounded-md border border-slate-200">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>员工姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>职位</TableHead>
                    <TableHead>考核周期</TableHead>
                    <TableHead>绩效得分</TableHead>
                    <TableHead>评级</TableHead>
                    <TableHead>考核人</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assessments.map((assessment) => (
                    <TableRow key={assessment.id} className="hover:bg-slate-50">
                      <TableCell>
                        <div className="font-medium">{assessment.employeeName}</div>
                      </TableCell>
                      <TableCell>{assessment.employeeId}</TableCell>
                      <TableCell>{assessment.department}</TableCell>
                      <TableCell>{assessment.position}</TableCell>
                      <TableCell>{assessment.assessmentPeriod}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="mr-2">{assessment.performanceScore}</span>
                          <Progress value={assessment.performanceScore} className="h-2 w-16" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star className={`h-4 w-4 mr-1 ${
                            assessment.rating === "卓越"
                              ? "text-yellow-500 fill-yellow-500"
                              : assessment.rating === "优秀"
                                ? "text-green-500 fill-green-500"
                                : "text-blue-500 fill-blue-500"
                          }`} />
                          {assessment.rating}
                        </div>
                      </TableCell>
                      <TableCell>{assessment.assessor}</TableCell>
                      <TableCell>
                        <Badge
                          variant={assessment.status === "已完成" ? "default" : "secondary"}
                          className={
                            assessment.status === "已完成"
                              ? "bg-green-100 text-green-700"
                              : "bg-orange-100 text-orange-700"
                          }
                        >
                          {assessment.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetail(assessment)}
                            className="hover:bg-blue-50 hover:text-blue-600"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAssessment(assessment)}
                            className="hover:bg-amber-50 hover:text-amber-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAssessment(assessment)}
                            className="hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-500" />
              考核详情
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[500px] w-full rounded-md border p-4">
            {selectedAssessment && (
              <div className="space-y-6">
                <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                  <div className="rounded-full bg-blue-100 p-3">
                    <UserCheck className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedAssessment.employeeName}</h3>
                    <p className="text-sm text-muted-foreground">{selectedAssessment.department} - {selectedAssessment.position}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>考核周期</Label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAssessment.assessmentPeriod}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>考核人</Label>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAssessment.assessor}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>绩效得分</Label>
                  <div className="p-4 bg-slate-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl font-bold text-blue-600">
                        {selectedAssessment.performanceScore}
                      </span>
                      <Badge
                        variant="default"
                        className={
                          selectedAssessment.rating === "卓越"
                            ? "bg-yellow-100 text-yellow-700"
                            : selectedAssessment.rating === "优秀"
                            ? "bg-green-100 text-green-700"
                            : "bg-blue-100 text-blue-700"
                        }
                      >
                        {selectedAssessment.rating}
                      </Badge>
                    </div>
                    <Progress value={selectedAssessment.performanceScore} className="h-2" />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>评语</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">{selectedAssessment.comments}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>改进计划</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">{selectedAssessment.improvementPlan}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* 添加考核对话框 */}
      <Dialog open={isAddAssessmentOpen} onOpenChange={setIsAddAssessmentOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-green-500" />
              添加考核
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            handleAddAssessment({
              employeeName: formData.get('employeeName') as string,
              employeeId: formData.get('employeeId') as string,
              department: formData.get('department') as string,
              position: formData.get('position') as string,
              assessmentPeriod: formData.get('assessmentPeriod') as string,
              performanceScore: Number(formData.get('performanceScore')),
              rating: formData.get('rating') as string,
              assessor: formData.get('assessor') as string,
              comments: formData.get('comments') as string,
              improvementPlan: formData.get('improvementPlan') as string,
            })
          }}>
            <ScrollArea className="h-[500px] w-full rounded-md border p-4">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="employeeName">
                      员工姓名 <span className="text-red-500">*</span>
                    </Label>
                    <Input 
                      id="employeeName" 
                      name="employeeName" 
                      required 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="employeeId">
                      员工编号 <span className="text-red-500">*</span>
                    </Label>
                    <Input 
                      id="employeeId" 
                      name="employeeId" 
                      required 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">
                      部门 <span className="text-red-500">*</span>
                    </Label>
                    <Select name="department" required>
                      <SelectTrigger>
                        <SelectValue placeholder="选择部门" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="安全管理部">安全管理部</SelectItem>
                        <SelectItem value="工程管理部">工程管理部</SelectItem>
                        <SelectItem value="人事管理部">人事管理部</SelectItem>
                        <SelectItem value="财务管理部">财务管理部</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="position">
                      职位 <span className="text-red-500">*</span>
                    </Label>
                    <Input 
                      id="position" 
                      name="position" 
                      required 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="assessmentPeriod">
                      考核周期 <span className="text-red-500">*</span>
                    </Label>
                    <Select name="assessmentPeriod" required>
                      <SelectTrigger>
                        <SelectValue placeholder="选择考核周期" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2023年第一季度">2023年第一季度</SelectItem>
                        <SelectItem value="2023年第二季度">2023年第二季度</SelectItem>
                        <SelectItem value="2023年第三季度">2023年第三季度</SelectItem>
                        <SelectItem value="2023年第四季度">2023年第四季度</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="assessor">
                      考核人 <span className="text-red-500">*</span>
                    </Label>
                    <Input 
                      id="assessor" 
                      name="assessor" 
                      required 
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="performanceScore">
                      绩效得分 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="performanceScore"
                      name="performanceScore"
                      type="number"
                      min={0}
                      max={100}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rating">
                      评级 <span className="text-red-500">*</span>
                    </Label>
                    <Select name="rating" required>
                      <SelectTrigger>
                        <SelectValue placeholder="选择评级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="卓越">卓越</SelectItem>
                        <SelectItem value="优秀">优秀</SelectItem>
                        <SelectItem value="良好">良好</SelectItem>
                        <SelectItem value="一般">一般</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="comments">评语</Label>
                    <Textarea
                      id="comments"
                      name="comments"
                      placeholder="请输入评语..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="improvementPlan">改进计划</Label>
                    <Textarea
                      id="improvementPlan"
                      name="improvementPlan"
                      placeholder="请输入改进计划..."
                    />
                  </div>
                </div>
              </div>
            </ScrollArea>
            <DialogFooter className="mt-6">
              <Button 
                type="button"
                variant="outline" 
                onClick={() => setIsAddAssessmentOpen(false)}
              >
                取消
              </Button>
              <Button type="submit">
                确认添加
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 编辑考核对话框 */}
      <Dialog open={isEditAssessmentOpen} onOpenChange={setIsEditAssessmentOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-amber-500" />
              编辑考核
            </DialogTitle>
          </DialogHeader>
          {selectedAssessment && (
            <form onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)
              handleUpdateAssessment({
                performanceScore: Number(formData.get('performanceScore')),
                rating: formData.get('rating') as string,
                comments: formData.get('comments') as string,
                improvementPlan: formData.get('improvementPlan') as string,
              })
            }}>
              <ScrollArea className="h-[500px] w-full rounded-md border p-4">
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>员工姓名</Label>
                      <Input value={selectedAssessment.employeeName} disabled />
                    </div>
                    <div className="space-y-2">
                      <Label>员工编号</Label>
                      <Input value={selectedAssessment.employeeId} disabled />
                    </div>
                    <div className="space-y-2">
                      <Label>部门</Label>
                      <Input value={selectedAssessment.department} disabled />
                    </div>
                    <div className="space-y-2">
                      <Label>职位</Label>
                      <Input value={selectedAssessment.position} disabled />
                    </div>
                    <div className="space-y-2">
                      <Label>考核周期</Label>
                      <Input value={selectedAssessment.assessmentPeriod} disabled />
                    </div>
                    <div className="space-y-2">
                      <Label>考核人</Label>
                      <Input value={selectedAssessment.assessor} disabled />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="performanceScore">
                        绩效得分 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="performanceScore"
                        name="performanceScore"
                        type="number"
                        min={0}
                        max={100}
                        defaultValue={selectedAssessment.performanceScore}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rating">
                        评级 <span className="text-red-500">*</span>
                      </Label>
                      <Select name="rating" defaultValue={selectedAssessment.rating}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择评级" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="卓越">卓越</SelectItem>
                          <SelectItem value="优秀">优秀</SelectItem>
                          <SelectItem value="良好">良好</SelectItem>
                          <SelectItem value="一般">一般</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="comments">评语</Label>
                      <Textarea
                        id="comments"
                        name="comments"
                        placeholder="请输入评语..."
                        defaultValue={selectedAssessment.comments}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="improvementPlan">改进计划</Label>
                      <Textarea
                        id="improvementPlan"
                        name="improvementPlan"
                        placeholder="请输入改进计划..."
                        defaultValue={selectedAssessment.improvementPlan}
                      />
                    </div>
                  </div>
                </div>
              </ScrollArea>
              <DialogFooter className="mt-6">
                <Button 
                  type="button"
                  variant="outline" 
                  onClick={() => setIsEditAssessmentOpen(false)}
                >
                  取消
                </Button>
                <Button type="submit">
                  保存修改
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              确认删除
            </DialogTitle>
            <DialogDescription>
              您确定要删除该考核记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 