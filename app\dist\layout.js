"use strict";
exports.__esModule = true;
exports.metadata = void 0;
var google_1 = require("next/font/google");
require("./globals.css");
var toaster_1 = require("@/components/ui/toaster");
var sonner_1 = require("@/components/ui/sonner");
var auth_context_1 = require("@/contexts/auth-context");
var theme_context_1 = require("@/contexts/theme-context");
var tooltip_1 = require("@/components/ui/tooltip");
var inter = google_1.Inter({ subsets: ["latin"] });
exports.metadata = {
    title: "2025 矿业公司综合管理系统",
    description: "安全、高效、智能化的企业管理平台",
    keywords: "矿业, 管理系统, 安全管理, 工程管理, 人事管理",
    generator: 'v0.dev'
};
function RootLayout(_a) {
    var children = _a.children;
    return (React.createElement("html", { lang: "zh-CN", suppressHydrationWarning: true },
        React.createElement("body", { className: inter.className },
            React.createElement(theme_context_1.ThemeProvider, null,
                React.createElement(auth_context_1.AuthProvider, null,
                    React.createElement(tooltip_1.TooltipProvider, null,
                        children,
                        React.createElement(toaster_1.Toaster, null),
                        React.createElement(sonner_1.Toaster, { position: "top-right", closeButton: true, richColors: true })))))));
}
exports["default"] = RootLayout;
require("./globals.css");
