"use client"

import { useState, useEffect } from "react"
import { Bell, User, LogOut, Home, Sun, Moon, ChevronRight, ChevronLeft, BarChart2, CheckCircle, AlertTriangle, Info, X, Calendar, Clock, Gift, Settings, UserCircle, Key, Shield } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useAuth } from "@/contexts/auth-context"
import { useTheme } from "@/contexts/theme-context"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

// 背景动效组件
const BackgroundAnimation = ({ themeMode }: { themeMode: string }) => {
  const [particles, setParticles] = useState<Array<{id: number; x: number; y: number; size: number}>>([]);
  const [bubbles, setBubbles] = useState<Array<{id: number; x: number; y: number; size: number; color: string}>>([]);
  const [beams, setBeams] = useState<Array<{id: number; x: number; rotation: number; width: number; opacity: number}>>([]);
  
  useEffect(() => {
    // 创建背景粒子
    const createParticles = () => {
      const newParticles = [];
      for (let i = 0; i < 15; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100, // 随机 x 位置 (%)
          y: Math.random() * 100, // 随机 y 位置 (%)
          size: Math.random() * 3 + 1, // 随机大小 1-4px
        });
      }
      setParticles(newParticles);
    };
    
    // 创建彩色气泡
    const createBubbles = () => {
      const colors = [
        'rgba(59, 130, 246, 0.2)',   // 蓝色
        'rgba(99, 102, 241, 0.2)',   // 靛蓝色
        'rgba(139, 92, 246, 0.2)',   // 紫色
        'rgba(236, 72, 153, 0.2)',   // 粉色 
        'rgba(14, 165, 233, 0.2)',   // 浅蓝色
      ];
      
      const newBubbles = [];
      for (let i = 0; i < 6; i++) {  // 减少气泡数量
        newBubbles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 20 + 10, // 10-30px 减小气泡尺寸
          color: colors[Math.floor(Math.random() * colors.length)]
        });
      }
      setBubbles(newBubbles);
    };
    
    // 创建光束
    const createBeams = () => {
      const newBeams = [];
      for (let i = 0; i < 3; i++) {
        newBeams.push({
          id: i,
          x: Math.random() * 100,
          rotation: Math.random() * 180,
          width: Math.random() * 150 + 50,
          opacity: Math.random() * 0.05 + 0.02 // 低不透明度
        });
      }
      setBeams(newBeams);
    };
    
    createParticles();
    createBubbles();
    createBeams();
    
    // 定期刷新粒子位置
    const particleInterval = setInterval(() => {
      setParticles(prev => 
        prev.map(particle => ({
          ...particle,
          x: (particle.x + (Math.random() * 5 - 2.5)) % 100,
          y: (particle.y + (Math.random() * 5 - 2.5)) % 100,
        }))
      );
    }, 3000);
    
    // 定期刷新气泡位置
    const bubbleInterval = setInterval(() => {
      setBubbles(prev => 
        prev.map(bubble => ({
          ...bubble,
          x: (bubble.x + (Math.random() * 2 - 1)) % 100,
          y: (bubble.y + (Math.random() * 2 - 1)) % 100,
        }))
      );
    }, 5000);
    
    // 定期刷新光束位置
    const beamInterval = setInterval(() => {
      setBeams(prev => 
        prev.map(beam => ({
          ...beam,
          x: (beam.x + (Math.random() * 5 - 2.5)) % 100,
          rotation: (beam.rotation + Math.random() * 10 - 5) % 180
        }))
      );
    }, 8000);
    
    return () => {
      clearInterval(particleInterval);
      clearInterval(bubbleInterval);
      clearInterval(beamInterval);
    };
  }, []);
  
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* 光束效果 */}
      <AnimatePresence>
        {beams.map((beam) => (
          <motion.div
            key={`beam-${beam.id}`}
            className={`absolute top-1/3 rounded-full ${
              themeMode === "dark" ? "bg-indigo-500" : "bg-blue-300"
            }`}
            style={{
              width: beam.width,
              height: "1px",
              left: `${beam.x}%`,
              opacity: beam.opacity,
              rotate: `${beam.rotation}deg`,
              transformOrigin: "left center",
              filter: "blur(2px)"
            }}
            animate={{
              opacity: [beam.opacity, beam.opacity * 1.5, beam.opacity],
              scale: [1, 1.1, 1],
              width: [beam.width, beam.width * 1.1, beam.width]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
        ))}
      </AnimatePresence>
      
      {/* 气泡效果 */}
      <AnimatePresence>
        {bubbles.map((bubble) => (
          <motion.div
            key={`bubble-${bubble.id}`}
            className="absolute rounded-full opacity-20"
            style={{
              width: bubble.size,
              height: bubble.size,
              backgroundColor: bubble.color,
              filter: `blur(${bubble.size / 4}px)`,
            }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ 
              opacity: [0.1, 0.2, 0.1],
              scale: [0.8, 1, 0.8],
              x: `${bubble.x}%`,
              y: `${bubble.y}%`,
              rotate: [0, 10, -10, 0],
            }}
            transition={{ 
              duration: 15 + bubble.id * 2,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          />
        ))}
      </AnimatePresence>
      
      {/* 光粒子效果 */}
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={`particle-${particle.id}`}
            className={`absolute rounded-full ${
              themeMode === "dark" 
                ? "bg-blue-400" 
                : "bg-indigo-400"
            }`}
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: [0, 0.3, 0],
              scale: [0, 1, 0],
              x: `${particle.x}%`,
              y: `${particle.y}%`,
            }}
            transition={{ 
              duration: 10,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeInOut",
              delay: particle.id * 0.5,
            }}
            style={{
              width: particle.size,
              height: particle.size,
              filter: `blur(${particle.size}px)`,
            }}
          />
        ))}
      </AnimatePresence>
      
      {/* 光效 */}
      <div className="absolute inset-0 opacity-30">
        <div className={`absolute -top-20 -left-20 w-40 h-40 rounded-full ${
          themeMode === "dark" ? "bg-blue-500/20" : "bg-blue-300/20"
        }`} style={{ filter: "blur(40px)" }} />
        
        <div className={`absolute -bottom-20 -right-20 w-60 h-60 rounded-full ${
          themeMode === "dark" ? "bg-purple-500/20" : "bg-indigo-300/20"
        }`} style={{ filter: "blur(50px)" }} />
        
        <motion.div 
          className={`absolute top-1/4 right-1/4 w-32 h-32 rounded-full ${
            themeMode === "dark" ? "bg-cyan-500/10" : "bg-cyan-300/10"
          }`} 
          style={{ filter: "blur(30px)" }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
      </div>
      
      {/* 添加动态渐变背景 */}
      <div 
        className={`absolute inset-0 transition-opacity duration-1000 ${
          themeMode === "dark" 
            ? "bg-gradient-to-r from-blue-900/5 via-purple-900/5 to-blue-900/5" 
            : "bg-gradient-to-r from-blue-100/20 via-indigo-100/20 to-blue-100/20"
        }`}
      >
        <motion.div 
          className="w-full h-full"
          animate={{
            background: [
              "linear-gradient(60deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
              "linear-gradient(120deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
              "linear-gradient(180deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
              "linear-gradient(240deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
              "linear-gradient(300deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
              "linear-gradient(360deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
            ],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "loop",
          }}
        />
      </div>
    </div>
  );
};

// 通知类型定义
interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
  link?: string;  // 可选的链接，点击通知可以跳转
  actionText?: string; // 可选的操作文本
  createdAt: Date; // 创建时间，用于排序和计算时间差
}

interface HeaderProps {
  toggleSidebar: () => void
  sidebarCollapsed?: boolean
}

export function Header({ toggleSidebar, sidebarCollapsed = true }: HeaderProps) {
  const { user, logout } = useAuth()
  const { themeMode, toggleTheme } = useTheme()
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: '系统更新',
      message: '系统将于今晚22:00进行维护升级',
      time: '10分钟前',
      read: false,
      type: 'info',
      createdAt: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
    },
    {
      id: 2,
      title: '安全检查',
      message: '您有一项安全检查任务已完成',
      time: '30分钟前',
      read: false,
      type: 'success',
      link: '/safety-management/safety-check',
      actionText: '查看详情',
      createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
    },
    {
      id: 3,
      title: '设备预警',
      message: '有3台设备需要维护',
      time: '2小时前',
      read: false,
      type: 'warning',
      link: '/material-supply-chain/material-maintenance',
      actionText: '立即处理',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
    }
  ])
  const [isNotificationOpen, setIsNotificationOpen] = useState(false)
  const [currentTime, setCurrentTime] = useState('')
  const [showNewNotification, setShowNewNotification] = useState(false)
  const [hasNewNotification, setHasNewNotification] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [interactionPoint, setInteractionPoint] = useState({ x: 0, y: 0 });
  const [showInteractionEffect, setShowInteractionEffect] = useState(false);
  const router = useRouter()

  // 处理鼠标交互效果
  const handleMouseInteraction = (e: React.MouseEvent) => {
    // 限制效果触发频率，避免过度消耗性能
    if (Math.random() > 0.1) return;
    
    // 记录鼠标位置
    setInteractionPoint({
      x: e.clientX,
      y: e.clientY
    });
    
    // 显示交互效果
    setShowInteractionEffect(true);
    
    // 一段时间后隐藏效果
    setTimeout(() => setShowInteractionEffect(false), 1000);
  };

  // 时间格式化函数 - 将日期转换为"x分钟前"格式
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 30) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 更新所有通知的时间
  const updateNotificationTimes = () => {
    setNotifications(prev => 
      prev.map(notification => ({
        ...notification,
        time: formatTimeAgo(notification.createdAt)
      }))
    );
  };

  // 实时时间显示
  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }
      setCurrentTime(now.toLocaleString('zh-CN', options))
      
      // 同时更新通知时间显示
      updateNotificationTimes();
    }
    
    updateTime()
    const interval = setInterval(updateTime, 60000) // 每分钟更新一次
    
    return () => clearInterval(interval)
  }, [])

  // 模拟随机生成新通知的功能（仅用于演示）
  useEffect(() => {
    const generateRandomNotification = () => {
      const types: ('info' | 'success' | 'warning' | 'error')[] = ['info', 'success', 'warning', 'error'];
      const randomType = types[Math.floor(Math.random() * types.length)];
      
      const titles: Record<string, string[]> = {
        info: ['系统公告', '信息更新', '功能提示', '新功能上线'],
        success: ['任务完成', '操作成功', '审批通过', '安全检查完成'],
        warning: ['设备预警', '安全提醒', '库存预警', '系统提示'],
        error: ['系统错误', '操作失败', '连接中断', '数据异常']
      };
      
      const messages: Record<string, string[]> = {
        info: ['系统将进行例行维护，请注意保存工作', '新版本发布，请查看更新日志', '您有新的任务需要处理'],
        success: ['您的申请已审批通过', '数据同步完成', '备份已成功创建'],
        warning: ['库存物资即将不足，请及时补充', '设备运行时间过长，需要检查', '有待处理的安全隐患'],
        error: ['服务器连接失败，请稍后重试', '操作未能完成，请联系管理员', '数据提交出错']
      };
      
      const links: Record<string, string[]> = {
        info: ['/', '/system-management/system-config'],
        success: ['/task-process-management/task-management', '/safety-management/safety-check'],
        warning: ['/material-supply-chain/material-maintenance', '/safety-management/safety-hazard-management'],
        error: ['/system-management/system-settings', '/system-management/user-management']
      };
      
      const randomTitle = titles[randomType][Math.floor(Math.random() * titles[randomType].length)];
      const randomMessage = messages[randomType][Math.floor(Math.random() * messages[randomType].length)];
      const randomLink = links[randomType][Math.floor(Math.random() * links[randomType].length)];
      
      const newNotification: Notification = {
        id: Date.now(),
        title: randomTitle,
        message: randomMessage,
        time: '刚刚',
        read: false,
        type: randomType,
        link: randomLink,
        actionText: randomType === 'warning' || randomType === 'error' ? '立即处理' : '查看详情',
        createdAt: new Date()
      };
      
      setNotifications(prev => [newNotification, ...prev]);
      setHasNewNotification(true);
      
      // 显示新通知的动画效果
      setShowNewNotification(true);
      setTimeout(() => setShowNewNotification(false), 3000);
    };
    
    // 随机间隔生成通知（仅用于演示）
    const randomInterval = Math.floor(Math.random() * (180000 - 60000) + 60000); // 1-3分钟
    const timer = setTimeout(generateRandomNotification, randomInterval);
    
    return () => clearTimeout(timer);
  }, [notifications]);

  // 通知动画效果
  useEffect(() => {
    if (hasNewNotification) {
      // 通知铃铛震动动画效果
      const bellIcon = document.querySelector('.notification-bell');
      if (bellIcon) {
        bellIcon.classList.add('animate-bell');
        setTimeout(() => {
          bellIcon.classList.remove('animate-bell');
          setHasNewNotification(false);
        }, 1000);
      }
    }
  }, [hasNewNotification]);

  // 标记通知为已读
  const markAsRead = (id: number) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    )
  }

  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  // 删除通知
  const removeNotification = (id: number) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== id)
    )
  }

  // 处理通知点击，如果有链接则跳转
  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);
    if (notification.link) {
      setIsNotificationOpen(false);
      // 延迟跳转，确保先关闭通知面板
      setTimeout(() => {
        router.push(notification.link || '/');
      }, 100);
    }
  }

  // 未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch(type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 导航到首页
  const navigateToHome = () => {
    router.push("/")
  }

  // 导航到可视化大屏
  const navigateToVisualDashboard = () => {
    router.push("/visual-dashboard")
  }

  // 导航到个人资料页面
  const navigateToProfile = () => {
    setIsUserMenuOpen(false)
    router.push("/system-management/user-management") // 修改为现有页面
  }

  // 导航到密码修改页面
  const navigateToChangePassword = () => {
    setIsUserMenuOpen(false)
    router.push("/system-management/user-management?tab=password") // 修改为现有页面并添加参数
  }

  // 导航到安全设置页面
  const navigateToSecuritySettings = () => {
    setIsUserMenuOpen(false)
    router.push("/system-management/system-settings") // 修改为现有页面
  }

  // 导航到系统设置
  const navigateToSettings = () => {
    setIsUserMenuOpen(false)
    router.push("/system-management/system-settings") // 修改为现有页面
  }

  // 处理登出
  const handleLogout = () => {
    setIsUserMenuOpen(false)
    // 弹出确认对话框
    if (confirm('确定要退出登录吗？')) {
      logout()
    }
  }

  return (
    <motion.header 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`sticky top-0 z-10 flex h-16 items-center gap-4 border-b px-4 md:px-6 relative overflow-hidden ${
        themeMode === "dark" 
          ? "bg-[#1c1c1e]/90 border-[#2c2c2e] shadow-md shadow-black/10" 
          : "bg-[#f5f5f7]/90 border-gray-200 backdrop-blur-sm shadow-md shadow-black/5"
      }`}
      onMouseMove={handleMouseInteraction}
    >
      {/* 背景图片和渐变遮罩 */}
      <div 
        className="absolute inset-0 transition-all duration-500"
        style={{
          backgroundImage: `url(/menu-bg/ding.png)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: '0.8'
        }}
      />
      <div 
        className="absolute inset-0 transition-all duration-500"
        style={{
          background: themeMode === "dark"
            ? 'linear-gradient(to right, #1E1E1E 10%, transparent 50%, #1E1E1E 90%)'
            : 'linear-gradient(to right, #F9FAFC 10%, transparent 50%, #F9FAFC 90%)',
          opacity: '0.95'
        }}
      />
      
      {/* 鼠标交互效果 */}
      <AnimatePresence>
        {showInteractionEffect && (
          <motion.div
            className={`absolute rounded-full pointer-events-none ${
              themeMode === "dark" ? "bg-blue-500" : "bg-indigo-400"
            }`}
            style={{
              left: interactionPoint.x,
              top: interactionPoint.y,
              translateX: "-50%",
              translateY: "-50%",
            }}
            initial={{ width: 0, height: 0, opacity: 0.5 }}
            animate={{ width: 100, height: 100, opacity: 0 }}
            exit={{ width: 0, height: 0, opacity: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        )}
      </AnimatePresence>
      
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={toggleSidebar} 
          className={`flex items-center justify-center transition-all duration-300 ${
            themeMode === "dark" 
              ? "text-white hover:bg-[#2c2c2e] hover:text-blue-400" 
              : "hover:bg-gray-100 hover:text-blue-600"
          } relative group`}
          title={sidebarCollapsed ? "展开菜单" : "收起菜单"}
        >
          <motion.div
            animate={{ 
              rotate: sidebarCollapsed ? 0 : 180,
              scale: sidebarCollapsed ? 1 : 0.9
            }}
            transition={{ duration: 0.3 }}
            className="relative z-10"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <ChevronLeft className="h-5 w-5" />
            )}
          </motion.div>
          <div className={`absolute inset-0 rounded-full transition-all duration-300 ${
            themeMode === "dark"
              ? "bg-blue-500/0 group-hover:bg-blue-500/10"
              : "bg-blue-500/0 group-hover:bg-blue-500/10"
          }`}></div>
          <span className="sr-only">切换侧边栏</span>
        </Button>
      </motion.div>
      <div className="w-full flex justify-between">
        <div className="flex items-center gap-4">
          <motion.div
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
          >
            <Button 
              variant="ghost" 
              className={`flex items-center gap-2 mr-4 transition-all duration-300 ${
                themeMode === "dark" 
                  ? "bg-[#2c2c2e]/60 text-white hover:bg-[#2c2c2e]" 
                  : "bg-white/60 text-gray-800 hover:bg-gray-100"
              }`}
              onClick={navigateToHome}
            >
              <Home className={`h-5 w-5 ${
                themeMode === "dark" 
                  ? "text-blue-400" 
                  : "text-blue-600"
              }`} />
              <span className={`font-medium text-base ${
                themeMode === "dark" 
                  ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]" 
                  : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]"
              }`}>首页</span>
            </Button>
          </motion.div>

          {/* 搜索框 */}
          <div className={`relative flex items-center ${
            themeMode === "dark" 
              ? "text-gray-300" 
              : "text-gray-600"
          }`}>
            <div className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ${
              themeMode === "dark" 
                ? "text-gray-400" 
                : "text-gray-500"
            }`}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="search"
              placeholder="搜索..."
              className={`block w-64 pl-10 pr-3 py-2 rounded-lg border ${
                themeMode === "dark"
                  ? "bg-[#2c2c2e]/60 border-[#3a3a3c] text-white placeholder-gray-400 focus:border-blue-500"
                  : "bg-white/60 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500"
              } focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-all duration-300`}
            />
          </div>

          <motion.div
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
          >
            <Button 
              variant="ghost" 
              className={`flex items-center gap-2 mr-4 transition-all duration-300 neon-button ${
                themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"
              }`}
              onClick={navigateToVisualDashboard}
            >
              <BarChart2 className={`h-5 w-5 ${themeMode === "dark" ? "text-white" : "text-gray-700"}`} />
              <span className={`font-medium text-base ${
                themeMode === "dark" 
                  ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]" 
                  : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]"
              }`}>可视化大屏</span>
            </Button>
          </motion.div>
          
          {/* 时间显示 */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className={`hidden md:flex items-center gap-2 ${
              themeMode === "dark" ? "text-gray-200" : "text-gray-700"
            }`}
          >
            <Clock className="h-4 w-4" />
            <span className={`text-sm font-medium ${
              themeMode === "dark" 
                ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]" 
                : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]"
            }`}>{currentTime}</span>
          </motion.div>
        </div>
        <div className="flex items-center gap-4">
          {/* 主题切换按钮 */}
          <motion.div
            whileHover={{ scale: 1.1, rotate: themeMode === "dark" ? 180 : 0 }}
            whileTap={{ scale: 0.9 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleTheme}
              className={`rounded-full transition-all duration-300 neon-button ${themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"}`}
              title={themeMode === "dark" ? "切换到亮色模式" : "切换到暗色模式"}
            >
              {themeMode === "dark" ? (
                <motion.div
                  initial={{ rotate: -90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: 90, opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Sun className="h-5 w-5 text-yellow-400" />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ rotate: 90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: -90, opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Moon className="h-5 w-5" />
                </motion.div>
              )}
              <span className="sr-only">切换主题</span>
            </Button>
          </motion.div>
          
          {/* 通知下拉菜单 */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            animate={{ y: hasNewNotification ? [0, -5, 0] : 0 }}
            transition={{ duration: 0.2 }}
          >
            <Popover open={isNotificationOpen} onOpenChange={setIsNotificationOpen}>
              <PopoverTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className={`relative transition-all duration-300 neon-button ${themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"}`}
                  title="通知中心"
                >
                  <Bell className={`h-5 w-5 notification-bell ${hasNewNotification ? 'animate-bell' : ''}`} />
                  {unreadCount > 0 && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 10
                      }}
                      className={`absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full text-[10px] ${
                        themeMode === "dark" ? "bg-white text-black" : "bg-black text-white"
                      }`}
                    >
                      {unreadCount}
                    </motion.span>
                  )}
                  <span className="sr-only">通知</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent 
                className={`w-80 p-0 ${themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c] text-white" : ""}`} 
                align="end"
                sideOffset={10}
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center justify-between px-4 py-3 border-b">
                    <h3 className="font-medium">通知中心</h3>
                    <div className="flex items-center gap-2">
                      {unreadCount > 0 && (
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={markAllAsRead} 
                          className={`text-xs hover:bg-transparent transition-colors ${themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-500 hover:text-black"}`}
                        >
                          全部标为已读
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setNotifications([])}
                        className={`text-xs hover:bg-transparent transition-colors ${themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-500 hover:text-black"}`}
                      >
                        清空
                      </Button>
                    </div>
                  </div>
                  
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <motion.div 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1, duration: 0.3 }}
                        className="flex flex-col items-center justify-center py-8 px-4 text-center"
                      >
                        <Bell className={`h-12 w-12 mb-2 ${themeMode === "dark" ? "text-gray-600" : "text-gray-300"}`} />
                        <p className={`${themeMode === "dark" ? "text-gray-400" : "text-gray-600"}`}>暂无通知</p>
                      </motion.div>
                    ) : (
                      <div>
                        {notifications.map((notification, index) => (
                          <motion.div 
                            key={notification.id} 
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05, duration: 0.2 }}
                            className={cn(
                              "flex items-start p-3 border-b relative transition-all duration-200",
                              !notification.read ? (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-50") : "",
                              themeMode === "dark" ? "border-[#3a3a3c]" : "border-gray-100",
                              "hover:bg-gray-100 dark:hover:bg-[#444]",
                              notification.link ? "cursor-pointer" : ""
                            )}
                          >
                            <div className="flex-shrink-0 mt-1 mr-3">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div 
                              className="flex-1"
                              onClick={() => handleNotificationClick(notification)}
                            >
                              <div className="flex justify-between items-start">
                                <p className={`font-medium ${!notification.read ? "font-bold" : ""}`}>
                                  {notification.title}
                                </p>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 -mt-1 -mr-1 hover:bg-transparent"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeNotification(notification.id);
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                  <span className="sr-only">关闭</span>
                                </Button>
                              </div>
                              <p className={`text-sm ${themeMode === "dark" ? "text-gray-300" : "text-gray-600"}`}>
                                {notification.message}
                              </p>
                              <div className="flex items-center justify-between mt-1">
                                <p className={`text-xs ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                                  {notification.time}
                                </p>
                                {notification.actionText && notification.link && (
                                  <Button
                                    variant="link"
                                    size="sm"
                                    className={`p-0 h-auto text-xs ${
                                      notification.type === 'success' ? 'text-green-500' :
                                      notification.type === 'warning' ? 'text-yellow-500' :
                                      notification.type === 'error' ? 'text-red-500' :
                                      'text-blue-500'
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleNotificationClick(notification);
                                    }}
                                  >
                                    {notification.actionText}
                                  </Button>
                                )}
                              </div>
                              {!notification.read && (
                                <Badge 
                                  variant="secondary" 
                                  className={`absolute top-3 right-10 ${
                                    themeMode === "dark" ? "bg-blue-900/30 text-blue-400" : "bg-blue-100 text-blue-800"
                                  }`}
                                >
                                  新
                                </Badge>
                              )}
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <motion.div 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                    className="p-2 border-t"
                  >
                    <Button 
                      variant="outline" 
                      className="w-full text-center transition-all duration-300 hover:bg-gray-100 dark:hover:bg-[#3a3a3c]"
                      onClick={() => {
                        setIsNotificationOpen(false)
                        router.push('/notifications')
                      }}
                    >
                      查看所有通知
                    </Button>
                  </motion.div>
                </motion.div>
              </PopoverContent>
            </Popover>
          </motion.div>
          
          {/* 用户菜单 */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <DropdownMenu open={isUserMenuOpen} onOpenChange={setIsUserMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className={`rounded-full transition-all duration-300 ${themeMode === "dark" ? "hover:bg-[#2c2c2e]" : "hover:bg-gray-100"}`}
                >
                  <div className="avatar-container relative">
                    <Avatar className="h-8 w-8 border-2 border-transparent hover:border-blue-400 transition-all duration-300">
                      <AvatarImage src={user?.avatar || ""} alt={user?.username || "用户头像"} />
                      <AvatarFallback className={`text-sm transition-colors ${
                        themeMode === "dark" ? "bg-[#2c2c2e]" : "bg-gray-200"
                      }`}>
                        {user?.username.charAt(0).toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <motion.div 
                      className={`absolute inset-0 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-300 ${
                        themeMode === "dark" ? "glow-effect-dark" : "glow-effect-light"
                      }`}
                      animate={{
                        boxShadow: [
                          "0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3)",
                          "0 0 15px rgba(59, 130, 246, 0.7), 0 0 20px rgba(59, 130, 246, 0.5)",
                          "0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3)"
                        ]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatType: "loop"
                      }}
                    />
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end"
                className={themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c] text-white" : ""}
                sideOffset={5}
                alignOffset={0}
                forceMount
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className={`px-4 py-3 flex items-center gap-3 ${themeMode === "dark" ? "text-gray-100" : ""}`}>
                    <Avatar className="h-10 w-10 ring-2 ring-blue-400/50">
                      <AvatarImage src={user?.avatar || ""} alt={user?.username || "用户头像"} />
                      <AvatarFallback className={`${
                        themeMode === "dark" ? "bg-[#444]" : "bg-gray-200"
                      }`}>
                        {user?.username.charAt(0).toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className={`font-semibold text-base ${
                        themeMode === "dark" 
                          ? "text-white drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]" 
                          : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]"
                      }`}>{user?.username || "用户"}</p>
                      <p className={`text-xs truncate max-w-[160px] ${
                        themeMode === "dark" 
                          ? "text-gray-300 drop-shadow-[0_0_0.2px_rgba(255,255,255,0.5)]" 
                          : "text-gray-600 drop-shadow-[0_0_0.2px_rgba(0,0,0,0.3)]"
                      }`}>
                        {user?.email || user?.role || "普通用户"}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator className={themeMode === "dark" ? "bg-[#3a3a3c]" : ""} />
                  
                  <DropdownMenuGroup>
                    <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                      <DropdownMenuItem 
                        onClick={navigateToProfile}
                        className={`transition-colors duration-200 ${themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : ""}`}
                      >
                        <UserCircle className="mr-2 h-4 w-4" />
                        <span>个人资料</span>
                        <DropdownMenuShortcut>⇧P</DropdownMenuShortcut>
                      </DropdownMenuItem>
                    </motion.div>
                    <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                      <DropdownMenuItem 
                        onClick={navigateToChangePassword}
                        className={`transition-colors duration-200 ${themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : ""}`}
                      >
                        <Key className="mr-2 h-4 w-4" />
                        <span>修改密码</span>
                      </DropdownMenuItem>
                    </motion.div>
                    <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                      <DropdownMenuItem 
                        onClick={navigateToSecuritySettings}
                        className={`transition-colors duration-200 ${themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : ""}`}
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        <span>安全设置</span>
                      </DropdownMenuItem>
                    </motion.div>
                  </DropdownMenuGroup>
                  
                  <DropdownMenuSeparator className={themeMode === "dark" ? "bg-[#3a3a3c]" : ""} />
                  
                  <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                    <DropdownMenuItem 
                      onClick={navigateToSettings}
                      className={`transition-colors duration-200 ${themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : ""}`}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      <span>系统设置</span>
                      <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
                    </DropdownMenuItem>
                  </motion.div>
                  
                  <DropdownMenuSeparator className={themeMode === "dark" ? "bg-[#3a3a3c]" : ""} />
                  
                  <motion.div 
                    whileHover={{ x: 5, scale: 1.03 }} 
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    <DropdownMenuItem 
                      onClick={handleLogout}
                      className={`transition-colors duration-200 ${themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c] text-red-400 hover:text-red-300" : "text-red-600 hover:text-red-700"}`}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>退出登录</span>
                      <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
                    </DropdownMenuItem>
                  </motion.div>
                </motion.div>
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
        </div>
      </div>

      {/* 新通知提示浮窗 */}
      {showNewNotification && notifications.length > 0 && (
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 100 }}
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
          className={`fixed top-20 right-4 z-50 w-72 p-3 rounded-lg shadow-lg transform ${
            themeMode === "dark" ? "bg-[#2c2c2e] text-white" : "bg-white text-gray-800"
          }`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0 mt-1 mr-3">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: [0, 1.2, 1] }}
                transition={{ duration: 0.5 }}
              >
                {getNotificationIcon(notifications[0].type)}
              </motion.div>
            </div>
            <div className="flex-1">
              <motion.p 
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="font-bold"
              >
                {notifications[0].title}
              </motion.p>
              <motion.p 
                initial={{ y: -5, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-sm mt-1"
              >
                {notifications[0].message}
              </motion.p>
              <motion.div 
                initial={{ y: -5, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="flex justify-between items-center mt-2"
              >
                <span className="text-xs opacity-75">刚刚</span>
                {notifications[0].actionText && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="p-0 h-auto text-xs"
                    onClick={() => {
                      setShowNewNotification(false);
                      if (notifications[0].link) {
                        router.push(notifications[0].link);
                      }
                    }}
                  >
                    {notifications[0].actionText}
                  </Button>
                )}
              </motion.div>
            </div>
          </div>
        </motion.div>
      )}

      <style jsx global>{`
        @keyframes bell-shake {
          0% { transform: rotate(0); }
          20% { transform: rotate(15deg); }
          40% { transform: rotate(-15deg); }
          60% { transform: rotate(7deg); }
          80% { transform: rotate(-7deg); }
          100% { transform: rotate(0); }
        }
        
        .animate-bell {
          animation: bell-shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
          transform-origin: top center;
        }
        
        @keyframes pulse-ring {
          0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
          70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
          100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
        
        .pulse-animation {
          animation: pulse-ring 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        /* 为新增的动态效果添加样式 */
        @keyframes float {
          0% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
          100% { transform: translateY(0px); }
        }
        
        .float-animation {
          animation: float 6s ease-in-out infinite;
        }
        
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .gradient-animation {
          background-size: 200% 200%;
          animation: gradient-shift 15s ease infinite;
        }
        
        /* 为按钮添加霓虹效果 */
        .neon-button {
          position: relative;
          overflow: hidden;
        }
        
        .neon-button::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          z-index: -1;
          background: linear-gradient(45deg, 
            #ff0000, #ff7300, #fffb00, #48ff00, 
            #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
          background-size: 400%;
          border-radius: 10px;
          opacity: 0;
          transition: 0.5s;
          animation: neon-border 20s linear infinite;
        }
        
        .neon-button:hover::before {
          opacity: 0.3;
        }
        
        @keyframes neon-border {
          0% { background-position: 0 0; }
          50% { background-position: 400% 0; }
          100% { background-position: 0 0; }
        }
        
        /* 为点击添加波纹效果 */
        @keyframes ripple {
          0% { transform: scale(0); opacity: 1; }
          100% { transform: scale(2.5); opacity: 0; }
        }
        
        .ripple-effect {
          position: relative;
          overflow: hidden;
        }
        
        .ripple-effect::after {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.3);
          top: 0;
          left: 0;
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s linear;
        }
        
        /* 用户头像发光效果 */
        .glow-effect-light {
          border-radius: 50%;
          box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),
                      0 0 10px rgba(59, 130, 246, 0.3);
        }
        
        .glow-effect-dark {
          border-radius: 50%;
          box-shadow: 0 0 5px rgba(147, 197, 253, 0.5),
                      0 0 10px rgba(147, 197, 253, 0.3);
        }
        
        .avatar-container:hover .glow-effect-light,
        .avatar-container:hover .glow-effect-dark {
          animation: pulse-glow 2s infinite;
        }
        
        @keyframes pulse-glow {
          0% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),
                        0 0 10px rgba(59, 130, 246, 0.3);
          }
          50% {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.8),
                       0 0 20px rgba(59, 130, 246, 0.5),
                       0 0 25px rgba(59, 130, 246, 0.3);
          }
          100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),
                       0 0 10px rgba(59, 130, 246, 0.3);
          }
        }
        
        /* Add this to smoothen all transitions */
        * {
          transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-duration: 200ms;
        }
      `}</style>
    </motion.header>
  )
}

