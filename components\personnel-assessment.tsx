"use client"

import { useState } from "react"
import { Search, Plus, Edit, Trash2, Download, MoreHorizontal, FileText, Star, BarChart2, Filter, Settings2, UserCheck, AlertCircle, UserPlus, AlertTriangle, LineChart, PieChart, TrendingUp, Calendar, ClipboardCheck, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart as RechartsPieChart, Pie, Cell } from 'recharts'

// 添加类型定义
interface Assessment {
  id: string
  employeeName: string
  employeeId: string
  department: string
  position: string
  assessmentPeriod: string
  performanceScore: number
  rating: string
  assessor: string
  status: string
  comments?: string
  improvementPlan?: string
  assessmentDate?: string
}

export function PersonnelAssessment() {
  const { toast } = useToast()
  const [isAddAssessmentOpen, setIsAddAssessmentOpen] = useState(false)
  const [isEditAssessmentOpen, setIsEditAssessmentOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [periodFilter, setPeriodFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)

  // 修改 assessments 状态，添加类型
  const [assessments, setAssessments] = useState<Assessment[]>([
    {
      id: "1",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "安全管理部",
      position: "安全员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 85,
      rating: "优秀",
      assessor: "李经理",
      status: "已完成",
    },
    {
      id: "2",
      employeeName: "李四",
      employeeId: "EMP002",
      department: "工程管理部",
      position: "工程师",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 92,
      rating: "卓越",
      assessor: "王主管",
      status: "已完成",
    },
    {
      id: "3",
      employeeName: "王五",
      employeeId: "EMP003",
      department: "人事管理部",
      position: "人事专员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 78,
      rating: "良好",
      assessor: "赵经理",
      status: "已完成",
    },
    {
      id: "4",
      employeeName: "赵六",
      employeeId: "EMP004",
      department: "财务管理部",
      position: "财务主管",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 65,
      rating: "一般",
      assessor: "钱总监",
      status: "已完成",
    },
    {
      id: "5",
      employeeName: "钱七",
      employeeId: "EMP005",
      department: "工程管理部",
      position: "技术员",
      assessmentPeriod: "2023年第一季度",
      performanceScore: 0,
      rating: "-",
      assessor: "孙经理",
      status: "进行中",
    },
  ])

  // 图表数据
  const performanceTrendData = [
    { month: '1月', score: 82 },
    { month: '2月', score: 85 },
    { month: '3月', score: 83 },
    { month: '4月', score: 87 },
    { month: '5月', score: 89 },
    { month: '6月', score: 88 },
  ]

  const ratingDistributionData = [
    { name: '卓越', value: 20, color: '#facc15' },
    { name: '优秀', value: 35, color: '#22c55e' },
    { name: '良好', value: 30, color: '#3b82f6' },
    { name: '一般', value: 15, color: '#6b7280' },
  ]

  // 筛选后的考核数据
  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = !searchTerm || 
      assessment.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.employeeId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment = departmentFilter === "all" || assessment.department === departmentFilter;
    const matchesPeriod = periodFilter === "all" || assessment.assessmentPeriod === periodFilter;
    const matchesStatus = statusFilter === "all" || assessment.status === statusFilter;

    return matchesSearch && matchesDepartment && matchesPeriod && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredAssessments.length / pageSize)
  const paginatedAssessments = filteredAssessments.slice(
    (page - 1) * pageSize,
    page * pageSize
  )

  // 处理查看详情
  const handleViewDetail = (assessment: Assessment) => {
    setSelectedAssessment(assessment)
    setIsViewDetailOpen(true)
  }

  // 处理编辑考核
  const handleEditAssessment = (assessment: Assessment) => {
    setSelectedAssessment(assessment)
    setIsEditAssessmentOpen(true)
  }

  // 处理删除考核
  const handleDeleteAssessment = (assessment: Assessment) => {
    setSelectedAssessment(assessment)
    setIsDeleteDialogOpen(true)
  }

  // 修改 confirmDelete 函数
  const confirmDelete = () => {
    if (selectedAssessment) {
      setAssessments((prev: Assessment[]) => prev.filter(a => a.id !== selectedAssessment.id))
      toast({
        title: "删除成功",
        description: "已成功删除考核记录",
      })
      setIsDeleteDialogOpen(false)
      setSelectedAssessment(null)
    }
  }

  // 修改 updateAssessment 函数
  const updateAssessment = (formData: Partial<Assessment>) => {
    setAssessments((prev: Assessment[]) => prev.map(a => 
      a.id === selectedAssessment?.id ? { ...a, ...formData } : a
    ))
    toast({
      title: "更新成功",
      description: "已成功更新考核记录",
    })
    setIsEditAssessmentOpen(false)
    setSelectedAssessment(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">人事考核</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="hover:bg-blue-50 hover:text-blue-600">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm" className="hover:bg-green-50 hover:text-green-600">
            <LineChart className="h-4 w-4 mr-2" />
            生成报告
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="col-span-2">
          <CardHeader>
            <CardTitle className="text-lg font-medium">绩效趋势</CardTitle>
            <CardDescription>近6个月平均绩效分数趋势</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <RechartsLineChart
                width={600}
                height={300}
                data={performanceTrendData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[60, 100]} />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="score"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6' }}
                />
              </RechartsLineChart>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-medium">评级分布</CardTitle>
            <CardDescription>当前考核评级分布情况</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full flex items-center justify-center">
              <RechartsPieChart width={250} height={250}>
                <Pie
                  data={ratingDistributionData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {ratingDistributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </RechartsPieChart>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader>
          <CardTitle>考核记录</CardTitle>
          <CardDescription>管理员工的绩效考核记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    type="search" 
                    placeholder="搜索员工..." 
                    className="pl-8 w-[250px] border-slate-200 focus-visible:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="工程管理部">工程管理部</SelectItem>
                    <SelectItem value="人事管理部">人事管理部</SelectItem>
                    <SelectItem value="财务管理部">财务管理部</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={periodFilter} onValueChange={setPeriodFilter}>
                  <SelectTrigger className="w-[180px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="考核周期" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有周期</SelectItem>
                    <SelectItem value="2023年第一季度">2023年第一季度</SelectItem>
                    <SelectItem value="2022年第四季度">2022年第四季度</SelectItem>
                    <SelectItem value="2022年第三季度">2022年第三季度</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="未开始">未开始</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={() => setIsAddAssessmentOpen(true)} 
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加考核
              </Button>
            </div>

            <div className="rounded-md border border-slate-200">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead>员工姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>职位</TableHead>
                    <TableHead>考核周期</TableHead>
                    <TableHead>绩效得分</TableHead>
                    <TableHead>评级</TableHead>
                    <TableHead>考核人</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedAssessments.map((assessment) => (
                    <TableRow key={assessment.id} className="hover:bg-slate-50">
                      <TableCell>
                        <div className="font-medium">{assessment.employeeName}</div>
                      </TableCell>
                      <TableCell>{assessment.employeeId}</TableCell>
                      <TableCell>{assessment.department}</TableCell>
                      <TableCell>{assessment.position}</TableCell>
                      <TableCell>{assessment.assessmentPeriod}</TableCell>
                      <TableCell>
                        {assessment.status === "已完成" ? (
                          <div className="flex items-center">
                            <span className="mr-2">{assessment.performanceScore}</span>
                            <Progress value={assessment.performanceScore} className="h-2 w-16" />
                          </div>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>
                        {assessment.rating !== "-" ? (
                          <div className="flex items-center">
                            <Star
                              className={`h-4 w-4 mr-1 ${
                                assessment.rating === "卓越"
                                  ? "text-yellow-500 fill-yellow-500"
                                  : assessment.rating === "优秀"
                                    ? "text-green-500 fill-green-500"
                                    : assessment.rating === "良好"
                                      ? "text-blue-500 fill-blue-500"
                                      : "text-gray-500"
                              }`}
                            />
                            {assessment.rating}
                          </div>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>{assessment.assessor}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            assessment.status === "已完成"
                              ? "default"
                              : assessment.status === "进行中"
                                ? "outline"
                                : "secondary"
                          }
                          className={
                            assessment.status === "已完成"
                              ? "bg-green-100 text-green-700"
                              : assessment.status === "进行中"
                                ? "bg-blue-50 text-blue-700"
                                : "bg-slate-100 text-slate-700"
                          }
                        >
                          {assessment.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetail(assessment)}
                            className="hover:bg-blue-50 hover:text-blue-600"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAssessment(assessment)}
                            className="hover:bg-amber-50 hover:text-amber-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAssessment(assessment)}
                            className="hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <BarChart2 className="h-4 w-4 mr-2" />
                                绩效分析
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Calendar className="h-4 w-4 mr-2" />
                                历史记录
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t bg-slate-50 px-6 py-3">
          <div className="text-sm text-slate-600">
            共 {filteredAssessments.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className="border-slate-200 hover:bg-slate-100"
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="px-3 border-slate-200 bg-white hover:bg-slate-100"
            >
              {page} / {totalPages}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
              className="border-slate-200 hover:bg-slate-100"
            >
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 添加考核对话框 */}
      <Dialog open={isAddAssessmentOpen} onOpenChange={setIsAddAssessmentOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-blue-500" />
              <span>添加考核</span>
            </DialogTitle>
            <DialogDescription>
              添加新的考核记录，带 <span className="text-red-500">*</span> 为必填项
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-2 bg-slate-100">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white data-[state=active]:text-blue-600">
                基本信息
              </TabsTrigger>
              <TabsTrigger value="assessment" className="data-[state=active]:bg-white data-[state=active]:text-blue-600">
                考核评估
              </TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-name">
                    员工姓名 <span className="text-red-500">*</span>
                  </Label>
                  <Select>
                    <SelectTrigger id="employee-name">
                      <SelectValue placeholder="选择员工" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="emp001">张三</SelectItem>
                      <SelectItem value="emp002">李四</SelectItem>
                      <SelectItem value="emp003">王五</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assessment-period">
                    考核周期 <span className="text-red-500">*</span>
                  </Label>
                  <Select>
                    <SelectTrigger id="assessment-period">
                      <SelectValue placeholder="选择考核周期" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2023-q1">2023年第一季度</SelectItem>
                      <SelectItem value="2022-q4">2022年第四季度</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assessor">
                    考核人 <span className="text-red-500">*</span>
                  </Label>
                  <Input id="assessor" placeholder="请输入考核人" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assessment-date">考核日期</Label>
                  <Input id="assessment-date" type="date" />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="assessment" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="performance-score">
                    绩效得分 <span className="text-red-500">*</span>
                  </Label>
                  <Input id="performance-score" type="number" min="0" max="100" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rating">
                    评级 <span className="text-red-500">*</span>
                  </Label>
                  <Select>
                    <SelectTrigger id="rating">
                      <SelectValue placeholder="选择评级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excellent">卓越</SelectItem>
                      <SelectItem value="good">优秀</SelectItem>
                      <SelectItem value="satisfactory">良好</SelectItem>
                      <SelectItem value="average">一般</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="assessment-comments">评语</Label>
                  <Textarea id="assessment-comments" placeholder="请输入考核评语" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="improvement-plan">改进计划</Label>
                  <Textarea id="improvement-plan" placeholder="请输入改进计划" />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsAddAssessmentOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              onClick={() => {
                // 添加考核逻辑
                toast({
                  title: "添加成功",
                  description: "已成功添加新的考核记录",
                })
                setIsAddAssessmentOpen(false)
              }}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              确认添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑考核对话框 */}
      <Dialog open={isEditAssessmentOpen} onOpenChange={setIsEditAssessmentOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-amber-500" />
              <span>编辑考核</span>
            </DialogTitle>
            <DialogDescription>修改考核记录，带 <span className="text-red-500">*</span> 为必填项</DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-2 bg-slate-100">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white data-[state=active]:text-amber-600">
                基本信息
              </TabsTrigger>
              <TabsTrigger value="assessment" className="data-[state=active]:bg-white data-[state=active]:text-amber-600">
                考核评估
              </TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-employee-name">
                    员工姓名 <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Input 
                      id="edit-employee-name" 
                      value={selectedAssessment?.employeeName || ""} 
                      disabled 
                      className="bg-slate-50"
                    />
                    <Badge variant="outline" className="shrink-0">
                      {selectedAssessment?.employeeId || ""}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-department">
                    部门 <span className="text-red-500">*</span>
                  </Label>
                  <Input 
                    id="edit-department" 
                    value={selectedAssessment?.department || ""} 
                    disabled 
                    className="bg-slate-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-position">
                    职位 <span className="text-red-500">*</span>
                  </Label>
                  <Input 
                    id="edit-position" 
                    value={selectedAssessment?.position || ""} 
                    disabled 
                    className="bg-slate-50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-assessment-period">
                    考核周期 <span className="text-red-500">*</span>
                  </Label>
                  <Select defaultValue={selectedAssessment?.assessmentPeriod}>
                    <SelectTrigger id="edit-assessment-period">
                      <SelectValue placeholder="选择考核周期" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2023年第一季度">2023年第一季度</SelectItem>
                      <SelectItem value="2022年第四季度">2022年第四季度</SelectItem>
                      <SelectItem value="2022年第三季度">2022年第三季度</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-assessor">
                    考核人 <span className="text-red-500">*</span>
                  </Label>
                  <Input 
                    id="edit-assessor" 
                    placeholder="请输入考核人"
                    defaultValue={selectedAssessment?.assessor || ""}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-assessment-date">考核日期</Label>
                  <Input 
                    id="edit-assessment-date" 
                    type="date"
                    defaultValue={selectedAssessment?.assessmentDate || ""}
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="assessment" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-performance-score">
                    绩效得分 <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex items-center space-x-4">
                    <Input 
                      id="edit-performance-score" 
                      type="number" 
                      min="0" 
                      max="100"
                      defaultValue={selectedAssessment?.performanceScore || ""}
                      className="w-32"
                    />
                    <Progress 
                      value={selectedAssessment?.performanceScore || 0} 
                      className="flex-1 h-2"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-rating">
                    评级 <span className="text-red-500">*</span>
                  </Label>
                  <Select defaultValue={selectedAssessment?.rating}>
                    <SelectTrigger id="edit-rating">
                      <SelectValue placeholder="选择评级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="卓越">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-2 text-yellow-500 fill-yellow-500" />
                          卓越
                        </div>
                      </SelectItem>
                      <SelectItem value="优秀">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-2 text-green-500 fill-green-500" />
                          优秀
                        </div>
                      </SelectItem>
                      <SelectItem value="良好">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-2 text-blue-500 fill-blue-500" />
                          良好
                        </div>
                      </SelectItem>
                      <SelectItem value="一般">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-2 text-gray-500" />
                          一般
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-assessment-comments">评语</Label>
                  <Textarea 
                    id="edit-assessment-comments" 
                    placeholder="请输入考核评语"
                    defaultValue={selectedAssessment?.comments || ""}
                    className="min-h-[100px]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-improvement-plan">改进计划</Label>
                  <Textarea 
                    id="edit-improvement-plan" 
                    placeholder="请输入改进计划"
                    defaultValue={selectedAssessment?.improvementPlan || ""}
                    className="min-h-[100px]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-status">
                    考核状态 <span className="text-red-500">*</span>
                  </Label>
                  <Select defaultValue={selectedAssessment?.status}>
                    <SelectTrigger id="edit-status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="已完成">
                        <div className="flex items-center">
                          <Badge variant="default" className="bg-green-100 text-green-700">已完成</Badge>
                        </div>
                      </SelectItem>
                      <SelectItem value="进行中">
                        <div className="flex items-center">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700">进行中</Badge>
                        </div>
                      </SelectItem>
                      <SelectItem value="未开始">
                        <div className="flex items-center">
                          <Badge variant="secondary" className="bg-slate-100 text-slate-700">未开始</Badge>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsEditAssessmentOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              onClick={() => {
                // 更新考核逻辑
                toast({
                  title: "更新成功",
                  description: "已成功更新考核记录",
                })
                setIsEditAssessmentOpen(false)
              }}
              className="bg-amber-500 hover:bg-amber-600 text-white"
            >
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>确认删除</span>
            </DialogTitle>
            <DialogDescription>
              您确定要删除该考核记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-500" />
              考核详情
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[500px] w-full rounded-md border p-4">
            {selectedAssessment && (
              <div className="space-y-6">
                <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                  <div className="rounded-full bg-blue-100 p-3">
                    <UserCheck className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedAssessment.employeeName}</h3>
                    <p className="text-sm text-muted-foreground">{selectedAssessment.department} - {selectedAssessment.position}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>考核周期</Label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAssessment.assessmentPeriod}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>考核人</Label>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAssessment.assessor}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>绩效得分</Label>
                  <div className="p-4 bg-slate-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl font-bold text-blue-600">
                        {selectedAssessment.performanceScore}
                      </span>
                      <Badge
                        variant="default"
                        className={
                          selectedAssessment.rating === "卓越"
                            ? "bg-yellow-100 text-yellow-700"
                            : selectedAssessment.rating === "优秀"
                            ? "bg-green-100 text-green-700"
                            : selectedAssessment.rating === "良好"
                            ? "bg-blue-100 text-blue-700"
                            : "bg-slate-100 text-slate-700"
                        }
                      >
                        {selectedAssessment.rating}
                      </Badge>
                    </div>
                    <Progress value={selectedAssessment.performanceScore} className="h-2" />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>评语</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">这是一段评语内容...</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>改进计划</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">这是一段改进计划内容...</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  )
}

