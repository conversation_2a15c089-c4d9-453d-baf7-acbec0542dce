"use client"

import { useState } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  ArrowRight,
  CheckSquare,
  XCircle,
  ClockIcon,
  FileText,
  AlertTriangle,
  BarChart,
  RefreshCw,
  Printer,
  UserCheck,
  Calendar as CalendarIcon,
  Clock,
  AlertCircle,
  CheckCircle,
  XSquare,
  Edit,
  Trash2,
  ChevronRight,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Info,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select as UISelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  <PERSON>alog<PERSON>es<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { toast } from "@/components/ui/use-toast"
import * as XLSX from 'xlsx-js-style'
import axios from 'axios'
import dayjs from 'dayjs'
import { Card as AntdCard, Table as AntdTable, Button as AntdButton, Space, Tag, Modal, Form, DatePicker, Upload, message, Row, Col, Statistic, Tooltip, Popconfirm, Progress as AntdProgress, Select as AntSelect } from "antd"
import { UploadOutlined } from "@ant-design/icons"
import type { SelectProps } from "antd"
import TextArea from "antd/es/input/TextArea"
import { Drawer } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, FileTextOutlined, ClockCircleOutlined, TeamOutlined, CheckCircleOutlined, ExclamationCircleOutlined, SearchOutlined, FilterOutlined, ReloadOutlined, ExportOutlined, PrinterOutlined, EyeOutlined, CommentOutlined, BarChartOutlined } from '@ant-design/icons'
import { Key } from 'react'

const { Option } = AntSelect;

interface Approval {
  id: string;
  title: string;
  type: string;
  project: string;
  applicant: string;
  department: string;
  submitDate: string;
  deadline: string;
  status: string;
  priority: string;
  description: string;
  progress: number;
  attachments: string[];
  approvers: Array<{
    name: string;
    role: string;
    status: string;
    date: string;
    comments?: string;
  }>;
}

// 审批类型选项
const approvalTypeOptions = [
  { value: "预算调整", label: "预算调整" },
  { value: "进度调整", label: "进度调整" },
  { value: "方案变更", label: "方案变更" },
  { value: "资源调整", label: "资源调整" },
  { value: "范围调整", label: "范围调整" }
] as const;

// 优先级选项
const priorityOptions = [
  { value: "高", label: "高" },
  { value: "中", label: "中" },
  { value: "低", label: "低" }
] as const;

export function ProjectApproval() {
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false)
  const [selectedApproval, setSelectedApproval] = useState<Approval | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [selectedRows, setSelectedRows] = useState<Key[]>([])
  const [currentTab, setCurrentTab] = useState("all")
  const [isNewApprovalDialogOpen, setIsNewApprovalDialogOpen] = useState(false)
  const [approvals, setApprovals] = useState<Approval[]>([
    {
      id: "1",
      title: "矿区A3开发项目预算调整",
      type: "预算调整",
      project: "矿区A3开发项目",
      applicant: "张三",
      department: "开发部",
      submitDate: "2025-03-10",
      deadline: "2025-03-20",
      status: "待审批",
      priority: "高",
      description: "由于原材料价格上涨，申请增加项目预算50万元",
      progress: 0,
      attachments: ["预算调整申请.pdf", "成本分析报告.xlsx"],
      approvers: [
        { name: "李四", role: "部门经理", status: "已通过", date: "2025-03-11" },
        { name: "王五", role: "财务总监", status: "待审批", date: "" },
      ],
    },
    {
      id: "2",
      title: "设备更新计划进度调整",
      type: "进度调整",
      project: "设备更新计划",
      applicant: "李四",
      department: "设备部",
      submitDate: "2025-03-08",
      deadline: "2025-03-18",
      status: "审批中",
      priority: "中",
      description: "因设备供应商延迟交付，申请将项目结束日期延后15天",
      progress: 0,
      attachments: [],
      approvers: [],
    },
    {
      id: "3",
      title: "安全系统升级方案变更",
      type: "方案变更",
      project: "安全系统升级",
      applicant: "王五",
      department: "安全部",
      submitDate: "2025-03-05",
      deadline: "2025-03-15",
      status: "已通过",
      priority: "高",
      description: "申请变更系统升级方案，采用更先进的技术架构",
      progress: 100,
      attachments: [],
      approvers: [],
    },
    {
      id: "4",
      title: "新矿区勘探设备增加",
      type: "资源调整",
      project: "新矿区勘探",
      applicant: "赵六",
      department: "勘探部",
      submitDate: "2025-03-12",
      deadline: "2025-03-22",
      status: "待审批",
      priority: "中",
      description: "申请增加2台勘探设备，以加快勘探进度",
      progress: 0,
      attachments: [],
      approvers: [],
    },
    {
      id: "5",
      title: "环保设施改造范围扩大",
      type: "范围调整",
      project: "环保设施改造",
      applicant: "钱七",
      department: "环保部",
      submitDate: "2025-03-11",
      deadline: "2025-03-21",
      status: "已拒绝",
      priority: "低",
      description: "申请扩大环保设施改造范围，增加废水处理系统升级",
      progress: 0,
      attachments: [],
      approvers: [],
    },
  ])
  const [form] = Form.useForm()
  const [newApprovalForm] = Form.useForm()

  // 统计信息
  const stats = {
    total: approvals.length,
    pending: approvals.filter(a => a.status === "待审批").length,
    inProgress: approvals.filter(a => a.status === "审批中").length,
    approved: approvals.filter(a => a.status === "已通过").length,
    rejected: approvals.filter(a => a.status === "已拒绝").length,
    urgent: approvals.filter(a => a.priority === "高").length,
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待审批":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-300">
            <Clock className="w-3 h-3 mr-1" />
            待审批
          </Badge>
        )
      case "审批中":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-300">
            <ArrowRight className="w-3 h-3 mr-1" />
            审批中
          </Badge>
        )
      case "已通过":
        return (
          <Badge className="bg-green-50 text-green-600 border-green-300">
            <CheckSquare className="w-3 h-3 mr-1" />
            已通过
          </Badge>
        )
      case "已拒绝":
        return (
          <Badge className="bg-red-50 text-red-600 border-red-300">
            <XCircle className="w-3 h-3 mr-1" />
            已拒绝
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取优先级对应的徽章样式
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "高":
        return (
          <Badge className="bg-red-50 text-red-600 border-red-300">
            <AlertTriangle className="w-3 h-3 mr-1" />
            高
          </Badge>
        )
      case "中":
        return (
          <Badge className="bg-yellow-50 text-yellow-600 border-yellow-300">
            <AlertCircle className="w-3 h-3 mr-1" />
            中
          </Badge>
        )
      case "低":
        return (
          <Badge className="bg-blue-50 text-blue-600 border-blue-300">
            <Info className="w-3 h-3 mr-1" />
            低
          </Badge>
        )
      default:
        return <Badge>{priority}</Badge>
    }
  }

  // 查看审批详情
  const handleView = (approval: Approval) => {
    setSelectedApproval(approval);
    setIsReviewDialogOpen(true);
  };

  // 新增审批
  const handleAdd = () => {
    setSelectedApproval(null);
    newApprovalForm.resetFields();
    setIsNewApprovalDialogOpen(true);
  };

  // 编辑审批
  const handleEdit = (approval: Approval) => {
    setSelectedApproval(approval);
    newApprovalForm.setFieldsValue({
      ...approval,
      submitDate: dayjs(approval.submitDate),
      deadline: dayjs(approval.deadline),
    });
    setIsNewApprovalDialogOpen(true);
  };

  // 删除审批
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个审批吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里可以添加调用删除 API 的代码
          const newApprovals = approvals.filter(approval => approval.id !== id);
          setApprovals(newApprovals);
          message.success('审批已删除');
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRows.length} 个审批吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里可以添加调用批量删除 API 的代码
          const newApprovals = approvals.filter(approval => !selectedRows.includes(approval.id));
          setApprovals(newApprovals);
          setSelectedRows([]);
          message.success(`已删除 ${selectedRows.length} 个审批`);
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  };

  // 处理表单提交
  const handleModalOk = () => {
    newApprovalForm.validateFields().then(async values => {
      try {
        const formattedValues = {
          ...values,
          submitDate: values.submitDate ? dayjs(values.submitDate).format('YYYY-MM-DD') : '',
          deadline: values.deadline ? dayjs(values.deadline).format('YYYY-MM-DD') : '',
        };

        if (selectedApproval) {
          // 编辑现有审批
          const updatedApprovals = approvals.map(approval =>
            approval.id === selectedApproval.id
              ? {
                  ...approval,
                  ...formattedValues,
                  updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                }
              : approval
          );
          setApprovals(updatedApprovals);
          message.success('审批已更新');
        } else {
          // 添加新审批
          const newApproval: Approval = {
            id: Date.now().toString(),
            ...formattedValues,
            progress: 0,
            attachments: [],
            approvers: [],
            createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          };
          setApprovals([...approvals, newApproval]);
          message.success('审批已创建');
        }
        setIsNewApprovalDialogOpen(false);
        setSelectedApproval(null);
        newApprovalForm.resetFields();
      } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败');
      }
    });
  };

  // 处理审批状态更新
  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      // 这里可以添加调用更新状态 API 的代码
      const updatedApprovals = approvals.map(approval =>
        approval.id === id
          ? {
              ...approval,
              status,
              updatedAt: '2025-03-15 14:30:00',
            }
          : approval
      );
      setApprovals(updatedApprovals);
      message.success(`状态已更新为: ${status}`);
    } catch (error) {
      console.error('状态更新失败:', error);
      message.error('状态更新失败');
    }
  };

  // 处理批量更新状态
  const handleBatchUpdateStatus = async (status: string) => {
    try {
      // 这里可以添加调用批量更新状态 API 的代码
      const updatedApprovals = approvals.map(approval =>
        selectedRows.includes(approval.id)
          ? {
              ...approval,
              status,
              updatedAt: '2025-03-15 14:30:00',
            }
          : approval
      );
      setApprovals(updatedApprovals);
      setSelectedRows([]);
      message.success(`已将 ${selectedRows.length} 个审批的状态更新为: ${status}`);
    } catch (error) {
      console.error('批量更新状态失败:', error);
      message.error('批量更新状态失败');
    }
  };

  // 处理导出
  const handleExport = () => {
    try {
      // 准备导出数据
      const exportData = approvals.map(approval => ({
        '审批标题': approval.title,
        '项目名称': approval.project,
        '审批类型': approval.type,
        '申请人': approval.applicant,
        '所属部门': approval.department,
        '提交日期': approval.submitDate,
        '截止日期': approval.deadline,
        '状态': approval.status,
        '优先级': approval.priority,
        '申请说明': approval.description,
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 审批标题
        { wch: 20 }, // 项目名称
        { wch: 15 }, // 审批类型
        { wch: 10 }, // 申请人
        { wch: 15 }, // 所属部门
        { wch: 15 }, // 提交日期
        { wch: 15 }, // 截止日期
        { wch: 10 }, // 状态
        { wch: 10 }, // 优先级
        { wch: 50 }, // 申请说明
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '审批列表');

      // 导出文件
      // 使用2025年的固定日期而不是当前日期
      XLSX.writeFile(wb, `审批列表_2025-03-15.xlsx`);
      toast({
        title: "导出数据",
        description: "导出成功",
      })
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "导出失败",
      })
    }
  }

  // 处理打印
  const handlePrint = () => {
    const printContent = document.createElement('div');
    printContent.innerHTML = `
      <style>
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .header { margin-bottom: 20px; }
        .footer { margin-top: 20px; text-align: right; }
        @media print {
          button { display: none; }
        }
      </style>
      <div class="header">
        <h2>工程审批列表</h2>
        <p>导出时间：2025-03-15 10:30:45</p>
        </div>
      <table>
        <thead>
          <tr>
            <th>审批标题</th>
            <th>项目名称</th>
            <th>审批类型</th>
            <th>申请人</th>
            <th>状态</th>
            <th>优先级</th>
            <th>提交日期</th>
            <th>截止日期</th>
          </tr>
        </thead>
        <tbody>
          ${approvals.map(approval => `
            <tr>
              <td>${approval.title}</td>
              <td>${approval.project}</td>
              <td>${approval.type}</td>
              <td>${approval.applicant}</td>
              <td>${approval.status}</td>
              <td>${approval.priority}</td>
              <td>${approval.submitDate}</td>
              <td>${approval.deadline}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      <div class="footer">
        <p>总计：${approvals.length} 条记录</p>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow?.document.write(printContent.innerHTML);
    printWindow?.document.close();
    printWindow?.print();
  }

  // 渲染审批详情抽屉
  const renderApprovalDrawer = () => (
    <Drawer
      title={
        <Space>
          <FileTextOutlined />
          审批详情
        </Space>
      }
      placement="right"
      onClose={() => setIsReviewDialogOpen(false)}
      open={isReviewDialogOpen}
      width={600}
    >
      {selectedApproval && (
        <div>
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-2">基本信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">审批标题：</span>
                {selectedApproval.title}
            </div>
              <div>
                <span className="font-medium">项目名称：</span>
                {selectedApproval.project}
            </div>
              <div>
                <span className="font-medium">申请人：</span>
                {selectedApproval.applicant}
            </div>
              <div>
                <span className="font-medium">提交日期：</span>
                {selectedApproval.submitDate}
            </div>
              <div>
                <span className="font-medium">截止日期：</span>
                {selectedApproval.deadline}
      </div>
            <div>
                <span className="font-medium">状态：</span>
                {getStatusBadge(selectedApproval.status)}
            </div>
              <div>
                <span className="font-medium">优先级：</span>
                {getPriorityBadge(selectedApproval.priority)}
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-bold mb-2">审批描述</h3>
            <p>{selectedApproval.description}</p>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-bold mb-2">进度</h3>
            <AntdProgress percent={selectedApproval.progress} />
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-bold mb-2">附件</h3>
            <ul className="space-y-2">
              {selectedApproval.attachments.map((attachment, index) => (
                <li key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileTextOutlined className="mr-2" />
                    <span>{attachment}</span>
                  </div>
                  <Space>
                    <AntdButton
                      type="link"
                      onClick={() => {
                        message.success(`开始下载: ${attachment}`);
                      }}
                    >
                      下载
                    </AntdButton>
                    <AntdButton
                      type="link"
                      danger
                      onClick={() => {
                        const newAttachments = selectedApproval.attachments.filter((_, i) => i !== index);
                        setApprovals(approvals.map(approval =>
                          approval.id === selectedApproval.id
                            ? { ...approval, attachments: newAttachments }
                            : approval
                        ));
                        message.success('附件已删除');
                      }}
                    >
                      删除
                    </AntdButton>
                  </Space>
                </li>
              ))}
            </ul>
                </div>

                  <div>
            <h3 className="text-lg font-bold mb-2">审批人</h3>
            <ul className="space-y-2">
              {selectedApproval.approvers.map((approver, index) => (
                <li key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TeamOutlined className="mr-2" />
                    <span>{approver.name} - {approver.role}</span>
                  </div>
                  <span>{approver.status} - {approver.date}</span>
                </li>
              ))}
            </ul>
                  </div>
                  </div>
      )}
    </Drawer>
  );

  // 渲染表格
  const renderTable = () => (
    <AntdTable
      rowSelection={{
        selectedRowKeys: selectedRows,
        onChange: (selectedKeys: Key[]) => setSelectedRows(selectedKeys),
      }}
      columns={[
        {
          title: '审批标题',
          dataIndex: 'title',
          key: 'title',
          render: (text: string, record: Approval) => (
            <Tooltip title="点击查看详情">
              <a onClick={() => handleView(record)}>{text}</a>
            </Tooltip>
          ),
        },
        {
          title: '项目名称',
          dataIndex: 'project',
          key: 'project',
        },
        {
          title: '申请人',
          dataIndex: 'applicant',
          key: 'applicant',
          render: (text: string) => (
            <Space>
              <TeamOutlined />
              {text}
            </Space>
          ),
        },
        {
          title: '提交日期',
          dataIndex: 'submitDate',
          key: 'submitDate',
          render: (date: string) => (
            <Space>
              <ClockCircleOutlined />
              {date}
            </Space>
          ),
        },
        {
          title: '截止日期',
          dataIndex: 'deadline',
          key: 'deadline',
          render: (date: string) => (
            <Space>
              <ClockCircleOutlined />
              {date}
            </Space>
          ),
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          render: (status: string) => getStatusBadge(status),
        },
        {
          title: '优先级',
          dataIndex: 'priority',
          key: 'priority',
          render: (priority: string) => getPriorityBadge(priority),
        },
        {
          title: '操作',
          key: 'action',
          render: (_: any, record: Approval) => (
            <Space>
              <AntdButton
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                编辑
              </AntdButton>
              <AntdButton
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record.id)}
              >
                删除
              </AntdButton>
            </Space>
          ),
        },
      ]}
      dataSource={approvals}
      rowKey="id"
    />
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-yellow-100 p-3">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">待审批</p>
                  <h3 className="text-2xl font-bold">{stats.pending}</h3>
                </div>
              </div>
              <AntdProgress type="circle" percent={stats.pending / stats.total * 100} width={60} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <ArrowRight className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">审批中</p>
                  <h3 className="text-2xl font-bold">{stats.inProgress}</h3>
                </div>
              </div>
              <AntdProgress type="circle" percent={stats.inProgress / stats.total * 100} width={60} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <CheckSquare className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已通过</p>
                  <h3 className="text-2xl font-bold">{stats.approved}</h3>
                </div>
              </div>
              <AntdProgress type="circle" percent={stats.approved / stats.total * 100} width={60} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-red-100 p-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">紧急审批</p>
                  <h3 className="text-2xl font-bold">{stats.urgent}</h3>
                </div>
              </div>
              <AntdProgress type="circle" percent={stats.urgent / stats.total * 100} width={60} />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">流程审批</h2>
        <div className="flex items-center gap-2">
          {selectedRows.length > 0 ? (
            <>
              <AntdButton onClick={handleBatchDelete} danger>
                <DeleteOutlined />
                批量删除
              </AntdButton>
              <AntdButton onClick={() => handleBatchUpdateStatus('已通过')} type="primary">
                <CheckCircleOutlined />
                批量通过
              </AntdButton>
              <AntdButton onClick={() => handleBatchUpdateStatus('已拒绝')} danger>
                <ExclamationCircleOutlined />
                批量拒绝
              </AntdButton>
            </>
          ) : (
            <>
              <Button variant="outline" size="sm" onClick={handleAdd}>
                <PlusOutlined className="h-4 w-4 mr-2" />
                新建审批
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport}>
                <ExportOutlined className="h-4 w-4 mr-2" />
                导出
              </Button>
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <PrinterOutlined className="h-4 w-4 mr-2" />
                打印
              </Button>
            </>
          )}
        </div>
      </div>

      {renderTable()}
      {renderApprovalDrawer()}

      <Modal
        title={selectedApproval ? '编辑审批' : '新建审批'}
        open={isNewApprovalDialogOpen}
        onOk={handleModalOk}
        onCancel={() => {
          setIsNewApprovalDialogOpen(false);
          newApprovalForm.resetFields();
        }}
        width={800}
      >
        <Form
          form={newApprovalForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="审批标题"
            rules={[{ required: true, message: '请输入审批标题' }]}
          >
            <Input placeholder="请输入审批标题" />
          </Form.Item>
          <Form.Item
            name="project"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人" />
          </Form.Item>
          <Form.Item
            name="submitDate"
            label="提交日期"
            rules={[{ required: true, message: '请选择提交日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="deadline"
            label="截止日期"
            rules={[{ required: true, message: '请选择截止日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <AntSelect>
              <Option value="待审批">待审批</Option>
              <Option value="审批中">审批中</Option>
              <Option value="已通过">已通过</Option>
              <Option value="已拒绝">已拒绝</Option>
            </AntSelect>
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <AntSelect>
              <Option value="高">高</Option>
              <Option value="中">中</Option>
              <Option value="低">低</Option>
            </AntSelect>
          </Form.Item>
          <Form.Item
            name="description"
            label="审批描述"
            rules={[{ required: true, message: '请输入审批描述' }]}
          >
            <TextArea rows={4} placeholder="请输入审批描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

