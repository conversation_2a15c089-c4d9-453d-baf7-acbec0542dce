/**
 * 邮件管理组件 - 现代化邮件管理界面
 * 
 * 主要功能:
 * 1. 邮件查看: 支持卡片视图和表格视图两种模式
 * 2. 邮件管理: 包括新建、回复、转发、删除、归档等基本功能
 * 3. 邮件过滤: 支持按文件夹、标签、优先级等多种条件过滤
 * 4. 邮件排序: 支持按日期、主题、发件人、优先级等多种方式排序
 * 5. 批量操作: 支持批量标记为已读、添加星标、删除等批量操作
 * 
 * 界面结构:
 * - 头部: 搜索栏和功能按钮
 * - 侧边栏: 邮件文件夹、标签和分类导航
 * - 主内容区: 邮件列表(卡片/表格视图)
 * - 邮件详情: 查看邮件详细内容
 * 
 * 技术实现:
 * - 使用React状态管理邮件数据
 * - 响应式布局适配不同设备
 * - 组件化设计便于维护和扩展
 * - 使用Shadcn UI组件库构建美观的界面
 *
 * 当前状态: 基本功能已实现，但存在部分类型错误，布局优化中
 */

"use client"

import { useState, useEffect, useMemo } from "react"
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash, 
  Search, 
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  Building2,
  Briefcase,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Mail,
  Send,
  Inbox,
  Archive,
  Star,
  Paperclip,
  Save,
  ArrowRight,
  Reply,
  Forward,
  Filter,
  Bell,
  Tag,
  MailQuestion,
  Settings,
  RotateCw,
  Loader2,
  Download,
  Bookmark,
  Copy,
  BarChart,
  Folder,
  X,
  BarChart2,
  TrendingUp,
  TrendingDown,
  ChevronDown,
  ChevronUp,
  Eye
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
  SheetFooter
} from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { useTheme } from "next-themes"

interface MailRecord {
  id: string
  subject: string
  content: string
  sender: string
  senderAvatar: string
  email: string
  recipients: string[]
  cc: string[]
  bcc: string[]
  sendDate: string
  receivedDate: string
  isRead: boolean
  isStarred: boolean
  isReplied: boolean
  replyCount?: number
  isForwarded: boolean
  priority: '高' | '中' | '低'
  status: string
  folder: string
  tags: string[]
  attachments: string[]
  category: string
  isReminder?: boolean
  reminderDate?: string
  isScheduled?: boolean
  scheduledDate?: string
  relatedMails?: string[]
}

interface Tag {
  id: string
  name: string
  color: string
}

interface MailFolder {
  id: string
  name: string
  icon: React.ReactNode
  count: number
}

interface MailTemplate {
  id: string
  name: string
  subject: string
  content: string
  category: string
}

interface MailContact {
  id: string
  name: string
  email: string
  department?: string
  avatar?: string
  isFavorite: boolean
  group?: string
}

interface MailStatistics {
  total: number
  unread: number
  starred: number
  scheduled: number
  sent: number
  drafts: number
  archived: number
  deleted: number
  byCategory: Record<string, number>
}

export function MailManagement() {
  const { toast } = useToast()
  const { theme } = useTheme()
  
  // 初始邮件记录数据
  const initialRecords: MailRecord[] = [
    {
      id: "1",
      subject: "2024年第一季度工作总结会议通知",
      sender: "张三",
      senderAvatar: "/avatars/zhang-san.png",
      email: "<EMAIL>",
      recipients: ["李四", "王五"],
      cc: ["赵六"],
      bcc: [],
      content: "请各位部门负责人准时参加2024年第一季度工作总结会议...\n\n会议时间：2024年4月5日 14:00-16:00\n会议地点：公司大会议室\n\n请各部门准备好工作汇报材料，提前10分钟到场。\n\n人力资源部",
      attachments: ["会议议程.docx"],
      sendDate: "2024-03-15 09:00",
      receivedDate: "2024-03-15 09:01",
      status: "已发送",
      priority: "高",
      category: "会议通知",
      isRead: true,
      isStarred: true,
      isReplied: false,
      isForwarded: false,
      folder: "收件箱",
      tags: ["重要", "会议"],
      replyCount: 3
    },
    {
      id: "2",
      subject: "安全生产管理制度修订征求意见",
      sender: "李四",
      senderAvatar: "/avatars/li-si.png",
      email: "<EMAIL>",
      recipients: ["安全部全体成员"],
      cc: ["王五"],
      bcc: [],
      content: "请各位同事对安全生产管理制度的修订提出宝贵意见...\n\n附件为最新修订的安全生产管理制度草案，请在3月20日前将修改意见发送至安全部邮箱。\n\n安全生产是公司的第一要务，感谢大家的积极参与。",
      attachments: ["管理制度修订稿.docx"],
      sendDate: "2024-03-14 14:30",
      receivedDate: "2024-03-14 14:31",
      status: "草稿",
      priority: "中",
      category: "制度文件",
      isRead: false,
      isStarred: false,
      isReplied: false,
      isForwarded: false,
      folder: "草稿箱",
      tags: ["安全", "制度"],
      isScheduled: true,
      scheduledDate: "2024-03-20 10:00"
    },
    {
      id: "3",
      subject: "设备维护计划表",
      sender: "赵六",
      senderAvatar: "/avatars/zhao-liu.png",
      email: "<EMAIL>",
      recipients: ["设备部全体成员"],
      cc: ["李四"],
      bcc: [],
      content: "附件是本月设备维护计划表，请各位同事按照计划执行...\n\n注意事项：\n1. 维护前请做好安全防护\n2. 按照操作规程进行维护\n3. 维护完成后填写设备维护记录\n4. 发现异常情况及时向部门主管报告",
      attachments: ["维护计划表.xlsx"],
      sendDate: "2024-03-13 10:00",
      receivedDate: "2024-03-13 10:01",
      status: "已发送",
      priority: "低",
      category: "工作计划",
      isRead: true,
      isStarred: false,
      isReplied: true,
      isForwarded: false,
      folder: "工作",
      tags: ["设备", "计划"]
    },
    {
      id: "4",
      subject: "员工培训计划",
      sender: "HR部门",
      senderAvatar: "/avatars/hr.png",
      email: "<EMAIL>",
      recipients: ["全体员工"],
      cc: [],
      bcc: [],
      content: "各位同事：\n\n为提升全员专业能力，公司将在4月组织一系列培训活动，具体安排见附件。请各部门协调好工作时间，确保员工参与培训。\n\n人力资源部",
      attachments: ["培训计划.pdf", "报名表.docx"],
      sendDate: "2024-03-12 11:30",
      receivedDate: "2024-03-12 11:31",
      status: "已发送",
      priority: "中",
      category: "培训通知",
      isRead: true,
      isStarred: false,
      isReplied: false,
      isForwarded: false,
      folder: "收件箱",
      tags: ["培训", "HR"],
      isReminder: true,
      reminderDate: "2024-03-29 09:00"
    },
    {
      id: "5",
      subject: "月度财务报表",
      sender: "财务部",
      senderAvatar: "/avatars/finance.png",
      email: "<EMAIL>",
      recipients: ["各部门负责人"],
      cc: ["总经理"],
      bcc: [],
      content: "各部门负责人：\n\n附件是2月份的财务报表和预算执行情况，请各部门分析预算执行偏差原因，并在本周五前提交说明。\n\n财务部",
      attachments: ["2月财务报表.xlsx", "预算执行情况.pdf"],
      sendDate: "2024-03-10 14:20",
      receivedDate: "2024-03-10 14:21",
      status: "已发送",
      priority: "高",
      category: "财务文件",
      isRead: false,
      isStarred: true,
      isReplied: false,
      isForwarded: false,
      folder: "财务",
      tags: ["财务", "月报"]
    }
  ]

  // 标签数据
  const initialTags: Tag[] = [
    { id: "1", name: "重要", color: "#ef4444" },
    { id: "2", name: "会议", color: "#3b82f6" },
    { id: "3", name: "培训", color: "#8b5cf6" },
    { id: "4", name: "安全", color: "#84cc16" },
    { id: "5", name: "财务", color: "#f59e0b" },
    { id: "6", name: "HR", color: "#ec4899" },
    { id: "7", name: "月报", color: "#14b8a6" },
    { id: "8", name: "制度", color: "#6366f1" }
  ]

  // 文件夹数据
  const initialFolders: MailFolder[] = [
    { id: "1", name: "工作", icon: <Briefcase className="h-4 w-4" />, count: 12 },
    { id: "2", name: "项目", icon: <FileText className="h-4 w-4" />, count: 5 },
    { id: "3", name: "客户", icon: <Users className="h-4 w-4" />, count: 8 },
    { id: "4", name: "财务", icon: <BarChart className="h-4 w-4" />, count: 3 }
  ]

  // 联系人数据
  const initialContacts: MailContact[] = [
    { id: "1", name: "张三", email: "<EMAIL>", department: "人力资源部", avatar: "/avatars/zhang-san.png", isFavorite: true, group: "同事" },
    { id: "2", name: "李四", email: "<EMAIL>", department: "安全部", avatar: "/avatars/li-si.png", isFavorite: true, group: "同事" },
    { id: "3", name: "王五", email: "<EMAIL>", department: "生产部", avatar: "/avatars/wang-wu.png", isFavorite: false, group: "同事" },
    { id: "4", name: "赵六", email: "<EMAIL>", department: "设备部", avatar: "/avatars/zhao-liu.png", isFavorite: false, group: "同事" },
    { id: "5", name: "HR部门", email: "<EMAIL>", department: "人力资源部", avatar: "/avatars/hr.png", isFavorite: true, group: "部门" },
    { id: "6", name: "财务部", email: "<EMAIL>", department: "财务部", avatar: "/avatars/finance.png", isFavorite: true, group: "部门" }
  ]

  // 邮件模板数据
  const initialTemplates: MailTemplate[] = [
    { id: "1", name: "会议通知", subject: "[会议通知] {{主题}}", content: "各位同事：\n\n兹定于 {{日期}} {{时间}} 在 {{地点}} 召开 {{主题}} 会议，请各相关部门准时参加。\n\n会议议程：\n1. {{议程1}}\n2. {{议程2}}\n\n请与会人员提前10分钟到达会议室。\n\n{{发起部门}}", category: "会议" },
    { id: "2", name: "工作汇报", subject: "{{时间段}}工作汇报", content: "尊敬的领导：\n\n{{时间段}}工作完成情况如下：\n\n一、主要工作成果\n1. {{成果1}}\n2. {{成果2}}\n\n二、存在问题\n1. {{问题1}}\n2. {{问题2}}\n\n三、下阶段工作计划\n1. {{计划1}}\n2. {{计划2}}\n\n此致\n敬礼\n\n{{汇报人}}\n{{日期}}", category: "工作" },
    { id: "3", name: "请假申请", subject: "请假申请", content: "尊敬的领导：\n\n因 {{请假原因}}，本人需请 {{假期类型}} {{请假天数}} 天，请假时间从 {{开始日期}} 至 {{结束日期}}。\n\n请假期间，本人负责的工作由 {{代理人}} 代为处理。\n\n恳请批准！\n\n{{申请人}}\n{{申请日期}}", category: "HR" }
  ]

  // 状态管理
  const [records, setRecords] = useState<MailRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isComposeDialogOpen, setIsComposeDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewMailOpen, setIsViewMailOpen] = useState(false)
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [isContactsDialogOpen, setIsContactsDialogOpen] = useState(false)
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false)
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("inbox")
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [selectedTag, setSelectedTag] = useState<string | null>(null)
  const [selectedMailIds, setSelectedMailIds] = useState<string[]>([])
  const [tags, setTags] = useState<Tag[]>(initialTags)
  const [folders, setFolders] = useState<MailFolder[]>(initialFolders)
  const [contacts, setContacts] = useState<MailContact[]>(initialContacts)
  const [templates, setTemplates] = useState<MailTemplate[]>(initialTemplates)
  const [showSidebar, setShowSidebar] = useState(true)
  const [mailViewMode, setMailViewMode] = useState<"card" | "table">("card")
  const [currentRecord, setCurrentRecord] = useState<MailRecord>({
    id: "",
    subject: "",
    sender: "",
    senderAvatar: "",
    email: "",
    recipients: [],
    cc: [],
    bcc: [],
    content: "",
    attachments: [],
    sendDate: "",
    receivedDate: "",
    status: "草稿",
    priority: "中",
    category: "",
    isRead: false,
    isStarred: false,
    isReplied: false,
    isForwarded: false,
    folder: "草稿箱",
    tags: []
  })
  const [currentTemplate, setCurrentTemplate] = useState<MailTemplate | null>(null)
  const [advancedFilter, setAdvancedFilter] = useState({
    categories: [] as string[],
    priority: [] as string[],
    hasAttachment: false,
    dateRange: {
      start: "",
      end: ""
    }
  })
  const [sortConfig, setSortConfig] = useState({
    key: "sendDate" as keyof MailRecord,
    direction: "desc" as "asc" | "desc"
  })
  const [contactSearchTerm, setContactSearchTerm] = useState("")
  const [selectedContacts, setSelectedContacts] = useState<string[]>([])
  const [isSendMenuOpen, setIsSendMenuOpen] = useState(false)

  // 自动计算统计数据
  const statistics: MailStatistics = useMemo(() => {
    const stats: MailStatistics = {
      total: records.length,
      unread: records.filter(r => !r.isRead).length,
      starred: records.filter(r => r.isStarred).length,
      scheduled: records.filter(r => r.isScheduled).length,
      sent: records.filter(r => r.status === "已发送").length,
      drafts: records.filter(r => r.status === "草稿").length,
      archived: records.filter(r => r.status === "已归档").length,
      deleted: records.filter(r => r.status === "已删除").length,
      byCategory: {}
    }

    // 计算按类别统计
    records.forEach(record => {
      if (record.category) {
        if (!stats.byCategory[record.category]) {
          stats.byCategory[record.category] = 0
        }
        stats.byCategory[record.category]++
      }
    })

    return stats
  }, [records])

  // 过滤记录
  const filteredRecords = useMemo(() => {
    // 先应用基础过滤条件
    let filtered = records.filter(record => {
      const matchesSearch = 
        record.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.sender.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (record.recipients.join(" ").toLowerCase().includes(searchTerm.toLowerCase()));
      
      let typeMatch = true;
      
      if (activeTab === "inbox") typeMatch = record.status === "已发送" && !(record.isScheduled || false);
      else if (activeTab === "draft") typeMatch = record.status === "草稿";
      else if (activeTab === "sent") typeMatch = record.status === "已发送";
      else if (activeTab === "starred") typeMatch = record.isStarred;
      else if (activeTab === "scheduled") typeMatch = (record.isScheduled || false);
      else if (activeTab === "archived") typeMatch = record.status === "已归档";
      else if (activeTab === "deleted") typeMatch = record.status === "已删除";
      
      // 检查文件夹过滤
      const folderMatch = selectedFolder ? record.folder === selectedFolder : true;
      
      // 检查标签过滤
      const tagMatch = selectedTag 
        ? record.tags?.includes(selectedTag) || false
        : true;
        
      return matchesSearch && typeMatch && folderMatch && tagMatch;
    });
    
    // 应用高级过滤
    filtered = filtered.filter(record => {
      // 类别过滤
      const categoryMatch = advancedFilter.categories.length === 0 || 
        advancedFilter.categories.includes(record.category);
      
      // 优先级过滤
      const priorityMatch = advancedFilter.priority.length === 0 || 
        advancedFilter.priority.includes(record.priority);
        
      // 附件过滤
      const attachmentMatch = !advancedFilter.hasAttachment || 
        (record.attachments.length > 0);
        
      // 日期范围过滤
      let dateMatch = true;
      if (advancedFilter.dateRange.start) {
        dateMatch = dateMatch && new Date(record.sendDate) >= new Date(advancedFilter.dateRange.start);
      }
      if (advancedFilter.dateRange.end) {
        dateMatch = dateMatch && new Date(record.sendDate) <= new Date(advancedFilter.dateRange.end);
      }
      
      return categoryMatch && priorityMatch && attachmentMatch && dateMatch;
    });
    
    // 排序
    return [...filtered].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (aValue === undefined || bValue === undefined) return 0;
      
      let compareResult = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        compareResult = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        compareResult = aValue - bValue;
      } else if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        compareResult = aValue === bValue ? 0 : aValue ? 1 : -1;
      } else {
        compareResult = String(aValue).localeCompare(String(bValue));
      }
      
      return sortConfig.direction === 'asc' ? compareResult : -compareResult;
    });
  }, [records, searchTerm, activeTab, selectedFolder, selectedTag, advancedFilter, sortConfig]);
  
  // 处理排序
  const handleSort = (key: keyof MailRecord) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };
  
  // 重置所有过滤器
  const handleResetFilters = () => {
    setSearchTerm("");
    setSelectedFolder(null);
    setSelectedTag(null);
    setAdvancedFilter({
      categories: [],
      priority: [],
      hasAttachment: false,
      dateRange: {
        start: "",
        end: ""
      }
    });
  };
  
  // 更新高级过滤器
  const updateAdvancedFilter = (
    field: keyof typeof advancedFilter, 
    value: any
  ) => {
    setAdvancedFilter(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // 更新日期范围
  const updateDateRange = (type: 'start' | 'end', value: string) => {
    setAdvancedFilter(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [type]: value
      }
    }));
  };
  
  // 切换类别过滤器
  const toggleCategoryFilter = (category: string) => {
    setAdvancedFilter(prev => {
      const categories = [...prev.categories];
      const index = categories.indexOf(category);
      if (index >= 0) {
        categories.splice(index, 1);
      } else {
        categories.push(category);
      }
      return {
        ...prev,
        categories
      };
    });
  };
  
  // 切换优先级过滤器
  const togglePriorityFilter = (priority: string) => {
    setAdvancedFilter(prev => {
      const priorities = [...prev.priority];
      const index = priorities.indexOf(priority);
      if (index >= 0) {
        priorities.splice(index, 1);
      } else {
        priorities.push(priority);
      }
      return {
        ...prev,
        priority: priorities
      };
    });
  };
  
  // 切换附件过滤器
  const toggleAttachmentFilter = () => {
    setAdvancedFilter(prev => ({
      ...prev,
      hasAttachment: !prev.hasAttachment
    }));
  };

  // 新建邮件
  const handleComposeMail = () => {
    if (!currentRecord.subject.trim()) {
      toast({
        title: "操作失败",
        description: "邮件主题不能为空",
        variant: "destructive",
      });
      return;
    }
    
    if (currentRecord.recipients.length === 0) {
      toast({
        title: "操作失败",
        description: "至少需要一位收件人",
        variant: "destructive",
      });
      return;
    }
    
    const newMail: MailRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      sendDate: new Date().toLocaleString(),
      status: "草稿",
      isRead: false,
      isStarred: false
    };
    
    setRecords(prev => [...prev, newMail]);
    resetCurrentRecord();
    setIsComposeDialogOpen(false);
    
    toast({
      title: "保存成功",
      description: "邮件已保存到草稿箱",
    });
  }

  // 发送邮件
  const handleSendMail = () => {
    if (!currentRecord.subject.trim()) {
      toast({
        title: "操作失败",
        description: "邮件主题不能为空",
        variant: "destructive",
      });
      return;
    }
    
    if (currentRecord.recipients.length === 0) {
      toast({
        title: "操作失败",
        description: "至少需要一位收件人",
        variant: "destructive",
      });
      return;
    }
    
    // 检查是定时发送还是立即发送
    if (currentRecord.isScheduled && currentRecord.scheduledDate) {
      const scheduledMail: MailRecord = {
        ...currentRecord,
        id: currentRecord.id || (records.length + 1).toString(),
        sendDate: new Date().toLocaleString(),
        status: "草稿",
        isScheduled: true
      };
      
    const updatedRecords = records.map((record) =>
        record.id === currentRecord.id ? scheduledMail : record
      );
      
      const foundExisting = records.some(r => r.id === currentRecord.id);
      
      setRecords(foundExisting ? updatedRecords : [...records, scheduledMail]);
      resetCurrentRecord();
      setIsComposeDialogOpen(false);
      
      toast({
        title: "定时发送设置成功",
        description: `邮件将在 ${scheduledMail.scheduledDate} 发送`,
      });
    } else {
      // 立即发送邮件
      const sentMail: MailRecord = {
        ...currentRecord,
        id: currentRecord.id || (records.length + 1).toString(),
        status: "已发送",
        sendDate: new Date().toLocaleString()
      };
      
      const updatedRecords = records.map((record) =>
        record.id === currentRecord.id ? sentMail : record
      );
      
      const foundExisting = records.some(r => r.id === currentRecord.id);
      
      setRecords(foundExisting ? updatedRecords : [...records, sentMail]);
      resetCurrentRecord();
      setIsComposeDialogOpen(false);
      
      toast({
        title: "发送成功",
        description: "邮件已成功发送",
      });
    }
  }

  // 重置当前邮件记录
  const resetCurrentRecord = () => {
    setCurrentRecord({
      id: "",
      subject: "",
      sender: "",
      senderAvatar: "",
      email: "",
      recipients: [],
      cc: [],
      bcc: [],
      content: "",
      attachments: [],
      sendDate: "",
      receivedDate: "",
      status: "草稿",
      priority: "中",
      category: "",
      isRead: false,
      isStarred: false,
      isReplied: false,
      isForwarded: false,
      folder: "草稿箱",
      tags: []
    });
  }

  // 编辑邮件
  const handleEditMail = () => {
    if (!currentRecord.subject.trim()) {
      toast({
        title: "操作失败",
        description: "邮件主题不能为空",
        variant: "destructive",
      });
      return;
    }
    
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    );
    
    setRecords(updatedRecords);
    resetCurrentRecord();
    setIsEditDialogOpen(false);
    
    toast({
      title: "编辑成功",
      description: "邮件已成功更新"
    });
  }

  // 删除邮件
  const handleDeleteMail = () => {
    const deletedMail: MailRecord = {
      ...currentRecord,
      status: "已删除"
    };
    
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? deletedMail : record
    );
    
    setRecords(updatedRecords);
    resetCurrentRecord();
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "删除成功",
      description: "邮件已移至回收站",
    });
  }
  
  // 永久删除邮件
  const handlePermanentDeleteMail = (id: string) => {
    const updatedRecords = records.filter(record => record.id !== id);
    setRecords(updatedRecords);
    
    toast({
      title: "永久删除成功",
      description: "邮件已永久删除",
    });
  }

  // 标记已读/未读
  const handleToggleRead = (record: MailRecord) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? { ...r, isRead: !r.isRead } : r
    );
    
    setRecords(updatedRecords);
    
    toast({
      title: record.isRead ? "标记为未读" : "标记为已读",
      description: `邮件 "${record.subject}" 已${record.isRead ? '标记为未读' : '标记为已读'}`,
    });
  }

  // 标记星标/取消星标
  const handleToggleStar = (record: MailRecord) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? { ...r, isStarred: !r.isStarred } : r
    );
    
    setRecords(updatedRecords);
    
    toast({
      title: record.isStarred ? "取消星标" : "标记星标",
      description: `邮件 "${record.subject}" 已${record.isStarred ? '取消星标' : '标记星标'}`,
    });
  }
  
  // 归档邮件
  const handleArchiveMail = (record: MailRecord) => {
    const archivedMail: MailRecord = {
      ...record,
      status: "已归档"
    };
    
    const updatedRecords = records.map((r) =>
      r.id === record.id ? archivedMail : r
    );
    
    setRecords(updatedRecords);
    
    toast({
      title: "归档成功",
      description: `邮件 "${record.subject}" 已归档`,
    });
  }
  
  // 恢复邮件
  const handleRestoreMail = (record: MailRecord) => {
    const restoredMail: MailRecord = {
      ...record,
      status: "已发送"
    };
    
    const updatedRecords = records.map((r) =>
      r.id === record.id ? restoredMail : r
    );
    
    setRecords(updatedRecords);
    
    toast({
      title: "恢复成功",
      description: `邮件 "${record.subject}" 已恢复`,
    });
  }
  
  // 添加标签
  const handleAddTag = (mailId: string, tagId: string) => {
    // 找到对应的标签名称
    const tag = tags.find(t => t.id === tagId);
    if (!tag) return;
    
    const updatedRecords = records.map((record) => {
      if (record.id === mailId) {
        const currentTags = record.tags || [];
        // 检查标签是否已存在
        if (!currentTags.includes(tag.name)) {
          return {
            ...record,
            tags: [...currentTags, tag.name]
          };
        }
      }
      return record;
    });
    
    setRecords(updatedRecords);
    
    toast({
      title: "添加标签成功",
      description: `已将标签 "${tag.name}" 添加到邮件`,
    });
  }
  
  // 移除标签
  const handleRemoveTag = (mailId: string, tagName: string) => {
    const updatedRecords = records.map((record) => {
      if (record.id === mailId && record.tags) {
        return {
          ...record,
          tags: record.tags.filter(t => t !== tagName)
        };
      }
      return record;
    });
    
    setRecords(updatedRecords);
    
    toast({
      title: "移除标签成功",
      description: `已从邮件中移除标签 "${tagName}"`,
    });
  }
  
  // 移动到文件夹
  const handleMoveToFolder = (mailId: string, folderId: string) => {
    // 找到对应的文件夹名称
    const folder = folders.find(f => f.id === folderId);
    if (!folder) return;
    
    const updatedRecords = records.map((record) => {
      if (record.id === mailId) {
        return {
          ...record,
          folder: folder.name
        };
      }
      return record;
    });
    
    setRecords(updatedRecords);
    
    toast({
      title: "移动成功",
      description: `邮件已移动到 "${folder.name}" 文件夹`,
    });
  }
  
  // 回复邮件
  const handleReplyMail = (record: MailRecord) => {
    const replySubject = !record.subject.startsWith("回复:") 
      ? `回复: ${record.subject}` 
      : record.subject;
      
    setCurrentRecord({
      id: "",
      subject: replySubject,
      sender: "",
      senderAvatar: "",
      email: "",
      recipients: [record.sender],
      cc: [],
      bcc: [],
      content: `\n\n--------------- 原始邮件 ---------------\n发件人: ${record.sender}\n日期: ${record.sendDate}\n主题: ${record.subject}\n\n${record.content}`,
      attachments: [],
      sendDate: "",
      receivedDate: "",
      status: "草稿",
      priority: record.priority as "高" | "中" | "低",
      category: record.category,
      isRead: false,
      isStarred: false,
      isReplied: true,
      isForwarded: false,
      folder: "草稿箱",
      tags: []
    });
    
    // 更新原邮件的回复状态
    const updatedRecords = records.map((r) =>
      r.id === record.id ? { ...r, isReplied: true } : r
    );
    
    setRecords(updatedRecords);
    
    // 打开邮件编辑对话框
    setIsComposeDialogOpen(true);
  }
  
  // 转发邮件
  const handleForwardMail = (record: MailRecord) => {
    const forwardSubject = !record.subject.startsWith("转发:") 
      ? `转发: ${record.subject}` 
      : record.subject;
      
    setCurrentRecord({
      id: "",
      subject: forwardSubject,
      sender: "",
      senderAvatar: "",
      email: "",
      recipients: [],
      cc: [],
      bcc: [],
      content: `\n\n--------------- 转发邮件 ---------------\n发件人: ${record.sender}\n日期: ${record.sendDate}\n主题: ${record.subject}\n收件人: ${record.recipients.join(", ")}\n\n${record.content}`,
      attachments: [...record.attachments],
      sendDate: "",
      receivedDate: "",
      status: "草稿",
      priority: record.priority as "高" | "中" | "低",
      category: record.category,
      isRead: false,
      isStarred: false,
      isReplied: false,
      isForwarded: true,
      folder: "草稿箱",
      tags: []
    });
    
    // 更新原邮件的转发状态
    const updatedRecords = records.map((r) =>
      r.id === record.id ? { ...r, isForwarded: true } : r
    );
    
    setRecords(updatedRecords);
    
    // 打开邮件编辑对话框
    setIsComposeDialogOpen(true);
  }
  
  // 使用模板创建邮件
  const handleUseTemplate = (template: MailTemplate) => {
    setCurrentRecord({
      ...currentRecord,
      subject: template.subject,
      content: template.content,
      category: template.category
    });
    
    setIsTemplateDialogOpen(false);
    
    toast({
      title: "应用模板成功",
      description: `已应用"${template.name}"模板`,
    });
  }
  
  // 保存邮件为模板
  const handleSaveAsTemplate = () => {
    if (!currentRecord.subject || !currentRecord.content) {
      toast({
        title: "保存失败",
        description: "邮件主题和内容不能为空",
        variant: "destructive",
      });
      return;
    }
    
    const templateName = prompt("请输入模板名称:", currentRecord.subject);
    if (!templateName) return;
    
    const newTemplate: MailTemplate = {
      id: (templates.length + 1).toString(),
      name: templateName,
      subject: currentRecord.subject,
      content: currentRecord.content,
      category: currentRecord.category || "其他"
    };
    
    setTemplates([...templates, newTemplate]);
    
    toast({
      title: "保存成功",
      description: `已将邮件保存为"${templateName}"模板`,
    });
  }
  
  // 创建新文件夹
  const handleCreateFolder = () => {
    const folderName = prompt("请输入文件夹名称:");
    if (!folderName || folderName.trim() === "") return;
    
    const existingFolder = folders.find(f => f.name === folderName);
    if (existingFolder) {
      toast({
        title: "创建失败",
        description: "文件夹名称已存在",
        variant: "destructive",
      });
      return;
    }
    
    const newFolder: MailFolder = {
      id: (folders.length + 1).toString(),
      name: folderName,
      icon: <Folder className="h-4 w-4" />,
      count: 0
    };
    
    setFolders([...folders, newFolder]);
    
    toast({
      title: "创建成功",
      description: `已创建"${folderName}"文件夹`,
    });
  }
  
  // 创建新标签
  const handleCreateTag = () => {
    const tagName = prompt("请输入标签名称:");
    if (!tagName || tagName.trim() === "") return;
    
    const existingTag = tags.find(t => t.name === tagName);
    if (existingTag) {
      toast({
        title: "创建失败",
        description: "标签名称已存在",
        variant: "destructive",
      });
      return;
    }
    
    // 随机颜色
    const colors = ["#ef4444", "#f97316", "#f59e0b", "#84cc16", "#10b981", "#06b6d4", "#3b82f6", "#8b5cf6", "#d946ef"];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    
    const newTag: Tag = {
      id: (tags.length + 1).toString(),
      name: tagName,
      color: randomColor
    };
    
    setTags([...tags, newTag]);
    
    toast({
      title: "创建成功",
      description: `已创建"${tagName}"标签`,
    });
  }
  
  // 设置定时发送
  const handleScheduleMail = (date: string) => {
    setCurrentRecord({
      ...currentRecord,
      isScheduled: true,
      scheduledDate: date
    });
    
    toast({
      title: "设置成功",
      description: `邮件将在 ${date} 发送`,
    });
  }
  
  // 取消定时发送
  const handleCancelSchedule = () => {
    setCurrentRecord({
      ...currentRecord,
      isScheduled: false,
      scheduledDate: undefined
    });
    
    toast({
      title: "已取消",
      description: "已取消定时发送设置",
    });
  }
  
  // 设置提醒
  const handleSetReminder = (date: string) => {
    setCurrentRecord({
      ...currentRecord,
      isReminder: true,
      reminderDate: date
    });
    
    toast({
      title: "设置成功",
      description: `将在 ${date} 提醒您处理该邮件`,
    });
  }
  
  // 取消提醒
  const handleCancelReminder = () => {
    setCurrentRecord({
      ...currentRecord,
      isReminder: false,
      reminderDate: undefined
    });
    
    toast({
      title: "已取消",
      description: "已取消邮件提醒",
    });
  }

  // 打开编辑对话框
  const openEditDialog = (record: MailRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: MailRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }
  
  // 打开查看邮件详情
  const openViewMail = (record: MailRecord) => {
    // 如果邮件未读，标记为已读
    if (!record.isRead) {
      const updatedRecords = records.map((r) =>
        r.id === record.id ? { ...r, isRead: true } : r
      );
      setRecords(updatedRecords);
    }
    
    setCurrentRecord(record);
    setIsViewMailOpen(true);
  }

  // 获取优先级对应的徽章样式
  const getPriorityBadge = (priority: string): "default" | "destructive" | "secondary" | "outline" => {
    switch (priority) {
      case '高':
        return "destructive";
      case '中':
        return "secondary";
      case '低':
        return "outline";
      default:
        return "outline";
    }
  };

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string): "default" | "destructive" | "secondary" | "outline" => {
    switch (status) {
      case '已读':
        return "secondary";
      case '未读':
        return "default";
      case '已回复':
        return "secondary";
      case '已转发':
        return "outline";
      case '已删除':
        return "destructive";
      case '已归档':
        return "outline";
      default:
        return "outline";
    }
  };

  // 获取标签徽章样式
  const getTagBadge = (tagName: string) => {
    const tag = tags.find(t => t.name === tagName);
    if (!tag) return null;
    
    return (
      <Badge 
        key={tag.id}
        className="mr-1 mb-1"
        style={{ 
          backgroundColor: `${tag.color}20`, 
          color: tag.color,
          borderColor: `${tag.color}40` 
        }}
        variant="outline"
      >
        {tag.name}
      </Badge>
    );
  }

  // 邮件卡片组件
  const MailCard = ({ mail, isSelected, onSelect, onOpen }: { 
    mail: MailRecord; 
    isSelected: boolean; 
    onSelect: (id: string) => void; 
    onOpen: (mail: MailRecord) => void; 
  }) => {
  return (
      <Card 
        className={`mb-2 cursor-pointer transition-all hover:shadow-md 
          ${isSelected ? "bg-primary/10" : ""} 
          ${!mail.isRead ? "bg-secondary/10" : "bg-background"}`}
        onClick={() => onOpen(mail)}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            <Checkbox 
              checked={isSelected}
              onClick={(e) => {
                e.stopPropagation();
                onSelect(mail.id);
              }}
              className="mt-1"
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={mail.senderAvatar} alt={mail.sender} />
                    <AvatarFallback>{mail.sender.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className={`text-sm font-semibold ${!mail.isRead ? "text-primary font-bold" : ""}`}>
                      {mail.sender}
                    </p>
                    <p className="text-xs text-muted-foreground">{mail.email}</p>
                </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <p className="text-xs text-muted-foreground">
                    {new Date(mail.sendDate).toLocaleDateString('zh-CN')}
                  </p>
                  
                  {mail.attachments.length > 0 && (
                    <Badge variant="outline" className="flex items-center gap-1 text-xs border-muted-foreground">
                      <Paperclip className="h-3 w-3" />
                    </Badge>
                  )}
                  
                  <Badge variant={getPriorityBadge(mail.priority)}>
                    {mail.priority}
                  </Badge>
                </div>
              </div>
              
              <div>
                <h4 className={`text-sm ${!mail.isRead ? "font-bold" : ""}`}>{mail.subject}</h4>
                <p className="text-xs text-muted-foreground line-clamp-2 mt-1">{mail.content}</p>
                </div>
                </div>
              </div>
        </CardContent>
      </Card>
    );
  };

  // 邮件表格组件
  const MailTable = ({ 
    mails, 
    selectedMails, 
    onSelectMail, 
    onOpenMail 
  }: { 
    mails: MailRecord[]; 
    selectedMails: string[]; 
    onSelectMail: (id: string) => void; 
    onOpenMail: (mail: MailRecord) => void; 
  }) => {
    const isSelected = (id: string) => selectedMails.includes(id);
    const allSelected = mails.length > 0 && selectedMails.length === mails.length;
    
    const handleSelectAll = () => {
      if (allSelected) {
        onSelectMail("clear-all");
      } else {
        mails.forEach(mail => {
          if (!selectedMails.includes(mail.id)) {
            onSelectMail(mail.id);
          }
        });
      }
    };

    return (
      <Table className="border">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">
              <Checkbox 
                checked={allSelected}
                onClick={handleSelectAll}
              />
            </TableHead>
            <TableHead className="w-[200px]">发件人</TableHead>
            <TableHead className="w-[300px]">主题</TableHead>
            <TableHead className="w-[100px]">优先级</TableHead>
            <TableHead className="w-[120px]">状态</TableHead>
            <TableHead className="w-[200px]">标签</TableHead>
            <TableHead className="w-[150px]">日期</TableHead>
            <TableHead className="w-[80px]">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mails.map((mail) => (
            <TableRow 
              key={mail.id} 
              className={`cursor-pointer ${!mail.isRead ? "font-medium bg-secondary/10" : ""} ${isSelected(mail.id) ? "bg-primary/10" : ""}`}
              onClick={() => onOpenMail(mail)}
            >
              <TableCell>
                <Checkbox 
                  checked={isSelected(mail.id)}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectMail(mail.id);
                  }}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={mail.senderAvatar} alt={mail.sender} />
                    <AvatarFallback>{mail.sender.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{mail.sender}</p>
                    <p className="text-xs text-muted-foreground">{mail.email}</p>
                </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {mail.attachments.length > 0 && <Paperclip className="h-3 w-3" />}
                  {mail.isStarred && <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />}
                  <span className="truncate max-w-[240px]">{mail.subject}</span>
              </div>
              </TableCell>
              <TableCell>
                <Badge variant={getPriorityBadge(mail.priority)}>
                  {mail.priority}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge variant={getStatusBadge(mail.status)}>
                  {mail.status}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1 max-w-[180px]">
                  {mail.tags?.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
              </div>
              </TableCell>
              <TableCell>{new Date(mail.sendDate).toLocaleDateString('zh-CN')}</TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleReplyMail(mail);
                    }}
                  >
                    <Reply className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleForwardMail(mail);
                    }}
                  >
                    <Forward className="h-4 w-4" />
                  </Button>
              </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex flex-col">
        <div className="p-4 border-b bg-card flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="icon" 
              className="md:hidden" 
              onClick={() => setShowSidebar(!showSidebar)}
            >
              <Filter className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-bold">
              邮件管理
              <Badge className="ml-2 bg-primary/20 text-primary">{statistics.total}</Badge>
            </h1>
          </div>

          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => setIsStatsDialogOpen(true)}
                  >
                    <BarChart className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>邮件统计</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => setIsContactsDialogOpen(true)}
                  >
                    <Users className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>通讯录</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => setIsTemplateDialogOpen(true)}
                  >
                    <FileText className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>邮件模板</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => setIsSettingsDialogOpen(true)}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>邮件设置</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
        <Dialog open={isComposeDialogOpen} onOpenChange={setIsComposeDialogOpen}>
          <DialogTrigger asChild>
                <Button className="gap-2 bg-primary px-4">
                  <Plus className="h-4 w-4" />
              新建邮件
            </Button>
          </DialogTrigger>
              <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden">
            <DialogHeader>
                  <DialogTitle>撰写新邮件</DialogTitle>
              <DialogDescription>
                    填写收件人、主题和内容后，可以选择保存为草稿或直接发送。
              </DialogDescription>
            </DialogHeader>
                
                <ScrollArea className="max-h-[60vh]">
                  <div className="space-y-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="recipients" className="text-right">
                        收件人
                      </Label>
                      <div className="col-span-3">
                        <div className="flex gap-2">
                  <Input
                    id="recipients"
                            placeholder="输入收件人，多个收件人用分号分隔"
                            value={currentRecord.recipients.join("; ")}
                            onChange={(e) => {
                              const recipientsArray = e.target.value.split(";").map(r => r.trim()).filter(Boolean);
                              setCurrentRecord({
                      ...currentRecord, 
                                recipients: recipientsArray
                              });
                            }}
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => setIsContactsDialogOpen(true)}
                            title="从通讯录选择"
                          >
                            <Users className="h-4 w-4" />
                          </Button>
                </div>
              </div>
                    </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="cc" className="text-right">
                        抄送
                      </Label>
                  <Input
                    id="cc"
                        placeholder="抄送"
                        value={currentRecord.cc.join("; ")}
                        onChange={(e) => {
                          const ccArray = e.target.value.split(";").map(cc => cc.trim()).filter(Boolean);
                          setCurrentRecord({
                      ...currentRecord, 
                            cc: ccArray
                          });
                        }}
                        className="col-span-3"
                  />
                </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="bcc" className="text-right">
                        密送
                      </Label>
                  <Input
                    id="bcc"
                        placeholder="密送"
                        value={currentRecord.bcc.join("; ")}
                        onChange={(e) => {
                          const bccArray = e.target.value.split(";").map(bcc => bcc.trim()).filter(Boolean);
                          setCurrentRecord({
                      ...currentRecord, 
                            bcc: bccArray
                          });
                        }}
                        className="col-span-3"
                  />
                </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="subject" className="text-right">
                        主题
                      </Label>
                      <div className="col-span-3">
                        <div className="flex gap-2">
                          <Input
                            id="subject"
                            placeholder="邮件主题"
                            value={currentRecord.subject}
                            onChange={(e) => {
                              setCurrentRecord({
                                ...currentRecord,
                                subject: e.target.value
                              });
                            }}
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => setIsTemplateDialogOpen(true)}
                            title="应用模板"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
              </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 items-start gap-4">
                      <Label htmlFor="content" className="text-right pt-2">
                        内容
                      </Label>
                      <div className="col-span-3 space-y-2">
                        <Textarea
                          id="content"
                          placeholder="输入邮件内容"
                          value={currentRecord.content}
                          onChange={(e) => {
                            setCurrentRecord({
                              ...currentRecord,
                              content: e.target.value
                            });
                          }}
                          rows={10}
                          className="resize-none"
                        />
                        
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <Label htmlFor="priority" className="text-sm">优先级</Label>
                  <Select
                    value={currentRecord.priority}
                              onValueChange={(value: "高" | "中" | "低") => {
                                setCurrentRecord({
                                  ...currentRecord,
                                  priority: value
                                });
                              }}
                            >
                              <SelectTrigger className="w-24">
                                <SelectValue placeholder="优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="中">中</SelectItem>
                      <SelectItem value="低">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                          
                          <div className="flex items-center gap-2">
                            <Label htmlFor="category" className="text-sm">分类</Label>
                  <Select
                    value={currentRecord.category}
                              onValueChange={(value) => {
                                setCurrentRecord({
                                  ...currentRecord,
                                  category: value
                                });
                              }}
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="会议通知">会议通知</SelectItem>
                      <SelectItem value="工作计划">工作计划</SelectItem>
                                <SelectItem value="培训通知">培训通知</SelectItem>
                                <SelectItem value="财务文件">财务文件</SelectItem>
                                <SelectItem value="制度文件">制度文件</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
                        
                        <div className="flex items-center gap-2">
                          <Label htmlFor="attachments" className="text-sm">附件</Label>
                          <div className="flex-1">
                            {currentRecord.attachments.length > 0 ? (
                              <div className="flex flex-wrap gap-2">
                                {currentRecord.attachments.map((attachment, index) => (
                                  <Badge key={index} variant="outline" className="py-1 px-2 gap-1">
                                    <Paperclip className="h-3 w-3" />
                                    {attachment}
                                    <X 
                                      className="h-3 w-3 ml-1 cursor-pointer" 
                                      onClick={() => {
                                        const updatedAttachments = [...currentRecord.attachments];
                                        updatedAttachments.splice(index, 1);
                                        setCurrentRecord({
                                          ...currentRecord,
                                          attachments: updatedAttachments
                                        });
                                      }}
                                    />
                                  </Badge>
                                ))}
              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground">暂无附件</p>
                            )}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            type="button"
                            onClick={() => {
                              const fileName = prompt("请输入附件名称");
                              if (fileName && fileName.trim()) {
                                setCurrentRecord({
                                  ...currentRecord,
                                  attachments: [...currentRecord.attachments, fileName.trim()]
                                });
                              }
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            添加附件
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <div className="text-right">
                        <Label htmlFor="scheduled" className="text-sm">
                          定时发送
                        </Label>
                      </div>
                      <div className="col-span-3 flex items-center gap-2">
                        <Switch
                          id="scheduled"
                          checked={currentRecord.isScheduled || false}
                          onCheckedChange={(checked) => {
                            setCurrentRecord({
                              ...currentRecord,
                              isScheduled: checked,
                              scheduledDate: checked ? currentRecord.scheduledDate || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16) : undefined
                            });
                          }}
                        />
                        
                        {currentRecord.isScheduled && (
                <Input
                            type="datetime-local"
                            value={currentRecord.scheduledDate}
                            onChange={(e) => {
                              setCurrentRecord({
                    ...currentRecord, 
                                scheduledDate: e.target.value
                              });
                            }}
                            className="w-auto"
                          />
                        )}
              </div>
            </div>
                  </div>
                </ScrollArea>
                
                <DialogFooter className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={handleSaveAsTemplate}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      保存为模板
              </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={handleComposeMail}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      存草稿
              </Button>
                    <Button
                      type="button"
                      onClick={handleSendMail}
                    >
                <Send className="h-4 w-4 mr-2" />
                      {currentRecord.isScheduled ? '定时发送' : '发送'}
              </Button>
                  </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
          </div>
      </div>

        <div className="p-4 border-b bg-gray-50/70 flex flex-wrap gap-2 items-center justify-between">
          <div className="relative w-full md:w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索邮件..."
              className="pl-9 pr-4"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetFilters}
              className="gap-2"
            >
              <RotateCw className="h-4 w-4" />
              重置筛选
            </Button>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <BarChart2 className="h-4 w-4" />
                  排序方式
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-0" align="end">
                <div className="p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start gap-2 mb-1"
                    onClick={() => handleSort('sendDate')}
                  >
                    <Calendar className="h-4 w-4" />
                    日期
                    {sortConfig.key === 'sendDate' && (
                      sortConfig.direction === 'asc' ?
                      <TrendingUp className="h-4 w-4 ml-auto" /> :
                      <TrendingDown className="h-4 w-4 ml-auto" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start gap-2 mb-1"
                    onClick={() => handleSort('subject')}
                  >
                    <FileText className="h-4 w-4" />
                    主题
                    {sortConfig.key === 'subject' && (
                      sortConfig.direction === 'asc' ?
                      <TrendingUp className="h-4 w-4 ml-auto" /> :
                      <TrendingDown className="h-4 w-4 ml-auto" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start gap-2 mb-1"
                    onClick={() => handleSort('sender')}
                  >
                    <User className="h-4 w-4" />
                    发件人
                    {sortConfig.key === 'sender' && (
                      sortConfig.direction === 'asc' ?
                      <TrendingUp className="h-4 w-4 ml-auto" /> :
                      <TrendingDown className="h-4 w-4 ml-auto" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start gap-2"
                    onClick={() => handleSort('priority')}
                  >
                    <AlertCircle className="h-4 w-4" />
                    优先级
                    {sortConfig.key === 'priority' && (
                      sortConfig.direction === 'asc' ?
                      <TrendingUp className="h-4 w-4 ml-auto" /> :
                      <TrendingDown className="h-4 w-4 ml-auto" />
                    )}
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Eye className="h-4 w-4" />
                  视图
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuGroup>
                  <DropdownMenuItem onClick={() => setMailViewMode("card")}>
                    <Mail className="h-4 w-4 mr-2" />
                    卡片视图
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setMailViewMode("table")}>
              <FileText className="h-4 w-4 mr-2" />
                    表格视图
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleCreateFolder}>
                  <Folder className="h-4 w-4 mr-2" />
                  创建文件夹
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCreateTag}>
                  <Tag className="h-4 w-4 mr-2" />
                  创建标签
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
      </div>

        {/* 活动过滤器显示 */}
        {(selectedFolder || selectedTag) && (
          <div className="p-2 border-b bg-blue-50/50 flex flex-wrap gap-2">
            {selectedFolder && (
              <Badge variant="outline" className="bg-white flex items-center gap-1 pl-1">
                <div className="p-1 rounded-sm bg-blue-100">
                  <Folder className="h-3 w-3 text-blue-600" />
                </div>
                <span className="px-1">{selectedFolder}</span>
                <X 
                  className="h-3 w-3 ml-1 cursor-pointer" 
                  onClick={() => setSelectedFolder(null)}
                />
              </Badge>
            )}
            
            {selectedTag && (
              <Badge variant="outline" className="bg-white flex items-center gap-1 pl-1">
                <div className="p-1 rounded-sm bg-green-100">
                  <Tag className="h-3 w-3 text-green-600" />
                </div>
                <span className="px-1">{
                  tags.find(t => t.id === selectedTag)?.name || 
                  tags.find(t => t.name === selectedTag)?.name || 
                  selectedTag
                }</span>
                <X 
                  className="h-3 w-3 ml-1 cursor-pointer" 
                  onClick={() => setSelectedTag(null)}
                />
              </Badge>
            )}
            
                <Button
                  variant="ghost"
                  size="sm"
              className="ml-auto"
              onClick={handleResetFilters}
                >
              清除所有
                </Button>
                </div>
        )}
              </div>
      
      <div className="flex-1 flex overflow-hidden">
        {/* 侧边栏 */}
        {showSidebar && (
          <div className="w-60 border-r bg-gray-50/70 flex flex-col">
            <ScrollArea className="flex-1">
              <div className="p-3 space-y-6">
                <div className="space-y-4">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between px-2 py-1.5 mb-2">
                      <div className="text-sm font-semibold text-primary flex items-center">
                        <Mail className="h-4 w-4 mr-2" />
                        邮件管理
              </div>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7"
                        onClick={() => setIsComposeDialogOpen(true)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                  </div>
                  </div>
                  
                  {/* 系统文件夹 */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between px-2 py-1.5">
                      <div className="text-sm font-medium text-muted-foreground flex items-center">
                        <Folder className="h-4 w-4 mr-2" />
                        系统文件夹
                </div>
                </div>
                    
                    <Button 
                      variant={activeTab === "inbox" ? "secondary" : "ghost"} 
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveTab("inbox")}
                    >
                      <div className="flex items-center gap-2">
                        <Inbox className="h-4 w-4" />
                        <span>收件箱</span>
                        <Badge className="ml-auto">{statistics.unread}</Badge>
                  </div>
                    </Button>
                    
                    <Button 
                      variant={activeTab === "sent" ? "secondary" : "ghost"} 
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveTab("sent")}
                    >
                      <div className="flex items-center gap-2">
                        <Send className="h-4 w-4" />
                        <span>已发送</span>
                        <Badge className="ml-auto">{statistics.sent}</Badge>
                </div>
                    </Button>
                    
                    <Button 
                      variant={activeTab === "archived" ? "secondary" : "ghost"} 
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveTab("archived")}
                    >
                      <div className="flex items-center gap-2">
                        <Archive className="h-4 w-4" />
                        <span>归档</span>
                        <Badge className="ml-auto">{statistics.archived}</Badge>
                      </div>
                    </Button>
                    
                    <Button 
                      variant={activeTab === "deleted" ? "secondary" : "ghost"} 
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveTab("deleted")}
                    >
                      <div className="flex items-center gap-2">
                        <Trash className="h-4 w-4" />
                        <span>已删除</span>
                        <Badge className="ml-auto">{statistics.deleted}</Badge>
                      </div>
                    </Button>
                  </div>
                  
                  {/* 自定义文件夹 */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between px-2 py-1.5">
                      <div className="text-sm font-medium text-muted-foreground flex items-center">
                        <Folder className="h-4 w-4 mr-2" />
                        自定义文件夹
                      </div>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7"
                        onClick={handleCreateFolder}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {folders.map(folder => (
                      <Button 
                        key={folder.id}
                        variant={selectedFolder === folder.name ? "secondary" : "ghost"} 
                        className="w-full justify-start text-sm"
                        onClick={() => setSelectedFolder(selectedFolder === folder.name ? null : folder.name)}
                      >
                        <div className="flex items-center gap-2">
                          {folder.icon}
                          <span>{folder.name}</span>
                          <Badge className="ml-auto">{folder.count}</Badge>
                        </div>
                      </Button>
                    ))}
                  </div>
                  
                  {/* 标签 */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between px-2 py-1.5">
                      <div className="text-sm font-medium text-muted-foreground flex items-center">
                        <Tag className="h-4 w-4 mr-2" />
                        标签
                      </div>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7"
                        onClick={handleCreateTag}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {tags.map(tag => (
                      <Button 
                        key={tag.id}
                        variant={selectedTag === tag.id ? "secondary" : "ghost"} 
                        className="w-full justify-start text-sm"
                        onClick={() => setSelectedTag(selectedTag === tag.id ? null : tag.id)}
                      >
                        <div className="flex items-center gap-2">
                          <div 
                            className="h-3 w-3 rounded-full" 
                            style={{ backgroundColor: tag.color }}
                          />
                          <span>{tag.name}</span>
                          <Badge 
                            className="ml-auto"
                            variant="outline"
                          >
                            {records.filter(r => r.tags?.includes(tag.name)).length}
                          </Badge>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </ScrollArea>
                  </div>
                )}
        
        {/* 主内容区域 */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1">
            <div className="p-4 h-full">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <p className="text-muted-foreground">正在加载邮件...</p>
              </div>
              ) : filteredRecords.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-12 border rounded-md">
                  <MailQuestion className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-1">未找到邮件</h3>
                  <p className="text-muted-foreground text-center mb-4">
                    尝试更改过滤条件或者创建一封新邮件
                  </p>
                  <Button onClick={() => setIsComposeDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    新建邮件
                  </Button>
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <h3 className="text-lg font-medium">
                        {activeTab === "inbox" ? "收件箱" : 
                         activeTab === "draft" ? "草稿箱" : 
                         activeTab === "sent" ? "已发送" : 
                         activeTab === "scheduled" ? "定时邮件" : 
                         activeTab === "starred" ? "星标邮件" : 
                         activeTab === "archived" ? "归档" :
                         activeTab === "deleted" ? "已删除" : "所有邮件"}
                        <span className="text-muted-foreground ml-2 text-sm">({filteredRecords.length}条记录)</span>
                      </h3>
                    </div>
                    {selectedMailIds.length > 0 && (
                      <div className="flex items-center gap-2">
                        <p className="text-sm text-muted-foreground">
                          已选择 {selectedMailIds.length} 封邮件
                        </p>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              批量操作
                              <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => {
                              // 批量标记已读
                              const updatedRecords = records.map(rec => 
                                selectedMailIds.includes(rec.id) ? { ...rec, isRead: true } : rec
                              );
                              setRecords(updatedRecords);
                              setSelectedMailIds([]);
                              toast({
                                title: "操作成功",
                                description: `已将 ${selectedMailIds.length} 封邮件标记为已读`,
                              });
                            }}>
                              <CheckCircle2 className="mr-2 h-4 w-4" />
                        标记为已读
                  </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              // 批量添加星标
                              const updatedRecords = records.map(rec => 
                                selectedMailIds.includes(rec.id) ? { ...rec, isStarred: true } : rec
                              );
                              setRecords(updatedRecords);
                              setSelectedMailIds([]);
                              toast({
                                title: "操作成功",
                                description: `已为 ${selectedMailIds.length} 封邮件添加星标`,
                              });
                            }}>
                              <Star className="mr-2 h-4 w-4" />
                              添加星标
                  </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              // 批量删除
                              const updatedRecords = records.map(rec => 
                                selectedMailIds.includes(rec.id) ? { ...rec, status: "已删除" } : rec
                              );
                              setRecords(updatedRecords);
                              setSelectedMailIds([]);
                              toast({
                                title: "操作成功",
                                description: `已删除 ${selectedMailIds.length} 封邮件`,
                              });
                            }}>
                              <Trash className="mr-2 h-4 w-4" />
                              删除所选
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className={mailViewMode === "card" ? "bg-secondary" : ""}
                        onClick={() => setMailViewMode("card")}
                      >
                        <BarChart2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={mailViewMode === "table" ? "bg-secondary" : ""}
                        onClick={() => setMailViewMode("table")}
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {mailViewMode === "card" ? (
                    <div className="grid grid-cols-1 gap-4">
                      {filteredRecords.map((record) => (
                        <MailCard 
                          key={record.id} 
                          mail={record} 
                          isSelected={selectedMailIds.includes(record.id)}
                          onSelect={(id) => {
                            if (selectedMailIds.includes(id)) {
                              setSelectedMailIds(selectedMailIds.filter(selectedId => selectedId !== id));
                            } else {
                              setSelectedMailIds([...selectedMailIds, id]);
                            }
                          }}
                          onOpen={(mail) => openViewMail(mail)}
                        />
                      ))}
                    </div>
                  ) : (
                    <MailTable 
                      mails={filteredRecords} 
                      selectedMails={selectedMailIds} 
                      onSelectMail={(id) => {
                        if (id === "clear-all") {
                          setSelectedMailIds([]);
                        } else if (selectedMailIds.includes(id)) {
                          setSelectedMailIds(selectedMailIds.filter(selectedId => selectedId !== id));
                        } else {
                          setSelectedMailIds([...selectedMailIds, id]);
                        }
                      }}
                      onOpenMail={(mail) => openViewMail(mail)}
                    />
                  )}
                </>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
      
      {/* 查看邮件详情对话框 */}
      <Dialog open={isViewMailOpen} onOpenChange={setIsViewMailOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="mr-8">{currentRecord.subject}</DialogTitle>
              <div className="flex items-center gap-1">
                {currentRecord.isStarred ? (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleToggleStar(currentRecord)}
                    className="h-8 w-8"
                  >
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleToggleStar(currentRecord)}
                    className="h-8 w-8"
                  >
                    <Star className="h-5 w-5" />
                    </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleArchiveMail(currentRecord)}
                  className="h-8 w-8"
                >
                  <Archive className="h-5 w-5" />
                    </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => openDeleteDialog(currentRecord)}
                  className="h-8 w-8"
                >
                  <Trash className="h-5 w-5" />
                </Button>
              </div>
            </div>
            <div className="flex justify-between items-center mt-2">
              <Badge variant={getPriorityBadge(currentRecord.priority)}>
                {currentRecord.priority}
              </Badge>
              <p className="text-sm text-muted-foreground">
                {new Date(currentRecord.sendDate).toLocaleString('zh-CN')}
              </p>
            </div>
          </DialogHeader>
          
          <ScrollArea className="max-h-[60vh]">
            <div className="py-4 space-y-4">
              <div className="flex items-start gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={currentRecord.senderAvatar} alt={currentRecord.sender} />
                  <AvatarFallback>{currentRecord.sender?.slice(0, 2) || "?"}</AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="flex items-baseline gap-2">
                    <p className="font-medium">{currentRecord.sender}</p>
                    <p className="text-sm text-muted-foreground">{currentRecord.email}</p>
            </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">收件人: </span>
                    {currentRecord.recipients.join(", ")}
              </div>
                  {currentRecord.cc.length > 0 && (
                    <div className="text-sm">
                      <span className="text-muted-foreground">抄送: </span>
                      {currentRecord.cc.join(", ")}
              </div>
                  )}
            </div>
              </div>
              
              <div className="border-t border-b py-4">
                <div className="whitespace-pre-wrap">{currentRecord.content}</div>
              </div>
              
              {currentRecord.attachments.length > 0 && (
              <div className="space-y-2">
                  <p className="text-sm font-medium">附件</p>
                  <div className="flex flex-wrap gap-2">
                    {currentRecord.attachments.map((attachment, index) => (
                      <div key={index} className="border rounded-md p-2 flex items-center gap-2">
                        <FileText className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm">{attachment}</p>
              </div>
                        <Button variant="ghost" size="icon" className="h-6 w-6 ml-2">
                          <Download className="h-4 w-4" />
                        </Button>
            </div>
                    ))}
              </div>
              </div>
              )}
              
              {currentRecord.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {currentRecord.tags.map(tag => getTagBadge(tag))}
            </div>
              )}
            </div>
          </ScrollArea>
          
          <DialogFooter className="flex justify-between sm:justify-between">
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Tag className="h-4 w-4 mr-2" />
                    添加标签
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {tags.map(tag => (
                    <DropdownMenuItem 
                      key={tag.id}
                      onClick={() => handleAddTag(currentRecord.id, tag.id)}
                      disabled={currentRecord.tags?.includes(tag.name)}
                    >
                      <div 
                        className="h-3 w-3 rounded-full mr-2" 
                        style={{ backgroundColor: tag.color }}
                      />
                      {tag.name}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleCreateTag}>
                    <Plus className="h-4 w-4 mr-2" />
                    创建新标签
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Folder className="h-4 w-4 mr-2" />
                    移动至
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleMoveToFolder(currentRecord.id, "1")}>
                    <Inbox className="h-4 w-4 mr-2" />
                    收件箱
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleMoveToFolder(currentRecord.id, "2")}>
                    <Archive className="h-4 w-4 mr-2" />
                    归档
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {folders.map(folder => (
                    <DropdownMenuItem 
                      key={folder.id}
                      onClick={() => handleMoveToFolder(currentRecord.id, folder.id)}
                    >
                      {folder.icon}
                      <span className="ml-2">{folder.name}</span>
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleCreateFolder}>
                    <Plus className="h-4 w-4 mr-2" />
                    创建新文件夹
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => handleReplyMail(currentRecord)}
              >
                <Reply className="h-4 w-4 mr-2" />
                回复
            </Button>
              <Button
                variant="outline"
                onClick={() => handleForwardMail(currentRecord)}
              >
                <Forward className="h-4 w-4 mr-2" />
                转发
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除邮件</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这封邮件吗？它将被移至回收站，30天后自动永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteMail}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* 邮件模板对话框 */}
      <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>邮件模板</DialogTitle>
            <DialogDescription>
              选择一个模板快速创建邮件，或保存当前邮件为新模板。
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4 flex-1">
            <Tabs defaultValue="all">
              <TabsList className="mb-2">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="meeting">会议</TabsTrigger>
                <TabsTrigger value="work">工作</TabsTrigger>
                <TabsTrigger value="hr">HR</TabsTrigger>
                <TabsTrigger value="other">其他</TabsTrigger>
              </TabsList>
              
              <ScrollArea className="h-[50vh]">
                <TabsContent value="all" className="space-y-4 mt-0">
                  {templates.length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      暂无模板，请先创建模板。
                    </div>
                  ) : (
                    templates.map(template => (
                      <Card key={template.id} className="cursor-pointer hover:bg-secondary/5">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <CardTitle className="text-base">{template.name}</CardTitle>
                            <Badge variant="outline">{template.category}</Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm font-medium">主题: {template.subject}</p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.content}
                          </p>
                        </CardContent>
                        <CardFooter className="pt-1 flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7"
                            onClick={() => {
                              const newName = prompt("请输入新的模板名称:", template.name);
                              if (newName && newName.trim()) {
                                const updatedTemplates = templates.map(t =>
                                  t.id === template.id ? { ...t, name: newName } : t
                                );
                                setTemplates(updatedTemplates);
                                toast({
                                  title: "重命名成功",
                                  description: `模板已重命名为"${newName}"`,
                                });
                              }
                            }}
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            重命名
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7"
                            onClick={() => {
                              if (confirm(`确定要删除"${template.name}"模板吗？`)) {
                                const updatedTemplates = templates.filter(t => t.id !== template.id);
                                setTemplates(updatedTemplates);
                                toast({
                                  title: "删除成功",
                                  description: `已删除"${template.name}"模板`,
                                });
                              }
                            }}
                          >
                            <Trash className="h-3.5 w-3.5 mr-1" />
                            删除
                          </Button>
                          <Button
                            size="sm"
                            className="h-7"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <ArrowRight className="h-3.5 w-3.5 mr-1" />
                            使用
                          </Button>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </TabsContent>
                
                <TabsContent value="meeting" className="space-y-4 mt-0">
                  {templates.filter(t => t.category === "会议").length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      暂无会议类模板。
                    </div>
                  ) : (
                    templates.filter(t => t.category === "会议").map(template => (
                      <Card key={template.id} className="cursor-pointer hover:bg-secondary/5">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm font-medium">主题: {template.subject}</p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.content}
                          </p>
                        </CardContent>
                        <CardFooter className="pt-1 flex justify-end">
                          <Button
                            size="sm"
                            className="h-7"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <ArrowRight className="h-3.5 w-3.5 mr-1" />
                            使用
                          </Button>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </TabsContent>
                
                <TabsContent value="work" className="space-y-4 mt-0">
                  {templates.filter(t => t.category === "工作").length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      暂无工作类模板。
                    </div>
                  ) : (
                    templates.filter(t => t.category === "工作").map(template => (
                      <Card key={template.id} className="cursor-pointer hover:bg-secondary/5">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm font-medium">主题: {template.subject}</p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.content}
                          </p>
                        </CardContent>
                        <CardFooter className="pt-1 flex justify-end">
                          <Button
                            size="sm"
                            className="h-7"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <ArrowRight className="h-3.5 w-3.5 mr-1" />
                            使用
                          </Button>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </TabsContent>
                
                <TabsContent value="hr" className="space-y-4 mt-0">
                  {templates.filter(t => t.category === "HR").length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      暂无HR类模板。
                    </div>
                  ) : (
                    templates.filter(t => t.category === "HR").map(template => (
                      <Card key={template.id} className="cursor-pointer hover:bg-secondary/5">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm font-medium">主题: {template.subject}</p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.content}
                          </p>
                        </CardContent>
                        <CardFooter className="pt-1 flex justify-end">
                          <Button
                            size="sm"
                            className="h-7"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <ArrowRight className="h-3.5 w-3.5 mr-1" />
                            使用
                          </Button>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </TabsContent>
                
                <TabsContent value="other" className="space-y-4 mt-0">
                  {templates.filter(t => t.category !== "会议" && t.category !== "工作" && t.category !== "HR").length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      暂无其他类模板。
                    </div>
                  ) : (
                    templates.filter(t => t.category !== "会议" && t.category !== "工作" && t.category !== "HR").map(template => (
                      <Card key={template.id} className="cursor-pointer hover:bg-secondary/5">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm font-medium">主题: {template.subject}</p>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {template.content}
                          </p>
                        </CardContent>
                        <CardFooter className="pt-1 flex justify-end">
                          <Button
                            size="sm"
                            className="h-7"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <ArrowRight className="h-3.5 w-3.5 mr-1" />
                            使用
                          </Button>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </TabsContent>
              </ScrollArea>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button
              onClick={() => {
                const templateName = prompt("请输入模板名称:");
                if (templateName && templateName.trim()) {
                  const newTemplate: MailTemplate = {
                    id: (templates.length + 1).toString(),
                    name: templateName,
                    subject: "新建邮件",
                    content: "邮件内容...",
                    category: "其他"
                  };
                  
                  setTemplates([...templates, newTemplate]);
                  
                  toast({
                    title: "创建成功",
                    description: `已创建"${templateName}"模板`,
                  });
                }
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              新建模板
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 通讯录对话框 */}
      <Dialog open={isContactsDialogOpen} onOpenChange={setIsContactsDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>联系人</DialogTitle>
            <DialogDescription>
              选择联系人添加到收件人、抄送或密送列表。
            </DialogDescription>
          </DialogHeader>
          
          <ScrollArea className="h-[50vh]">
            <div className="py-4">
              <div className="mb-4">
                <Input
                  placeholder="搜索联系人..."
                  value={contactSearchTerm}
                  onChange={(e) => setContactSearchTerm(e.target.value)}
                  className="mb-2"
                />
                
                <Tabs defaultValue="all">
                  <TabsList className="w-full">
                    <TabsTrigger value="all" className="flex-1">全部</TabsTrigger>
                    <TabsTrigger value="favorites" className="flex-1">常用</TabsTrigger>
                    <TabsTrigger value="group1" className="flex-1">研发部</TabsTrigger>
                    <TabsTrigger value="group2" className="flex-1">市场部</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="all" className="mt-2">
                    {/* ... existing contacts content ... */}
                  </TabsContent>
                  {/* ... other tabs content ... */}
                </Tabs>
              </div>
            </div>
          </ScrollArea>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsContactsDialogOpen(false)}>关闭</Button>
            <Button onClick={() => {
              setIsContactsDialogOpen(false);
              toast({
                title: "联系人已添加",
                description: `${selectedContacts.length} 位联系人已添加到收件人列表`,
              });
            }}>确认</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
    </div>
  )
}
