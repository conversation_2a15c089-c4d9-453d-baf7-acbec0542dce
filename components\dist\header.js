"use client";
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
exports.__esModule = true;
exports.Header = void 0;
var react_1 = require("react");
var lucide_react_1 = require("lucide-react");
var button_1 = require("@/components/ui/button");
var avatar_1 = require("@/components/ui/avatar");
var dropdown_menu_1 = require("@/components/ui/dropdown-menu");
var popover_1 = require("@/components/ui/popover");
var auth_context_1 = require("@/contexts/auth-context");
var theme_context_1 = require("@/contexts/theme-context");
var navigation_1 = require("next/navigation");
var badge_1 = require("@/components/ui/badge");
var utils_1 = require("@/lib/utils");
var framer_motion_1 = require("framer-motion");
// 背景动效组件
var BackgroundAnimation = function (_a) {
    var themeMode = _a.themeMode;
    var _b = react_1.useState([]), particles = _b[0], setParticles = _b[1];
    var _c = react_1.useState([]), bubbles = _c[0], setBubbles = _c[1];
    var _d = react_1.useState([]), beams = _d[0], setBeams = _d[1];
    react_1.useEffect(function () {
        // 创建背景粒子
        var createParticles = function () {
            var newParticles = [];
            for (var i = 0; i < 15; i++) {
                newParticles.push({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    size: Math.random() * 3 + 1
                });
            }
            setParticles(newParticles);
        };
        // 创建彩色气泡
        var createBubbles = function () {
            var colors = [
                'rgba(59, 130, 246, 0.2)',
                'rgba(99, 102, 241, 0.2)',
                'rgba(139, 92, 246, 0.2)',
                'rgba(236, 72, 153, 0.2)',
                'rgba(14, 165, 233, 0.2)',
            ];
            var newBubbles = [];
            for (var i = 0; i < 6; i++) { // 减少气泡数量
                newBubbles.push({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 100,
                    size: Math.random() * 20 + 10,
                    color: colors[Math.floor(Math.random() * colors.length)]
                });
            }
            setBubbles(newBubbles);
        };
        // 创建光束
        var createBeams = function () {
            var newBeams = [];
            for (var i = 0; i < 3; i++) {
                newBeams.push({
                    id: i,
                    x: Math.random() * 100,
                    rotation: Math.random() * 180,
                    width: Math.random() * 150 + 50,
                    opacity: Math.random() * 0.05 + 0.02 // 低不透明度
                });
            }
            setBeams(newBeams);
        };
        createParticles();
        createBubbles();
        createBeams();
        // 定期刷新粒子位置
        var particleInterval = setInterval(function () {
            setParticles(function (prev) {
                return prev.map(function (particle) { return (__assign(__assign({}, particle), { x: (particle.x + (Math.random() * 5 - 2.5)) % 100, y: (particle.y + (Math.random() * 5 - 2.5)) % 100 })); });
            });
        }, 3000);
        // 定期刷新气泡位置
        var bubbleInterval = setInterval(function () {
            setBubbles(function (prev) {
                return prev.map(function (bubble) { return (__assign(__assign({}, bubble), { x: (bubble.x + (Math.random() * 2 - 1)) % 100, y: (bubble.y + (Math.random() * 2 - 1)) % 100 })); });
            });
        }, 5000);
        // 定期刷新光束位置
        var beamInterval = setInterval(function () {
            setBeams(function (prev) {
                return prev.map(function (beam) { return (__assign(__assign({}, beam), { x: (beam.x + (Math.random() * 5 - 2.5)) % 100, rotation: (beam.rotation + Math.random() * 10 - 5) % 180 })); });
            });
        }, 8000);
        return function () {
            clearInterval(particleInterval);
            clearInterval(bubbleInterval);
            clearInterval(beamInterval);
        };
    }, []);
    return (React.createElement("div", { className: "absolute inset-0 overflow-hidden pointer-events-none" },
        React.createElement(framer_motion_1.AnimatePresence, null, beams.map(function (beam) { return (React.createElement(framer_motion_1.motion.div, { key: "beam-" + beam.id, className: "absolute top-1/3 rounded-full " + (themeMode === "dark" ? "bg-indigo-500" : "bg-blue-300"), style: {
                width: beam.width,
                height: "1px",
                left: beam.x + "%",
                opacity: beam.opacity,
                rotate: beam.rotation + "deg",
                transformOrigin: "left center",
                filter: "blur(2px)"
            }, animate: {
                opacity: [beam.opacity, beam.opacity * 1.5, beam.opacity],
                scale: [1, 1.1, 1],
                width: [beam.width, beam.width * 1.1, beam.width]
            }, transition: {
                duration: 10,
                repeat: Infinity,
                repeatType: "reverse"
            } })); })),
        React.createElement(framer_motion_1.AnimatePresence, null, bubbles.map(function (bubble) { return (React.createElement(framer_motion_1.motion.div, { key: "bubble-" + bubble.id, className: "absolute rounded-full opacity-20", style: {
                width: bubble.size,
                height: bubble.size,
                backgroundColor: bubble.color,
                filter: "blur(" + bubble.size / 4 + "px)"
            }, initial: { opacity: 0, scale: 0 }, animate: {
                opacity: [0.1, 0.2, 0.1],
                scale: [0.8, 1, 0.8],
                x: bubble.x + "%",
                y: bubble.y + "%",
                rotate: [0, 10, -10, 0]
            }, transition: {
                duration: 15 + bubble.id * 2,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
            } })); })),
        React.createElement(framer_motion_1.AnimatePresence, null, particles.map(function (particle) { return (React.createElement(framer_motion_1.motion.div, { key: "particle-" + particle.id, className: "absolute rounded-full " + (themeMode === "dark"
                ? "bg-blue-400"
                : "bg-indigo-400"), initial: { opacity: 0 }, animate: {
                opacity: [0, 0.3, 0],
                scale: [0, 1, 0],
                x: particle.x + "%",
                y: particle.y + "%"
            }, transition: {
                duration: 10,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut",
                delay: particle.id * 0.5
            }, style: {
                width: particle.size,
                height: particle.size,
                filter: "blur(" + particle.size + "px)"
            } })); })),
        React.createElement("div", { className: "absolute inset-0 opacity-30" },
            React.createElement("div", { className: "absolute -top-20 -left-20 w-40 h-40 rounded-full " + (themeMode === "dark" ? "bg-blue-500/20" : "bg-blue-300/20"), style: { filter: "blur(40px)" } }),
            React.createElement("div", { className: "absolute -bottom-20 -right-20 w-60 h-60 rounded-full " + (themeMode === "dark" ? "bg-purple-500/20" : "bg-indigo-300/20"), style: { filter: "blur(50px)" } }),
            React.createElement(framer_motion_1.motion.div, { className: "absolute top-1/4 right-1/4 w-32 h-32 rounded-full " + (themeMode === "dark" ? "bg-cyan-500/10" : "bg-cyan-300/10"), style: { filter: "blur(30px)" }, animate: {
                    scale: [1, 1.2, 1],
                    opacity: [0.1, 0.2, 0.1]
                }, transition: {
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse"
                } })),
        React.createElement("div", { className: "absolute inset-0 transition-opacity duration-1000 " + (themeMode === "dark"
                ? "bg-gradient-to-r from-blue-900/5 via-purple-900/5 to-blue-900/5"
                : "bg-gradient-to-r from-blue-100/20 via-indigo-100/20 to-blue-100/20") },
            React.createElement(framer_motion_1.motion.div, { className: "w-full h-full", animate: {
                    background: [
                        "linear-gradient(60deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                        "linear-gradient(120deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                        "linear-gradient(180deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                        "linear-gradient(240deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                        "linear-gradient(300deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                        "linear-gradient(360deg, rgba(59, 130, 246, 0.05) 0%, rgba(79, 70, 229, 0.05) 50%, rgba(59, 130, 246, 0.05) 100%)",
                    ]
                }, transition: {
                    duration: 20,
                    repeat: Infinity,
                    repeatType: "loop"
                } }))));
};
function Header(_a) {
    var toggleSidebar = _a.toggleSidebar, _b = _a.sidebarCollapsed, sidebarCollapsed = _b === void 0 ? true : _b;
    var _c = auth_context_1.useAuth(), user = _c.user, logout = _c.logout;
    var _d = theme_context_1.useTheme(), themeMode = _d.themeMode, toggleTheme = _d.toggleTheme;
    var _e = react_1.useState([
        {
            id: 1,
            title: '系统更新',
            message: '系统将于今晚22:00进行维护升级',
            time: '10分钟前',
            read: false,
            type: 'info',
            createdAt: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
        },
        {
            id: 2,
            title: '安全检查',
            message: '您有一项安全检查任务已完成',
            time: '30分钟前',
            read: false,
            type: 'success',
            link: '/safety-management/safety-check',
            actionText: '查看详情',
            createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
        },
        {
            id: 3,
            title: '设备预警',
            message: '有3台设备需要维护',
            time: '2小时前',
            read: false,
            type: 'warning',
            link: '/material-supply-chain/material-maintenance',
            actionText: '立即处理',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
        }
    ]), notifications = _e[0], setNotifications = _e[1];
    var _f = react_1.useState(false), isNotificationOpen = _f[0], setIsNotificationOpen = _f[1];
    var _g = react_1.useState(''), currentTime = _g[0], setCurrentTime = _g[1];
    var _h = react_1.useState(false), showNewNotification = _h[0], setShowNewNotification = _h[1];
    var _j = react_1.useState(false), hasNewNotification = _j[0], setHasNewNotification = _j[1];
    var _k = react_1.useState(false), isUserMenuOpen = _k[0], setIsUserMenuOpen = _k[1];
    var _l = react_1.useState({ x: 0, y: 0 }), interactionPoint = _l[0], setInteractionPoint = _l[1];
    var _m = react_1.useState(false), showInteractionEffect = _m[0], setShowInteractionEffect = _m[1];
    var router = navigation_1.useRouter();
    // 处理鼠标交互效果
    var handleMouseInteraction = function (e) {
        // 限制效果触发频率，避免过度消耗性能
        if (Math.random() > 0.1)
            return;
        // 记录鼠标位置
        setInteractionPoint({
            x: e.clientX,
            y: e.clientY
        });
        // 显示交互效果
        setShowInteractionEffect(true);
        // 一段时间后隐藏效果
        setTimeout(function () { return setShowInteractionEffect(false); }, 1000);
    };
    // 时间格式化函数 - 将日期转换为"x分钟前"格式
    var formatTimeAgo = function (date) {
        var now = new Date();
        var diffMs = now.getTime() - date.getTime();
        var diffSecs = Math.floor(diffMs / 1000);
        var diffMins = Math.floor(diffSecs / 60);
        var diffHours = Math.floor(diffMins / 60);
        var diffDays = Math.floor(diffHours / 24);
        if (diffSecs < 60) {
            return '刚刚';
        }
        else if (diffMins < 60) {
            return diffMins + "\u5206\u949F\u524D";
        }
        else if (diffHours < 24) {
            return diffHours + "\u5C0F\u65F6\u524D";
        }
        else if (diffDays < 30) {
            return diffDays + "\u5929\u524D";
        }
        else {
            return date.toLocaleDateString('zh-CN');
        }
    };
    // 更新所有通知的时间
    var updateNotificationTimes = function () {
        setNotifications(function (prev) {
            return prev.map(function (notification) { return (__assign(__assign({}, notification), { time: formatTimeAgo(notification.createdAt) })); });
        });
    };
    // 实时时间显示
    react_1.useEffect(function () {
        var updateTime = function () {
            var now = new Date();
            var options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            };
            setCurrentTime(now.toLocaleString('zh-CN', options));
            // 同时更新通知时间显示
            updateNotificationTimes();
        };
        updateTime();
        var interval = setInterval(updateTime, 60000); // 每分钟更新一次
        return function () { return clearInterval(interval); };
    }, []);
    // 模拟随机生成新通知的功能（仅用于演示）
    react_1.useEffect(function () {
        var generateRandomNotification = function () {
            var types = ['info', 'success', 'warning', 'error'];
            var randomType = types[Math.floor(Math.random() * types.length)];
            var titles = {
                info: ['系统公告', '信息更新', '功能提示', '新功能上线'],
                success: ['任务完成', '操作成功', '审批通过', '安全检查完成'],
                warning: ['设备预警', '安全提醒', '库存预警', '系统提示'],
                error: ['系统错误', '操作失败', '连接中断', '数据异常']
            };
            var messages = {
                info: ['系统将进行例行维护，请注意保存工作', '新版本发布，请查看更新日志', '您有新的任务需要处理'],
                success: ['您的申请已审批通过', '数据同步完成', '备份已成功创建'],
                warning: ['库存物资即将不足，请及时补充', '设备运行时间过长，需要检查', '有待处理的安全隐患'],
                error: ['服务器连接失败，请稍后重试', '操作未能完成，请联系管理员', '数据提交出错']
            };
            var links = {
                info: ['/', '/system-management/system-config'],
                success: ['/task-process-management/task-management', '/safety-management/safety-check'],
                warning: ['/material-supply-chain/material-maintenance', '/safety-management/safety-hazard-management'],
                error: ['/system-management/system-settings', '/system-management/user-management']
            };
            var randomTitle = titles[randomType][Math.floor(Math.random() * titles[randomType].length)];
            var randomMessage = messages[randomType][Math.floor(Math.random() * messages[randomType].length)];
            var randomLink = links[randomType][Math.floor(Math.random() * links[randomType].length)];
            var newNotification = {
                id: Date.now(),
                title: randomTitle,
                message: randomMessage,
                time: '刚刚',
                read: false,
                type: randomType,
                link: randomLink,
                actionText: randomType === 'warning' || randomType === 'error' ? '立即处理' : '查看详情',
                createdAt: new Date()
            };
            setNotifications(function (prev) { return __spreadArrays([newNotification], prev); });
            setHasNewNotification(true);
            // 显示新通知的动画效果
            setShowNewNotification(true);
            setTimeout(function () { return setShowNewNotification(false); }, 3000);
        };
        // 随机间隔生成通知（仅用于演示）
        var randomInterval = Math.floor(Math.random() * (180000 - 60000) + 60000); // 1-3分钟
        var timer = setTimeout(generateRandomNotification, randomInterval);
        return function () { return clearTimeout(timer); };
    }, [notifications]);
    // 通知动画效果
    react_1.useEffect(function () {
        if (hasNewNotification) {
            // 通知铃铛震动动画效果
            var bellIcon_1 = document.querySelector('.notification-bell');
            if (bellIcon_1) {
                bellIcon_1.classList.add('animate-bell');
                setTimeout(function () {
                    bellIcon_1.classList.remove('animate-bell');
                    setHasNewNotification(false);
                }, 1000);
            }
        }
    }, [hasNewNotification]);
    // 标记通知为已读
    var markAsRead = function (id) {
        setNotifications(function (prev) {
            return prev.map(function (notification) {
                return notification.id === id ? __assign(__assign({}, notification), { read: true }) : notification;
            });
        });
    };
    // 标记所有通知为已读
    var markAllAsRead = function () {
        setNotifications(function (prev) {
            return prev.map(function (notification) { return (__assign(__assign({}, notification), { read: true })); });
        });
    };
    // 删除通知
    var removeNotification = function (id) {
        setNotifications(function (prev) {
            return prev.filter(function (notification) { return notification.id !== id; });
        });
    };
    // 处理通知点击，如果有链接则跳转
    var handleNotificationClick = function (notification) {
        markAsRead(notification.id);
        if (notification.link) {
            setIsNotificationOpen(false);
            // 延迟跳转，确保先关闭通知面板
            setTimeout(function () {
                router.push(notification.link || '/');
            }, 100);
        }
    };
    // 未读通知数量
    var unreadCount = notifications.filter(function (n) { return !n.read; }).length;
    // 获取通知图标
    var getNotificationIcon = function (type) {
        switch (type) {
            case 'success': return React.createElement(lucide_react_1.CheckCircle, { className: "h-5 w-5 text-green-500" });
            case 'warning': return React.createElement(lucide_react_1.AlertTriangle, { className: "h-5 w-5 text-yellow-500" });
            case 'error': return React.createElement(lucide_react_1.AlertTriangle, { className: "h-5 w-5 text-red-500" });
            default: return React.createElement(lucide_react_1.Info, { className: "h-5 w-5 text-blue-500" });
        }
    };
    // 导航到首页
    var navigateToHome = function () {
        router.push("/");
    };
    // 导航到可视化大屏
    var navigateToVisualDashboard = function () {
        router.push("/visual-dashboard");
    };
    // 导航到个人资料页面
    var navigateToProfile = function () {
        setIsUserMenuOpen(false);
        router.push("/system-management/user-management"); // 修改为现有页面
    };
    // 导航到密码修改页面
    var navigateToChangePassword = function () {
        setIsUserMenuOpen(false);
        router.push("/system-management/user-management?tab=password"); // 修改为现有页面并添加参数
    };
    // 导航到安全设置页面
    var navigateToSecuritySettings = function () {
        setIsUserMenuOpen(false);
        router.push("/system-management/system-settings"); // 修改为现有页面
    };
    // 导航到系统设置
    var navigateToSettings = function () {
        setIsUserMenuOpen(false);
        router.push("/system-management/system-settings"); // 修改为现有页面
    };
    // 处理登出
    var handleLogout = function () {
        setIsUserMenuOpen(false);
        // 弹出确认对话框
        if (confirm('确定要退出登录吗？')) {
            logout();
        }
    };
    return (React.createElement(framer_motion_1.motion.header, { initial: { opacity: 0, y: -20 }, animate: { opacity: 1, y: 0 }, transition: { duration: 0.3 }, className: "sticky top-0 z-10 flex h-16 items-center gap-4 border-b px-4 md:px-6 relative overflow-hidden " + (themeMode === "dark"
            ? "bg-[#1c1c1e]/90 border-[#2c2c2e] shadow-md shadow-black/10"
            : "bg-[#f5f5f7]/90 border-gray-200 backdrop-blur-sm shadow-md shadow-black/5"), onMouseMove: handleMouseInteraction },
        React.createElement("div", { className: "absolute inset-0 transition-all duration-500", style: {
                backgroundImage: "url(/menu-bg/ding.png)",
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                opacity: '0.8'
            } }),
        React.createElement("div", { className: "absolute inset-0 transition-all duration-500", style: {
                background: themeMode === "dark"
                    ? 'linear-gradient(to right, #1E1E1E 10%, transparent 50%, #1E1E1E 90%)'
                    : 'linear-gradient(to right, #F9FAFC 10%, transparent 50%, #F9FAFC 90%)',
                opacity: '0.95'
            } }),
        React.createElement(framer_motion_1.AnimatePresence, null, showInteractionEffect && (React.createElement(framer_motion_1.motion.div, { className: "absolute rounded-full pointer-events-none " + (themeMode === "dark" ? "bg-blue-500" : "bg-indigo-400"), style: {
                left: interactionPoint.x,
                top: interactionPoint.y,
                translateX: "-50%",
                translateY: "-50%"
            }, initial: { width: 0, height: 0, opacity: 0.5 }, animate: { width: 100, height: 100, opacity: 0 }, exit: { width: 0, height: 0, opacity: 0 }, transition: { duration: 1, ease: "easeOut" } }))),
        React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.05 }, whileTap: { scale: 0.95 } },
            React.createElement(button_1.Button, { variant: "ghost", size: "icon", onClick: toggleSidebar, className: "flex items-center justify-center transition-all duration-300 " + (themeMode === "dark"
                    ? "text-white hover:bg-[#2c2c2e] hover:text-blue-400"
                    : "hover:bg-gray-100 hover:text-blue-600") + " relative group", title: sidebarCollapsed ? "展开菜单" : "收起菜单" },
                React.createElement(framer_motion_1.motion.div, { animate: {
                        rotate: sidebarCollapsed ? 0 : 180,
                        scale: sidebarCollapsed ? 1 : 0.9
                    }, transition: { duration: 0.3 }, className: "relative z-10" }, sidebarCollapsed ? (React.createElement(lucide_react_1.ChevronRight, { className: "h-5 w-5" })) : (React.createElement(lucide_react_1.ChevronLeft, { className: "h-5 w-5" }))),
                React.createElement("div", { className: "absolute inset-0 rounded-full transition-all duration-300 " + (themeMode === "dark"
                        ? "bg-blue-500/0 group-hover:bg-blue-500/10"
                        : "bg-blue-500/0 group-hover:bg-blue-500/10") }),
                React.createElement("span", { className: "sr-only" }, "\u5207\u6362\u4FA7\u8FB9\u680F"))),
        React.createElement("div", { className: "w-full flex justify-between" },
            React.createElement("div", { className: "flex items-center gap-4" },
                React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.03 }, whileTap: { scale: 0.97 } },
                    React.createElement(button_1.Button, { variant: "ghost", className: "flex items-center gap-2 mr-4 transition-all duration-300 " + (themeMode === "dark"
                            ? "bg-[#2c2c2e]/60 text-white hover:bg-[#2c2c2e]"
                            : "bg-white/60 text-gray-800 hover:bg-gray-100"), onClick: navigateToHome },
                        React.createElement(lucide_react_1.Home, { className: "h-5 w-5 " + (themeMode === "dark"
                                ? "text-blue-400"
                                : "text-blue-600") }),
                        React.createElement("span", { className: "font-medium text-base " + (themeMode === "dark"
                                ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]"
                                : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]") }, "\u9996\u9875"))),
                React.createElement("div", { className: "relative flex items-center " + (themeMode === "dark"
                        ? "text-gray-300"
                        : "text-gray-600") },
                    React.createElement("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none " + (themeMode === "dark"
                            ? "text-gray-400"
                            : "text-gray-500") },
                        React.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-5 w-5", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor" },
                            React.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }))),
                    React.createElement("input", { type: "search", placeholder: "\u641C\u7D22...", className: "block w-64 pl-10 pr-3 py-2 rounded-lg border " + (themeMode === "dark"
                            ? "bg-[#2c2c2e]/60 border-[#3a3a3c] text-white placeholder-gray-400 focus:border-blue-500"
                            : "bg-white/60 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500") + " focus:ring-2 focus:ring-blue-500/20 focus:outline-none transition-all duration-300" })),
                React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.03 }, whileTap: { scale: 0.97 } },
                    React.createElement(button_1.Button, { variant: "ghost", className: "flex items-center gap-2 mr-4 transition-all duration-300 neon-button " + (themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"), onClick: navigateToVisualDashboard },
                        React.createElement(lucide_react_1.BarChart2, { className: "h-5 w-5 " + (themeMode === "dark" ? "text-white" : "text-gray-700") }),
                        React.createElement("span", { className: "font-medium text-base " + (themeMode === "dark"
                                ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]"
                                : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]") }, "\u53EF\u89C6\u5316\u5927\u5C4F"))),
                React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0 }, animate: { opacity: 1 }, transition: { delay: 0.3, duration: 0.5 }, className: "hidden md:flex items-center gap-2 " + (themeMode === "dark" ? "text-gray-200" : "text-gray-700") },
                    React.createElement(lucide_react_1.Clock, { className: "h-4 w-4" }),
                    React.createElement("span", { className: "text-sm font-medium " + (themeMode === "dark"
                            ? "text-white/90 drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]"
                            : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]") }, currentTime))),
            React.createElement("div", { className: "flex items-center gap-4" },
                React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.1, rotate: themeMode === "dark" ? 180 : 0 }, whileTap: { scale: 0.9 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                    React.createElement(button_1.Button, { variant: "ghost", size: "icon", onClick: toggleTheme, className: "rounded-full transition-all duration-300 neon-button " + (themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"), title: themeMode === "dark" ? "切换到亮色模式" : "切换到暗色模式" },
                        themeMode === "dark" ? (React.createElement(framer_motion_1.motion.div, { initial: { rotate: -90, opacity: 0 }, animate: { rotate: 0, opacity: 1 }, exit: { rotate: 90, opacity: 0 }, transition: { duration: 0.5 } },
                            React.createElement(lucide_react_1.Sun, { className: "h-5 w-5 text-yellow-400" }))) : (React.createElement(framer_motion_1.motion.div, { initial: { rotate: 90, opacity: 0 }, animate: { rotate: 0, opacity: 1 }, exit: { rotate: -90, opacity: 0 }, transition: { duration: 0.5 } },
                            React.createElement(lucide_react_1.Moon, { className: "h-5 w-5" }))),
                        React.createElement("span", { className: "sr-only" }, "\u5207\u6362\u4E3B\u9898"))),
                React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.05 }, whileTap: { scale: 0.95 }, animate: { y: hasNewNotification ? [0, -5, 0] : 0 }, transition: { duration: 0.2 } },
                    React.createElement(popover_1.Popover, { open: isNotificationOpen, onOpenChange: setIsNotificationOpen },
                        React.createElement(popover_1.PopoverTrigger, { asChild: true },
                            React.createElement(button_1.Button, { variant: "ghost", size: "icon", className: "relative transition-all duration-300 neon-button " + (themeMode === "dark" ? "text-white hover:bg-[#2c2c2e]" : "hover:bg-gray-100"), title: "\u901A\u77E5\u4E2D\u5FC3" },
                                React.createElement(lucide_react_1.Bell, { className: "h-5 w-5 notification-bell " + (hasNewNotification ? 'animate-bell' : '') }),
                                unreadCount > 0 && (React.createElement(framer_motion_1.motion.span, { initial: { scale: 0 }, animate: { scale: 1 }, transition: {
                                        type: "spring",
                                        stiffness: 400,
                                        damping: 10
                                    }, className: "absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full text-[10px] " + (themeMode === "dark" ? "bg-white text-black" : "bg-black text-white") }, unreadCount)),
                                React.createElement("span", { className: "sr-only" }, "\u901A\u77E5"))),
                        React.createElement(popover_1.PopoverContent, { className: "w-80 p-0 " + (themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c] text-white" : ""), align: "end", sideOffset: 10 },
                            React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0, y: 10 }, animate: { opacity: 1, y: 0 }, transition: { duration: 0.2 } },
                                React.createElement("div", { className: "flex items-center justify-between px-4 py-3 border-b" },
                                    React.createElement("h3", { className: "font-medium" }, "\u901A\u77E5\u4E2D\u5FC3"),
                                    React.createElement("div", { className: "flex items-center gap-2" },
                                        unreadCount > 0 && (React.createElement(button_1.Button, { variant: "ghost", size: "sm", onClick: markAllAsRead, className: "text-xs hover:bg-transparent transition-colors " + (themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-500 hover:text-black") }, "\u5168\u90E8\u6807\u4E3A\u5DF2\u8BFB")),
                                        React.createElement(button_1.Button, { variant: "ghost", size: "sm", onClick: function () { return setNotifications([]); }, className: "text-xs hover:bg-transparent transition-colors " + (themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-500 hover:text-black") }, "\u6E05\u7A7A"))),
                                React.createElement("div", { className: "max-h-80 overflow-y-auto" }, notifications.length === 0 ? (React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0 }, animate: { opacity: 1 }, transition: { delay: 0.1, duration: 0.3 }, className: "flex flex-col items-center justify-center py-8 px-4 text-center" },
                                    React.createElement(lucide_react_1.Bell, { className: "h-12 w-12 mb-2 " + (themeMode === "dark" ? "text-gray-600" : "text-gray-300") }),
                                    React.createElement("p", { className: "" + (themeMode === "dark" ? "text-gray-400" : "text-gray-600") }, "\u6682\u65E0\u901A\u77E5"))) : (React.createElement("div", null, notifications.map(function (notification, index) { return (React.createElement(framer_motion_1.motion.div, { key: notification.id, initial: { opacity: 0, y: 10 }, animate: { opacity: 1, y: 0 }, transition: { delay: index * 0.05, duration: 0.2 }, className: utils_1.cn("flex items-start p-3 border-b relative transition-all duration-200", !notification.read ? (themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-50") : "", themeMode === "dark" ? "border-[#3a3a3c]" : "border-gray-100", "hover:bg-gray-100 dark:hover:bg-[#444]", notification.link ? "cursor-pointer" : "") },
                                    React.createElement("div", { className: "flex-shrink-0 mt-1 mr-3" }, getNotificationIcon(notification.type)),
                                    React.createElement("div", { className: "flex-1", onClick: function () { return handleNotificationClick(notification); } },
                                        React.createElement("div", { className: "flex justify-between items-start" },
                                            React.createElement("p", { className: "font-medium " + (!notification.read ? "font-bold" : "") }, notification.title),
                                            React.createElement(button_1.Button, { variant: "ghost", size: "icon", className: "h-6 w-6 -mt-1 -mr-1 hover:bg-transparent", onClick: function (e) {
                                                    e.stopPropagation();
                                                    removeNotification(notification.id);
                                                } },
                                                React.createElement(lucide_react_1.X, { className: "h-4 w-4" }),
                                                React.createElement("span", { className: "sr-only" }, "\u5173\u95ED"))),
                                        React.createElement("p", { className: "text-sm " + (themeMode === "dark" ? "text-gray-300" : "text-gray-600") }, notification.message),
                                        React.createElement("div", { className: "flex items-center justify-between mt-1" },
                                            React.createElement("p", { className: "text-xs " + (themeMode === "dark" ? "text-gray-400" : "text-gray-500") }, notification.time),
                                            notification.actionText && notification.link && (React.createElement(button_1.Button, { variant: "link", size: "sm", className: "p-0 h-auto text-xs " + (notification.type === 'success' ? 'text-green-500' :
                                                    notification.type === 'warning' ? 'text-yellow-500' :
                                                        notification.type === 'error' ? 'text-red-500' :
                                                            'text-blue-500'), onClick: function (e) {
                                                    e.stopPropagation();
                                                    handleNotificationClick(notification);
                                                } }, notification.actionText))),
                                        !notification.read && (React.createElement(badge_1.Badge, { variant: "secondary", className: "absolute top-3 right-10 " + (themeMode === "dark" ? "bg-blue-900/30 text-blue-400" : "bg-blue-100 text-blue-800") }, "\u65B0"))))); })))),
                                React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0 }, animate: { opacity: 1 }, transition: { delay: 0.2, duration: 0.3 }, className: "p-2 border-t" },
                                    React.createElement(button_1.Button, { variant: "outline", className: "w-full text-center transition-all duration-300 hover:bg-gray-100 dark:hover:bg-[#3a3a3c]", onClick: function () {
                                            setIsNotificationOpen(false);
                                            router.push('/notifications');
                                        } }, "\u67E5\u770B\u6240\u6709\u901A\u77E5")))))),
                React.createElement(framer_motion_1.motion.div, { whileHover: { scale: 1.05 }, whileTap: { scale: 0.95 } },
                    React.createElement(dropdown_menu_1.DropdownMenu, { open: isUserMenuOpen, onOpenChange: setIsUserMenuOpen },
                        React.createElement(dropdown_menu_1.DropdownMenuTrigger, { asChild: true },
                            React.createElement(button_1.Button, { variant: "ghost", size: "icon", className: "rounded-full transition-all duration-300 " + (themeMode === "dark" ? "hover:bg-[#2c2c2e]" : "hover:bg-gray-100") },
                                React.createElement("div", { className: "avatar-container relative" },
                                    React.createElement(avatar_1.Avatar, { className: "h-8 w-8 border-2 border-transparent hover:border-blue-400 transition-all duration-300" },
                                        React.createElement(avatar_1.AvatarImage, { src: (user === null || user === void 0 ? void 0 : user.avatar) || "", alt: (user === null || user === void 0 ? void 0 : user.username) || "用户头像" }),
                                        React.createElement(avatar_1.AvatarFallback, { className: "text-sm transition-colors " + (themeMode === "dark" ? "bg-[#2c2c2e]" : "bg-gray-200") }, (user === null || user === void 0 ? void 0 : user.username.charAt(0).toUpperCase()) || "U")),
                                    React.createElement(framer_motion_1.motion.div, { className: "absolute inset-0 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-300 " + (themeMode === "dark" ? "glow-effect-dark" : "glow-effect-light"), animate: {
                                            boxShadow: [
                                                "0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3)",
                                                "0 0 15px rgba(59, 130, 246, 0.7), 0 0 20px rgba(59, 130, 246, 0.5)",
                                                "0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3)"
                                            ]
                                        }, transition: {
                                            duration: 2,
                                            repeat: Infinity,
                                            repeatType: "loop"
                                        } })))),
                        React.createElement(dropdown_menu_1.DropdownMenuContent, { align: "end", className: themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c] text-white" : "", sideOffset: 5, alignOffset: 0, forceMount: true },
                            React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0, y: 10 }, animate: { opacity: 1, y: 0 }, transition: { duration: 0.2 } },
                                React.createElement("div", { className: "px-4 py-3 flex items-center gap-3 " + (themeMode === "dark" ? "text-gray-100" : "") },
                                    React.createElement(avatar_1.Avatar, { className: "h-10 w-10 ring-2 ring-blue-400/50" },
                                        React.createElement(avatar_1.AvatarImage, { src: (user === null || user === void 0 ? void 0 : user.avatar) || "", alt: (user === null || user === void 0 ? void 0 : user.username) || "用户头像" }),
                                        React.createElement(avatar_1.AvatarFallback, { className: "" + (themeMode === "dark" ? "bg-[#444]" : "bg-gray-200") }, (user === null || user === void 0 ? void 0 : user.username.charAt(0).toUpperCase()) || "U")),
                                    React.createElement("div", null,
                                        React.createElement("p", { className: "font-semibold text-base " + (themeMode === "dark"
                                                ? "text-white drop-shadow-[0_0_0.3px_rgba(255,255,255,0.7)]"
                                                : "text-gray-800 drop-shadow-[0_0_0.3px_rgba(0,0,0,0.3)]") }, (user === null || user === void 0 ? void 0 : user.username) || "用户"),
                                        React.createElement("p", { className: "text-xs truncate max-w-[160px] " + (themeMode === "dark"
                                                ? "text-gray-300 drop-shadow-[0_0_0.2px_rgba(255,255,255,0.5)]"
                                                : "text-gray-600 drop-shadow-[0_0_0.2px_rgba(0,0,0,0.3)]") }, (user === null || user === void 0 ? void 0 : user.email) || (user === null || user === void 0 ? void 0 : user.role) || "普通用户"))),
                                React.createElement(dropdown_menu_1.DropdownMenuSeparator, { className: themeMode === "dark" ? "bg-[#3a3a3c]" : "" }),
                                React.createElement(dropdown_menu_1.DropdownMenuGroup, null,
                                    React.createElement(framer_motion_1.motion.div, { whileHover: { x: 5 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                                        React.createElement(dropdown_menu_1.DropdownMenuItem, { onClick: navigateToProfile, className: "transition-colors duration-200 " + (themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : "") },
                                            React.createElement(lucide_react_1.UserCircle, { className: "mr-2 h-4 w-4" }),
                                            React.createElement("span", null, "\u4E2A\u4EBA\u8D44\u6599"),
                                            React.createElement(dropdown_menu_1.DropdownMenuShortcut, null, "\u21E7P"))),
                                    React.createElement(framer_motion_1.motion.div, { whileHover: { x: 5 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                                        React.createElement(dropdown_menu_1.DropdownMenuItem, { onClick: navigateToChangePassword, className: "transition-colors duration-200 " + (themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : "") },
                                            React.createElement(lucide_react_1.Key, { className: "mr-2 h-4 w-4" }),
                                            React.createElement("span", null, "\u4FEE\u6539\u5BC6\u7801"))),
                                    React.createElement(framer_motion_1.motion.div, { whileHover: { x: 5 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                                        React.createElement(dropdown_menu_1.DropdownMenuItem, { onClick: navigateToSecuritySettings, className: "transition-colors duration-200 " + (themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : "") },
                                            React.createElement(lucide_react_1.Shield, { className: "mr-2 h-4 w-4" }),
                                            React.createElement("span", null, "\u5B89\u5168\u8BBE\u7F6E")))),
                                React.createElement(dropdown_menu_1.DropdownMenuSeparator, { className: themeMode === "dark" ? "bg-[#3a3a3c]" : "" }),
                                React.createElement(framer_motion_1.motion.div, { whileHover: { x: 5 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                                    React.createElement(dropdown_menu_1.DropdownMenuItem, { onClick: navigateToSettings, className: "transition-colors duration-200 " + (themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c]" : "") },
                                        React.createElement(lucide_react_1.Settings, { className: "mr-2 h-4 w-4" }),
                                        React.createElement("span", null, "\u7CFB\u7EDF\u8BBE\u7F6E"),
                                        React.createElement(dropdown_menu_1.DropdownMenuShortcut, null, "\u2318S"))),
                                React.createElement(dropdown_menu_1.DropdownMenuSeparator, { className: themeMode === "dark" ? "bg-[#3a3a3c]" : "" }),
                                React.createElement(framer_motion_1.motion.div, { whileHover: { x: 5, scale: 1.03 }, transition: { type: "spring", stiffness: 400, damping: 17 } },
                                    React.createElement(dropdown_menu_1.DropdownMenuItem, { onClick: handleLogout, className: "transition-colors duration-200 " + (themeMode === "dark" ? "hover:bg-[#3a3a3c] focus:bg-[#3a3a3c] text-red-400 hover:text-red-300" : "text-red-600 hover:text-red-700") },
                                        React.createElement(lucide_react_1.LogOut, { className: "mr-2 h-4 w-4" }),
                                        React.createElement("span", null, "\u9000\u51FA\u767B\u5F55"),
                                        React.createElement(dropdown_menu_1.DropdownMenuShortcut, null, "\u21E7\u2318Q"))))))))),
        showNewNotification && notifications.length > 0 && (React.createElement(framer_motion_1.motion.div, { initial: { opacity: 0, x: 100 }, animate: { opacity: 1, x: 0 }, exit: { opacity: 0, x: 100 }, transition: { type: "spring", stiffness: 300, damping: 25 }, className: "fixed top-20 right-4 z-50 w-72 p-3 rounded-lg shadow-lg transform " + (themeMode === "dark" ? "bg-[#2c2c2e] text-white" : "bg-white text-gray-800") },
            React.createElement("div", { className: "flex items-start" },
                React.createElement("div", { className: "flex-shrink-0 mt-1 mr-3" },
                    React.createElement(framer_motion_1.motion.div, { initial: { scale: 0 }, animate: { scale: [0, 1.2, 1] }, transition: { duration: 0.5 } }, getNotificationIcon(notifications[0].type))),
                React.createElement("div", { className: "flex-1" },
                    React.createElement(framer_motion_1.motion.p, { initial: { y: -10, opacity: 0 }, animate: { y: 0, opacity: 1 }, transition: { delay: 0.1 }, className: "font-bold" }, notifications[0].title),
                    React.createElement(framer_motion_1.motion.p, { initial: { y: -5, opacity: 0 }, animate: { y: 0, opacity: 1 }, transition: { delay: 0.2 }, className: "text-sm mt-1" }, notifications[0].message),
                    React.createElement(framer_motion_1.motion.div, { initial: { y: -5, opacity: 0 }, animate: { y: 0, opacity: 1 }, transition: { delay: 0.3 }, className: "flex justify-between items-center mt-2" },
                        React.createElement("span", { className: "text-xs opacity-75" }, "\u521A\u521A"),
                        notifications[0].actionText && (React.createElement(button_1.Button, { variant: "link", size: "sm", className: "p-0 h-auto text-xs", onClick: function () {
                                setShowNewNotification(false);
                                if (notifications[0].link) {
                                    router.push(notifications[0].link);
                                }
                            } }, notifications[0].actionText))))))),
        React.createElement("style", { jsx: true, global: true }, "\n        @keyframes bell-shake {\n          0% { transform: rotate(0); }\n          20% { transform: rotate(15deg); }\n          40% { transform: rotate(-15deg); }\n          60% { transform: rotate(7deg); }\n          80% { transform: rotate(-7deg); }\n          100% { transform: rotate(0); }\n        }\n        \n        .animate-bell {\n          animation: bell-shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;\n          transform-origin: top center;\n        }\n        \n        @keyframes pulse-ring {\n          0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }\n          70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }\n          100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }\n        }\n        \n        .pulse-animation {\n          animation: pulse-ring 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;\n        }\n        \n        /* \u4E3A\u65B0\u589E\u7684\u52A8\u6001\u6548\u679C\u6DFB\u52A0\u6837\u5F0F */\n        @keyframes float {\n          0% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n          100% { transform: translateY(0px); }\n        }\n        \n        .float-animation {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        @keyframes gradient-shift {\n          0% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n          100% { background-position: 0% 50%; }\n        }\n        \n        .gradient-animation {\n          background-size: 200% 200%;\n          animation: gradient-shift 15s ease infinite;\n        }\n        \n        /* \u4E3A\u6309\u94AE\u6DFB\u52A0\u9713\u8679\u6548\u679C */\n        .neon-button {\n          position: relative;\n          overflow: hidden;\n        }\n        \n        .neon-button::before {\n          content: '';\n          position: absolute;\n          top: -2px;\n          left: -2px;\n          right: -2px;\n          bottom: -2px;\n          z-index: -1;\n          background: linear-gradient(45deg, \n            #ff0000, #ff7300, #fffb00, #48ff00, \n            #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);\n          background-size: 400%;\n          border-radius: 10px;\n          opacity: 0;\n          transition: 0.5s;\n          animation: neon-border 20s linear infinite;\n        }\n        \n        .neon-button:hover::before {\n          opacity: 0.3;\n        }\n        \n        @keyframes neon-border {\n          0% { background-position: 0 0; }\n          50% { background-position: 400% 0; }\n          100% { background-position: 0 0; }\n        }\n        \n        /* \u4E3A\u70B9\u51FB\u6DFB\u52A0\u6CE2\u7EB9\u6548\u679C */\n        @keyframes ripple {\n          0% { transform: scale(0); opacity: 1; }\n          100% { transform: scale(2.5); opacity: 0; }\n        }\n        \n        .ripple-effect {\n          position: relative;\n          overflow: hidden;\n        }\n        \n        .ripple-effect::after {\n          content: '';\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          background: rgba(255, 255, 255, 0.3);\n          top: 0;\n          left: 0;\n          border-radius: 50%;\n          transform: scale(0);\n          animation: ripple 0.6s linear;\n        }\n        \n        /* \u7528\u6237\u5934\u50CF\u53D1\u5149\u6548\u679C */\n        .glow-effect-light {\n          border-radius: 50%;\n          box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),\n                      0 0 10px rgba(59, 130, 246, 0.3);\n        }\n        \n        .glow-effect-dark {\n          border-radius: 50%;\n          box-shadow: 0 0 5px rgba(147, 197, 253, 0.5),\n                      0 0 10px rgba(147, 197, 253, 0.3);\n        }\n        \n        .avatar-container:hover .glow-effect-light,\n        .avatar-container:hover .glow-effect-dark {\n          animation: pulse-glow 2s infinite;\n        }\n        \n        @keyframes pulse-glow {\n          0% {\n            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),\n                        0 0 10px rgba(59, 130, 246, 0.3);\n          }\n          50% {\n            box-shadow: 0 0 15px rgba(59, 130, 246, 0.8),\n                       0 0 20px rgba(59, 130, 246, 0.5),\n                       0 0 25px rgba(59, 130, 246, 0.3);\n          }\n          100% {\n            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5),\n                       0 0 10px rgba(59, 130, 246, 0.3);\n          }\n        }\n        \n        /* Add this to smoothen all transitions */\n        * {\n          transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;\n          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n          transition-duration: 200ms;\n        }\n      ")));
}
exports.Header = Header;
