"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON><PERSON> } from "react"
import {
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  FileText,
  Clock,
  CheckCircle2,
  AlertCircle,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  RefreshCw,
  Upload,
  ArrowUpDown,
  BarChart2,
  FileBarChart,
  X,
  Loader2
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"
import * as XLSX from "xlsx"

// 定义计划接口
interface Plan {
  id: string
  name: string
  type: string
  startDate: string
  endDate: string
  manager: string
  department: string
  status: string
  priority: string
  completion: string
  completionNumber?: number
  lastUpdated: string
  description?: string
  objectives?: string
  resources?: string
  milestones?: {
    name: string
    dueDate: string
    completed: boolean
  }[]
  risks?: string
  budget?: string
  attachments?: string[]
  createdAt?: string
  updatedAt?: string
  disabled?: boolean
}

// 定义里程碑接口
interface Milestone {
  name: string
  dueDate: string
  completed: boolean
}

// 统计数据接口
interface Statistics {
  totalPlans: number
  inProgressPlans: number
  preparingPlans: number
  notStartedPlans: number
  completedPlans: number
  delayedPlans: number
  plansByType: Record<string, number>
  plansByDepartment: Record<string, number>
  plansByPriority: Record<string, number>
  completionRate: number
  onTimePlans: number
  delayedRate: number
}

export function ProjectPlanning() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [selectedPlanType, setSelectedPlanType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [selectedDepartment, setSelectedDepartment] = useState("all")

  const [currentTab, setCurrentTab] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewSheetOpen, setIsViewSheetOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isBatchDeleteDialogOpen, setIsBatchDeleteDialogOpen] = useState(false)

  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [selectedPlans, setSelectedPlans] = useState<string[]>([])
  const [formData, setFormData] = useState<Partial<Plan>>({})
  const [exportFormat, setExportFormat] = useState("excel")
  const [sortConfig, setSortConfig] = useState<{column: string, direction: 'asc' | 'desc'} | null>(null)

  // 示例数据
  const [plans, setPlans] = useState<Plan[]>([
    {
      id: "1",
      name: "矿区A3开发计划",
      type: "开发计划",
      startDate: "2025-01-01",
      endDate: "2025-03-15",
      manager: "张三",
      department: "开发部",
      status: "进行中",
      priority: "高",
      completion: "35%",
      lastUpdated: "2025-03-01",
    },
    {
      id: "2",
      name: "设备更新年度计划",
      type: "设备计划",
      startDate: "2025-01-10",
      endDate: "2025-04-10",
      manager: "李四",
      department: "设备部",
      status: "准备中",
      priority: "中",
      completion: "10%",
      lastUpdated: "2025-02-10",
    },
    {
      id: "3",
      name: "安全系统升级计划",
      type: "安全计划",
      startDate: "2025-01-15",
      endDate: "2025-03-28",
      manager: "王五",
      department: "安全部",
      status: "进行中",
      priority: "高",
      completion: "60%",
      lastUpdated: "2025-02-12",
    },
    {
      id: "4",
      name: "新矿区勘探计划",
      type: "勘探计划",
      startDate: "2025-03-01",
      endDate: "2025-04-15",
      manager: "赵六",
      department: "勘探部",
      status: "未开始",
      priority: "中",
      completion: "0%",
      lastUpdated: "2025-03-05",
    },
    {
      id: "5",
      name: "环保设施改造计划",
      type: "环保计划",
      startDate: "2025-02-15",
      endDate: "2025-04-15",
      manager: "钱七",
      department: "环保部",
      status: "准备中",
      priority: "中",
      completion: "15%",
      lastUpdated: "2025-02-14",
    },
  ])

  // 统计数据
  const [statistics, setStatistics] = useState<Statistics>({
    totalPlans: 0,
    inProgressPlans: 0,
    preparingPlans: 0,
    notStartedPlans: 0,
    completedPlans: 0,
    delayedPlans: 0,
    plansByType: {},
    plansByDepartment: {},
    plansByPriority: {},
    completionRate: 0,
    onTimePlans: 0,
    delayedRate: 0
  })

  // 当前时间
  const [currentTime, setCurrentTime] = useState(new Date())

  // 计算统计数据
  const calculateStatistics = (plansList: Plan[]) => {
    const stats: Statistics = {
      totalPlans: plansList.length,
      inProgressPlans: 0,
      preparingPlans: 0,
      notStartedPlans: 0,
      completedPlans: 0,
      delayedPlans: 0,
      plansByType: {},
      plansByDepartment: {},
      plansByPriority: {},
      completionRate: 0,
      onTimePlans: 0,
      delayedRate: 0
    }

    // 初始化类型统计
    const typeCount: Record<string, number> = {}
    const departmentCount: Record<string, number> = {}
    const priorityCount: Record<string, number> = {}

    plansList.forEach(plan => {
      // 按状态统计
      if (plan.status === "进行中") {
        stats.inProgressPlans++
      } else if (plan.status === "准备中") {
        stats.preparingPlans++
      } else if (plan.status === "未开始") {
        stats.notStartedPlans++
      } else if (plan.status === "已完成") {
        stats.completedPlans++
      }

      // 延期计划
      const today = new Date()
      const endDate = new Date(plan.endDate)
      const completionNumber = parseInt(plan.completion) || 0

      if (plan.status !== "已完成" && endDate < today) {
        stats.delayedPlans++
      }

      // 按时完成的计划
      if (plan.status === "已完成") {
        const completionDate = new Date(plan.lastUpdated)
        if (completionDate <= endDate) {
          stats.onTimePlans++
        }
      }

      // 按类型分组
      if (plan.type) {
        typeCount[plan.type] = (typeCount[plan.type] || 0) + 1
      }

      // 按部门分组
      if (plan.department) {
        departmentCount[plan.department] = (departmentCount[plan.department] || 0) + 1
      }

      // 按优先级分组
      if (plan.priority) {
        priorityCount[plan.priority] = (priorityCount[plan.priority] || 0) + 1
      }
    })

    // 计算完成率
    stats.completionRate = plansList.length > 0
      ? Math.round((stats.completedPlans / plansList.length) * 100)
      : 0

    // 计算延期率
    stats.delayedRate = plansList.length > 0
      ? Math.round((stats.delayedPlans / plansList.length) * 100)
      : 0

    stats.plansByType = typeCount
    stats.plansByDepartment = departmentCount
    stats.plansByPriority = priorityCount

    return stats
  }

  // 更新统计数据和当前时间
  useEffect(() => {
    setStatistics(calculateStatistics(plans))

    // 每分钟更新一次当前时间
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [plans])

  // 显示消息提示
  const showMessage = (type: 'success' | 'error', title: string, description?: string) => {
    toast({
      title,
      description,
      variant: type === 'success' ? 'default' : 'destructive',
    })
  }

  // 过滤和排序计划
  const filteredAndSortedPlans = useMemo(() => {
    // 先过滤
    let result = [...plans]

    // 按标签过滤
    if (currentTab !== "all") {
      result = result.filter(plan => {
        if (currentTab === "ongoing") return plan.status === "进行中"
        if (currentTab === "preparing") return plan.status === "准备中"
        if (currentTab === "notstarted") return plan.status === "未开始"
        if (currentTab === "completed") return plan.status === "已完成"
        return true
      })
    }

    // 按类型过滤
    if (selectedPlanType !== "all") {
      result = result.filter(plan => plan.type === selectedPlanType)
    }

    // 按状态过滤
    if (selectedStatus !== "all") {
      result = result.filter(plan => plan.status === selectedStatus)
    }

    // 按优先级过滤
    if (selectedPriority !== "all") {
      result = result.filter(plan => plan.priority === selectedPriority)
    }

    // 按部门过滤
    if (selectedDepartment !== "all") {
      result = result.filter(plan => plan.department === selectedDepartment)
    }

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase()
      result = result.filter(plan =>
        plan.name.toLowerCase().includes(searchLower) ||
        plan.manager.toLowerCase().includes(searchLower) ||
        plan.department.toLowerCase().includes(searchLower) ||
        plan.type.toLowerCase().includes(searchLower)
      )
    }

    // 排序
    if (sortConfig) {
      result.sort((a, b) => {
        // 获取排序字段
        const aValue = a[sortConfig.column as keyof Plan]
        const bValue = b[sortConfig.column as keyof Plan]

        // 数字类型排序
        if (sortConfig.column === 'completion') {
          const aNum = parseInt((aValue as string) || '0')
          const bNum = parseInt((bValue as string) || '0')
          return sortConfig.direction === 'asc' ? aNum - bNum : bNum - aNum
        }

        // 日期类型排序
        if (sortConfig.column === 'startDate' || sortConfig.column === 'endDate' || sortConfig.column === 'lastUpdated') {
          const aDate = new Date(aValue as string)
          const bDate = new Date(bValue as string)
          return sortConfig.direction === 'asc'
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime()
        }

        // 字符串类型排序
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue)
        }

        return 0
      })
    }

    return result
  }, [plans, currentTab, selectedPlanType, selectedStatus, selectedPriority, selectedDepartment, searchText, sortConfig])

  // 处理排序
  const handleSort = (column: string) => {
    if (sortConfig && sortConfig.column === column) {
      // 切换相同列的排序方向
      setSortConfig({
        column,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc'
      })
    } else {
      // 设置新的排序列（默认升序）
      setSortConfig({
        column,
        direction: 'asc'
      })
    }
  }

  // 获取唯一的计划类型、状态、优先级和部门列表
  const planTypes = useMemo(() => Array.from(new Set(plans.map(plan => plan.type))), [plans])
  const planStatuses = useMemo(() => Array.from(new Set(plans.map(plan => plan.status))), [plans])
  const planPriorities = useMemo(() => Array.from(new Set(plans.map(plan => plan.priority))), [plans])
  const planDepartments = useMemo(() => Array.from(new Set(plans.map(plan => plan.department))), [plans])

  // 处理导出Excel
  const handleExportExcel = () => {
    setIsExportDialogOpen(true)
  }

  // 确认导出
  const confirmExport = () => {
    try {
      setLoading(true)

      // 准备导出数据
      const exportData = filteredAndSortedPlans.map(plan => ({
        "计划名称": plan.name,
        "计划类型": plan.type,
        "开始日期": plan.startDate,
        "结束日期": plan.endDate,
        "负责人": plan.manager,
        "所属部门": plan.department,
        "状态": plan.status,
        "优先级": plan.priority,
        "完成进度": plan.completion,
        "最后更新": plan.lastUpdated,
        "描述": plan.description || "",
        "目标": plan.objectives || "",
        "资源": plan.resources || "",
        "风险": plan.risks || "",
        "预算": plan.budget || ""
      }))

      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const columnWidths = [
        { wch: 20 }, // 计划名称
        { wch: 15 }, // 计划类型
        { wch: 15 }, // 开始日期
        { wch: 15 }, // 结束日期
        { wch: 10 }, // 负责人
        { wch: 15 }, // 所属部门
        { wch: 10 }, // 状态
        { wch: 10 }, // 优先级
        { wch: 10 }, // 完成进度
        { wch: 15 }, // 最后更新
        { wch: 30 }, // 描述
        { wch: 30 }, // 目标
        { wch: 30 }, // 资源
        { wch: 30 }, // 风险
        { wch: 15 }  // 预算
      ]

      worksheet['!cols'] = columnWidths

      // 添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, "计划列表")

      // 导出
      if (exportFormat === "excel") {
        XLSX.writeFile(workbook, `计划列表_${new Date().toISOString().slice(0, 10)}.xlsx`)
        showMessage("success", "导出成功", "计划数据已成功导出为Excel文件")
      } else if (exportFormat === "csv") {
        XLSX.writeFile(workbook, `计划列表_${new Date().toISOString().slice(0, 10)}.csv`)
        showMessage("success", "导出成功", "计划数据已成功导出为CSV文件")
      }
    } catch (error) {
      console.error("导出失败:", error)
      showMessage("error", "导出失败", "导出过程中发生错误，请重试")
    } finally {
      setLoading(false)
      setIsExportDialogOpen(false)
    }
  }

  // 处理导入
  const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setLoading(true)
      const file = e.target.files?.[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = e.target?.result
          if (!data) return

          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)

          // 转换导入的数据到计划格式
          const importedPlans: Plan[] = jsonData.map((row: any, index) => {
            return {
              id: `imported-${Date.now()}-${index}`,
              name: row["计划名称"] || `导入计划 ${index + 1}`,
              type: row["计划类型"] || "",
              startDate: row["开始日期"] || "",
              endDate: row["结束日期"] || "",
              manager: row["负责人"] || "",
              department: row["所属部门"] || "",
              status: row["状态"] || "未开始",
              priority: row["优先级"] || "中",
              completion: row["完成进度"]?.toString() || "0",
              lastUpdated: row["最后更新"] || new Date().toISOString().split('T')[0],
              description: row["描述"] || "",
              objectives: row["目标"] || "",
              resources: row["资源"] || "",
              risks: row["风险"] || "",
              budget: row["预算"] || "",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          })

          // 添加到现有计划列表
          setPlans(prev => [...prev, ...importedPlans])
          showMessage("success", "导入成功", `成功导入 ${importedPlans.length} 个计划`)
        } catch (error) {
          console.error("处理导入文件失败:", error)
          showMessage("error", "导入失败", "文件格式不正确或内容有误")
        }
      }

      reader.readAsArrayBuffer(file)
    } catch (error) {
      console.error("导入失败:", error)
      showMessage("error", "导入失败", "导入过程中发生错误")
    } finally {
      setLoading(false)
      setIsImportDialogOpen(false)
      // 清空文件输入
      if (e.target) e.target.value = ""
    }
  }

  // 处理添加计划
  const handleAddPlan = () => {
    // 打开添加对话框并重置表单
    setFormData({
      name: "",
      type: "",
      startDate: "",
      endDate: "",
      manager: "",
      department: "",
      status: "未开始",
      priority: "中",
      completion: "0",
      description: "",
      objectives: "",
      resources: "",
      risks: "",
      budget: "",
      milestones: []
    })
    setIsAddDialogOpen(true)
  }

  // 处理保存新计划
  const handleSavePlan = () => {
    if (!formData.name || !formData.startDate || !formData.endDate) {
      showMessage("error", "保存失败", "请填写必填字段")
      return
    }

    setLoading(true)

    try {
      const newPlan: Plan = {
        id: `plan-${Date.now()}`,
        name: formData.name || "",
        type: formData.type || "",
        startDate: formData.startDate || "",
        endDate: formData.endDate || "",
        manager: formData.manager || "",
        department: formData.department || "",
        status: formData.status || "未开始",
        priority: formData.priority || "中",
        completion: formData.completion || "0",
        lastUpdated: new Date().toISOString().split('T')[0],
        description: formData.description,
        objectives: formData.objectives,
        resources: formData.resources,
        risks: formData.risks,
        budget: formData.budget,
        milestones: formData.milestones,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 添加到计划列表
      setPlans(prev => [...prev, newPlan])
      showMessage("success", "添加成功", `计划"${newPlan.name}"已成功添加`)
      setIsAddDialogOpen(false)
    } catch (error) {
      console.error("添加计划失败:", error)
      showMessage("error", "添加失败", "添加过程中发生错误")
    } finally {
      setLoading(false)
    }
  }

  // 处理编辑计划
  const handleEditPlan = (plan: Plan) => {
    setSelectedPlan(plan)
    setFormData({
      name: plan.name,
      type: plan.type,
      startDate: plan.startDate,
      endDate: plan.endDate,
      manager: plan.manager,
      department: plan.department,
      status: plan.status,
      priority: plan.priority,
      completion: plan.completion,
      lastUpdated: plan.lastUpdated,
      description: plan.description,
      objectives: plan.objectives,
      resources: plan.resources,
      risks: plan.risks,
      budget: plan.budget,
      milestones: plan.milestones || []
    })
    setIsEditDialogOpen(true)
  }

  // 处理更新计划
  const handleUpdatePlan = () => {
    if (!selectedPlan || !formData.name || !formData.startDate || !formData.endDate) {
      showMessage("error", "更新失败", "请填写必填字段")
      return
    }

    setLoading(true)

    try {
      const updatedPlan: Plan = {
        ...selectedPlan,
        name: formData.name,
        type: formData.type || selectedPlan.type,
        startDate: formData.startDate,
        endDate: formData.endDate,
        manager: formData.manager || selectedPlan.manager,
        department: formData.department || selectedPlan.department,
        status: formData.status || selectedPlan.status,
        priority: formData.priority || selectedPlan.priority,
        completion: formData.completion || selectedPlan.completion,
        lastUpdated: new Date().toISOString().split('T')[0],
        description: formData.description,
        objectives: formData.objectives,
        resources: formData.resources,
        risks: formData.risks,
        budget: formData.budget,
        milestones: formData.milestones,
        updatedAt: new Date().toISOString()
      }

      // 更新计划列表
      setPlans(prev => prev.map(plan => plan.id === selectedPlan.id ? updatedPlan : plan))
      showMessage("success", "更新成功", `计划"${updatedPlan.name}"已成功更新`)
      setIsEditDialogOpen(false)
    } catch (error) {
      console.error("更新计划失败:", error)
      showMessage("error", "更新失败", "更新过程中发生错误")
    } finally {
      setLoading(false)
    }
  }

  // 处理查看计划
  const handleViewPlan = (plan: Plan) => {
    setSelectedPlan(plan)
    setIsViewSheetOpen(true)
  }

  // 处理删除计划
  const handleDeletePlan = (plan: Plan) => {
    setSelectedPlan(plan)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (!selectedPlan) return

    setLoading(true)

    try {
      // 从计划列表中移除
      setPlans(prev => prev.filter(plan => plan.id !== selectedPlan.id))
      showMessage("success", "删除成功", `计划"${selectedPlan.name}"已成功删除`)
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error("删除计划失败:", error)
      showMessage("error", "删除失败", "删除过程中发生错误")
    } finally {
      setLoading(false)
    }
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedPlans.length === 0) {
      showMessage("error", "请选择要删除的计划", "未选择任何计划")
      return
    }

    setIsBatchDeleteDialogOpen(true)
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    if (selectedPlans.length === 0) return

    setLoading(true)

    try {
      // 从计划列表中移除选中的计划
      setPlans(prev => prev.filter(plan => !selectedPlans.includes(plan.id)))
      showMessage("success", "批量删除成功", `成功删除 ${selectedPlans.length} 个计划`)
      setSelectedPlans([])
      setIsBatchDeleteDialogOpen(false)
    } catch (error) {
      console.error("批量删除计划失败:", error)
      showMessage("error", "批量删除失败", "删除过程中发生错误")
    } finally {
      setLoading(false)
    }
  }

  // 处理表单字段变更
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // 处理里程碑添加
  const handleAddMilestone = () => {
    const newMilestone: Milestone = {
      name: "",
      dueDate: "",
      completed: false
    }

    setFormData(prev => ({
      ...prev,
      milestones: [...(prev.milestones || []), newMilestone]
    }))
  }

  // 处理里程碑编辑
  const handleEditMilestone = (index: number, field: keyof Milestone, value: any) => {
    if (!formData.milestones) return

    const updatedMilestones = [...formData.milestones]
    updatedMilestones[index] = {
      ...updatedMilestones[index],
      [field]: value
    }

    setFormData(prev => ({
      ...prev,
      milestones: updatedMilestones
    }))
  }

  // 处理里程碑删除
  const handleRemoveMilestone = (index: number) => {
    if (!formData.milestones) return

    const updatedMilestones = formData.milestones.filter((_, i) => i !== index)

    setFormData(prev => ({
      ...prev,
      milestones: updatedMilestones
    }))
  }

  // 处理选择计划
  const handleSelectPlan = (planId: string, checked: boolean) => {
    if (checked) {
      setSelectedPlans(prev => [...prev, planId])
    } else {
      setSelectedPlans(prev => prev.filter(id => id !== planId))
    }
  }

  // 处理选择全部计划
  const handleSelectAllPlans = (checked: boolean) => {
    if (checked) {
      setSelectedPlans(filteredAndSortedPlans.map(plan => plan.id))
    } else {
      setSelectedPlans([])
    }
  }

  // 处理打开统计抽屉
  const handleOpenStats = () => {
    setIsStatsDrawerOpen(true)
  }

  // 获取状态对应的徽章样式
  const getBadgeVariantForStatus = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "进行中":
        return "default"
      case "准备中":
        return "secondary"
      case "已完成":
        return "outline"
      case "未开始":
        return "outline"
      default:
        return "destructive"
    }
  }

  // 获取优先级对应的徽章样式
  const getBadgeVariantForPriority = (priority: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (priority) {
      case "高":
        return "destructive"
      case "中":
        return "default"
      case "低":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <div className="flex flex-col h-full gap-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">项目规划</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleOpenStats}>
            <BarChart2 className="mr-2 h-4 w-4" />
            <span>统计分析</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                <span>日历视图</span>
          </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Calendar className="mr-2 h-4 w-4" />
                <span>月视图</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calendar className="mr-2 h-4 w-4" />
                <span>周视图</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calendar className="mr-2 h-4 w-4" />
                <span>天视图</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                <span>导出</span>
          </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleExportExcel}>
                <FileText className="mr-2 h-4 w-4" />
                <span>导出数据</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsImportDialogOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                <span>导入数据</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={handleAddPlan}>
            <Plus className="mr-2 h-4 w-4" />
            <span>添加计划</span>
              </Button>
        </div>
      </div>

      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总计划数</CardTitle>
            <div className="bg-blue-100 p-2 rounded-full">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalPlans}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {statistics.inProgressPlans} 个进行中, {statistics.preparingPlans} 个准备中
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">计划进度</CardTitle>
            <div className="bg-green-100 p-2 rounded-full">
              <RefreshCw className="h-5 w-5 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.completionRate}%</div>
            <p className="text-xs text-muted-foreground mt-1">
              已完成 {statistics.completedPlans} 个计划
            </p>
            <div className="mt-2">
              <Progress value={statistics.completionRate} className="h-2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">按时完成率</CardTitle>
            <div className="bg-amber-100 p-2 rounded-full">
              <Clock className="h-5 w-5 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.totalPlans > 0
                ? Math.round((statistics.onTimePlans / statistics.totalPlans) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {statistics.delayedPlans} 个计划已延期
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">延期率</CardTitle>
            <div className="bg-purple-100 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.delayedRate}%</div>
            <p className="text-xs text-muted-foreground mt-1">
              {statistics.delayedPlans} 个计划已延期
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="flex-1">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle>项目计划列表</CardTitle>
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-auto">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="ongoing">进行中</TabsTrigger>
                <TabsTrigger value="preparing">准备中</TabsTrigger>
                <TabsTrigger value="notstarted">未开始</TabsTrigger>
                <TabsTrigger value="completed">已完成</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-3">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索计划..."
                className="pl-8"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
            </div>
            <Select value={selectedPlanType} onValueChange={setSelectedPlanType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="计划类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                {planTypes.map((type) => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {planStatuses.map((status) => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon" onClick={() => {
              setSearchText("")
              setSelectedPlanType("all")
              setSelectedStatus("all")
              setSelectedPriority("all")
              setSelectedDepartment("all")
              setCurrentTab("all")
            }}>
              <X className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>高级筛选</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 p-2">
                <p className="text-sm font-medium mb-2 px-2">优先级</p>
                <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部优先级</SelectItem>
                    {planPriorities.map((priority) => (
                      <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm font-medium mb-2 px-2 mt-3">所属部门</p>
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部部门</SelectItem>
                    {planDepartments.map((department) => (
                      <SelectItem key={department} value={department}>{department}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </DropdownMenuContent>
            </DropdownMenu>
            {selectedPlans.length > 0 && (
              <Button variant="destructive" onClick={handleBatchDelete}>
                <Trash2 className="mr-2 h-4 w-4" />
                <span>批量删除 ({selectedPlans.length})</span>
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead style={{ width: 32 }}>
                  <Checkbox
                    checked={
                      filteredAndSortedPlans.length > 0 &&
                      selectedPlans.length === filteredAndSortedPlans.length
                    }
                    onCheckedChange={handleSelectAllPlans}
                  />
                </TableHead>
                <TableHead onClick={() => handleSort('name')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>计划名称</span>
                    {sortConfig?.column === 'name' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('type')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>类型</span>
                    {sortConfig?.column === 'type' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('startDate')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>开始日期</span>
                    {sortConfig?.column === 'startDate' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('endDate')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>结束日期</span>
                    {sortConfig?.column === 'endDate' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('manager')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>负责人</span>
                    {sortConfig?.column === 'manager' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('department')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>所属部门</span>
                    {sortConfig?.column === 'department' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('status')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>状态</span>
                    {sortConfig?.column === 'status' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('priority')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>优先级</span>
                    {sortConfig?.column === 'priority' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('completion')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>完成进度</span>
                    {sortConfig?.column === 'completion' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort('lastUpdated')} className="cursor-pointer">
                  <div className="flex items-center space-x-1">
                    <span>最后更新</span>
                    {sortConfig?.column === 'lastUpdated' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedPlans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell className="w-32">
                    <Checkbox
                      checked={selectedPlans.includes(plan.id)}
                      onCheckedChange={(checked) => handleSelectPlan(plan.id, !!checked)}
                    />
                  </TableCell>
                  <TableCell>{plan.name}</TableCell>
                  <TableCell>{plan.type}</TableCell>
                  <TableCell>{plan.startDate}</TableCell>
                  <TableCell>{plan.endDate}</TableCell>
                  <TableCell>{plan.manager}</TableCell>
                  <TableCell>{plan.department}</TableCell>
                  <TableCell>
                    <Badge variant={getBadgeVariantForStatus(plan.status)}>
                      {plan.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getBadgeVariantForPriority(plan.priority)}>
                      {plan.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-[80px]">
                        <Progress value={parseInt(plan.completion)} className="h-2" />
                      </div>
                      <span className="text-xs">{plan.completion}%</span>
                    </div>
                  </TableCell>
                  <TableCell>{plan.lastUpdated}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewPlan(plan)}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>查看详情</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditPlan(plan)}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>编辑计划</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeletePlan(plan)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>删除计划</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredAndSortedPlans.length} 个计划</div>
        </CardFooter>
      </Card>

      {/* 添加计划对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
            <DialogTitle>添加新计划</DialogTitle>
            <DialogDescription>创建新的项目计划并分配相关资源和负责人</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                <Label htmlFor="name">计划名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="输入计划名称"
                  value={formData.name}
                  onChange={(e) => handleFormChange("name", e.target.value)}
                />
                  </div>
                  <div className="space-y-2">
                <Label htmlFor="type">计划类型</Label>
                <Select value={formData.type} onValueChange={(value) => handleFormChange("type", value)}>
                  <SelectTrigger id="type">
                        <SelectValue placeholder="选择计划类型" />
                      </SelectTrigger>
                      <SelectContent>
                    <SelectItem value="开发计划">开发计划</SelectItem>
                    <SelectItem value="设备计划">设备计划</SelectItem>
                    <SelectItem value="安全计划">安全计划</SelectItem>
                    <SelectItem value="勘探计划">勘探计划</SelectItem>
                    <SelectItem value="环保计划">环保计划</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                <Label htmlFor="startDate">开始日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleFormChange("startDate", e.target.value)}
                />
                  </div>
                  <div className="space-y-2">
                <Label htmlFor="endDate">结束日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleFormChange("endDate", e.target.value)}
                />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="manager">负责人</Label>
                <Input
                  id="manager"
                  placeholder="输入负责人姓名"
                  value={formData.manager}
                  onChange={(e) => handleFormChange("manager", e.target.value)}
                />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">所属部门</Label>
                <Select value={formData.department} onValueChange={(value) => handleFormChange("department", value)}>
                      <SelectTrigger id="department">
                        <SelectValue placeholder="选择所属部门" />
                      </SelectTrigger>
                      <SelectContent>
                    <SelectItem value="开发部">开发部</SelectItem>
                    <SelectItem value="设备部">设备部</SelectItem>
                    <SelectItem value="安全部">安全部</SelectItem>
                    <SelectItem value="勘探部">勘探部</SelectItem>
                    <SelectItem value="环保部">环保部</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                <Select value={formData.status} onValueChange={(value) => handleFormChange("status", value)}>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="准备中">准备中</SelectItem>
                    <SelectItem value="未开始">未开始</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priority">优先级</Label>
                <Select value={formData.priority} onValueChange={(value) => handleFormChange("priority", value)}>
                      <SelectTrigger id="priority">
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
            <div className="space-y-2">
              <Label htmlFor="completion">完成进度</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="completion"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.completion}
                  onChange={(e) => handleFormChange("completion", e.target.value)}
                />
                <span>%</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="budget">预算</Label>
              <Input
                id="budget"
                placeholder="输入计划预算"
                value={formData.budget}
                onChange={(e) => handleFormChange("budget", e.target.value)}
              />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">计划描述</Label>
              <Textarea
                id="description"
                placeholder="输入计划详细描述"
                rows={3}
                value={formData.description}
                onChange={(e) => handleFormChange("description", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="objectives">计划目标</Label>
              <Textarea
                id="objectives"
                placeholder="输入计划目标"
                rows={3}
                value={formData.objectives}
                onChange={(e) => handleFormChange("objectives", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="resources">所需资源</Label>
              <Textarea
                id="resources"
                placeholder="输入所需资源"
                rows={3}
                value={formData.resources}
                onChange={(e) => handleFormChange("resources", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="risks">风险分析</Label>
              <Textarea
                id="risks"
                placeholder="输入风险分析"
                rows={3}
                value={formData.risks}
                onChange={(e) => handleFormChange("risks", e.target.value)}
              />
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>里程碑</Label>
                <Button type="button" variant="outline" size="sm" onClick={handleAddMilestone}>
                  <Plus className="h-4 w-4 mr-1" /> 添加里程碑
                </Button>
              </div>
              {formData.milestones?.map((milestone, index) => (
                <div key={index} className="border rounded-md p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">里程碑 #{index + 1}</h4>
                    <Button type="button" variant="ghost" size="icon" onClick={() => handleRemoveMilestone(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`milestone-name-${index}`}>名称</Label>
                    <Input
                      id={`milestone-name-${index}`}
                      value={milestone.name}
                      onChange={(e) => handleEditMilestone(index, "name", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`milestone-date-${index}`}>截止日期</Label>
                    <Input
                      id={`milestone-date-${index}`}
                      type="date"
                      value={milestone.dueDate}
                      onChange={(e) => handleEditMilestone(index, "dueDate", e.target.value)}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`milestone-completed-${index}`}
                      checked={milestone.completed}
                      onCheckedChange={(checked) => handleEditMilestone(index, "completed", !!checked)}
                    />
                    <Label htmlFor={`milestone-completed-${index}`}>已完成</Label>
                  </div>
                </div>
              ))}
                </div>
              </div>
              <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
            <Button onClick={handleSavePlan} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑计划对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑计划</DialogTitle>
            <DialogDescription>修改项目计划的详细信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">计划名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-name"
                  placeholder="输入计划名称"
                  value={formData.name}
                  onChange={(e) => handleFormChange("name", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">计划类型</Label>
                <Select value={formData.type} onValueChange={(value) => handleFormChange("type", value)}>
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择计划类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="开发计划">开发计划</SelectItem>
                    <SelectItem value="设备计划">设备计划</SelectItem>
                    <SelectItem value="安全计划">安全计划</SelectItem>
                    <SelectItem value="勘探计划">勘探计划</SelectItem>
                    <SelectItem value="环保计划">环保计划</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-startDate">开始日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleFormChange("startDate", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-endDate">结束日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleFormChange("endDate", e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-manager">负责人</Label>
                <Input
                  id="edit-manager"
                  placeholder="输入负责人姓名"
                  value={formData.manager}
                  onChange={(e) => handleFormChange("manager", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-department">所属部门</Label>
                <Select value={formData.department} onValueChange={(value) => handleFormChange("department", value)}>
                  <SelectTrigger id="edit-department">
                    <SelectValue placeholder="选择所属部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="开发部">开发部</SelectItem>
                    <SelectItem value="设备部">设备部</SelectItem>
                    <SelectItem value="安全部">安全部</SelectItem>
                    <SelectItem value="勘探部">勘探部</SelectItem>
                    <SelectItem value="环保部">环保部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select value={formData.status} onValueChange={(value) => handleFormChange("status", value)}>
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="准备中">准备中</SelectItem>
                    <SelectItem value="未开始">未开始</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-priority">优先级</Label>
                <Select value={formData.priority} onValueChange={(value) => handleFormChange("priority", value)}>
                  <SelectTrigger id="edit-priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-completion">完成进度</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="edit-completion"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.completion}
                  onChange={(e) => handleFormChange("completion", e.target.value)}
                />
                <span>%</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-budget">预算</Label>
              <Input
                id="edit-budget"
                placeholder="输入计划预算"
                value={formData.budget}
                onChange={(e) => handleFormChange("budget", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">计划描述</Label>
              <Textarea
                id="edit-description"
                placeholder="输入计划详细描述"
                rows={3}
                value={formData.description}
                onChange={(e) => handleFormChange("description", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-objectives">计划目标</Label>
              <Textarea
                id="edit-objectives"
                placeholder="输入计划目标"
                rows={3}
                value={formData.objectives}
                onChange={(e) => handleFormChange("objectives", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-resources">所需资源</Label>
              <Textarea
                id="edit-resources"
                placeholder="输入所需资源"
                rows={3}
                value={formData.resources}
                onChange={(e) => handleFormChange("resources", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-risks">风险分析</Label>
              <Textarea
                id="edit-risks"
                placeholder="输入风险分析"
                rows={3}
                value={formData.risks}
                onChange={(e) => handleFormChange("risks", e.target.value)}
              />
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>里程碑</Label>
                <Button type="button" variant="outline" size="sm" onClick={handleAddMilestone}>
                  <Plus className="h-4 w-4 mr-1" /> 添加里程碑
                </Button>
              </div>
              {formData.milestones?.map((milestone, index) => (
                <div key={index} className="border rounded-md p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">里程碑 #{index + 1}</h4>
                    <Button type="button" variant="ghost" size="icon" onClick={() => handleRemoveMilestone(index)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`edit-milestone-name-${index}`}>名称</Label>
                    <Input
                      id={`edit-milestone-name-${index}`}
                      value={milestone.name}
                      onChange={(e) => handleEditMilestone(index, "name", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`edit-milestone-date-${index}`}>截止日期</Label>
                    <Input
                      id={`edit-milestone-date-${index}`}
                      type="date"
                      value={milestone.dueDate}
                      onChange={(e) => handleEditMilestone(index, "dueDate", e.target.value)}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`edit-milestone-completed-${index}`}
                      checked={milestone.completed}
                      onCheckedChange={(checked) => handleEditMilestone(index, "completed", !!checked)}
                    />
                    <Label htmlFor={`edit-milestone-completed-${index}`}>已完成</Label>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleUpdatePlan} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              更新
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看计划详情抽屉 */}
      <Sheet open={isViewSheetOpen} onOpenChange={setIsViewSheetOpen}>
        <SheetContent className="sm:max-w-[640px] overflow-y-auto">
          <SheetHeader className="mb-6">
            <SheetTitle className="text-2xl">{selectedPlan?.name}</SheetTitle>
            <SheetDescription>
              项目计划详情
            </SheetDescription>
          </SheetHeader>
          {selectedPlan && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">计划类型</h3>
                  <p className="text-lg">{selectedPlan.type || "未指定"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">负责人</h3>
                  <p className="text-lg">{selectedPlan.manager || "未指定"}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">开始日期</h3>
                  <p className="text-lg">{selectedPlan.startDate}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">结束日期</h3>
                  <p className="text-lg">{selectedPlan.endDate}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">所属部门</h3>
                  <p className="text-lg">{selectedPlan.department || "未指定"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">优先级</h3>
                  <Badge variant={getBadgeVariantForPriority(selectedPlan.priority)}>
                    {selectedPlan.priority}
                  </Badge>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">状态</h3>
                  <Badge variant={getBadgeVariantForStatus(selectedPlan.status)}>
                    {selectedPlan.status}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">完成进度</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Progress value={parseInt(selectedPlan.completion)} className="h-2 w-[120px]" />
                    <span className="text-sm">{selectedPlan.completion}%</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">预算</h3>
                <p className="text-lg">{selectedPlan.budget || "未指定"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">计划描述</h3>
                <p className="text-sm mt-1">{selectedPlan.description || "无描述"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">计划目标</h3>
                <p className="text-sm mt-1">{selectedPlan.objectives || "无目标"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">所需资源</h3>
                <p className="text-sm mt-1">{selectedPlan.resources || "无资源需求"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">风险分析</h3>
                <p className="text-sm mt-1">{selectedPlan.risks || "无风险分析"}</p>
              </div>
              {selectedPlan.milestones && selectedPlan.milestones.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">里程碑</h3>
                  <div className="space-y-3">
                    {selectedPlan.milestones.map((milestone, index) => (
                      <div key={index} className="border rounded-md p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Checkbox checked={milestone.completed} disabled />
                            <h4 className="font-medium">{milestone.name || `里程碑 ${index + 1}`}</h4>
                          </div>
                          <span className="text-sm text-muted-foreground">{milestone.dueDate}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4 pt-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                  <p className="text-sm">{selectedPlan.createdAt ? new Date(selectedPlan.createdAt).toLocaleString() : "未知"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">最后更新</h3>
                  <p className="text-sm">{selectedPlan.lastUpdated}</p>
                </div>
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsViewSheetOpen(false)}>关闭</Button>
                <Button onClick={() => {
                  setIsViewSheetOpen(false)
                  handleEditPlan(selectedPlan)
                }}>
                  <Edit className="mr-2 h-4 w-4" />
                  编辑计划
                </Button>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除计划 "{selectedPlan?.name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量删除确认对话框 */}
      <AlertDialog open={isBatchDeleteDialogOpen} onOpenChange={setIsBatchDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认批量删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除所选的 {selectedPlans.length} 个计划吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBatchDelete} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 导出数据对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>导出计划数据</DialogTitle>
            <DialogDescription>选择导出格式和导出范围</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select value={exportFormat} onValueChange={setExportFormat}>
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>导出范围</Label>
              <div className="text-sm text-muted-foreground">
                当前将导出 {filteredAndSortedPlans.length} 条计划记录
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                  取消
                </Button>
            <Button onClick={confirmExport} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              导出
            </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

      {/* 导入数据对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>导入计划数据</DialogTitle>
            <DialogDescription>选择Excel或CSV文件导入计划数据</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="file-upload">选择文件</Label>
              <Input
                id="file-upload"
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleImport}
              />
        </div>
            <div className="text-sm text-muted-foreground">
              <p>支持的文件格式: Excel (.xlsx, .xls) 和 CSV (.csv)</p>
              <p className="mt-2">请确保文件包含以下列: 计划名称, 计划类型, 开始日期, 结束日期等</p>
      </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计抽屉 */}
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-[90%] sm:max-w-[640px] overflow-y-auto">
          <SheetHeader className="mb-6">
            <SheetTitle className="text-2xl">计划统计分析</SheetTitle>
            <SheetDescription>
              项目计划数据可视化分析
            </SheetDescription>
          </SheetHeader>
          <ScrollArea className="h-[calc(100vh-150px)]">
            <div className="space-y-8 pb-10">
              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <div className="bg-blue-100 p-1.5 rounded-full mr-2">
                    <BarChart2 className="h-4 w-4 text-blue-600" />
            </div>
                  基本统计数据
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Card className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">计划总数</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.totalPlans}</div>
          </CardContent>
        </Card>
                  <Card className="border-l-4 border-l-green-500">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">完成率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.completionRate}%</div>
                      <Progress value={statistics.completionRate} className="h-2 mt-2" />
                    </CardContent>
                  </Card>
                  <Card className="border-l-4 border-l-amber-500">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">按时完成率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {statistics.totalPlans > 0
                          ? Math.round((statistics.onTimePlans / statistics.totalPlans) * 100)
                          : 0}%
            </div>
          </CardContent>
        </Card>
                  <Card className="border-l-4 border-l-red-500">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">延期率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{statistics.delayedRate}%</div>
                    </CardContent>
                  </Card>
            </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <div className="bg-purple-100 p-1.5 rounded-full mr-2">
                    <FileBarChart className="h-4 w-4 text-purple-600" />
                  </div>
                  计划状态分布
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100">
                    <CardContent className="pt-6">
                      <div className="flex flex-col items-center">
                        <div className="rounded-full bg-blue-100 border border-blue-200 p-2 mb-2">
                          <RefreshCw className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="text-2xl font-bold">{statistics.inProgressPlans}</div>
                        <p className="text-sm text-muted-foreground">进行中</p>
                      </div>
          </CardContent>
        </Card>
                  <Card className="bg-gradient-to-br from-amber-50 to-amber-100">
                    <CardContent className="pt-6">
                      <div className="flex flex-col items-center">
                        <div className="rounded-full bg-amber-100 border border-amber-200 p-2 mb-2">
                          <Clock className="h-5 w-5 text-amber-600" />
                        </div>
                        <div className="text-2xl font-bold">{statistics.preparingPlans}</div>
                        <p className="text-sm text-muted-foreground">准备中</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-gray-50 to-gray-100">
                    <CardContent className="pt-6">
                      <div className="flex flex-col items-center">
                        <div className="rounded-full bg-gray-100 border border-gray-200 p-2 mb-2">
                          <FileText className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="text-2xl font-bold">{statistics.notStartedPlans}</div>
                        <p className="text-sm text-muted-foreground">未开始</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-green-50 to-green-100">
                    <CardContent className="pt-6">
                      <div className="flex flex-col items-center">
                        <div className="rounded-full bg-green-100 border border-green-200 p-2 mb-2">
                          <CheckCircle2 className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="text-2xl font-bold">{statistics.completedPlans}</div>
                        <p className="text-sm text-muted-foreground">已完成</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <div className="bg-green-100 p-1.5 rounded-full mr-2">
                    <FileText className="h-4 w-4 text-green-600" />
                  </div>
                  计划类型分布
                </h3>
        <Card>
                  <CardContent className="pt-6">
                    <div className="overflow-x-auto">
                      <div className="min-w-[400px]">
                        {Object.entries(statistics.plansByType).length > 0 ? (
                          <div className="space-y-2">
                            {Object.entries(statistics.plansByType).map(([type, count], index) => {
                              const percentage = Math.round((count / statistics.totalPlans) * 100)
                              const colors = [
                                "bg-blue-500", "bg-green-500", "bg-amber-500",
                                "bg-purple-500", "bg-indigo-500", "bg-rose-500"
                              ]
                              const color = colors[index % colors.length]
                              return (
                                <div key={type} className="space-y-1">
                                  <div className="flex justify-between text-sm">
                                    <div className="flex items-center gap-2">
                                      <div className={`rounded-full w-3 h-3 ${color}`}></div>
                                      <span>{type}</span>
            </div>
                                    <span>{count} 个 ({percentage}%)</span>
                                  </div>
                                  <div className="w-full bg-secondary h-2 rounded-full">
                                    <div
                                      className={`h-2 rounded-full ${color}`}
                                      style={{ width: `${percentage}%` }}
                                    ></div>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-muted-foreground">暂无类型数据</div>
                        )}
                      </div>
                    </div>
          </CardContent>
        </Card>
      </div>

              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <div className="bg-indigo-100 p-1.5 rounded-full mr-2">
                    <BarChart2 className="h-4 w-4 text-indigo-600" />
                  </div>
                  优先级分布
                </h3>
      <Card>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      {Object.entries(statistics.plansByPriority).map(([priority, count]) => {
                        let bgColor = "bg-gray-100"
                        let textColor = "text-gray-600"

                        if (priority === "高") {
                          bgColor = "bg-red-100"
                          textColor = "text-red-600"
                        } else if (priority === "中") {
                          bgColor = "bg-blue-100"
                          textColor = "text-blue-600"
                        } else if (priority === "低") {
                          bgColor = "bg-green-100"
                          textColor = "text-green-600"
                        }

                        return (
                          <Card key={priority} className={`${bgColor} border-none`}>
                            <CardContent className="p-4">
          <div className="flex justify-between items-center">
            <div>
                                  <div className="text-sm font-medium text-muted-foreground">
                                    {priority}优先级
            </div>
                                  <div className={`text-2xl font-bold ${textColor}`}>
                                    {count}
          </div>
              </div>
                                <div className={`text-sm ${textColor}`}>
                                  {Math.round((count / statistics.totalPlans) * 100) || 0}%
            </div>
          </div>
                            </CardContent>
                          </Card>
                        )
                      })}
          </div>
        </CardContent>
                </Card>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <div className="bg-amber-100 p-1.5 rounded-full mr-2">
                    <FileBarChart className="h-4 w-4 text-amber-600" />
                  </div>
                  部门分布
                </h3>
                <Card>
                  <CardContent className="pt-6">
                    <div className="overflow-x-auto">
                      <div className="min-w-[400px]">
                        {Object.entries(statistics.plansByDepartment).length > 0 ? (
                          <div className="space-y-2">
                            {Object.entries(statistics.plansByDepartment).map(([dept, count], index) => {
                              const percentage = Math.round((count / statistics.totalPlans) * 100)
                              const colors = [
                                "bg-green-500", "bg-blue-500", "bg-amber-500",
                                "bg-rose-500", "bg-purple-500", "bg-indigo-500"
                              ]
                              const color = colors[index % colors.length]
                              return (
                                <div key={dept} className="space-y-1">
                                  <div className="flex justify-between text-sm">
          <div className="flex items-center gap-2">
                                      <div className={`rounded-full w-3 h-3 ${color}`}></div>
                                      <span>{dept}</span>
          </div>
                                    <span>{count} 个 ({percentage}%)</span>
                                  </div>
                                  <div className="w-full bg-secondary h-2 rounded-full">
                                    <div
                                      className={`h-2 rounded-full ${color}`}
                                      style={{ width: `${percentage}%` }}
                                    ></div>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-muted-foreground">暂无部门数据</div>
                        )}
                      </div>
                    </div>
                  </CardContent>
      </Card>
              </div>
            </div>
          </ScrollArea>
          <SheetFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsStatsDrawerOpen(false)}>关闭</Button>
            <Button onClick={handleExportExcel}>
              <Download className="mr-2 h-4 w-4" />
              导出数据
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  )
}

