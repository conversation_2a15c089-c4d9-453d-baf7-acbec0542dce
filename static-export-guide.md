# 矿业公司综合管理系统 - 静态部署指南

本文档提供了如何将项目导出为静态文件并部署到Netlify的详细步骤。

## 静态导出步骤

### 1. 生成静态文件

运行以下命令生成静态文件：

```bash
npm run export
```

这将在项目根目录创建一个 `static-export` 文件夹，其中包含所有静态文件。

### 2. 部署到Netlify

#### 方法一：通过Netlify拖放部署

1. 登录到 [Netlify](https://app.netlify.com/)
2. 在主仪表板中，找到并点击"Sites"部分
3. 将生成的 `static-export` 文件夹拖放到指定区域
4. Netlify将自动上传和部署您的网站
5. 网站部署完成后，Netlify会提供一个随机生成的URL（例如：https://random-name-123456.netlify.app）

#### 方法二：使用Netlify CLI

1. 安装Netlify CLI（如果尚未安装）
```bash
npm install -g netlify-cli
```

2. 登录到Netlify账户
```bash
netlify login
```

3. 初始化新网站
```bash
cd static-export
netlify init
```

4. 按照CLI提示操作，选择"Create & configure a new site"
5. 部署网站
```bash
netlify deploy --prod
```

### 3. 自定义域名设置（可选）

1. 在Netlify仪表板中，选择您的站点
2. 点击"Domain settings"
3. 在"Custom domains"部分，点击"Add custom domain"
4. 输入您的域名并按照指示完成DNS设置

## 常见问题

### 页面刷新出现404问题

Netlify已通过自动生成的 `_redirects` 文件和 `netlify.toml` 配置处理SPA导航问题。这些文件在静态导出过程中自动添加到导出目录。

### 图片和资源路径问题

如果部署后发现图片或其他资源无法正常加载，请检查资源路径。确保所有资源路径都是相对于根目录的，或使用相对路径。

### 环境变量配置

对于需要环境变量的情况，您可以在Netlify的站点设置中的"Build & deploy"部分下的"Environment"选项卡进行配置。

## 部署更新

当您需要更新网站内容时，只需重新运行导出命令并将新生成的静态文件再次部署到Netlify即可。

```bash
npm run export
# 然后使用上述部署方法之一进行部署
```

如果您使用的是Netlify CLI，可以在执行导出后直接运行：

```bash
cd static-export
netlify deploy --prod
``` 