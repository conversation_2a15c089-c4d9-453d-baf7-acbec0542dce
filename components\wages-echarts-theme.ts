/**
 * ECharts 工资分析主题
 */
export const theme = {
  color: [
    '#3b82f6', // 蓝色 - 基础工资
    '#0ea5e9', // 浅蓝 - 绩效奖金
    '#22c55e', // 绿色 - 部门人数
    '#7c3aed', // 紫色 - 平均工资
    '#ef4444', // 红色 - 异常警告
    '#f59e0b', // 橙色 - 社保公积金
    '#10b981', // 深绿 - 增长
    '#f43f5e', // 粉红 - 减少
    '#64748b', // 青灰 - 预算
    '#6366f1'  // 靛蓝 - 其他数据
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'Roboto, Arial, sans-serif',
    fontSize: 12,
    color: '#475569'
  },
  title: {
    textStyle: {
      color: '#1e293b',
      fontWeight: 500,
      fontSize: 16
    },
    subtextStyle: {
      color: '#64748b',
      fontSize: 14
    }
  },
  legend: {
    textStyle: {
      color: '#475569'
    }
  },
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e2e8f0',
    borderWidth: 1,
    textStyle: {
      color: '#334155'
    },
    axisPointer: {
      lineStyle: {
        color: '#cbd5e1'
      },
      crossStyle: {
        color: '#94a3b8'
      },
      shadowStyle: {
        color: 'rgba(203, 213, 225, 0.2)'
      }
    }
  },
  // 图形样式
  itemStyle: {
    borderWidth: 0,
    borderColor: '#fff'
  },
  // 线条样式
  lineStyle: {
    width: 2
  },
  // 区域样式
  areaStyle: {
    opacity: 0.15
  },
  // 柱状图样式
  barStyle: {
    barBorderRadius: [4, 4, 0, 0]
  },
  // 饼图样式
  pieStyle: {
    borderRadius: 4
  },
  // 散点图样式
  scatterStyle: {
    borderWidth: 0
  },
  // 仪表盘样式
  gaugeStyle: {
    progress: {
      width: 10
    }
  }
} 