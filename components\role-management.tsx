"use client"

import { useState } from "react"
import {
  Search,
  Edit,
  Trash2,
  Plus,
  Download,
  Upload,
  MoreHorizontal,
  Check,
  X,
  Shield,
  Users,
  RefreshCw,
  AlertTriangle,
  UserCheck,
  UserMinus,
  FileText,
  Clock,
  Info,
  Settings,
  Key,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Switch } from "@/components/ui/switch"

interface Role {
  id: string
  name: string
  description: string
  users: number
  status: string
  createdAt: string
  permissions?: {
    system: string[]
    safety: string[]
    project: string[]
  }
}

export function RoleManagement() {
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false)
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [searchText, setSearchText] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(false)
  const [isRoleDetailOpen, setIsRoleDetailOpen] = useState(false)
  const [selectedRoleDetail, setSelectedRoleDetail] = useState<Role | null>(null)
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false)
  const [roleToEdit, setRoleToEdit] = useState<Role | null>(null)

  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    enabled: true,
    permissions: {
      system: [] as string[],
      safety: [] as string[],
      project: [] as string[],
    }
  })

  // 表单错误状态
  const [formErrors, setFormErrors] = useState({
    name: "",
    description: "",
  })

  const [roles, setRoles] = useState<Role[]>([
    {
      id: "1",
      name: "超级管理员",
      description: "系统最高权限，可以管理所有功能",
      users: 2,
      status: "启用",
      createdAt: "2025-01-15",
      permissions: {
        system: ["view", "add", "edit", "delete"],
        safety: ["view", "add", "edit", "delete"],
        project: ["view", "add", "edit", "delete"],
      }
    },
    {
      id: "2",
      name: "部门主管",
      description: "可以管理本部门的所有功能和人员",
      users: 8,
      status: "启用",
      createdAt: "2025-02-20",
    },
    {
      id: "3",
      name: "安全管理员",
      description: "负责安全相关功能的管理",
      users: 5,
      status: "启用",
      createdAt: "2025-03-10",
    },
    {
      id: "4",
      name: "普通用户",
      description: "基本的系统使用权限",
      users: 45,
      status: "启用",
      createdAt: "2025-01-20",
    },
    {
      id: "5",
      name: "临时角色",
      description: "临时访问权限，有效期限制",
      users: 3,
      status: "禁用",
      createdAt: "2025-04-15",
    },
  ])

  // 统计信息
  const stats = {
    total: roles.length,
    active: roles.filter(role => role.status === "启用").length,
    disabled: roles.filter(role => role.status === "禁用").length,
    totalUsers: roles.reduce((sum, role) => sum + role.users, 0),
  }

  const toggleSelectRole = (roleId: string) => {
    setSelectedRoles((prev) => (prev.includes(roleId) ? prev.filter((id) => id !== roleId) : [...prev, roleId]))
  }

  const toggleSelectAll = () => {
    if (selectedRoles.length === roles.length) {
      setSelectedRoles([])
    } else {
      setSelectedRoles(roles.map((role) => role.id))
    }
  }

  // 处理导出
  const handleExport = () => {
    try {
      const exportData = roles.map(role => ({
        '角色名称': role.name,
        '描述': role.description,
        '用户数量': role.users,
        '状态': role.status,
        '创建时间': role.createdAt,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 15 }, // 角色名称
        { wch: 30 }, // 描述
        { wch: 10 }, // 用户数量
        { wch: 10 }, // 状态
        { wch: 20 }, // 创建时间
      ]
      ws['!cols'] = colWidths

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      }

      // 为表头添加样式
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1"
        if (!ws[address]) continue
        ws[address].s = headerStyle
      }

      XLSX.utils.book_append_sheet(wb, ws, '角色列表')
      XLSX.writeFile(wb, `角色列表_${new Date().toLocaleDateString()}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 处理导入
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 转换导入的数据为角色格式
        const importedRoles: Role[] = jsonData.map((row: any, index) => ({
          id: (Date.now() + index).toString(),
          name: row['角色名称'] || '',
          description: row['描述'] || '',
          users: Number(row['用户数量']) || 0,
          status: row['状态'] || '启用',
          createdAt: new Date().toLocaleDateString(),
        }))

        setRoles(prev => [...prev, ...importedRoles])
        message.success(`成功导入 ${importedRoles.length} 个角色`)
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败，请检查文件格式')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 处理角色状态更新
  const handleUpdateStatus = (roleId: string, newStatus: string) => {
    setRoles(roles.map(role =>
      role.id === roleId ? { ...role, status: newStatus } : role
    ))
    message.success(`角色状态已更新为: ${newStatus}`)
  }

  // 批量更新状态
  const handleBatchUpdateStatus = (status: string) => {
    setRoles(roles.map(role =>
      selectedRoles.includes(role.id) ? { ...role, status } : role
    ))
    setSelectedRoles([])
    message.success(`已将 ${selectedRoles.length} 个角色的状态更新为: ${status}`)
  }

  // 删除角色
  const handleDelete = (roleId: string) => {
    setRoles(roles.filter(role => role.id !== roleId))
    message.success('角色已删除')
  }

  // 批量删除
  const handleBatchDelete = () => {
    setRoles(roles.filter(role => !selectedRoles.includes(role.id)))
    setSelectedRoles([])
    message.success(`已删除 ${selectedRoles.length} 个角色`)
  }

  // 处理表单输入变化
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    setFormErrors(prev => ({
      ...prev,
      [field]: ""
    }))
  }

  // 处理权限变化
  const handlePermissionChange = (category: string, permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [category]: checked
          ? [...prev.permissions[category as keyof typeof prev.permissions], permission]
          : prev.permissions[category as keyof typeof prev.permissions].filter(p => p !== permission)
      }
    }))
  }

  // 验证表单
  const validateForm = () => {
    const errors = {
      name: "",
      description: "",
    }
    let isValid = true

    if (!formData.name) {
      errors.name = "角色名称不能为空"
      isValid = false
    }
    if (!formData.description) {
      errors.description = "角色描述不能为空"
      isValid = false
    }

    setFormErrors(errors)
    return isValid
  }

  // 处理添加角色
  const handleAddRole = () => {
    if (!validateForm()) {
      message.error('请填写所有必填项')
      return
    }

    const newRole: Role = {
      id: Date.now().toString(),
      name: formData.name,
      description: formData.description,
      users: 0,
      status: formData.enabled ? "启用" : "禁用",
      createdAt: new Date().toLocaleDateString(),
      permissions: {
        system: [...formData.permissions.system],
        safety: [...formData.permissions.safety],
        project: [...formData.permissions.project],
      }
    }

    setRoles(prev => [...prev, newRole])
    message.success('角色添加成功')
    setIsAddRoleOpen(false)
    // 重置表单
    setFormData({
      name: "",
      description: "",
      enabled: true,
      permissions: {
        system: [],
        safety: [],
        project: [],
      }
    })
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  // 筛选角色
  const filteredRoles = roles.filter(role => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchText.toLowerCase()) ||
      role.description.toLowerCase().includes(searchText.toLowerCase())

    const matchesStatus = statusFilter === "all" || role.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // 处理查看角色详情
  const handleViewRoleDetail = (role: Role) => {
    setSelectedRoleDetail(role)
    setIsRoleDetailOpen(true)
  }

  // 处理编辑角色
  const handleEditRole = (role: Role) => {
    setRoleToEdit(role)
    setFormData({
      name: role.name,
      description: role.description,
      enabled: role.status === "启用",
      permissions: {
        system: role.permissions?.system || [],
        safety: role.permissions?.safety || [],
        project: role.permissions?.project || [],
      }
    })
    setIsEditRoleOpen(true)
  }

  // 处理更新角色
  const handleUpdateRole = () => {
    if (!roleToEdit) return

    if (!validateForm()) {
      message.error('请填写所有必填项')
      return
    }

    const updatedRole = {
      ...roleToEdit,
      name: formData.name,
      description: formData.description,
      status: formData.enabled ? "启用" : "禁用",
      permissions: {
        system: [...formData.permissions.system],
        safety: [...formData.permissions.safety],
        project: [...formData.permissions.project],
      }
    }

    setRoles(roles.map(role =>
      role.id === roleToEdit.id ? updatedRole : role
    ))

    message.success('角色更新成功')
    setIsEditRoleOpen(false)
    setRoleToEdit(null)
    setFormErrors({
      name: "",
      description: "",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">角色管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <label>
            <Button variant="outline" size="sm" asChild>
              <span>
                <Upload className="h-4 w-4 mr-2" />
                导入
              </span>
            </Button>
            <input
              type="file"
              accept=".xlsx,.xls"
              className="hidden"
              onChange={handleImport}
            />
          </label>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总角色数</p>
                  <h3 className="text-2xl font-bold">{stats.total}</h3>
                </div>
              </div>
              <Progress value={100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <UserCheck className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">启用角色</p>
                  <h3 className="text-2xl font-bold">{stats.active}</h3>
                </div>
              </div>
              <Progress value={(stats.active / stats.total) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-red-100 p-3">
                  <UserMinus className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">禁用角色</p>
                  <h3 className="text-2xl font-bold">{stats.disabled}</h3>
                </div>
              </div>
              <Progress value={(stats.disabled / stats.total) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-yellow-100 p-3">
                  <Users className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总用户数</p>
                  <h3 className="text-2xl font-bold">{stats.totalUsers}</h3>
                </div>
              </div>
              <Progress value={(stats.totalUsers / 100) * 100} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>角色列表</CardTitle>
          <CardDescription>管理系统角色和权限分配</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索角色..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="启用">启用</SelectItem>
                    <SelectItem value="禁用">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                {selectedRoles.length > 0 ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBatchUpdateStatus("禁用")}
                    >
                      <UserMinus className="h-4 w-4 mr-2" />
                      批量禁用
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBatchDelete}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      批量删除
                    </Button>
                  </>
                ) : (
                  <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        添加角色
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px]">
                      <DialogHeader>
                        <DialogTitle>添加新角色</DialogTitle>
                        <DialogDescription>创建新角色并分配权限</DialogDescription>
                      </DialogHeader>
                      <Tabs defaultValue="basic" className="mt-4">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="basic">基本信息</TabsTrigger>
                          <TabsTrigger value="permissions">权限设置</TabsTrigger>
                        </TabsList>
                        <TabsContent value="basic" className="space-y-4 mt-4">
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="role-name">角色名称</Label>
                              <Input
                                id="role-name"
                                placeholder="请输入角色名称"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                              />
                              {formErrors.name && (
                                <p className="text-sm text-red-500">{formErrors.name}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="role-description">角色描述</Label>
                              <Input
                                id="role-description"
                                placeholder="请输入角色描述"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                              />
                              {formErrors.description && (
                                <p className="text-sm text-red-500">{formErrors.description}</p>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="role-enabled"
                                checked={formData.enabled}
                                onCheckedChange={(checked) => handleInputChange("enabled", !!checked)}
                              />
                              <Label htmlFor="role-enabled">启用角色</Label>
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent value="permissions" className="space-y-4 mt-4">
                          <div className="space-y-4">
                            <h4 className="text-sm font-medium">系统管理权限</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["view", "add", "edit", "delete"].map((perm) => (
                                <div key={`system-${perm}`} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`perm-system-${perm}`}
                                    checked={formData.permissions.system.includes(perm)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionChange("system", perm, !!checked)
                                    }
                                  />
                                  <Label htmlFor={`perm-system-${perm}`}>
                                    {perm === "view" ? "查看" :
                                     perm === "add" ? "添加" :
                                     perm === "edit" ? "编辑" :
                                     "删除"}
                                  </Label>
                                </div>
                              ))}
                            </div>

                            <h4 className="text-sm font-medium">安全管理权限</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["view", "add", "edit", "delete"].map((perm) => (
                                <div key={`safety-${perm}`} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`perm-safety-${perm}`}
                                    checked={formData.permissions.safety.includes(perm)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionChange("safety", perm, !!checked)
                                    }
                                  />
                                  <Label htmlFor={`perm-safety-${perm}`}>
                                    {perm === "view" ? "查看" :
                                     perm === "add" ? "添加" :
                                     perm === "edit" ? "编辑" :
                                     "删除"}安全记录
                                  </Label>
                                </div>
                              ))}
                            </div>

                            <h4 className="text-sm font-medium">工程管理权限</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {["view", "add", "edit", "delete"].map((perm) => (
                                <div key={`project-${perm}`} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`perm-project-${perm}`}
                                    checked={formData.permissions.project.includes(perm)}
                                    onCheckedChange={(checked) =>
                                      handlePermissionChange("project", perm, !!checked)
                                    }
                                  />
                                  <Label htmlFor={`perm-project-${perm}`}>
                                    {perm === "view" ? "查看" :
                                     perm === "add" ? "添加" :
                                     perm === "edit" ? "编辑" :
                                     "删除"}工程
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                      <DialogFooter className="mt-6">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsAddRoleOpen(false)
                            setFormErrors({
                              name: "",
                              description: "",
                            })
                          }}
                        >
                          取消
                        </Button>
                        <Button onClick={handleAddRole}>保存</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedRoles.length === filteredRoles.length && filteredRoles.length > 0}
                        onCheckedChange={toggleSelectAll}
                      />
                    </TableHead>
                    <TableHead>角色名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>用户数量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="w-[180px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRoles.includes(role.id)}
                          onCheckedChange={() => toggleSelectRole(role.id)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="rounded-full bg-blue-100 p-2">
                            <Shield className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="font-medium">{role.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{role.description}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>{role.users}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={role.status === "启用" ? "default" : "destructive"}
                        >
                          {role.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{role.createdAt}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewRoleDetail(role)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditRole(role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(role.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() =>
                                handleUpdateStatus(role.id, role.status === "启用" ? "禁用" : "启用")
                              }>
                                {role.status === "启用" ? (
                                  <>
                                    <X className="h-4 w-4 mr-2" />
                                    禁用角色
                                  </>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-2" />
                                    启用角色
                                  </>
                                )}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            共 {filteredRoles.length} 条记录
          </div>
        </CardFooter>
      </Card>

      {/* 角色详情对话框 */}
      <Dialog open={isRoleDetailOpen} onOpenChange={setIsRoleDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>角色详情</DialogTitle>
            <DialogDescription>查看角色的详细信息</DialogDescription>
          </DialogHeader>
          <div className="relative">
            <ScrollArea className="h-[calc(80vh-8rem)] w-full rounded-md border p-4">
              {selectedRoleDetail && (
                <div className="grid gap-4">
                  <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                    <div className="rounded-full bg-blue-100 p-4">
                      <Shield className="h-8 w-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{selectedRoleDetail.name}</h3>
                      <p className="text-sm text-muted-foreground">{selectedRoleDetail.description}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>状态</Label>
                      <div className="mt-1">
                        <Badge variant={selectedRoleDetail.status === "启用" ? "default" : "destructive"}>
                          {selectedRoleDetail.status}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <Label>用户数量</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedRoleDetail.users}</span>
                      </div>
                    </div>
                    <div>
                      <Label>创建时间</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{selectedRoleDetail.createdAt}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">系统权限</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedRoleDetail.permissions?.system.map((permission) => (
                          <Badge key={permission} variant="outline">
                            {permission === "view" ? "查看" :
                             permission === "add" ? "添加" :
                             permission === "edit" ? "编辑" :
                             "删除"}
                          </Badge>
                        )) || "无系统权限"}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">安全管理权限</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedRoleDetail.permissions?.safety.map((permission) => (
                          <Badge key={permission} variant="outline">
                            {permission === "view" ? "查看" :
                             permission === "add" ? "添加" :
                             permission === "edit" ? "编辑" :
                             "删除"}安全记录
                          </Badge>
                        )) || "无安全管理权限"}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">工程管理权限</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedRoleDetail.permissions?.project.map((permission) => (
                          <Badge key={permission} variant="outline">
                            {permission === "view" ? "查看" :
                             permission === "add" ? "添加" :
                             permission === "edit" ? "编辑" :
                             "删除"}工程
                          </Badge>
                        )) || "无工程管理权限"}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑角色对话框 */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>修改角色信息和权限设置</DialogDescription>
          </DialogHeader>
          <div className="relative">
            <ScrollArea className="h-[calc(80vh-8rem)] w-full rounded-md border p-4">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="permissions">权限设置</TabsTrigger>
                </TabsList>
                <TabsContent value="basic" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-name">角色名称</Label>
                      <Input
                        id="edit-role-name"
                        placeholder="请输入角色名称"
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                      />
                      {formErrors.name && (
                        <p className="text-sm text-red-500">{formErrors.name}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-role-description">角色描述</Label>
                      <Input
                        id="edit-role-description"
                        placeholder="请输入角色描述"
                        value={formData.description}
                        onChange={(e) => handleInputChange("description", e.target.value)}
                      />
                      {formErrors.description && (
                        <p className="text-sm text-red-500">{formErrors.description}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="edit-role-enabled"
                        checked={formData.enabled}
                        onCheckedChange={(checked) => handleInputChange("enabled", checked)}
                      />
                      <Label htmlFor="edit-role-enabled">启用角色</Label>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="permissions" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">系统管理权限</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {["view", "add", "edit", "delete"].map((perm) => (
                        <div key={`system-${perm}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`perm-system-${perm}`}
                            checked={formData.permissions.system.includes(perm)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange("system", perm, !!checked)
                            }
                          />
                          <Label htmlFor={`perm-system-${perm}`}>
                            {perm === "view" ? "查看" :
                             perm === "add" ? "添加" :
                             perm === "edit" ? "编辑" :
                             "删除"}
                          </Label>
                        </div>
                      ))}
                    </div>

                    <h4 className="text-sm font-medium">安全管理权限</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {["view", "add", "edit", "delete"].map((perm) => (
                        <div key={`safety-${perm}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`perm-safety-${perm}`}
                            checked={formData.permissions.safety.includes(perm)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange("safety", perm, !!checked)
                            }
                          />
                          <Label htmlFor={`perm-safety-${perm}`}>
                            {perm === "view" ? "查看" :
                             perm === "add" ? "添加" :
                             perm === "edit" ? "编辑" :
                             "删除"}安全记录
                          </Label>
                        </div>
                      ))}
                    </div>

                    <h4 className="text-sm font-medium">工程管理权限</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {["view", "add", "edit", "delete"].map((perm) => (
                        <div key={`project-${perm}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`perm-project-${perm}`}
                            checked={formData.permissions.project.includes(perm)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange("project", perm, !!checked)
                            }
                          />
                          <Label htmlFor={`perm-project-${perm}`}>
                            {perm === "view" ? "查看" :
                             perm === "add" ? "添加" :
                             perm === "edit" ? "编辑" :
                             "删除"}工程
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </ScrollArea>
          </div>
          <DialogFooter className="mt-4">
            <Button onClick={handleUpdateRole}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

