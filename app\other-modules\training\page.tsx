"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { MainLayout } from "@/components/main-layout"
import { Users, Search, Plus, ChevronLeft, Calendar, User, Clock, CheckCircle, BookOpen } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Progress } from "@/components/ui/progress"

interface Training {
  id: string
  title: string
  type: string
  status: string
  startDate: string
  endDate: string
  instructor: string
  participants: number
  maxParticipants: number
  description: string
}

export default function TrainingPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [trainings, setTrainings] = useState<Training[]>([
    {
      id: "1",
      title: "安全生产培训",
      type: "安全培训",
      status: "进行中",
      startDate: "2025-03-10",
      endDate: "2025-03-15",
      instructor: "张工程师",
      participants: 28,
      maxParticipants: 30,
      description: "针对一线员工的安全生产培训，内容包括安全操作规程、应急处理等"
    },
    {
      id: "2",
      title: "新员工入职培训",
      type: "入职培训",
      status: "计划中",
      startDate: "2025-03-20",
      endDate: "2025-03-25",
      instructor: "人事部门",
      participants: 15,
      maxParticipants: 20,
      description: "新入职员工培训，包括公司文化、规章制度、工作流程等内容"
    },
    {
      id: "3",
      title: "设备操作技能提升",
      type: "技能培训",
      status: "已完成",
      startDate: "2025-02-15",
      endDate: "2025-02-20",
      instructor: "李工程师",
      participants: 25,
      maxParticipants: 25,
      description: "针对设备操作人员的技能提升培训，提高设备操作效率和安全性"
    }
  ])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedTraining, setSelectedTraining] = useState<Training | null>(null)
  const [activeTab, setActiveTab] = useState("all")

  const viewTraining = (training: Training) => {
    setSelectedTraining(training)
    setIsViewDialogOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中":
        return "bg-green-500"
      case "计划中":
        return "bg-blue-500"
      case "已完成":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const filteredTrainings = trainings.filter(training => {
    if (activeTab === "all") return true
    if (activeTab === "ongoing") return training.status === "进行中"
    if (activeTab === "planned") return training.status === "计划中"
    if (activeTab === "completed") return training.status === "已完成"
    return true
  })

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => router.push("/")}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h2 className="text-2xl font-bold">培训管理</h2>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            新建培训
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>培训列表</CardTitle>
                <CardDescription>管理员工培训计划和记录</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="搜索培训..." className="pl-8 w-[250px]" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-4">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="ongoing">进行中</TabsTrigger>
                <TabsTrigger value="planned">计划中</TabsTrigger>
                <TabsTrigger value="completed">已完成</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="space-y-4">
              {filteredTrainings.length > 0 ? (
                filteredTrainings.map(training => (
                  <Card key={training.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => viewTraining(training)}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-md ${training.type === "安全培训" ? "bg-red-100" : training.type === "入职培训" ? "bg-blue-100" : "bg-green-100"}`}>
                            {training.type === "安全培训" ? (
                              <BookOpen className="h-5 w-5 text-red-500" />
                            ) : training.type === "入职培训" ? (
                              <Users className="h-5 w-5 text-blue-500" />
                            ) : (
                              <BookOpen className="h-5 w-5 text-green-500" />
                            )}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{training.title}</h3>
                              <Badge variant={training.status === "进行中" ? "default" : training.status === "计划中" ? "secondary" : "outline"}>
                                {training.status}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                              {training.description}
                            </p>
                            <div className="flex items-center gap-4 mt-2">
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                <span>{training.startDate} 至 {training.endDate}</span>
                              </div>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <User className="h-3 w-3" />
                                <span>{training.instructor}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <div className="text-sm">
                            <span className="font-medium">{training.participants}</span>
                            <span className="text-muted-foreground">/{training.maxParticipants}人</span>
                          </div>
                          <Progress value={(training.participants / training.maxParticipants) * 100} className="h-2 w-24" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-medium">暂无培训</h3>
                  <p className="mt-2 text-sm text-muted-foreground">当前没有符合条件的培训</p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">共 {filteredTrainings.length} 个培训</div>
          </CardFooter>
        </Card>

        {/* 查看培训对话框 */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>培训详情</DialogTitle>
              <DialogDescription>查看培训信息</DialogDescription>
            </DialogHeader>
            {selectedTraining && (
              <div className="space-y-4 py-4">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-md ${selectedTraining.type === "安全培训" ? "bg-red-100" : selectedTraining.type === "入职培训" ? "bg-blue-100" : "bg-green-100"}`}>
                    {selectedTraining.type === "安全培训" ? (
                      <BookOpen className="h-6 w-6 text-red-500" />
                    ) : selectedTraining.type === "入职培训" ? (
                      <Users className="h-6 w-6 text-blue-500" />
                    ) : (
                      <BookOpen className="h-6 w-6 text-green-500" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-medium">{selectedTraining.title}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant={selectedTraining.status === "进行中" ? "default" : selectedTraining.status === "计划中" ? "secondary" : "outline"}>
                        {selectedTraining.status}
                      </Badge>
                      <span className="text-sm text-muted-foreground">{selectedTraining.type}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3 mt-4">
                  <div>
                    <Label className="text-muted-foreground">培训描述</Label>
                    <p className="mt-1">{selectedTraining.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label className="text-muted-foreground">开始日期</Label>
                      <p className="font-medium mt-1">{selectedTraining.startDate}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">结束日期</Label>
                      <p className="font-medium mt-1">{selectedTraining.endDate}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">培训讲师</Label>
                      <p className="font-medium mt-1">{selectedTraining.instructor}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">参与人数</Label>
                      <p className="font-medium mt-1">{selectedTraining.participants}/{selectedTraining.maxParticipants}人</p>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label className="text-muted-foreground mb-2 block">参与情况</Label>
                    <Progress value={(selectedTraining.participants / selectedTraining.maxParticipants) * 100} className="h-2" />
                    <div className="flex justify-between mt-1 text-sm">
                      <span>已报名: {selectedTraining.participants}人</span>
                      <span>剩余名额: {selectedTraining.maxParticipants - selectedTraining.participants}人</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>关闭</Button>
              <Button>
                <CheckCircle className="h-4 w-4 mr-2" />
                报名参加
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 添加培训对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>新建培训</DialogTitle>
              <DialogDescription>创建新的培训计划</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">培训标题</Label>
                <Input id="title" placeholder="请输入培训标题" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">培训类型</Label>
                  <Select defaultValue="安全培训">
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全培训">安全培训</SelectItem>
                      <SelectItem value="入职培训">入职培训</SelectItem>
                      <SelectItem value="技能培训">技能培训</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">培训状态</Label>
                  <Select defaultValue="计划中">
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="计划中">计划中</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startDate">开始日期</Label>
                  <Input id="startDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">结束日期</Label>
                  <Input id="endDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="instructor">培训讲师</Label>
                  <Input id="instructor" placeholder="请输入讲师姓名" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxParticipants">最大参与人数</Label>
                  <Input id="maxParticipants" type="number" placeholder="请输入人数" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">培训描述</Label>
                <Input id="description" placeholder="请输入培训描述" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={() => {
                setIsAddDialogOpen(false)
                toast({
                  title: "创建成功",
                  description: "培训计划已成功创建"
                })
              }}>创建</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}