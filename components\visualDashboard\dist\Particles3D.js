"use client";
"use strict";
exports.__esModule = true;
exports.Particles3D = void 0;
var react_1 = require("react");
var THREE = require("three");
exports.Particles3D = function () {
    var containerRef = react_1.useRef(null);
    var particlesRef = react_1.useRef([]);
    var sceneRef = react_1.useRef();
    var cameraRef = react_1.useRef();
    var rendererRef = react_1.useRef();
    react_1.useEffect(function () {
        if (!containerRef.current)
            return;
        // 初始化场景
        var scene = new THREE.Scene();
        sceneRef.current = scene;
        // 设置相机
        var camera = new THREE.PerspectiveCamera(75, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);
        camera.position.z = 50;
        cameraRef.current = camera;
        // 设置渲染器
        var renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
        });
        renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
        renderer.setClearColor(0x000000, 0);
        containerRef.current.appendChild(renderer.domElement);
        rendererRef.current = renderer;
        // 创建粒子
        var particleCount = 200;
        var particles = [];
        var geometry = new THREE.BufferGeometry();
        var positions = new Float32Array(particleCount * 3);
        var sizes = new Float32Array(particleCount);
        for (var i = 0; i < particleCount; i++) {
            var particle = {
                position: new THREE.Vector3((Math.random() - 0.5) * 100, (Math.random() - 0.5) * 100, (Math.random() - 0.5) * 100),
                velocity: new THREE.Vector3((Math.random() - 0.5) * 0.2, (Math.random() - 0.5) * 0.2, (Math.random() - 0.5) * 0.2),
                size: Math.random() * 2 + 1
            };
            particles.push(particle);
            positions[i * 3] = particle.position.x;
            positions[i * 3 + 1] = particle.position.y;
            positions[i * 3 + 2] = particle.position.z;
            sizes[i] = particle.size;
        }
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        // 创建着色器材质
        var material = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                color: { value: new THREE.Color(0x20dbff) }
            },
            vertexShader: "\n        attribute float size;\n        varying vec3 vColor;\n        uniform float time;\n        \n        void main() {\n          vColor = vec3(0.125, 0.858, 1.0);\n          vec3 pos = position;\n          pos.y += sin(time + position.x) * 0.5;\n          pos.x += cos(time + position.z) * 0.5;\n          \n          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);\n          gl_PointSize = size * (300.0 / -mvPosition.z);\n          gl_Position = projectionMatrix * mvPosition;\n        }\n      ",
            fragmentShader: "\n        varying vec3 vColor;\n        \n        void main() {\n          vec2 center = gl_PointCoord - vec2(0.5);\n          float dist = length(center);\n          float alpha = 1.0 - smoothstep(0.3, 0.5, dist);\n          gl_FragColor = vec4(vColor, alpha);\n        }\n      ",
            transparent: true,
            depthWrite: false,
            blending: THREE.AdditiveBlending
        });
        var points = new THREE.Points(geometry, material);
        scene.add(points);
        particlesRef.current = particles;
        // 动画循环
        var time = 0;
        var animate = function () {
            requestAnimationFrame(animate);
            time += 0.005;
            material.uniforms.time.value = time;
            // 更新粒子位置
            var positions = geometry.attributes.position.array;
            particles.forEach(function (particle, i) {
                particle.position.add(particle.velocity);
                // 边界检查
                if (Math.abs(particle.position.x) > 50)
                    particle.velocity.x *= -1;
                if (Math.abs(particle.position.y) > 50)
                    particle.velocity.y *= -1;
                if (Math.abs(particle.position.z) > 50)
                    particle.velocity.z *= -1;
                positions[i * 3] = particle.position.x;
                positions[i * 3 + 1] = particle.position.y;
                positions[i * 3 + 2] = particle.position.z;
            });
            geometry.attributes.position.needsUpdate = true;
            renderer.render(scene, camera);
        };
        animate();
        // 处理窗口大小变化
        var handleResize = function () {
            if (!containerRef.current || !cameraRef.current || !rendererRef.current)
                return;
            var width = containerRef.current.clientWidth;
            var height = containerRef.current.clientHeight;
            cameraRef.current.aspect = width / height;
            cameraRef.current.updateProjectionMatrix();
            rendererRef.current.setSize(width, height);
        };
        window.addEventListener('resize', handleResize);
        // 清理函数
        return function () {
            var _a;
            window.removeEventListener('resize', handleResize);
            (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.removeChild(renderer.domElement);
            scene.clear();
        };
    }, []);
    return React.createElement("div", { ref: containerRef, style: { width: '100%', height: '100%', position: 'absolute' } });
};
