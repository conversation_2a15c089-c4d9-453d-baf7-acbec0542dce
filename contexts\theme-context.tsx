"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"

type ThemeMode = "light" | "dark"

interface ThemeContextType {
  themeMode: ThemeMode
  toggleTheme: () => void
  setThemeMode: (theme: ThemeMode) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [themeMode, setThemeMode] = useState<ThemeMode>("light")
  const [mounted, setMounted] = useState(false)

  // 获取主题模式并监听系统主题变化
  useEffect(() => {
    setMounted(true)
    // 检查是否有保存的主题设置
    const savedTheme = localStorage.getItem("theme-mode") as ThemeMode | null
    if (savedTheme) {
      setThemeMode(savedTheme)
      document.documentElement.classList.toggle("dark", savedTheme === "dark")
    } else {
      // 检测系统偏好
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setThemeMode(prefersDark ? "dark" : "light")
      document.documentElement.classList.toggle("dark", prefersDark)
    }

    // 监听系统主题变化
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    const handleChange = (e: MediaQueryListEvent) => {
      if (!localStorage.getItem("theme-mode")) {
        const newTheme = e.matches ? "dark" : "light"
        setThemeMode(newTheme)
        document.documentElement.classList.toggle("dark", e.matches)
      }
    }
    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  // 切换主题
  const toggleTheme = () => {
    const newTheme = themeMode === "light" ? "dark" : "light"
    setThemeMode(newTheme)
    localStorage.setItem("theme-mode", newTheme)
    document.documentElement.classList.toggle("dark", newTheme === "dark")
  }

  // 手动设置主题
  const setThemeModeValue = (theme: ThemeMode) => {
    setThemeMode(theme)
    localStorage.setItem("theme-mode", theme)
    document.documentElement.classList.toggle("dark", theme === "dark")
  }

  const value = {
    themeMode,
    toggleTheme,
    setThemeMode: setThemeModeValue
  }

  // 避免SSR水合不匹配
  if (!mounted) {
    return <>{children}</>
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error("useTheme 必须在 ThemeProvider 内部使用")
  }
  return context
} 