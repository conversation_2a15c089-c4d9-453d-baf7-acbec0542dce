"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  Calendar,
  AlertTriangle,
  Shield,
  FileCheck,
  Users,
  BarChart2,
  RefreshCcw,
  Eye,
  Clock,
  EyeOff,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"
import dayjs from "dayjs"
import XLSX from "xlsx"

interface SafetyMeasure {
  id: string
  title: string
  type: string
  area: string
  department: string
  responsible: string
  priority: string
  startDate: string
  endDate: string
  status: string
  progress: number
  progressNote?: string
  description: string
  objectives: string
  requirements: string
  effectScore?: number
  effectNote?: string
  verifier?: string
  verifyDate?: string
  createdAt: string
  updatedAt: string
  implementation?: string
  resources?: string
  expectedEffect?: string
  verification?: string
  disabled?: boolean
}

interface Statistics {
  total: number
  inProgress: number
  completed: number
  planned: number
  byType: {
    technical: number
    management: number
    engineering: number
    personal: number
  }
  byPriority: {
    high: number
    medium: number
    low: number
  }
  overdue: number
  completionRate: string
}

export function SafetyMeasures() {
  const { toast } = useToast()
  const [isAddMeasureOpen, setIsAddMeasureOpen] = useState(false)
  const [isEditMeasureOpen, setIsEditMeasureOpen] = useState(false)
  const [isViewMeasureOpen, setIsViewMeasureOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedMeasure, setSelectedMeasure] = useState<SafetyMeasure | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [measureToDelete, setMeasureToDelete] = useState<string | null>(null)
  const [formData, setFormData] = useState<Partial<SafetyMeasure>>({})
  const [activeTab, setActiveTab] = useState("all")
  const [isUpdateProgressOpen, setIsUpdateProgressOpen] = useState(false)
  const [isVerifyEffectOpen, setIsVerifyEffectOpen] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [newMeasure, setNewMeasure] = useState<Partial<SafetyMeasure>>({
    title: '',
    type: '',
    area: '',
    department: '',
    responsible: '',
    priority: '',
    startDate: '2025-03-15',
    endDate: '2025-04-15',
    status: '计划中',
    progress: 0,
    description: '',
    objectives: '',
    requirements: '',
  })

  const [safetyMeasures, setSafetyMeasures] = useState<SafetyMeasure[]>([
    {
      id: "1",
      title: "安全培训计划",
      type: "培训",
      area: "全公司",
      department: "安全部",
      responsible: "张三",
      priority: "高",
      startDate: "2025-03-01",
      endDate: "2025-03-31",
      status: "进行中",
      progress: 60,
      description: "针对全公司员工的安全意识培训",
      objectives: "提高员工安全意识",
      requirements: "所有员工必须参加",
      createdAt: "2025-02-28",
      updatedAt: "2025-03-15"
    },
    {
      id: "2",
      title: "消防设备更新",
      type: "设备",
      area: "生产车间",
      department: "设备部",
      responsible: "李四",
      priority: "高",
      startDate: "2025-03-15",
      endDate: "2025-04-15",
      status: "未开始",
      progress: 0,
      description: "更换老旧消防设备",
      objectives: "确保消防设备正常运行",
      requirements: "符合最新安全标准",
      createdAt: "2025-03-01",
      updatedAt: "2025-03-01"
    },
    {
      id: "3",
      title: "矿区B2支护加固",
      type: "工程措施",
      area: "矿区B2",
      department: "工程管理部",
      responsible: "王五",
      startDate: "2025-03-10",
      endDate: "2025-04-20",
      status: "进行中",
      progress: 40,
      priority: "高",
      description: "对矿区B2进行支护加固",
      objectives: "提高矿区安全性",
      requirements: "符合矿山安全规范",
      createdAt: "2025-03-09",
      updatedAt: "2025-03-10"
    },
    {
      id: "4",
      title: "安全隐患排查制度完善",
      type: "管理措施",
      area: "全公司",
      department: "安全管理部",
      responsible: "钱七",
      startDate: "2025-04-01",
      endDate: "2025-04-30",
      status: "计划中",
      progress: 0,
      priority: "中",
      description: "完善安全隐患排查制度",
      objectives: "建立健全隐患排查机制",
      requirements: "覆盖所有部门和区域",
      createdAt: "2025-03-30",
      updatedAt: "2025-03-30"
    }
  ])

  // 消息提示
  const showMessage = (type: "success" | "error", content: string) => {
    toast({
      variant: type === "success" ? "default" : "destructive",
      title: content,
    })
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      // 准备导出数据
      const exportData = safetyMeasures.map(measure => ({
        '措施标题': measure.title,
        '措施类型': measure.type,
        '适用区域': measure.area,
        '责任部门': measure.department,
        '责任人': measure.responsible,
        '优先级': measure.priority,
        '开始日期': measure.startDate,
        '结束日期': measure.endDate,
        '状态': measure.status,
        '进度': `${measure.progress}%`,
        '进度说明': measure.progressNote || '',
        '措施描述': measure.description,
        '实施目标': measure.objectives,
        '实施要求': measure.requirements,
        '效果评分': measure.effectScore ? `${measure.effectScore}分` : '',
        '效果说明': measure.effectNote || '',
        '验证人': measure.verifier || '',
        '验证日期': measure.verifyDate || '',
        '创建时间': measure.createdAt,
        '更新时间': measure.updatedAt,
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 措施标题
        { wch: 15 }, // 措施类型
        { wch: 20 }, // 适用区域
        { wch: 15 }, // 责任部门
        { wch: 10 }, // 责任人
        { wch: 10 }, // 优先级
        { wch: 15 }, // 开始日期
        { wch: 15 }, // 结束日期
        { wch: 10 }, // 状态
        { wch: 10 }, // 进度
        { wch: 30 }, // 进度说明
        { wch: 50 }, // 措施描述
        { wch: 50 }, // 实施目标
        { wch: 50 }, // 实施要求
        { wch: 10 }, // 效果评分
        { wch: 30 }, // 效果说明
        { wch: 10 }, // 验证人
        { wch: 15 }, // 验证日期
        { wch: 20 }, // 创建时间
        { wch: 20 }, // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '安全措施列表');

      // 导出文件
      // 使用2025年的固定日期而不是当前日期
      const fileName = `安全措施列表_2025-03-15_10-30-00.xlsx`;
      XLSX.writeFile(wb, fileName);
      showMessage("success", "导出成功");
    } catch (error) {
      console.error('导出失败:', error);
      showMessage("error", "导出失败，请重试");
    }
  };

  // 刷新数据
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      // TODO: 这里替换为实际的API调用
      // const response = await fetch('/api/safety-measures');
      // const data = await response.json();
      // setSafetyMeasures(data);

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "刷新成功",
        description: "数据已更新",
      });
    } catch (error) {
      console.error('刷新失败:', error);
      toast({
        title: "刷新失败",
        description: "获取数据时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    refreshData();
  };

  // 处理查看详情
  const handleView = (measure: SafetyMeasure) => {
    setSelectedMeasure(measure)
    setIsViewMeasureOpen(true)
  }

  // 处理编辑
  const handleEdit = (measure: SafetyMeasure) => {
    setSelectedMeasure(measure)
    setFormData(measure)
    setIsEditMeasureOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = () => {
    if (!selectedMeasure || !formData) return

    const updatedMeasures = safetyMeasures.map(measure =>
      measure.id === selectedMeasure.id ? { ...measure, ...formData } : measure
    )
    setSafetyMeasures(updatedMeasures)
    setIsEditMeasureOpen(false)
    setSelectedMeasure(null)
    setFormData({})
    showMessage("success", "修改成功")
  }

  // 处理删除
  const handleDelete = (id: string) => {
    setMeasureToDelete(id)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (measureToDelete) {
      const newMeasures = safetyMeasures.filter(measure => measure.id !== measureToDelete)
      setSafetyMeasures(newMeasures)
      setMeasureToDelete(null)
      setIsDeleteDialogOpen(false)
      showMessage("success", "删除成功")
    }
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setIsDeleteDialogOpen(true)
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    const newMeasures = safetyMeasures.filter(measure => !selectedRowKeys.includes(measure.id))
    setSafetyMeasures(newMeasures)
    setSelectedRowKeys([])
    setIsDeleteDialogOpen(false)
    showMessage("success", "批量删除成功")
  }

  // 处理添加措施
  const handleAddMeasure = () => {
    try {
      // 验证必填字段
      if (!newMeasure.title || !newMeasure.type || !newMeasure.responsible) {
        toast({
          title: "验证失败",
          description: "请填写必填字段",
          variant: "destructive",
        });
        return;
      }

      // 生成新措施
      const { id: _, ...measureData } = newMeasure as Partial<SafetyMeasure>;
      const measure: SafetyMeasure = {
        ...measureData as Omit<SafetyMeasure, 'id' | 'createdAt' | 'updatedAt'>,
        id: `SM${Date.now()}`,
        createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };

      // 添加到列表
      setSafetyMeasures([measure, ...safetyMeasures]);

      // 关闭对话框并重置表单
      setIsAddMeasureOpen(false);
      setNewMeasure({
        title: '',
        type: '',
        area: '',
        department: '',
        responsible: '',
        priority: '',
        startDate: dayjs().format('YYYY-MM-DD'),
        endDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
        status: '计划中',
        progress: 0,
        description: '',
        objectives: '',
        requirements: '',
      });

      toast({
        title: "添加成功",
        description: "新的安全措施已添加",
      });
    } catch (error) {
      console.error('添加失败:', error);
      toast({
        title: "添加失败",
        description: "添加安全措施时发生错误",
        variant: "destructive",
      });
    }
  };

  // 处理更新进度
  const handleUpdateProgress = (measure: SafetyMeasure) => {
    setSelectedMeasure(measure)
    setIsUpdateProgressOpen(true)
  }

  // 保存进度更新
  const handleSaveProgress = () => {
    if (!selectedMeasure) return

    const updatedMeasures = safetyMeasures.map(measure =>
      measure.id === selectedMeasure.id
        ? {
            ...measure,
            progress: selectedMeasure.progress,
            progressNote: selectedMeasure.progressNote,
            status: selectedMeasure.progress === 100 ? "已完成" : "进行中",
            updatedAt: new Date().toISOString()
          }
        : measure
    )

    setSafetyMeasures(updatedMeasures)
    setIsUpdateProgressOpen(false)
    setSelectedMeasure(null)
    showMessage("success", "进度已更新")
  }

  // 处理验证效果
  const handleVerifyEffect = (measure: SafetyMeasure) => {
    setSelectedMeasure(measure)
    setIsVerifyEffectOpen(true)
  }

  // 保存效果验证
  const handleSaveVerification = () => {
    if (!selectedMeasure) return

    const updatedMeasures = safetyMeasures.map(measure =>
      measure.id === selectedMeasure.id
        ? {
            ...measure,
            effectScore: selectedMeasure.effectScore,
            effectNote: selectedMeasure.effectNote,
            verifier: selectedMeasure.verifier,
            verifyDate: selectedMeasure.verifyDate,
            updatedAt: new Date().toISOString()
          }
        : measure
    )

    setSafetyMeasures(updatedMeasures)
    setIsVerifyEffectOpen(false)
    setSelectedMeasure(null)
    showMessage("success", "效果验证已保存")
  }

  // 筛选逻辑
  const filteredMeasures = safetyMeasures.filter(measure => {
    const matchesSearch =
      measure.title.toLowerCase().includes(searchText.toLowerCase()) ||
      measure.description.toLowerCase().includes(searchText.toLowerCase()) ||
      measure.responsible.toLowerCase().includes(searchText.toLowerCase()) ||
      measure.area.toLowerCase().includes(searchText.toLowerCase());

    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(measure.type);
    const matchesPriority = selectedPriorities.length === 0 || selectedPriorities.includes(measure.priority);

    if (activeTab === "all") return matchesSearch && matchesType && matchesPriority;
    if (activeTab === "in-progress") return matchesSearch && matchesType && matchesPriority && measure.status === "进行中";
    if (activeTab === "completed") return matchesSearch && matchesType && matchesPriority && measure.status === "已完成";
    if (activeTab === "planned") return matchesSearch && matchesType && matchesPriority && measure.status === "计划中";

    return matchesSearch && matchesType && matchesPriority;
  });

  // 统计数据
  const statistics: Statistics = {
    total: safetyMeasures.length,
    inProgress: safetyMeasures.filter(m => m.status === "进行中").length,
    completed: safetyMeasures.filter(m => m.status === "已完成").length,
    planned: safetyMeasures.filter(m => m.status === "计划中").length,
    byType: {
      technical: safetyMeasures.filter(m => m.type === "技术措施").length,
      management: safetyMeasures.filter(m => m.type === "管理措施").length,
      engineering: safetyMeasures.filter(m => m.type === "工程措施").length,
      personal: safetyMeasures.filter(m => m.type === "个人防护").length,
    },
    byPriority: {
      high: safetyMeasures.filter(m => m.priority === "高").length,
      medium: safetyMeasures.filter(m => m.priority === "中").length,
      low: safetyMeasures.filter(m => m.priority === "低").length,
    },
    overdue: safetyMeasures.filter(m =>
      m.status !== "已完成" &&
      dayjs(m.endDate).isBefore(dayjs(), "day")
    ).length,
    completionRate: safetyMeasures.length > 0
      ? ((safetyMeasures.filter(m => m.status === "已完成").length / safetyMeasures.length) * 100).toFixed(1)
      : "0",
  }

  // 处理启用/禁用
  const handleToggleDisable = (id: string) => {
    setSafetyMeasures(safetyMeasures.map(measure =>
      measure.id === id ? { ...measure, disabled: !measure.disabled } : measure
    ));
    showMessage("success", `${safetyMeasures.find(m => m.id === id)?.disabled ? '启用' : '禁用'}成功`);
  };

  return (
    <div className="space-y-8">
      {/* 顶部标题和操作区 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gradient-to-r from-blue-50 via-white to-blue-50 rounded-xl shadow-sm p-8 border border-blue-100/50">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-800">安全措施</h2>
          <p className="text-muted-foreground text-sm">管理和跟踪安全措施实施情况</p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing} className="hover:bg-blue-50 transition-colors">
            {isRefreshing ? (
              <>
                <RefreshCcw className="h-4 w-4 mr-2 animate-spin" />
                刷新中...
              </>
            ) : (
              <>
                <RefreshCcw className="h-4 w-4 mr-2" />
                刷新
              </>
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportExcel} className="hover:bg-green-50 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button size="sm" onClick={() => setIsAddMeasureOpen(true)} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all">
            <Plus className="h-4 w-4 mr-2" />
            添加措施
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100/30 shadow-lg border-0 hover:shadow-xl transition-shadow duration-300">
        <CardContent className="p-8">
          <div className="flex items-center justify-between">
            <div className="space-y-4">
              <div>
                <h3 className="text-4xl font-bold tracking-tight text-blue-700">{statistics.total}</h3>
                <p className="text-base text-muted-foreground mt-1">安全措施总数</p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">完成进度</span>
                  <span className="font-medium text-blue-600">{statistics.completionRate}%</span>
                </div>
                <Progress value={Number(statistics.completionRate)} className="h-2 bg-blue-100" indicatorClassName="bg-gradient-to-r from-blue-500 to-blue-600" />
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                    <span className="text-muted-foreground">进行中：</span>
                    <span className="font-medium">{statistics.inProgress}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span className="text-muted-foreground">已完成：</span>
                    <span className="font-medium">{statistics.completed}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500"></div>
                    <span className="text-muted-foreground">已逾期：</span>
                    <span className="font-medium text-red-500">{statistics.overdue}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="h-20 w-20 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-inner">
              <FileText className="h-10 w-10 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容区 */}
      <Card className="shadow-lg border-0 rounded-xl overflow-hidden">
        <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white pb-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-xl text-blue-800">安全措施列表</CardTitle>
              <CardDescription>管理和跟踪安全措施实施情况</CardDescription>
            </div>
            <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-4 p-1 bg-blue-50/50">
                <TabsTrigger value="all" className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-sm">全部</TabsTrigger>
                <TabsTrigger value="in-progress" className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-sm">进行中</TabsTrigger>
                <TabsTrigger value="completed" className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-sm">已完成</TabsTrigger>
                <TabsTrigger value="planned" className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-sm">计划中</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-6">
            {/* 搜索和筛选区 */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-gradient-to-r from-gray-50 to-white rounded-lg border border-gray-100">
              <div className="flex flex-wrap items-center gap-3">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索措施..."
                    className="pl-8 w-[250px] focus:ring-2 focus:ring-blue-100"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select
                  value={selectedTypes.join(",")}
                  onValueChange={(value) => setSelectedTypes(value ? value.split(",") : [])}
                >
                  <SelectTrigger className="w-[150px] focus:ring-2 focus:ring-blue-100">
                    <SelectValue placeholder="措施类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="技术措施">技术措施</SelectItem>
                    <SelectItem value="管理措施">管理措施</SelectItem>
                    <SelectItem value="工程措施">工程措施</SelectItem>
                    <SelectItem value="个人防护">个人防护</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={selectedPriorities.join(",")}
                  onValueChange={(value) => setSelectedPriorities(value ? value.split(",") : [])}
                >
                  <SelectTrigger className="w-[150px] focus:ring-2 focus:ring-blue-100">
                    <SelectValue placeholder="优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有优先级</SelectItem>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {selectedRowKeys.length > 0 && (
                <div className="flex items-center gap-2 animate-fadeIn">
                  <span className="text-sm text-muted-foreground">
                    已选择 {selectedRowKeys.length} 项
                  </span>
                  <Button variant="destructive" size="sm" onClick={handleBatchDelete} className="hover:bg-red-600 transition-colors">
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                  </Button>
                </div>
              )}
            </div>

            {/* 表格区域 */}
            <div className="rounded-lg border border-gray-200 overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-gray-50 to-white hover:bg-gray-50/80">
                    <TableHead className="w-[300px]">标题</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>区域</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>责任人</TableHead>
                    <TableHead>进度</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMeasures.map((measure) => (
                    <TableRow key={measure.id} className={`${measure.disabled ? "opacity-50" : ""} hover:bg-blue-50/30 transition-colors`}>
                      <TableCell className="font-medium">
                        <Button variant="link" onClick={() => handleView(measure)} className="h-auto p-0 hover:text-blue-600">
                          {measure.title}
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="font-normal bg-blue-50">
                          {measure.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          measure.priority === "高" ? "destructive" :
                          measure.priority === "中" ? "secondary" : "default"
                        } className="font-normal">
                          {measure.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>{measure.area}</TableCell>
                      <TableCell>{measure.department}</TableCell>
                      <TableCell>{measure.responsible}</TableCell>
                      <TableCell>
                        <div className="w-[100px]">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-full rounded-full bg-gray-100">
                              <div
                                className="h-full rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
                                style={{ width: `${measure.progress}%` }}
                              />
                            </div>
                            <span className="text-sm text-muted-foreground whitespace-nowrap">
                              {measure.progress}%
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          measure.status === "已完成" ? "default" :
                          measure.status === "进行中" ? "secondary" :
                          "outline"
                        } className="font-normal">
                          {measure.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-1">
                          <Button variant="ghost" size="icon" onClick={() => handleView(measure)} className="hover:bg-blue-50">
                            <Eye className="h-4 w-4 text-blue-600" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleEdit(measure)} className="hover:bg-yellow-50">
                            <Edit className="h-4 w-4 text-yellow-600" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDelete(measure.id)} className="hover:bg-red-50">
                            <Trash2 className="h-4 w-4 text-red-600" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="hover:bg-gray-50">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-[160px]">
                              <DropdownMenuItem onClick={() => handleUpdateProgress(measure)} className="hover:bg-blue-50">
                                <Clock className="h-4 w-4 mr-2" />
                                更新进度
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleVerifyEffect(measure)} className="hover:bg-green-50">
                                <FileCheck className="h-4 w-4 mr-2" />
                                验证效果
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleDisable(measure.id)} className="hover:bg-yellow-50">
                                {measure.disabled ? (
                                  <>
                                    <Eye className="h-4 w-4 mr-2" />
                                    启用
                                  </>
                                ) : (
                                  <>
                                    <EyeOff className="h-4 w-4 mr-2" />
                                    禁用
                                  </>
                                )}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t p-4 bg-gradient-to-r from-gray-50 to-white">
          <div className="flex flex-col sm:flex-row justify-between items-center w-full gap-4">
            <div className="text-sm text-muted-foreground">
              共 {filteredMeasures.length} 条记录
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled className="hover:bg-blue-50">
                上一页
              </Button>
              <Button variant="outline" size="sm" className="px-3 bg-blue-50 text-blue-700">
                1
              </Button>
              <Button variant="outline" size="sm" disabled className="hover:bg-blue-50">
                下一页
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* 效果分析卡片 */}
      <Card className="shadow-lg border-0 rounded-xl overflow-hidden">
        <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white pb-4">
          <CardTitle className="text-xl text-blue-800">措施实施效果分析</CardTitle>
          <CardDescription>安全措施实施效果统计分析</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600">平均完成率</p>
                    <h3 className="text-2xl font-bold mt-1 text-blue-700">{statistics.completionRate}%</h3>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <BarChart2 className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-green-100/50 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600">按时完成率</p>
                    <h3 className="text-2xl font-bold mt-1 text-green-700">
                      {((statistics.completed / (statistics.completed + statistics.overdue)) * 100).toFixed(1)}%
                    </h3>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                    <Clock className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">高优先级完成率</p>
                    <h3 className="text-2xl font-bold mt-1 text-purple-700">
                      {((statistics.byPriority.high / statistics.total) * 100).toFixed(1)}%
                    </h3>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                    <AlertTriangle className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* 其他对话框和抽屉组件保持不变 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>删除确认</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedRowKeys.length > 0
                ? `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`
                : "确定要删除该记录吗？"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={selectedRowKeys.length > 0 ? confirmBatchDelete : confirmDelete}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Sheet open={isViewMeasureOpen} onOpenChange={setIsViewMeasureOpen}>
        <SheetContent className="w-[600px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                措施详情
              </div>
            </SheetTitle>
            <SheetDescription>查看安全措施的详细信息</SheetDescription>
          </SheetHeader>
          {selectedMeasure && (
            <div className="mt-6 space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>措施标题</Label>
                  <p className="mt-1">{selectedMeasure.title}</p>
                </div>
                <div>
                  <Label>措施类型</Label>
                  <p className="mt-1">{selectedMeasure.type}</p>
                </div>
                <div>
                  <Label>适用区域</Label>
                  <p className="mt-1">{selectedMeasure.area}</p>
                </div>
                <div>
                  <Label>责任部门</Label>
                  <p className="mt-1">{selectedMeasure.department}</p>
                </div>
                <div>
                  <Label>责任人</Label>
                  <p className="mt-1">{selectedMeasure.responsible}</p>
                </div>
                <div>
                  <Label>优先级</Label>
                  <p className="mt-1">
                    <Badge
                      variant={
                        selectedMeasure.priority === "高"
                          ? "destructive"
                          : selectedMeasure.priority === "中"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {selectedMeasure.priority}
                    </Badge>
                  </p>
                </div>
                <div>
                  <Label>开始日期</Label>
                  <p className="mt-1">{selectedMeasure.startDate}</p>
                </div>
                <div>
                  <Label>结束日期</Label>
                  <p className="mt-1">{selectedMeasure.endDate}</p>
                </div>
                <div>
                  <Label>状态</Label>
                  <p className="mt-1">
                    <Badge
                      variant={
                        selectedMeasure.status === "已完成"
                          ? "default"
                          : selectedMeasure.status === "进行中"
                            ? "secondary"
                            : "outline"
                      }
                    >
                      {selectedMeasure.status}
                    </Badge>
                  </p>
                </div>
                <div>
                  <Label>进度</Label>
                  <div className="mt-2 flex items-center gap-2">
                    <Progress value={selectedMeasure.progress} className="w-[100px]" />
                    <span className="text-sm text-muted-foreground">{selectedMeasure.progress}%</span>
                  </div>
                </div>
              </div>

              {selectedMeasure.description && (
                <div>
                  <Label>措施描述</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.description}</p>
                </div>
              )}

              {selectedMeasure.objectives && (
                <div>
                  <Label>实施目标</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.objectives}</p>
                </div>
              )}

              {selectedMeasure.requirements && (
                <div>
                  <Label>实施要求</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.requirements}</p>
                </div>
              )}

              {selectedMeasure.implementation && (
                <div>
                  <Label>实施方法</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.implementation}</p>
                </div>
              )}

              {selectedMeasure.resources && (
                <div>
                  <Label>所需资源</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.resources}</p>
                </div>
              )}

              {selectedMeasure.expectedEffect && (
                <div>
                  <Label>预期效果</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.expectedEffect}</p>
                </div>
              )}

              {selectedMeasure.verification && (
                <div>
                  <Label>验证方法</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedMeasure.verification}</p>
                </div>
              )}
            </div>
          )}
        </SheetContent>
      </Sheet>

      <Sheet open={isEditMeasureOpen} onOpenChange={setIsEditMeasureOpen}>
        <SheetContent className="w-[600px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>
              <div className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                编辑措施
              </div>
            </SheetTitle>
            <SheetDescription>修改安全措施信息</SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="details">详细内容</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-title">措施标题 <span className="text-red-500">*</span></Label>
                    <Input
                      id="edit-title"
                      value={formData.title || ""}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-type">措施类型 <span className="text-red-500">*</span></Label>
                    <Select
                      value={formData.type || "technical"}
                      onValueChange={(value) => setFormData({ ...formData, type: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择措施类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">技术措施</SelectItem>
                        <SelectItem value="management">管理措施</SelectItem>
                        <SelectItem value="engineering">工程措施</SelectItem>
                        <SelectItem value="personal">个人防护</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-area">适用区域 <span className="text-red-500">*</span></Label>
                    <Input
                      id="edit-area"
                      value={formData.area || ""}
                      onChange={(e) => setFormData({ ...formData, area: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-department">责任部门 <span className="text-red-500">*</span></Label>
                    <Select
                      value={formData.department || "safety"}
                      onValueChange={(value) => setFormData({ ...formData, department: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择责任部门" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="safety">安全管理部</SelectItem>
                        <SelectItem value="engineering">工程管理部</SelectItem>
                        <SelectItem value="hr">人事管理部</SelectItem>
                        <SelectItem value="equipment">设备管理部</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-responsible">责任人 <span className="text-red-500">*</span></Label>
                    <Input
                      id="edit-responsible"
                      value={formData.responsible || ""}
                      onChange={(e) => setFormData({ ...formData, responsible: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-priority">优先级 <span className="text-red-500">*</span></Label>
                    <Select
                      value={formData.priority || "medium"}
                      onValueChange={(value) => setFormData({ ...formData, priority: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-start-date">开始日期 <span className="text-red-500">*</span></Label>
                    <Input
                      id="edit-start-date"
                      type="date"
                      value={formData.startDate || ""}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-end-date">结束日期 <span className="text-red-500">*</span></Label>
                    <Input
                      id="edit-end-date"
                      type="date"
                      value={formData.endDate || ""}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-description">措施描述</Label>
                    <Textarea
                      id="edit-description"
                      value={formData.description || ""}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-objectives">实施目标</Label>
                    <Textarea
                      id="edit-objectives"
                      value={formData.objectives || ""}
                      onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-requirements">实施要求</Label>
                    <Textarea
                      id="edit-requirements"
                      value={formData.requirements || ""}
                      onChange={(e) => setFormData({ ...formData, requirements: e.target.value })}
                      rows={2}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            <SheetFooter className="mt-6">
              <Button variant="outline" onClick={() => setIsEditMeasureOpen(false)}>
                取消
              </Button>
              <Button onClick={handleSaveEdit}>保存</Button>
            </SheetFooter>
          </div>
        </SheetContent>
      </Sheet>

      <Dialog open={isUpdateProgressOpen} onOpenChange={setIsUpdateProgressOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更新实施进度</DialogTitle>
            <DialogDescription>
              请输入最新的实施进度和相关说明
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="update-progress" className="text-right">
                进度 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="update-progress"
                type="number"
                min={0}
                max={100}
                className="col-span-3"
                value={selectedMeasure?.progress || 0}
                onChange={(e) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      progress: Number(e.target.value),
                    });
                  }
                }}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="update-progress-note" className="text-right">
                进度说明
              </Label>
              <Textarea
                id="update-progress-note"
                className="col-span-3"
                value={selectedMeasure?.progressNote || ""}
                onChange={(e) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      progressNote: e.target.value,
                    });
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateProgressOpen(false)}>
              取消
            </Button>
            <Button type="submit" onClick={handleSaveProgress}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isVerifyEffectOpen} onOpenChange={setIsVerifyEffectOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>验证实施效果</DialogTitle>
            <DialogDescription>
              请评估安全措施的实施效果
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="verify-effect-score" className="text-right">
                效果评分 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={selectedMeasure?.effectScore?.toString() || ""}
                onValueChange={(value) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      effectScore: Number(value),
                    });
                  }
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="请选择评分" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1分 - 效果很差</SelectItem>
                  <SelectItem value="2">2分 - 效果较差</SelectItem>
                  <SelectItem value="3">3分 - 效果一般</SelectItem>
                  <SelectItem value="4">4分 - 效果良好</SelectItem>
                  <SelectItem value="5">5分 - 效果优秀</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="verify-effect-note" className="text-right">
                效果说明 <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="verify-effect-note"
                className="col-span-3"
                value={selectedMeasure?.effectNote || ""}
                onChange={(e) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      effectNote: e.target.value,
                    });
                  }
                }}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="verify-verifier" className="text-right">
                验证人 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="verify-verifier"
                className="col-span-3"
                value={selectedMeasure?.verifier || ""}
                onChange={(e) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      verifier: e.target.value,
                    });
                  }
                }}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="verify-date" className="text-right">
                验证日期 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="verify-date"
                type="date"
                className="col-span-3"
                value={selectedMeasure?.verifyDate || ""}
                onChange={(e) => {
                  if (selectedMeasure) {
                    setSelectedMeasure({
                      ...selectedMeasure,
                      verifyDate: e.target.value,
                    });
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsVerifyEffectOpen(false)}>
              取消
            </Button>
            <Button type="submit" onClick={handleSaveVerification}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isAddMeasureOpen} onOpenChange={setIsAddMeasureOpen}>
        <DialogContent className="max-w-[800px]">
          <DialogHeader>
            <DialogTitle>添加安全措施</DialogTitle>
            <DialogDescription>
              请填写新安全措施的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="mt-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="details">详细内容</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="add-title">措施标题 <span className="text-red-500">*</span></Label>
                    <Input
                      id="add-title"
                      value={newMeasure.title}
                      onChange={(e) => setNewMeasure({ ...newMeasure, title: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-type">措施类型 <span className="text-red-500">*</span></Label>
                    <Select value={newMeasure.type} onValueChange={(value) => setNewMeasure({ ...newMeasure, type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="技术措施">技术措施</SelectItem>
                        <SelectItem value="管理措施">管理措施</SelectItem>
                        <SelectItem value="工程措施">工程措施</SelectItem>
                        <SelectItem value="个人防护">个人防护</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-area">适用区域</Label>
                    <Input
                      id="add-area"
                      value={newMeasure.area}
                      onChange={(e) => setNewMeasure({ ...newMeasure, area: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-department">责任部门</Label>
                    <Input
                      id="add-department"
                      value={newMeasure.department}
                      onChange={(e) => setNewMeasure({ ...newMeasure, department: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-responsible">责任人 <span className="text-red-500">*</span></Label>
                    <Input
                      id="add-responsible"
                      value={newMeasure.responsible}
                      onChange={(e) => setNewMeasure({ ...newMeasure, responsible: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-priority">优先级</Label>
                    <Select value={newMeasure.priority} onValueChange={(value) => setNewMeasure({ ...newMeasure, priority: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="高">高</SelectItem>
                        <SelectItem value="中">中</SelectItem>
                        <SelectItem value="低">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-start-date">开始日期</Label>
                    <Input
                      id="add-start-date"
                      type="date"
                      value={newMeasure.startDate}
                      onChange={(e) => setNewMeasure({ ...newMeasure, startDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-end-date">结束日期</Label>
                    <Input
                      id="add-end-date"
                      type="date"
                      value={newMeasure.endDate}
                      onChange={(e) => setNewMeasure({ ...newMeasure, endDate: e.target.value })}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="add-description">措施描述</Label>
                    <Textarea
                      id="add-description"
                      value={newMeasure.description}
                      onChange={(e) => setNewMeasure({ ...newMeasure, description: e.target.value })}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-objectives">实施目标</Label>
                    <Textarea
                      id="add-objectives"
                      value={newMeasure.objectives}
                      onChange={(e) => setNewMeasure({ ...newMeasure, objectives: e.target.value })}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="add-requirements">实施要求</Label>
                    <Textarea
                      id="add-requirements"
                      value={newMeasure.requirements}
                      onChange={(e) => setNewMeasure({ ...newMeasure, requirements: e.target.value })}
                      rows={2}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsAddMeasureOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddMeasure}>
              添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

