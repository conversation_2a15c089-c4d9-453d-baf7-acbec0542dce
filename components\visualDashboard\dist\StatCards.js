"use strict";
exports.__esModule = true;
exports.StatCards = void 0;
var react_1 = require("react");
var gsap_1 = require("gsap");
var visualDashboard_module_css_1 = require("./visualDashboard.module.css");
var icons_1 = require("@ant-design/icons");
exports.StatCards = function (_a) {
    var stats = _a.stats;
    var rootRef = react_1.useRef(null);
    var fadeRef = react_1.useRef(null);
    var setX = react_1.useRef(null);
    var setY = react_1.useRef(null);
    var pos = react_1.useRef({ x: 0, y: 0 });
    react_1.useEffect(function () {
        var el = rootRef.current;
        if (!el)
            return;
        setX.current = gsap_1.gsap.quickSetter(el, "--x", "px");
        setY.current = gsap_1.gsap.quickSetter(el, "--y", "px");
        var _a = el.getBoundingClientRect(), width = _a.width, height = _a.height;
        pos.current = { x: width / 2, y: height / 2 };
        setX.current(pos.current.x);
        setY.current(pos.current.y);
    }, []);
    var moveTo = function (x, y) {
        gsap_1.gsap.to(pos.current, {
            x: x,
            y: y,
            duration: 0.45,
            ease: "power3.out",
            onUpdate: function () {
                var _a, _b;
                (_a = setX.current) === null || _a === void 0 ? void 0 : _a.call(setX, pos.current.x);
                (_b = setY.current) === null || _b === void 0 ? void 0 : _b.call(setY, pos.current.y);
            },
            overwrite: true
        });
    };
    var handleMove = function (e) {
        var _a;
        var r = (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.getBoundingClientRect();
        if (!r)
            return;
        moveTo(e.clientX - r.left, e.clientY - r.top);
        gsap_1.gsap.to(fadeRef.current, { opacity: 0, duration: 0.25, overwrite: true });
    };
    var handleLeave = function () {
        gsap_1.gsap.to(fadeRef.current, {
            opacity: 1,
            duration: 0.6,
            overwrite: true
        });
    };
    var handleCardMove = function (e) {
        var card = e.currentTarget;
        var rect = card.getBoundingClientRect();
        var x = e.clientX - rect.left;
        var y = e.clientY - rect.top;
        card.style.setProperty("--mouse-x", x + "px");
        card.style.setProperty("--mouse-y", y + "px");
    };
    var items = [
        {
            title: "项目总数",
            value: stats.totalProjects,
            icon: React.createElement(icons_1.BuildOutlined, null),
            borderColor: "#3B82F6",
            gradient: "linear-gradient(145deg, #3B82F6, #000)"
        },
        {
            title: "进行中项目",
            value: stats.ongoingProjects,
            icon: React.createElement(icons_1.StarOutlined, null),
            borderColor: "#10B981",
            gradient: "linear-gradient(180deg, #10B981, #000)"
        },
        {
            title: "安全检查",
            value: stats.totalSafetyChecks,
            icon: React.createElement(icons_1.SafetyOutlined, null),
            borderColor: "#F59E0B",
            gradient: "linear-gradient(165deg, #F59E0B, #000)"
        },
        {
            title: "物资总数",
            value: stats.total,
            icon: React.createElement(icons_1.AndroidOutlined, null),
            borderColor: "#8B5CF6",
            gradient: "linear-gradient(225deg, #8B5CF6, #000)"
        },
    ];
    return (React.createElement("div", { ref: rootRef, className: visualDashboard_module_css_1["default"].chromaGrid, style: {
            "--r": "300px",
            "--cols": "4",
            "--rows": "1"
        }, onPointerMove: handleMove, onPointerLeave: handleLeave },
        items.map(function (item, i) { return (React.createElement("article", { key: i, className: visualDashboard_module_css_1["default"].chromaCard, onMouseMove: handleCardMove, style: {
                "--card-border": item.borderColor,
                "--card-gradient": item.gradient
            } },
            React.createElement("div", { className: visualDashboard_module_css_1["default"].statIcon }, item.icon),
            React.createElement("footer", { className: visualDashboard_module_css_1["default"].chromaInfo },
                React.createElement("h3", { className: visualDashboard_module_css_1["default"].statValue }, item.value),
                React.createElement("p", { className: visualDashboard_module_css_1["default"].statLabel }, item.title)))); }),
        React.createElement("div", { className: visualDashboard_module_css_1["default"].chromaOverlay }),
        React.createElement("div", { ref: fadeRef, className: visualDashboard_module_css_1["default"].chromaFade })));
};
exports["default"] = exports.StatCards;
