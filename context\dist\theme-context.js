"use client";
"use strict";
exports.__esModule = true;
exports.ThemeProvider = exports.useTheme = void 0;
var react_1 = require("react");
var ThemeContext = react_1.createContext({
    themeMode: 'light',
    toggleTheme: function () { }
});
exports.useTheme = function () {
    var context = react_1.useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
function ThemeProvider(_a) {
    var children = _a.children;
    var _b = react_1.useState('light'), themeMode = _b[0], setThemeMode = _b[1];
    react_1.useEffect(function () {
        // 从本地存储中获取主题设置
        var savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            setThemeMode(savedTheme);
        }
        else {
            // 检查系统主题偏好
            var prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            setThemeMode(prefersDark ? 'dark' : 'light');
        }
    }, []);
    var toggleTheme = function () {
        var newTheme = themeMode === 'light' ? 'dark' : 'light';
        setThemeMode(newTheme);
        localStorage.setItem('theme', newTheme);
    };
    react_1.useEffect(function () {
        // 更新文档根元素的类名以应用主题
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(themeMode);
    }, [themeMode]);
    return (react_1["default"].createElement(ThemeContext.Provider, { value: { themeMode: themeMode, toggleTheme: toggleTheme } }, children));
}
exports.ThemeProvider = ThemeProvider;
