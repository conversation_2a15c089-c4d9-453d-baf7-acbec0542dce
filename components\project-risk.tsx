"use client"

import { useState } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  AlertTriangle,
  Shield,
  AlertCircle,
  CheckCircle2,
  Upload,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

// 添加类型定义
interface Risk {
  id: string
  project: string
  riskType: string
  description: string
  probability: string
  impact: string
  level: string
  responseStrategy: string
  responsePlan: string
  owner: string
  status: string
  lastReview: string
  department?: string
  location?: string
  attachments?: string[]
  measures?: string[]
}

interface FormData {
  project: string
  riskType: string
  description: string
  probability: string
  impact: string
  level: string
  responseStrategy: string
  responsePlan: string
  owner: string
  status: string
  department: string
  location: string
  measures: string[]
  attachments: string[]
}

export function ProjectRisk() {
  // 添加状态管理
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedLevel, setSelectedLevel] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [currentRisk, setCurrentRisk] = useState<Risk | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [riskToDelete, setRiskToDelete] = useState<Risk | null>(null)
  const { toast } = useToast()

  const [formData, setFormData] = useState<FormData>({
    project: "",
    riskType: "",
    description: "",
    probability: "",
    impact: "",
    level: "",
    responseStrategy: "",
    responsePlan: "",
    owner: "",
    status: "",
    department: "",
    location: "",
    measures: [],
    attachments: []
  })

  // 示例数据
  const risks = [
    {
      id: "1",
      project: "矿区A3开发项目",
      riskType: "安全风险",
      description: "设备操作不当导致安全事故",
      probability: "中",
      impact: "高",
      level: "高风险",
      responseStrategy: "减轻",
      responsePlan: "加强设备操作培训，制定严格的操作规程",
      owner: "张三",
      status: "监控中",
      lastReview: "2023-12-10",
    },
    {
      id: "2",
      project: "设备更新计划",
      riskType: "进度风险",
      description: "设备供应商延迟交付",
      probability: "高",
      impact: "中",
      level: "中风险",
      responseStrategy: "转移",
      responsePlan: "合同中加入延迟罚款条款，寻找备选供应商",
      owner: "李四",
      status: "监控中",
      lastReview: "2023-12-08",
    },
    {
      id: "3",
      project: "安全系统升级",
      riskType: "技术风险",
      description: "新系统与现有设备不兼容",
      probability: "中",
      impact: "高",
      level: "高风险",
      responseStrategy: "减轻",
      responsePlan: "提前进行兼容性测试，准备适配方案",
      owner: "王五",
      status: "已解决",
      lastReview: "2023-12-12",
    },
    {
      id: "4",
      project: "新矿区勘探",
      riskType: "环境风险",
      description: "勘探活动对周边生态环境造成影响",
      probability: "中",
      impact: "中",
      level: "中风险",
      responseStrategy: "减轻",
      responsePlan: "制定环保措施，减少勘探活动对环境的影响",
      owner: "赵六",
      status: "监控中",
      lastReview: "2023-12-05",
    },
    {
      id: "5",
      project: "环保设施改造",
      riskType: "成本风险",
      description: "材料价格上涨导致成本超预算",
      probability: "高",
      impact: "中",
      level: "中风险",
      responseStrategy: "接受",
      responsePlan: "设立成本应急储备金，寻找替代材料",
      owner: "钱七",
      status: "监控中",
      lastReview: "2023-12-11",
    },
  ]

  // 获取风险等级对应的徽章样式
  const getRiskLevelBadge = (level: string) => {
    switch (level) {
      case "高风险":
        return <Badge variant="destructive">高风险</Badge>
      case "中风险":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            中风险
          </Badge>
        )
      case "低风险":
        return <Badge className="bg-green-500">低风险</Badge>
      default:
        return <Badge>{level}</Badge>
    }
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "监控中":
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            监控中
          </Badge>
        )
      case "已解决":
        return <Badge className="bg-green-500">已解决</Badge>
      case "已关闭":
        return <Badge variant="secondary">已关闭</Badge>
      case "升级":
        return <Badge variant="destructive">升级</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理查看详情
  const handleViewDetails = (risk: Risk) => {
    setCurrentRisk(risk)
    setIsViewDetailsOpen(true)
  }

  // 处理编辑风险
  const handleEditRisk = (risk: Risk) => {
    setFormData({
      project: risk.project,
      riskType: risk.riskType,
      description: risk.description,
      probability: risk.probability,
      impact: risk.impact,
      level: risk.level,
      responseStrategy: risk.responseStrategy,
      responsePlan: risk.responsePlan,
      owner: risk.owner,
      status: risk.status,
      department: risk.department || "",
      location: risk.location || "",
      measures: risk.measures || [],
      attachments: risk.attachments || []
    })
    setCurrentRisk(risk)
    setIsViewDetailsOpen(false)
    setIsEditDialogOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = async () => {
    setIsLoading(true)
    try {
      // 这里添加更新风险的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "更新成功",
        description: "风险信息已更新",
      })
      setIsEditDialogOpen(false)
    } catch (error) {
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理添加风险
  const handleAddRisk = () => {
    setFormData({
      project: "",
      riskType: "",
      description: "",
      probability: "",
      impact: "",
      level: "",
      responseStrategy: "",
      responsePlan: "",
      owner: "",
      status: "",
      department: "",
      location: "",
      measures: [],
      attachments: []
    })
    setIsAddDialogOpen(true)
  }

  // 处理保存新风险
  const handleSaveNewRisk = async () => {
    setIsLoading(true)
    try {
      // 这里添加保存新风险的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "添加成功",
        description: "新风险已添加",
      })
      setIsAddDialogOpen(false)
    } catch (error) {
      toast({
        title: "添加失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理删除风险
  const handleDeleteRisk = (risk: Risk) => {
    setRiskToDelete(risk)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!riskToDelete) return
    
    setIsLoading(true)
    try {
      // 这里添加删除风险的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "删除成功",
        description: "风险已删除",
      })
      setIsDeleteDialogOpen(false)
      setRiskToDelete(null)
    } catch (error) {
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导出
  const handleExport = async () => {
    setIsLoading(true)
    try {
      const headers = ['工程项目', '风险类型', '风险描述', '概率', '影响', '风险等级', '应对策略', '责任人', '状态']
      const csvData = risks.map(risk => [
        risk.project,
        risk.riskType,
        risk.description,
        risk.probability,
        risk.impact,
        risk.level,
        risk.responseStrategy,
        risk.owner,
        risk.status
      ])
      
      const csvContent = [headers, ...csvData].map(row => row.join(',')).join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `工程风险管理_${new Date().toLocaleDateString()}.csv`
      link.click()
      
      toast({
        title: "导出成功",
        description: "数据已成功导出为CSV文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    setIsLoading(true)
    try {
      const text = await file.text()
      const rows = text.split('\n').map(row => row.split(','))
      const headers = rows[0]
      
      const importedRisks = rows.slice(1).map(row => ({
        id: Math.random().toString(36).substr(2, 9),
        project: row[0],
        riskType: row[1],
        description: row[2],
        probability: row[3],
        impact: row[4],
        level: row[5],
        responseStrategy: row[6],
        owner: row[7],
        status: row[8],
        lastReview: new Date().toISOString().split('T')[0]
      }))
      
      toast({
        title: "导入成功",
        description: `成功导入 ${importedRisks.length} 条记录`,
      })
      
      event.target.value = ''
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 筛选记录
  const filteredRisks = risks.filter(risk => {
    const matchesSearch = 
      risk.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      risk.riskType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      risk.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      risk.owner.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = selectedType === "all" ? true : risk.riskType === selectedType
    const matchesLevel = selectedLevel === "all" ? true : risk.level === selectedLevel
    
    return matchesSearch && matchesType && matchesLevel
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">工程风险管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".csv"
              onChange={handleImport}
              className="hidden"
              id="import-file"
            />
            <Button variant="outline" size="sm" onClick={() => document.getElementById('import-file')?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              导入
          </Button>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加风险
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加风险项</DialogTitle>
                <DialogDescription>识别并记录工程项目的潜在风险</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目</Label>
                    <Select>
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mining-a3">矿区A3开发项目</SelectItem>
                        <SelectItem value="equipment-update">设备更新计划</SelectItem>
                        <SelectItem value="safety-upgrade">安全系统升级</SelectItem>
                        <SelectItem value="exploration">新矿区勘探</SelectItem>
                        <SelectItem value="environmental">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="risk-type">风险类型</Label>
                    <Select>
                      <SelectTrigger id="risk-type">
                        <SelectValue placeholder="选择风险类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="safety">安全风险</SelectItem>
                        <SelectItem value="schedule">进度风险</SelectItem>
                        <SelectItem value="technical">技术风险</SelectItem>
                        <SelectItem value="environmental">环境风险</SelectItem>
                        <SelectItem value="cost">成本风险</SelectItem>
                        <SelectItem value="quality">质量风险</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">风险描述</Label>
                  <Textarea id="description" placeholder="详细描述风险内容" rows={3} />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="probability">发生概率</Label>
                    <Select>
                      <SelectTrigger id="probability">
                        <SelectValue placeholder="选择发生概率" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="impact">影响程度</Label>
                    <Select>
                      <SelectTrigger id="impact">
                        <SelectValue placeholder="选择影响程度" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="response-strategy">应对策略</Label>
                    <Select>
                      <SelectTrigger id="response-strategy">
                        <SelectValue placeholder="选择应对策略" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="avoid">规避</SelectItem>
                        <SelectItem value="transfer">转移</SelectItem>
                        <SelectItem value="mitigate">减轻</SelectItem>
                        <SelectItem value="accept">接受</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="owner">责任人</Label>
                    <Input id="owner" placeholder="输入责任人姓名" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="response-plan">应对计划</Label>
                  <Textarea id="response-plan" placeholder="详细描述风险应对计划" rows={3} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monitoring">监控中</SelectItem>
                      <SelectItem value="resolved">已解决</SelectItem>
                      <SelectItem value="closed">已关闭</SelectItem>
                      <SelectItem value="escalated">升级</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{risks.filter(r => r.level === "高风险").length}</h3>
            <p className="text-sm text-muted-foreground">高风险项</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <AlertCircle className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{risks.filter(r => r.level === "中风险").length}</h3>
            <p className="text-sm text-muted-foreground">中风险项</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <Shield className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{risks.filter(r => r.level === "低风险").length}</h3>
            <p className="text-sm text-muted-foreground">低风险项</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <CheckCircle2 className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{risks.filter(r => r.status === "已解决").length}</h3>
            <p className="text-sm text-muted-foreground">已解决风险</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>风险登记表</CardTitle>
              <CardDescription>查看和管理所有工程风险</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  type="search" 
                  placeholder="搜索风险..." 
                  className="pl-8 w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select defaultValue="all" onValueChange={(value) => setSelectedType(value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="风险类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="safety">安全风险</SelectItem>
                  <SelectItem value="schedule">进度风险</SelectItem>
                  <SelectItem value="technical">技术风险</SelectItem>
                  <SelectItem value="environmental">环境风险</SelectItem>
                  <SelectItem value="cost">成本风险</SelectItem>
                  <SelectItem value="quality">质量风险</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all" onValueChange={(value) => setSelectedLevel(value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="风险等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有等级</SelectItem>
                  <SelectItem value="高风险">高风险</SelectItem>
                  <SelectItem value="中风险">中风险</SelectItem>
                  <SelectItem value="低风险">低风险</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>工程项目</TableHead>
                  <TableHead>风险类型</TableHead>
                  <TableHead>风险描述</TableHead>
                  <TableHead>概率</TableHead>
                  <TableHead>影响</TableHead>
                  <TableHead>风险等级</TableHead>
                  <TableHead>应对策略</TableHead>
                  <TableHead>责任人</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后审查</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRisks.map((risk) => (
                  <TableRow key={risk.id}>
                    <TableCell className="font-medium">{risk.project}</TableCell>
                    <TableCell>{risk.riskType}</TableCell>
                    <TableCell className="max-w-[200px] truncate">{risk.description}</TableCell>
                    <TableCell>{risk.probability}</TableCell>
                    <TableCell>{risk.impact}</TableCell>
                    <TableCell>{getRiskLevelBadge(risk.level)}</TableCell>
                    <TableCell>{risk.responseStrategy}</TableCell>
                    <TableCell>{risk.owner}</TableCell>
                    <TableCell>{getStatusBadge(risk.status)}</TableCell>
                    <TableCell>{risk.lastReview}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" onClick={() => handleViewDetails(risk)}>
                        详情
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleEditRisk(risk)}>
                        编辑
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredRisks.length} 个风险项</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-4">
            <DialogTitle>风险详情</DialogTitle>
            <DialogDescription>查看风险的详细信息</DialogDescription>
          </DialogHeader>
          
          {currentRisk && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="sticky top-0 bg-white z-10">基本信息</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>工程项目</Label>
                    <p className="mt-1">{currentRisk.project}</p>
                  </div>
                  <div>
                    <Label>风险类型</Label>
                    <p className="mt-1">{currentRisk.riskType}</p>
                  </div>
                  <div className="col-span-2">
                    <Label>风险描述</Label>
                    <p className="mt-1">{currentRisk.description}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="sticky top-0 bg-white z-10">风险评估</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>发生概率</Label>
                    <p className="mt-1">{currentRisk.probability}</p>
                  </div>
                  <div>
                    <Label>影响程度</Label>
                    <p className="mt-1">{currentRisk.impact}</p>
                  </div>
                  <div>
                    <Label>风险等级</Label>
                    <p className="mt-1">{currentRisk.level}</p>
                  </div>
                </CardContent>
              </Card>

        <Card>
          <CardHeader>
                  <CardTitle className="sticky top-0 bg-white z-10">应对措施</CardTitle>
          </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>应对策略</Label>
                    <p className="mt-1">{currentRisk.responseStrategy}</p>
                  </div>
                  <div>
                    <Label>责任人</Label>
                    <p className="mt-1">{currentRisk.owner}</p>
                  </div>
                  <div className="col-span-2">
                    <Label>应对计划</Label>
                    <p className="mt-1">{currentRisk.responsePlan}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
                  <CardTitle className="sticky top-0 bg-white z-10">其他信息</CardTitle>
          </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>所属部门</Label>
                    <p className="mt-1">{currentRisk.department || '未指定'}</p>
                  </div>
                  <div>
                    <Label>地点</Label>
                    <p className="mt-1">{currentRisk.location || '未指定'}</p>
                  </div>
                  <div>
                    <Label>状态</Label>
                    <p className="mt-1">{currentRisk.status}</p>
                  </div>
                  <div>
                    <Label>最后审查日期</Label>
                    <p className="mt-1">{currentRisk.lastReview}</p>
                  </div>
                  {currentRisk.measures && currentRisk.measures.length > 0 && (
                    <div className="col-span-2">
                      <Label>防范措施</Label>
                      <ul className="mt-1 list-disc list-inside">
                        {currentRisk.measures.map((measure, index) => (
                          <li key={index}>{measure}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {currentRisk.attachments && currentRisk.attachments.length > 0 && (
                    <div className="col-span-2">
                      <Label>附件</Label>
                      <ul className="mt-1 list-disc list-inside">
                        {currentRisk.attachments.map((attachment, index) => (
                          <li key={index}>{attachment}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          <DialogFooter className="sticky bottom-0 bg-white z-10 pt-4">
            <Button variant="outline" onClick={() => setIsViewDetailsOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => handleEditRisk(currentRisk!)}>编辑</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
            <DialogTitle className="text-xl">编辑风险</DialogTitle>
            <DialogDescription>修改风险的详细信息</DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => { e.preventDefault(); handleSaveEdit(); }} className="py-6">
            <div className="space-y-8">
              <Card className="border-2 shadow-sm">
                <CardHeader className="bg-slate-50">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-blue-500 rounded-full mr-3" />
                    基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="project" className="text-sm font-medium">
                      工程项目 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="project"
                      value={formData.project}
                      onChange={(e) => setFormData({ ...formData, project: e.target.value })}
                      className="border-slate-200 focus:border-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="riskType" className="text-sm font-medium">
                      风险类型 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.riskType}
                      onValueChange={(value) => setFormData({ ...formData, riskType: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择风险类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="技术风险">技术风险</SelectItem>
                        <SelectItem value="管理风险">管理风险</SelectItem>
                        <SelectItem value="商业风险">商业风险</SelectItem>
                        <SelectItem value="外部风险">外部风险</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2 space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">
                      风险描述 <span className="text-red-500">*</span>
                    </Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="min-h-[100px] border-slate-200 focus:border-blue-500"
                      placeholder="请详细描述风险的具体情况..."
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 shadow-sm">
                <CardHeader className="bg-slate-50">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-yellow-500 rounded-full mr-3" />
                    风险评估
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-3 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="probability" className="text-sm font-medium">
                      发生概率 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.probability}
                      onValueChange={(value) => setFormData({ ...formData, probability: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择概率" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="高">高</SelectItem>
                        <SelectItem value="中">中</SelectItem>
                        <SelectItem value="低">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="impact" className="text-sm font-medium">
                      影响程度 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.impact}
                      onValueChange={(value) => setFormData({ ...formData, impact: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择影响" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="高">高</SelectItem>
                        <SelectItem value="中">中</SelectItem>
                        <SelectItem value="低">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="level" className="text-sm font-medium">
                      风险等级 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.level}
                      onValueChange={(value) => setFormData({ ...formData, level: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择等级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="高风险">高风险</SelectItem>
                        <SelectItem value="中风险">中风险</SelectItem>
                        <SelectItem value="低风险">低风险</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 shadow-sm">
                <CardHeader className="bg-slate-50">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-green-500 rounded-full mr-3" />
                    应对措施
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="responseStrategy" className="text-sm font-medium">
                      应对策略 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.responseStrategy}
                      onValueChange={(value) => setFormData({ ...formData, responseStrategy: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择策略" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="规避">规避</SelectItem>
                        <SelectItem value="转移">转移</SelectItem>
                        <SelectItem value="减轻">减轻</SelectItem>
                        <SelectItem value="接受">接受</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="owner" className="text-sm font-medium">
                      责任人 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="owner"
                      value={formData.owner}
                      onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                      className="border-slate-200 focus:border-blue-500"
                    />
                  </div>
                  <div className="col-span-2 space-y-2">
                    <Label htmlFor="responsePlan" className="text-sm font-medium">
                      应对计划 <span className="text-red-500">*</span>
                    </Label>
                    <Textarea
                      id="responsePlan"
                      value={formData.responsePlan}
                      onChange={(e) => setFormData({ ...formData, responsePlan: e.target.value })}
                      className="min-h-[100px] border-slate-200 focus:border-blue-500"
                      placeholder="请详细描述风险应对计划..."
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 shadow-sm">
                <CardHeader className="bg-slate-50">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-purple-500 rounded-full mr-3" />
                    其他信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="department" className="text-sm font-medium">所属部门</Label>
                    <Input
                      id="department"
                      value={formData.department}
                      onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                      className="border-slate-200 focus:border-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-sm font-medium">地点</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      className="border-slate-200 focus:border-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status" className="text-sm font-medium">
                      状态 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => setFormData({ ...formData, status: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="监控中">监控中</SelectItem>
                        <SelectItem value="已解决">已解决</SelectItem>
                        <SelectItem value="已关闭">已关闭</SelectItem>
                        <SelectItem value="升级">升级</SelectItem>
                      </SelectContent>
                    </Select>
            </div>
          </CardContent>
        </Card>
      </div>

            <DialogFooter className="sticky bottom-0 bg-white z-10 pt-6 mt-8 border-t">
              <div className="flex items-center justify-end gap-4 w-full">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                  className="w-24"
                >
                  取消
                </Button>
                <Button 
                  type="submit" 
                  disabled={isLoading}
                  className="w-24"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      <span>保存中</span>
                    </div>
                  ) : (
                    "保存"
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个风险吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete} disabled={isLoading}>
              {isLoading ? "删除中..." : "删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

