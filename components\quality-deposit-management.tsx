"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Search, Trash2, Edit, Download, Plus, FileText, CheckCircle, Clock, AlertTriangle, RefreshCw, Filter, CalendarIcon, Bookmark, BarChart4, DollarSign, MailOpen, Building, User } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import * as echarts from 'echarts/core'
import { BarChart, LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import ReactECharts from 'echarts-for-react'
import { theme } from './quality-deposit-echarts-theme'

// 注册 echarts 组件和主题
try {
  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    BarChart,
    LineChart,
    PieChart,
    CanvasRenderer
  ])
  echarts.registerTheme('quality-deposit-theme', theme)
} catch (e) {
  console.log('已注册 echarts 组件或注册失败', e)
}

// 定义更丰富的质保金数据接口
interface DepositRecord {
  id: number
  projectName: string
  contractNo: string
  supplier: string
  supplierContact?: string
  depositAmount: string
  depositAmountValue: number // 用于计算的数值
  depositDate: string
  releaseDate: string
  status: "未到期" | "待释放" | "已释放" | "释放异常"
  projectType?: string
  projectManager?: string
  qualityIssues?: boolean
  actualReleaseDate?: string
  releaseDays?: number // 释放所需天数
  notes?: string
  documentLinks?: string[]
  lastUpdated?: string
}

// 定义项目类型数据接口
interface ProjectType {
  name: string
  count: number
  totalDeposit: number
}

// 定义供应商数据接口
interface SupplierData {
  name: string
  depositCount: number
  totalAmount: number
  releaseOnTimeRate: number
}

// 扩展模拟质保金数据
const depositData: DepositRecord[] = [
  {
    id: 1,
    projectName: "矿区A区开采工程",
    contractNo: "HT2023-001",
    supplier: "北方矿业工程有限公司",
    supplierContact: "张工 13800138001",
    depositAmount: "¥450,000",
    depositAmountValue: 450000,
    depositDate: "2025-01-15",
    releaseDate: "2025-04-15",
    status: "未到期",
    projectType: "开采工程",
    projectManager: "李明",
    qualityIssues: false,
    releaseDays: 365,
    notes: "按照合同约定，质保期为12个月",
    documentLinks: ["合同附件1.pdf", "质保条款.docx"],
    lastUpdated: "2025-01-20"
  },
  {
    id: 2,
    projectName: "安全监控系统升级",
    contractNo: "HT2023-012",
    supplier: "安全科技有限公司",
    supplierContact: "王经理 13900139002",
    depositAmount: "¥280,000",
    depositAmountValue: 280000,
    depositDate: "2025-01-20",
    releaseDate: "2025-04-10",
    status: "未到期",
    projectType: "系统工程",
    projectManager: "张华",
    qualityIssues: false,
    releaseDays: 365,
    notes: "系统稳定运行一年后释放",
    documentLinks: ["监控系统合同.pdf"],
    lastUpdated: "2025-01-25"
  },
  {
    id: 3,
    projectName: "矿区排水系统改造",
    contractNo: "HT2022-045",
    supplier: "水利工程集团",
    supplierContact: "刘工 13800138003",
    depositAmount: "¥320,000",
    depositAmountValue: 320000,
    depositDate: "2025-02-10",
    releaseDate: "2025-04-10",
    status: "已释放",
    projectType: "改造工程",
    projectManager: "赵强",
    qualityIssues: false,
    actualReleaseDate: "2025-03-15",
    releaseDays: 365,
    notes: "已完成最终验收并释放质保金",
    documentLinks: ["排水系统验收报告.pdf", "质保金释放单.pdf"],
    lastUpdated: "2025-03-15"
  },
  {
    id: 4,
    projectName: "矿区B区开采工程",
    contractNo: "HT2022-078",
    supplier: "东方矿业工程有限公司",
    supplierContact: "陈总 13900139004",
    depositAmount: "¥520,000",
    depositAmountValue: 520000,
    depositDate: "2025-01-05",
    releaseDate: "2025-04-05",
    status: "待释放",
    projectType: "开采工程",
    projectManager: "周明",
    qualityIssues: false,
    releaseDays: 365,
    notes: "等待最终验收",
    documentLinks: ["验收申请.pdf"],
    lastUpdated: "2025-03-01"
  },
  {
    id: 5,
    projectName: "矿区道路维修工程",
    contractNo: "HT2023-034",
    supplier: "路桥建设有限公司",
    supplierContact: "郑经理 13800138005",
    depositAmount: "¥180,000",
    depositAmountValue: 180000,
    depositDate: "2025-01-12",
    releaseDate: "2025-04-12",
    status: "未到期",
    projectType: "维修工程",
    projectManager: "王刚",
    qualityIssues: false,
    releaseDays: 180,
    notes: "半年质保期",
    documentLinks: ["道路维修合同.pdf", "工程进度报告.docx"],
    lastUpdated: "2025-02-01"
  },
  {
    id: 6,
    projectName: "矿区供电系统优化",
    contractNo: "HT2023-056",
    supplier: "电力工程有限公司",
    supplierContact: "林工 13900139006",
    depositAmount: "¥380,000",
    depositAmountValue: 380000,
    depositDate: "2025-01-25",
    releaseDate: "2025-04-10",
    status: "未到期",
    projectType: "系统工程",
    projectManager: "张建国",
    qualityIssues: false,
    releaseDays: 365,
    notes: "需满足一年无故障运行",
    documentLinks: ["供电系统合同.pdf"],
    lastUpdated: "2025-02-10"
  },
  {
    id: 7,
    projectName: "矿区办公楼翻新",
    contractNo: "HT2022-039",
    supplier: "建筑装饰有限公司",
    supplierContact: "黄总 13800138007",
    depositAmount: "¥260,000",
    depositAmountValue: 260000,
    depositDate: "2025-01-30",
    releaseDate: "2025-03-30",
    status: "待释放",
    projectType: "建筑工程",
    projectManager: "李刚",
    qualityIssues: true,
    releaseDays: 365,
    notes: "发现墙面渗水问题，待修复后再释放",
    documentLinks: ["问题整改通知.pdf"],
    lastUpdated: "2025-02-25"
  },
  {
    id: 8,
    projectName: "员工宿舍建设",
    contractNo: "HT2022-029",
    supplier: "城建集团",
    supplierContact: "吴经理 13900139008",
    depositAmount: "¥650,000",
    depositAmountValue: 650000,
    depositDate: "2025-01-15",
    releaseDate: "2025-03-15",
    status: "释放异常",
    projectType: "建筑工程",
    projectManager: "赵明",
    qualityIssues: true,
    releaseDays: 365,
    notes: "存在严重质量问题，暂不释放质保金",
    documentLinks: ["质量问题报告.pdf", "法律意见书.docx"],
    lastUpdated: "2025-02-20"
  }
];

export function QualityDepositManagement() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedDeposit, setSelectedDeposit] = useState<DepositRecord | null>(null)
  const [filteredData, setFilteredData] = useState(depositData)
  const [activeTab, setActiveTab] = useState<string>("记录")
  const [statusFilter, setStatusFilter] = useState<string>("全部")
  const [projectTypeFilter, setProjectTypeFilter] = useState<string>("全部")
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    projectName: "",
    contractNo: "",
    supplier: "",
    supplierContact: "",
    depositAmount: "",
    depositDate: "",
    releaseDate: "",
    status: "未到期",
    projectType: "",
    projectManager: "",
    notes: ""
  });

  // 计算总质保金数额
  const totalDepositAmount = useMemo(() => {
    return depositData.reduce((sum, item) => sum + item.depositAmountValue, 0);
  }, [depositData]);

  // 计算已释放质保金数额
  const releasedDepositAmount = useMemo(() => {
    return depositData
      .filter(item => item.status === "已释放")
      .reduce((sum, item) => sum + item.depositAmountValue, 0);
  }, [depositData]);

  // 计算待释放质保金数额
  const pendingReleaseAmount = useMemo(() => {
    return depositData
      .filter(item => item.status === "待释放")
      .reduce((sum, item) => sum + item.depositAmountValue, 0);
  }, [depositData]);

  // 计算异常质保金数额
  const abnormalDepositAmount = useMemo(() => {
    return depositData
      .filter(item => item.status === "释放异常")
      .reduce((sum, item) => sum + item.depositAmountValue, 0);
  }, [depositData]);

  // 计算项目类型分布
  const projectTypeDistribution = useMemo(() => {
    const distribution: Record<string, ProjectType> = {};

    depositData.forEach(item => {
      const type = item.projectType || "未分类";
      if (!distribution[type]) {
        distribution[type] = {
          name: type,
          count: 0,
          totalDeposit: 0
        };
      }

      distribution[type].count++;
      distribution[type].totalDeposit += item.depositAmountValue;
    });

    return Object.values(distribution);
  }, [depositData]);

  // 计算供应商统计
  const supplierStats = useMemo(() => {
    const stats: Record<string, SupplierData> = {};

    depositData.forEach(item => {
      if (!stats[item.supplier]) {
        stats[item.supplier] = {
          name: item.supplier,
          depositCount: 0,
          totalAmount: 0,
          releaseOnTimeRate: 0
        };
      }

      stats[item.supplier].depositCount++;
      stats[item.supplier].totalAmount += item.depositAmountValue;
    });

    // 只取前5个供应商
    return Object.values(stats)
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 5);
  }, [depositData]);

  // 计算质保金释放时间趋势（按月份分组）
  const releaseTimeline = useMemo(() => {
    const timeline: Record<string, number> = {};
    const now = new Date();
    const currentYear = now.getFullYear();

    // 初始化接下来12个月的数据
    for (let i = 0; i < 12; i++) {
      const month = new Date(currentYear, now.getMonth() + i, 1);
      const monthKey = format(month, 'yyyy-MM');
      timeline[monthKey] = 0;
    }

    // 按释放日期分组统计
    depositData.forEach(item => {
      if (item.status === "未到期") {
        const releaseDate = new Date(item.releaseDate);
        const monthKey = format(releaseDate, 'yyyy-MM');

        if (timeline[monthKey] !== undefined) {
          timeline[monthKey] += item.depositAmountValue;
        }
      }
    });

    return Object.entries(timeline).map(([month, amount]) => ({
      month,
      amount
    }));
  }, [depositData]);

  // 生成状态分布饼图选项
  const generateStatusDistributionChartOption = () => {
    const statusData = [
      { name: '未到期', value: depositData.filter(item => item.status === "未到期").length },
      { name: '待释放', value: depositData.filter(item => item.status === "待释放").length },
      { name: '已释放', value: depositData.filter(item => item.status === "已释放").length },
      { name: '释放异常', value: depositData.filter(item => item.status === "释放异常").length }
    ];

    const option = {
      title: {
        text: '质保金状态分布',
        subtext: '各状态占比',
        left: 'center',
        top: 10,
        padding: [10, 0],
        textStyle: {
          fontSize: 16
        },
        subtextStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        data: statusData.map(item => item.name)
      },
      series: [
        {
          name: '状态分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: statusData
        }
      ]
    };

    return option;
  };

  // 生成质保金释放趋势图选项
  const generateReleaseTimelineChartOption = () => {
    const option = {
      title: {
        text: '质保金释放计划',
        subtext: '未来12个月释放趋势',
        left: 'center',
        top: 10,
        padding: [10, 0],
        textStyle: {
          fontSize: 16
        },
        subtextStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${formatCurrency(data.value)}`;
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        top: 80,
        bottom: 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: releaseTimeline.map(item => item.month),
        axisLabel: {
          rotate: 45,
          formatter: function(value: string) {
            return value.substring(5); // 只显示月份
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '计划释放金额',
          type: 'bar',
          data: releaseTimeline.map(item => item.amount),
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    };

    return option;
  };

  // 生成项目类型分布图选项
  const generateProjectTypeChartOption = () => {
    const option = {
      title: {
        text: '各类型项目质保金统计',
        subtext: '按项目类型分布',
        left: 'center',
        top: 10,
        padding: [10, 0],
        textStyle: {
          fontSize: 16
        },
        subtextStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${formatCurrency(data.value)}`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        top: 80,
        bottom: 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: projectTypeDistribution.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '质保金金额',
          type: 'bar',
          data: projectTypeDistribution.map(item => item.totalDeposit),
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    };

    return option;
  };

  // 生成供应商统计图选项
  const generateSupplierChartOption = () => {
    const option = {
      title: {
        text: '主要供应商质保金情况',
        subtext: '按质保金金额排序前5名',
        left: 'center',
        top: 10,
        padding: [10, 0],
        textStyle: {
          fontSize: 16
        },
        subtextStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${formatCurrency(data.value)}`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        top: 80,
        bottom: 60,
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value;
          }
        }
      },
      yAxis: {
        type: 'category',
        data: supplierStats.map(item => item.name),
        axisLabel: {
          interval: 0,
          width: 100,
          overflow: 'truncate'
        }
      },
      series: [
        {
          name: '质保金总额',
          type: 'bar',
          data: supplierStats.map(item => item.totalAmount),
          itemStyle: {
            borderRadius: [0, 4, 4, 0]
          }
        }
      ]
    };

    return option;
  };

  // 格式化货币
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value);
  };

  // 搜索功能
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    applyFilters(value, statusFilter, projectTypeFilter);
  };

  // 应用所有筛选条件
  const applyFilters = (search: string, status: string, projectType: string) => {
    let filtered = depositData;

    // 搜索筛选
    if (search) {
      filtered = filtered.filter(
        (item) =>
          item.projectName.toLowerCase().includes(search.toLowerCase()) ||
          item.contractNo.toLowerCase().includes(search.toLowerCase()) ||
          item.supplier.toLowerCase().includes(search.toLowerCase()) ||
          (item.projectManager && item.projectManager.toLowerCase().includes(search.toLowerCase())) ||
          (item.notes && item.notes.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // 状态筛选
    if (status !== "全部") {
      filtered = filtered.filter(item => item.status === status);
    }

    // 项目类型筛选
    if (projectType !== "全部") {
      filtered = filtered.filter(item => item.projectType === projectType);
    }

    setFilteredData(filtered);
  };

  // 状态筛选变更
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    applyFilters(searchTerm, value, projectTypeFilter);
  };

  // 项目类型筛选变更
  const handleProjectTypeFilterChange = (value: string) => {
    setProjectTypeFilter(value);
    applyFilters(searchTerm, statusFilter, value);
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setFilteredData(depositData);
      setSearchTerm("");
      setStatusFilter("全部");
      setProjectTypeFilter("全部");
      toast({
        title: "数据已刷新",
        description: "质保金记录已更新"
      });
    }, 1000);
  };

  // 表单输入变化处理
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // 处理选择状态变化
  const handleStatusChange = (value: string) => {
    setFormData({
      ...formData,
      status: value
    });
  };

  // 处理日期选择变化
  const handleDateChange = (date: Date | undefined, fieldName: string) => {
    if (date) {
      setFormData({
        ...formData,
        [fieldName]: format(date, 'yyyy-MM-dd')
      });
    }
  };

  // 验证表单
  const validateForm = () => {
    if (!formData.projectName) {
      toast({
        title: "请输入项目名称",
        variant: "destructive"
      });
      return false;
    }

    if (!formData.contractNo) {
      toast({
        title: "请输入合同编号",
        variant: "destructive"
      });
      return false;
    }

    if (!formData.supplier) {
      toast({
        title: "请输入供应商",
        variant: "destructive"
      });
      return false;
    }

    if (!formData.depositAmount) {
      toast({
        title: "请输入质保金金额",
        variant: "destructive"
      });
      return false;
    }

    if (!formData.depositDate) {
      toast({
        title: "请选择缴纳日期",
        variant: "destructive"
      });
      return false;
    }

    if (!formData.releaseDate) {
      toast({
        title: "请选择预计释放日期",
        variant: "destructive"
      });
      return false;
    }

    return true;
  };

  // 处理添加质保金
  const handleAddDeposit = () => {
    if (!validateForm()) return;

    // 解析金额，移除货币符号和逗号
    const amountStr = formData.depositAmount.replace(/[^\d.-]/g, '');
    const depositAmountValue = parseFloat(amountStr) || 0;

    const newDeposit: DepositRecord = {
      id: depositData.length + 1,
      projectName: formData.projectName,
      contractNo: formData.contractNo,
      supplier: formData.supplier,
      supplierContact: formData.supplierContact,
      depositAmount: formData.depositAmount.startsWith('¥') ? formData.depositAmount : `¥${formData.depositAmount}`,
      depositAmountValue: depositAmountValue,
      depositDate: formData.depositDate,
      releaseDate: formData.releaseDate,
      status: formData.status as "未到期" | "待释放" | "已释放" | "释放异常",
      projectType: formData.projectType,
      projectManager: formData.projectManager,
      notes: formData.notes,
      lastUpdated: format(new Date(), 'yyyy-MM-dd')
    };

    // 模拟后端添加
    depositData.push(newDeposit);

    // 重新应用筛选条件
    applyFilters(searchTerm, statusFilter, projectTypeFilter);

    // 清空表单并关闭弹窗
    setFormData({
      projectName: "",
      contractNo: "",
      supplier: "",
      supplierContact: "",
      depositAmount: "",
      depositDate: "",
      releaseDate: "",
      status: "未到期",
      projectType: "",
      projectManager: "",
      notes: ""
    });

    setIsAddDialogOpen(false);

    toast({
      title: "添加成功",
      description: "新的质保金记录已添加"
    });
  };

  // 处理查看详情
  const handleViewDetail = (deposit: DepositRecord) => {
    setSelectedDeposit(deposit);
    setIsDetailDialogOpen(true);
  };

  // 处理编辑质保金
  const handleEditDeposit = (deposit: DepositRecord) => {
    setSelectedDeposit(deposit);

    setFormData({
      projectName: deposit.projectName,
      contractNo: deposit.contractNo,
      supplier: deposit.supplier,
      supplierContact: deposit.supplierContact || "",
      depositAmount: deposit.depositAmount,
      depositDate: deposit.depositDate,
      releaseDate: deposit.releaseDate,
      status: deposit.status,
      projectType: deposit.projectType || "",
      projectManager: deposit.projectManager || "",
      notes: deposit.notes || ""
    });

    setIsEditDialogOpen(true);
  };

  // 处理更新质保金
  const handleUpdateDeposit = () => {
    if (!validateForm() || !selectedDeposit) return;

    // 解析金额，移除货币符号和逗号
    const amountStr = formData.depositAmount.replace(/[^\d.-]/g, '');
    const depositAmountValue = parseFloat(amountStr) || 0;

    // 更新数据
    const updatedDeposit: DepositRecord = {
      ...selectedDeposit,
      projectName: formData.projectName,
      contractNo: formData.contractNo,
      supplier: formData.supplier,
      supplierContact: formData.supplierContact,
      depositAmount: formData.depositAmount.startsWith('¥') ? formData.depositAmount : `¥${formData.depositAmount}`,
      depositAmountValue: depositAmountValue,
      depositDate: formData.depositDate,
      releaseDate: formData.releaseDate,
      status: formData.status as "未到期" | "待释放" | "已释放" | "释放异常",
      projectType: formData.projectType,
      projectManager: formData.projectManager,
      notes: formData.notes,
      lastUpdated: format(new Date(), 'yyyy-MM-dd')
    };

    // 模拟后端更新
    const index = depositData.findIndex(item => item.id === selectedDeposit.id);
    if (index !== -1) {
      depositData[index] = updatedDeposit;
    }

    // 重新应用筛选条件
    applyFilters(searchTerm, statusFilter, projectTypeFilter);

    // 清空表单并关闭弹窗
    setIsEditDialogOpen(false);
    setSelectedDeposit(null);

    toast({
      title: "更新成功",
      description: "质保金记录已更新"
    });
  };

  // 处理删除质保金
  const handleDeleteDeposit = (deposit: DepositRecord) => {
    setSelectedDeposit(deposit);
    setIsDeleteDialogOpen(true);
  };

  // 确认删除质保金
  const confirmDelete = () => {
    if (!selectedDeposit) return;

    // 模拟后端删除
    const index = depositData.findIndex(item => item.id === selectedDeposit.id);
    if (index !== -1) {
      depositData.splice(index, 1);
    }

    // 重新应用筛选条件
    applyFilters(searchTerm, statusFilter, projectTypeFilter);

    // 关闭弹窗
    setIsDeleteDialogOpen(false);
    setSelectedDeposit(null);

    toast({
      title: "删除成功",
      description: "质保金记录已删除"
    });
  };

  // 处理导出数据
  const handleExport = () => {
    // 模拟导出功能
    toast({
      title: "导出成功",
      description: "质保金记录已导出至Excel文件"
    });
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已释放":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            已释放
          </Badge>
        )
      case "待释放":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
            待释放
          </Badge>
        )
      case "未到期":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            未到期
          </Badge>
        )
      case "释放异常":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
            释放异常
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  };

  // 添加自适应图表大小的功能
  useEffect(() => {
    const handleResize = () => {
      const charts = document.querySelectorAll('.echarts-for-react');
      charts.forEach((chart: any) => {
        if (chart.__echarts__) {
          chart.__echarts__.resize();
        }
      });
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">质保金管理</h1>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索项目名称、合同编号..."
              className="w-64 pl-8"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                添加质保金记录
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加质保金记录</DialogTitle>
                <DialogDescription>请填写质保金记录的详细信息</DialogDescription>
              </DialogHeader>
              <ScrollArea className="max-h-[65vh]">
                <div className="grid gap-4 py-4 px-1">
                  <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                      <Label htmlFor="projectName">项目名称 <span className="text-red-500">*</span></Label>
                      <Input
                        id="projectName"
                        name="projectName"
                        placeholder="输入项目名称"
                        value={formData.projectName}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contractNo">合同编号 <span className="text-red-500">*</span></Label>
                      <Input
                        id="contractNo"
                        name="contractNo"
                        placeholder="例如：HT2023-001"
                        value={formData.contractNo}
                        onChange={handleInputChange}
                      />
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                      <Label htmlFor="supplier">供应商 <span className="text-red-500">*</span></Label>
                      <Input
                        id="supplier"
                        name="supplier"
                        placeholder="输入供应商名称"
                        value={formData.supplier}
                        onChange={handleInputChange}
                      />
                  </div>
                  <div className="space-y-2">
                      <Label htmlFor="supplierContact">供应商联系人</Label>
                      <Input
                        id="supplierContact"
                        name="supplierContact"
                        placeholder="联系人姓名和电话"
                        value={formData.supplierContact}
                        onChange={handleInputChange}
                      />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                      <Label htmlFor="depositAmount">质保金金额 <span className="text-red-500">*</span></Label>
                      <Input
                        id="depositAmount"
                        name="depositAmount"
                        type="text"
                        placeholder="例如：100000"
                        value={formData.depositAmount}
                        onChange={handleInputChange}
                      />
                  </div>
                  <div className="space-y-2">
                      <Label htmlFor="projectType">项目类型</Label>
                      <Input
                        id="projectType"
                        name="projectType"
                        placeholder="例如：开采工程"
                        value={formData.projectType}
                        onChange={handleInputChange}
                      />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                      <Label htmlFor="depositDate">缴纳日期 <span className="text-red-500">*</span></Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.depositDate ? formData.depositDate : <span>选择日期</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={formData.depositDate ? new Date(formData.depositDate) : undefined}
                            onSelect={(date) => handleDateChange(date, 'depositDate')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                  </div>
                    <div className="space-y-2">
                      <Label htmlFor="releaseDate">预计释放日期 <span className="text-red-500">*</span></Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.releaseDate ? formData.releaseDate : <span>选择日期</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={formData.releaseDate ? new Date(formData.releaseDate) : undefined}
                            onSelect={(date) => handleDateChange(date, 'releaseDate')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                      <Select
                        value={formData.status}
                        onValueChange={handleStatusChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="未到期">未到期</SelectItem>
                          <SelectItem value="待释放">待释放</SelectItem>
                          <SelectItem value="已释放">已释放</SelectItem>
                          <SelectItem value="释放异常">释放异常</SelectItem>
                        </SelectContent>
                      </Select>
                  </div>
                    <div className="space-y-2">
                      <Label htmlFor="projectManager">项目经理</Label>
                      <Input
                        id="projectManager"
                        name="projectManager"
                        placeholder="负责人姓名"
                        value={formData.projectManager}
                        onChange={handleInputChange}
                      />
                </div>
              </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">备注</Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      placeholder="补充说明信息..."
                      value={formData.notes}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </ScrollArea>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddDeposit}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">质保金总额</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totalDepositAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{depositData.length}个项目的质保金总额</p>
            <div className="mt-2">
              <Progress value={100} className="h-2 bg-blue-100" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已释放质保金</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{releasedDepositAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {depositData.filter(item => item.status === "已释放").length}个项目已完成质保期
            </p>
            <div className="mt-2">
              <Progress
                value={(releasedDepositAmount / totalDepositAmount) * 100}
                className="h-2 bg-blue-100"
              />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待释放质保金</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{pendingReleaseAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {depositData.filter(item => item.status === "待释放").length}个项目待释放
            </p>
            <div className="mt-2">
              <Progress
                value={(pendingReleaseAmount / totalDepositAmount) * 100}
                className="h-2 bg-blue-100"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-2 md:flex md:space-x-2">
          <TabsTrigger value="记录" className="flex items-center space-x-1">
            <FileText className="h-4 w-4" />
            <span>质保金记录</span>
          </TabsTrigger>
          <TabsTrigger value="统计" className="flex items-center space-x-1">
            <BarChart4 className="h-4 w-4" />
            <span>数据统计</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="记录" className="mt-6 space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
              <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="全部">全部状态</SelectItem>
                  <SelectItem value="未到期">未到期</SelectItem>
                  <SelectItem value="待释放">待释放</SelectItem>
                  <SelectItem value="已释放">已释放</SelectItem>
                  <SelectItem value="释放异常">释放异常</SelectItem>
                </SelectContent>
              </Select>

              <Select value={projectTypeFilter} onValueChange={handleProjectTypeFilterChange}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="项目类型筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="全部">全部类型</SelectItem>
                  {projectTypeDistribution.map(type => (
                    <SelectItem key={type.name} value={type.name}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              找到 {filteredData.length} 条记录
            </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>质保金记录</CardTitle>
          <CardDescription>管理所有项目的质保金记录，包括缴纳、释放和状态跟踪</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>项目名称</TableHead>
                <TableHead>合同编号</TableHead>
                <TableHead>供应商</TableHead>
                <TableHead>质保金金额</TableHead>
                <TableHead>缴纳日期</TableHead>
                <TableHead>预计释放日期</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => (
                <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.projectName}</TableCell>
                  <TableCell>{item.contractNo}</TableCell>
                  <TableCell>{item.supplier}</TableCell>
                  <TableCell>{item.depositAmount}</TableCell>
                  <TableCell>{item.depositDate}</TableCell>
                  <TableCell>{item.releaseDate}</TableCell>
                  <TableCell>{getStatusBadge(item.status)}</TableCell>
                  <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => handleViewDetail(item)}>
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleEditDeposit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteDeposit(item)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="统计" className="mt-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>质保金状态分布</CardTitle>
                <CardDescription>各状态质保金记录数量统计</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={generateStatusDistributionChartOption()}
                    style={{ height: '100%', width: '100%' }}
                    theme="quality-deposit-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>质保金释放计划</CardTitle>
                <CardDescription>未来12个月质保金释放趋势</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={generateReleaseTimelineChartOption()}
                    style={{ height: '100%', width: '100%' }}
                    theme="quality-deposit-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>各类型项目质保金统计</CardTitle>
                <CardDescription>按项目类型分类的质保金金额统计</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={generateProjectTypeChartOption()}
                    style={{ height: '100%', width: '100%' }}
                    theme="quality-deposit-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>主要供应商质保金情况</CardTitle>
                <CardDescription>按质保金金额排名的供应商</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={generateSupplierChartOption()}
                    style={{ height: '100%', width: '100%' }}
                    theme="quality-deposit-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 查看详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>质保金详情</DialogTitle>
            <DialogDescription>查看质保金记录的详细信息</DialogDescription>
          </DialogHeader>
          <ScrollArea className="flex-1 max-h-[calc(90vh-120px)]">
            {selectedDeposit && (
              <div className="p-4 space-y-6">
                <div className="flex items-center space-x-4 p-4 bg-slate-50 rounded-lg">
                  <div className="rounded-full bg-blue-100 p-3">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedDeposit.projectName}</h3>
                    <p className="text-sm text-muted-foreground">合同编号: {selectedDeposit.contractNo}</p>
                  </div>
                  <div className="ml-auto">
                    {getStatusBadge(selectedDeposit.status)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">基本信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">项目类型</div>
                        <div className="text-sm">{selectedDeposit.projectType || '未指定'}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">项目经理</div>
                        <div className="text-sm">{selectedDeposit.projectManager || '未指定'}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">最后更新</div>
                        <div className="text-sm">{selectedDeposit.lastUpdated || '未知'}</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">质保金信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">金额</div>
                        <div className="text-sm font-bold">{selectedDeposit.depositAmount}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">缴纳日期</div>
                        <div className="text-sm">{selectedDeposit.depositDate}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">预计释放日期</div>
                        <div className="text-sm">{selectedDeposit.releaseDate}</div>
                      </div>
                      {selectedDeposit.actualReleaseDate && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">实际释放日期</div>
                          <div className="text-sm">{selectedDeposit.actualReleaseDate}</div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">供应商信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">供应商</div>
                        <div className="text-sm">{selectedDeposit.supplier}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">联系人</div>
                        <div className="text-sm">{selectedDeposit.supplierContact || '未指定'}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-muted-foreground">质量问题</div>
                        <div className="text-sm">{selectedDeposit.qualityIssues ? '是' : '否'}</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {selectedDeposit.notes && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">备注信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm border rounded-md p-3 bg-muted/50">
                        {selectedDeposit.notes}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {selectedDeposit.documentLinks && selectedDeposit.documentLinks.length > 0 && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">相关文档</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {selectedDeposit.documentLinks.map((doc, index) => (
                          <Badge key={index} variant="outline" className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {doc}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </ScrollArea>
          <DialogFooter className="px-6 py-4">
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>关闭</Button>
            <Button onClick={() => {
              setIsDetailDialogOpen(false);
              handleEditDeposit(selectedDeposit!);
            }}>编辑</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>编辑质保金记录</DialogTitle>
            <DialogDescription>修改质保金记录信息</DialogDescription>
          </DialogHeader>
          <ScrollArea className="flex-1 max-h-[calc(90vh-120px)]">
            <div className="grid gap-4 py-4 px-1">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-projectName">项目名称 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-projectName"
                    name="projectName"
                    placeholder="输入项目名称"
                    value={formData.projectName}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-contractNo">合同编号 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-contractNo"
                    name="contractNo"
                    placeholder="例如：HT2023-001"
                    value={formData.contractNo}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-supplier">供应商 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-supplier"
                    name="supplier"
                    placeholder="输入供应商名称"
                    value={formData.supplier}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-supplierContact">供应商联系人</Label>
                  <Input
                    id="edit-supplierContact"
                    name="supplierContact"
                    placeholder="联系人姓名和电话"
                    value={formData.supplierContact}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-depositAmount">质保金金额 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-depositAmount"
                    name="depositAmount"
                    type="text"
                    placeholder="例如：100000"
                    value={formData.depositAmount}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-projectType">项目类型</Label>
                  <Input
                    id="edit-projectType"
                    name="projectType"
                    placeholder="例如：开采工程"
                    value={formData.projectType}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-depositDate">缴纳日期 <span className="text-red-500">*</span></Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.depositDate ? formData.depositDate : <span>选择日期</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.depositDate ? new Date(formData.depositDate) : undefined}
                        onSelect={(date) => handleDateChange(date, 'depositDate')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-releaseDate">预计释放日期 <span className="text-red-500">*</span></Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.releaseDate ? formData.releaseDate : <span>选择日期</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.releaseDate ? new Date(formData.releaseDate) : undefined}
                        onSelect={(date) => handleDateChange(date, 'releaseDate')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-status">状态</Label>
                  <Select
                    value={formData.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="未到期">未到期</SelectItem>
                      <SelectItem value="待释放">待释放</SelectItem>
                      <SelectItem value="已释放">已释放</SelectItem>
                      <SelectItem value="释放异常">释放异常</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-projectManager">项目经理</Label>
                  <Input
                    id="edit-projectManager"
                    name="projectManager"
                    placeholder="负责人姓名"
                    value={formData.projectManager}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-notes">备注</Label>
                <Textarea
                  id="edit-notes"
                  name="notes"
                  placeholder="补充说明信息..."
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className="px-6 py-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleUpdateDeposit}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              确认删除
            </DialogTitle>
            <DialogDescription>
              您确定要删除"{selectedDeposit?.projectName}"的质保金记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

