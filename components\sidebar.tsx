"use client"

import type React from "react"

import {
  ChevronDown,
  ChevronRight,
  Settings,
  Shield,
  HardHat,
  Users,
  DollarSign,
  Database,
  Zap,
  Lock,
  FileText,
  Package,
  CheckSquare,
  BarChart2,
  Sun,
  Moon,
  LayoutTemplate,
} from "lucide-react"
import { useState, useEffect } from "react"

// 首先，导入 Next.js 的 useRouter 和 usePathname 钩子
import { useRouter } from "next/navigation"
import { usePathname } from "next/navigation"
import { useTheme } from "@/contexts/theme-context"
import Link from "next/link"

interface SidebarProps {
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
}

interface MenuItem {
  id: string
  label: string
  icon?: React.ReactNode
  children?: MenuItem[]
}

// 定义菜单项数据
const menuItems: MenuItem[] = [
  {
    id: "system-management",
    label: "系统管理",
    icon: <Settings size={16} />,
    children: [
      {
        id: "user-management",
        label: "用户管理",
      },
      {
        id: "role-management",
        label: "角色管理",
      },
      {
        id: "super-user-settings",
        label: "超级用户设置",
      },
      {
        id: "public-file-cabinet",
        label: "公共文件柜",
      },
      {
        id: "system-settings",
        label: "系统设置",
      },
    ],
  },
  {
    id: "safety-management",
    label: "安全管理",
    icon: <Shield size={16} />,
    children: [
      {
        id: "safety-check",
        label: "安全检查",
      },
      {
        id: "safety-hazard-management",
        label: "安全隐患管理",
      },
      {
        id: "special-personnel-management",
        label: "特种人员管理",
      },
      {
        id: "safety-laws-regulations",
        label: "安全法律法规",
      },
      {
        id: "emergency-plan-management",
        label: "应急预案管理",
      },
      {
        id: "hazard-source-management",
        label: "危险源管理",
      },
      {
        id: "accident-case",
        label: "事故与案例",
      },
      {
        id: "fire-equipment-management",
        label: "消防设备管理",
      },
      {
        id: "rescue-force-management",
        label: "救援力量管理",
      },
      {
        id: "emergency-drill-management",
        label: "应急演练管理",
      },
      {
        id: "safety-measures",
        label: "安全措施",
      },
    ],
  },
  {
    id: "project-management",
    label: "工程管理",
    icon: <HardHat size={16} />,
    children: [
      {
        id: "project-homepage",
        label: "工程概览",
      },
      {
        id: "project-plan",
        label: "工程计划管理",
      },
      {
        id: "project-progress",
        label: "工程进度管理",
      },
      {
        id: "project-cost",
        label: "工程费用",
      },
      {
        id: "project-safety",
        label: "工程安全",
      },
      {
        id: "project-quality-management",
        label: "工程质量管理",
      },
      {
        id: "project-maintenance",
        label: "工程维护",
      },
      {
        id: "project-file-management",
        label: "工程文档管理",
      },
      {
        id: "project-contract-management",
        label: "工程合同管理",
      },
      {
        id: "project-query",
        label: "工程查询",
      },
      {
        id: "project-approval",
        label: "工程审批",
      },
      {
        id: "project-resources",
        label: "工程资源管理",
      },
      {
        id: "project-risk",
        label: "工程风险管理",
      },
      {
        id: "project-documents",
        label: "工程文档管理",
      },
    ],
  },
  {
    id: "personnel-management",
    label: "人事管理",
    icon: <Users size={16} />,
    children: [
      {
        id: "personnel-info-management",
        label: "人员信息管理",
      },
      {
        id: "personnel-changes",
        label: "人事异动",
      },
      {
        id: "employee-management",
        label: "员工管理",
      },
      {
        id: "personnel-assessment",
        label: "人事考核",
      },
      {
        id: "party-affairs-management",
        label: "党务管理",
      },
      {
        id: "union-support",
        label: "工会与帮扶",
      },
      {
        id: "family-planning",
        label: "计划生育",
      },
      {
        id: "education-training",
        label: "教育与培训",
      },
      {
        id: "cadre-management",
        label: "干部管理",
      },
    ],
  },
  {
    id: "financial-management",
    label: "财务管理",
    icon: <DollarSign size={16} />,
    children: [
      {
        id: "financial-status-maintenance",
        label: "财务状况维护",
      },
      {
        id: "employees-wages",
        label: "从业人员及工资总额",
      },
      {
        id: "quality-deposit-management",
        label: "质保金管理",
      },
    ],
  },
  {
    id: "fixed-assets-management",
    label: "固定资产管理",
    icon: <Database size={16} />,
    children: [
      {
        id: "fixed-assets-info",
        label: "固定资产信息",
      },
      {
        id: "fixed-assets-maintenance",
        label: "固定资产维护",
      },
      {
        id: "fixed-assets-warning",
        label: "固定资产预警",
      },
      {
        id: "equipment-management",
        label: "设备管理",
      },
    ],
  },
  {
    id: "energy-management",
    label: "能源管理",
    icon: <Zap size={16} />,
    children: [
      {
        id: "energy-type",
        label: "能源类型",
      },
      {
        id: "energy-usage",
        label: "能源使用情况",
      },
    ],
  },
  {
    id: "security-management",
    label: "保卫管理",
    icon: <Lock size={16} />,
    children: [
      {
        id: "security-dept-inspection",
        label: "保卫部巡检",
      },
      {
        id: "blasting-management",
        label: "爆破管理",
      },
      {
        id: "personnel-positioning",
        label: "人员定位",
      },
    ],
  },
  {
    id: "office-admin-management",
    label: "办公与行政管理",
    icon: <FileText size={16} />,
    children: [
      {
        id: "office-management",
        label: "办公室管理",
      },
      {
        id: "archive-management",
        label: "档案管理",
      },
      {
        id: "mail-management",
        label: "邮件管理",
      },
    ],
  },
  {
    id: "material-supply-chain",
    label: "物资与供应链管理",
    icon: <Package size={16} />,
    children: [
      {
        id: "supply-management",
        label: "供应管理",
      },
      {
        id: "procurement-management",
        label: "采购管理",
      },
      {
        id: "warehouse-management",
        label: "仓库管理",
      },
      {
        id: "material-maintenance",
        label: "物资维护",
      },
      {
        id: "turnover-material-management",
        label: "周转材料管理",
      },
    ],
  },
  {
    id: "task-process-management",
    label: "任务与流程管理",
    icon: <CheckSquare size={16} />,
    children: [
      {
        id: "task-management",
        label: "任务管理",
      },
      {
        id: "process-approval",
        label: "流程审批",
      },
    ],
  },
  {
    id: "visual-dashboard",
    label: "可视化大屏",
    icon: <BarChart2 size={16} />,
  },
  {
    id: "enterprise-architecture",
    label: "企业架构展示",
    icon: <LayoutTemplate size={16} />,
  }
];

export function Sidebar({ collapsed, setCollapsed }: SidebarProps) {
  const [activeItem, setActiveItem] = useState<string | null>(null)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const { themeMode: contextThemeMode, toggleTheme } = useTheme()

  // 使用传入的themeMode，如果没有则使用上下文中的themeMode
  const currentTheme = contextThemeMode

  // 在组件挂载时，根据当前路径自动展开对应的菜单组
  useEffect(() => {
    // 从 localStorage 读取展开的菜单项
    const savedExpandedItems = localStorage.getItem("sidebar-expanded-items")
    if (savedExpandedItems) {
      setExpandedItems(JSON.parse(savedExpandedItems))
    } else {
      // 如果没有保存的状态，根据当前路径自动展开对应的菜单组
      const path = pathname.toLowerCase()
      if (path.includes("system-management")) {
        setExpandedItems(["system"])
      } else if (path.includes("safety-management")) {
        setExpandedItems(["safety"])
      } else if (path.includes("project-management")) {
        setExpandedItems(["project"])
      } else if (path.includes("personnel-management")) {
        setExpandedItems(["personnel"])
      } else if (path.includes("financial-management")) {
        setExpandedItems(["financial"])
      } else if (path.includes("fixed-assets-management")) {
        setExpandedItems(["fixed"])
      } else if (path.includes("energy-management")) {
        setExpandedItems(["energy"])
      } else if (path.includes("security-management")) {
        setExpandedItems(["security"])
      } else if (path.includes("office-admin-management")) {
        setExpandedItems(["office"])
      } else if (path.includes("material-supply-chain")) {
        setExpandedItems(["material"])
      } else if (path.includes("task-process-management")) {
        setExpandedItems(["task"])
      } else if (path.includes("visual-dashboard")) {
        setExpandedItems(["visual"])
      } else if (path.includes("enterprise-architecture")) {
        setExpandedItems(["enterprise"])
      }
    }

    // 根据当前路径设置活动项
    const pathSegments = pathname.split("/")
    if (pathSegments.length > 2) {
      setActiveItem(pathSegments[pathSegments.length - 1])
    }
  }, [])

  const toggleExpand = (id: string) => {
    let newExpandedItems;
    if (expandedItems.includes(id)) {
      newExpandedItems = expandedItems.filter((item) => item !== id);
    } else {
      newExpandedItems = [...expandedItems, id];
    }
    setExpandedItems(newExpandedItems);
    // 保存到 localStorage
    localStorage.setItem("sidebar-expanded-items", JSON.stringify(newExpandedItems));
  }

  // 修改 renderMenuItem 函数，添加导航功能
  const renderMenuItem = (item: MenuItem) => {
    const isExpanded = expandedItems.includes(item.id)
    const isActive = pathname.includes(item.id) || activeItem === item.id
    const isHovered = hoveredItem === item.id

    // 为菜单项添加路径
    const getItemPath = (item: MenuItem): string | null => {
      // 根据菜单项ID返回对应的路由路径
      const pathMap: Record<string, string> = {
        // 系统管理
        'user-management': '/system-management/user-management',
        'role-management': '/system-management/role-management',
        'super-user-settings': '/system-management/super-user-settings',
        'public-file-cabinet': '/system-management/public-file-cabinet',
        'system-settings': '/system-management/system-settings',

        // 安全管理
        'safety-check': '/safety-management/safety-check',
        'safety-hazard-management': '/safety-management/safety-hazard-management',
        'special-personnel-management': '/safety-management/special-personnel-management',
        'safety-laws-regulations': '/safety-management/safety-laws-regulations',
        'emergency-plan-management': '/safety-management/emergency-plan-management',
        'hazard-source-management': '/safety-management/hazard-source-management',
        'accident-case': '/safety-management/accident-case',
        'fire-equipment-management': '/safety-management/fire-equipment-management',
        'rescue-force-management': '/safety-management/rescue-force-management',
        'emergency-drill-management': '/safety-management/emergency-drill-management',
        'safety-measures': '/safety-management/safety-measures',

        // 工程管理
        'project-homepage': '/project-management/project-homepage',
        'project-plan': '/project-management/project-plan',
        'project-progress': '/project-management/project-progress',
        'project-cost': '/project-management/project-cost',
        'project-safety': '/project-management/project-safety',
        'project-quality-management': '/project-management/project-quality-management',
        'project-maintenance': '/project-management/project-maintenance',
        'project-file-management': '/project-management/project-file-management',
        'project-contract-management': '/project-management/project-contract-management',
        'project-query': '/project-management/project-query',
        'project-approval': '/project-management/project-approval',
        'project-resources': '/project-management/project-resources',
        'project-risk': '/project-management/project-risk',
        'project-documents': '/project-management/project-documents',

        // 人事管理
        'personnel-info-management': '/personnel-management/personnel-info-management',
        'personnel-changes': '/personnel-management/personnel-changes',
        'employee-management': '/personnel-management/employee-management',
        'personnel-assessment': '/personnel-management/personnel-assessment',
        'party-affairs-management': '/personnel-management/party-affairs-management',
        'union-support': '/personnel-management/union-support',
        'family-planning': '/personnel-management/family-planning',
        'education-training': '/personnel-management/education-training',
        'cadre-management': '/personnel-management/cadre-management',

        // 财务管理
        'financial-status-maintenance': '/financial-management/financial-status-maintenance',
        'employees-wages': '/financial-management/employees-wages',
        'quality-deposit-management': '/financial-management/quality-deposit-management',

        // 固定资产管理
        'fixed-assets-info': '/fixed-assets-management/fixed-assets-info',
        'fixed-assets-maintenance': '/fixed-assets-management/fixed-assets-maintenance',
        'fixed-assets-warning': '/fixed-assets-management/fixed-assets-warning',
        'equipment-management': '/fixed-assets-management/equipment-management',

        // 能源管理
        'energy-type': '/energy-management/energy-type',
        'energy-usage': '/energy-management/energy-usage',

        // 保卫管理
        'security-dept-inspection': '/security-management/security-dept-inspection',
        'blasting-management': '/security-management/blasting-management',
        'personnel-positioning': '/security-management/personnel-positioning',

        // 办公与行政管理
        'office-management': '/office-admin-management/office-management',
        'archive-management': '/office-admin-management/archive-management',
        'mail-management': '/office-admin-management/mail-management',

        // 物资与供应链管理
        'supply-management': '/material-supply-chain/supply-management',
        'procurement-management': '/material-supply-chain/procurement-management',
        'warehouse-management': '/material-supply-chain/warehouse-management',
        'material-maintenance': '/material-supply-chain/material-maintenance',
        'turnover-material-management': '/material-supply-chain/turnover-material-management',

        // 任务与流程管理
        'task-management': '/task-process-management/task-management',
        'process-approval': '/task-process-management/process-approval',

        // 可视化大屏
        'visual-dashboard': '/visual-dashboard',

        // 企业架构展示
        'enterprise-architecture': '/enterprise-architecture',
      };

      return pathMap[item.id] || null;
    }

    const itemPath = getItemPath(item)

    const handleItemClick = () => {
      if (item.children) {
        toggleExpand(item.id)
      } else {
        setActiveItem(item.id)
        if (itemPath) {
          router.push(itemPath)
        }
      }
    }

    return (
      <div key={item.id}>
        <div
          className={`flex items-center px-4 py-2.5 cursor-pointer rounded-md relative group transition-all duration-200 overflow-hidden ${
            isActive
              ? currentTheme === "dark"
                ? "bg-[#2c2c2e]/80 text-[#E5E7EB] font-medium"
                : "bg-white/90 text-[#2D3748] font-medium shadow-sm"
              : currentTheme === "dark"
                ? "hover:bg-[#2c2c2e]/60 text-[#A0AEC0]"
                : "hover:bg-white/70 text-[#4A5568]"
          } ${
            !item.children ? 'hover:translate-x-1' : ''
          }`}
          onClick={handleItemClick}
          onMouseEnter={() => setHoveredItem(item.id)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          {/* 背景图片和渐变遮罩 */}
          {(item.children || item.id === 'visual-dashboard' || item.id === 'enterprise-architecture') && (
            <>
              <div 
                className="absolute inset-0 transition-all duration-500"
                style={{
                  backgroundImage: `url(/menu-bg/${item.id}.png)`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center right',
                  opacity: isHovered ? '0.9' : '0.8',
                  transform: isHovered ? 'scale(1.05)' : 'scale(1)'
                }}
              />
              <div 
                className="absolute inset-0 transition-all duration-500"
                style={{
                  background: currentTheme === "dark"
                    ? 'linear-gradient(to left, transparent 0%, #1E1E1E 70%)'
                    : 'linear-gradient(to left, transparent 0%, #F9FAFC 70%)',
                  opacity: isHovered ? '0.95' : '1'
                }}
              />
              <div 
                className="absolute inset-0 transition-all duration-500"
                style={{
                  background: currentTheme === "dark"
                    ? 'linear-gradient(to right, #1E1E1E 10%, transparent 100%)'
                    : 'linear-gradient(to right, #F9FAFC 10%, transparent 100%)',
                  opacity: isHovered ? '0.95' : '1'
                }}
              />
            </>
          )}

          {/* 活动菜单项指示器 - 渐变竖条 */}
          {isActive && (
            <div
              className={`absolute left-0 top-0 w-1 h-full rounded-full bg-gradient-to-r from-[#3182CE] to-[#68D391]`}
            />
          )}
          <div className={`flex items-center relative z-10 ${isActive ? 'transform scale-105 transition-transform duration-150' : ''}`}>
            {item.icon ? (
              <div className={`transition-all duration-200 ${
                isActive || isHovered ? (currentTheme === "dark" ? "text-[#E5E7EB]" : "text-[#2D3748]") : ""
              }`}>
                {item.icon}
              </div>
            ) : (
              <div className="w-4" />
            )}
            <span className={`ml-2 truncate transition-all duration-200 ${
              isActive ? `font-bold bg-clip-text text-transparent bg-gradient-to-r ${
                currentTheme === "dark" 
                  ? "from-[#2B6CB0] to-[#4299E1]" 
                  : "from-[#2B6CB0] to-[#4299E1]"
              }` : ""
            }`}>{item.label}</span>
            {item.children && (
              <div className={`ml-auto transition-transform duration-200 ${
                isExpanded ? "rotate-180" : ""
              } ${
                isActive || isHovered ? (currentTheme === "dark" ? "text-[#E5E7EB]" : "text-[#2D3748]") : ""
              }`}>
                {isExpanded ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </div>
            )}
          </div>
        </div>

        {item.children && isExpanded && (
          <div className={`pl-6 space-y-1 overflow-hidden transition-all duration-300 ${
            currentTheme === "dark" ? "bg-[#1E1E1E]" : "bg-[#F9FAFC]"
          }`}>
            {item.children.map((child) => renderMenuItem(child))}
          </div>
        )}
      </div>
    )
  }

  // 侧边栏的宽度，基于是否折叠状态
  const sidebarWidth = collapsed ? "w-16" : "w-64"
  const sidebarTransition = "transition-all duration-300 ease-in-out"

  return (
    <aside className={`${sidebarWidth} ${sidebarTransition} ${
      currentTheme === "dark"
        ? "bg-[#1E1E1E] border-r border-[#2c2c2e]"
        : "bg-[#F9FAFC] border-r border-gray-200"
    } shadow-lg overflow-hidden relative flex flex-col`}>
      {/* 毛玻璃效果 */}
      <div className={`absolute inset-0 -z-10 ${
        currentTheme === "dark"
          ? "bg-[#1E1E1E]/80"
          : "bg-[#F9FAFC]/80"
      } backdrop-blur-md`}></div>

      {/* 切换折叠按钮 - 仅在展开状态显示 */}
      {!collapsed && (
        <button
          className={`absolute right-2 top-2 p-1.5 rounded-full z-50 transition-all duration-200 ${
            currentTheme === "dark"
              ? "bg-[#2c2c2e]/80 text-gray-400 hover:text-white hover:bg-[#3c3c3e]"
              : "bg-white/80 text-gray-600 hover:text-black hover:bg-white shadow-sm"
          }`}
          onClick={() => setCollapsed(true)}
          title="折叠菜单"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m15 18-6-6 6-6" />
          </svg>
        </button>
      )}

      {/* Logo 和公司名称 */}
      <div className={`flex items-center ${collapsed ? 'justify-center' : 'px-4'} h-16 border-b ${
        currentTheme === "dark" ? "border-[#2c2c2e]" : "border-gray-200"
      } relative overflow-hidden`}>
        {/* 顶部背景图片和渐变遮罩 */}
        <div 
          className="absolute inset-0 transition-all duration-500"
          style={{
            backgroundImage: `url(/menu-bg/dingbu.png)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: '0.8'
          }}
        />
        <div 
          className="absolute inset-0 transition-all duration-500"
          style={{
            background: currentTheme === "dark"
              ? 'linear-gradient(to right, #1E1E1E 30%, transparent 100%)'
              : 'linear-gradient(to right, #F9FAFC 30%, transparent 100%)',
            opacity: '0.95'
          }}
        />
        <div className={`flex items-center ${collapsed ? 'justify-center' : ''} relative z-10`}>
          {/* 矿石粒子动画 */}
          <div className="absolute -left-1 -top-1 w-full h-full">
            <div className="particle-1 absolute w-1.5 h-1.5 rounded-full bg-blue-400/50"></div>
            <div className="particle-2 absolute w-1 h-1 rounded-full bg-green-400/50"></div>
            <div className="particle-3 absolute w-1.5 h-1.5 rounded-full bg-blue-500/50"></div>
            <div className="particle-4 absolute w-1 h-1 rounded-full bg-green-500/50"></div>
            <div className="particle-5 absolute w-1.5 h-1.5 rounded-full bg-blue-300/50"></div>
            <style jsx>{`
              @keyframes float {
                0%, 100% { transform: translateY(0); opacity: 0; }
                50% { transform: translateY(-10px); opacity: 1; }
              }
              .particle-1 {
                left: 10%;
                animation: float 3s ease-in-out infinite;
              }
              .particle-2 {
                left: 30%;
                animation: float 2.5s ease-in-out infinite 0.3s;
              }
              .particle-3 {
                left: 50%;
                animation: float 3.2s ease-in-out infinite 0.5s;
              }
              .particle-4 {
                left: 70%;
                animation: float 2.8s ease-in-out infinite 0.2s;
              }
              .particle-5 {
                left: 90%;
                animation: float 3s ease-in-out infinite 0.4s;
              }
            `}</style>
          </div>
          <div className={`transition-all duration-300 ${
            currentTheme === "dark" ? "text-white" : "text-[#1d1d1f]"
          } ${collapsed ? "scale-90" : "scale-100"}`}>
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={collapsed ? "32" : "40"} height={collapsed ? "32" : "40"} className={`mr-2 transition-all duration-300 filter ${
              currentTheme === "dark" ? "drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))" : ""
            }`}>
              <path d="M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-112-119-220.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z" fill="currentColor" />
            </svg>
          </div>
          {!collapsed && (
            <div className={`ml-2 font-bold relative z-10`}>
              <div className="text-sm transition-all duration-300 bg-gradient-to-r from-[#2B6CB0] to-[#4299E1] bg-clip-text text-transparent font-bold">矿业公司</div>
              <div className={`text-xs opacity-70 transition-all duration-300 ${
                currentTheme === "dark" ? "text-white" : "text-[#1d1d1f]"
              }`}>综合管理系统</div>
            </div>
          )}
        </div>
      </div>

      {/* 菜单内容 */}
      <div className="flex-1 h-0 overflow-y-auto scrollbar-thin">
        <div className={`p-2 space-y-1.5 ${
          collapsed ? "px-2" : ""
        }`}>
          {collapsed ? (
            // 收起状态下的菜单图标
            <div className="space-y-4 py-4">
              {menuItems.map((item) => (
                <div
                  key={item.id}
                  className={`flex justify-center cursor-pointer transition-all duration-200 p-2 rounded-md ${
                    pathname.includes(item.id)
                      ? currentTheme === "dark"
                        ? "bg-[#2c2c2e] text-white shadow-[0_0_10px_rgba(0,0,0,0.2)]"
                        : "bg-white text-black shadow-sm"
                      : currentTheme === "dark"
                        ? "text-gray-400 hover:text-white hover:bg-[#2c2c2e] hover:shadow-[0_0_8px_rgba(0,0,0,0.15)]"
                        : "text-gray-700 hover:text-black hover:bg-white/80 hover:shadow-sm"
                  } hover:scale-110`}
                  onClick={() => {
                    if (item.children && item.children.length > 0) {
                      setCollapsed(false); // 展开侧边栏
                      // 延迟后展开子菜单
                      setTimeout(() => {
                        toggleExpand(item.id);
                      }, 300);
                    } else {
                      // 导航到相应的页面
                      const path = `/${item.id}`;
                      router.push(path);
                    }
                  }}
                  title={item.label}
                >
                  <div className="flex flex-col items-center">
                    {pathname.includes(item.id) ? (
                      <div className={currentTheme === "dark" ? "text-blue-400" : "text-blue-600"}>
                        {item.icon || <div className="w-6 h-6" />}
                      </div>
                    ) : (
                      <div>{item.icon || <div className="w-6 h-6" />}</div>
                    )}
                    <span className="text-[10px] mt-1 truncate max-w-12">{item.label}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // 展开状态下的菜单
            menuItems.map(renderMenuItem)
          )}
        </div>
      </div>

      {/* 底部区域 - 主题切换和版权信息 */}
      <div className={`border-t ${currentTheme === 'dark' ? 'border-[#2c2c2e]' : 'border-gray-200'} mt-auto`}>
        {collapsed ? (
          // 折叠状态时的底部
          <div className="py-3 flex flex-col items-center space-y-3">
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-full transition-all duration-200 ${
                currentTheme === "dark"
                  ? "bg-[#2c2c2e]/80 text-yellow-400 hover:bg-[#3c3c3e]"
                  : "bg-white/80 text-gray-700 hover:bg-gray-100"
              } hover:scale-110`}
              title={currentTheme === "dark" ? "切换到亮色模式" : "切换到暗色模式"}
            >
              {currentTheme === "dark" ? (
                <Sun size={16} />
              ) : (
                <Moon size={16} />
              )}
            </button>
            <div className={`text-[10px] ${
              currentTheme === "dark" ? "text-gray-500" : "text-gray-400"
            }`}>
              © 2025
            </div>
          </div>
        ) : (
          // 展开状态时的底部
          <div className="p-3">
            <div className="flex items-center justify-between">
              <button
                onClick={toggleTheme}
                className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200 ${
                  currentTheme === "dark"
                    ? "bg-[#2c2c2e]/80 text-gray-300 hover:text-white hover:bg-[#3c3c3e]"
                    : "bg-white/80 text-gray-700 hover:text-black hover:bg-gray-100"
                }`}
                title={currentTheme === "dark" ? "切换到亮色模式" : "切换到暗色模式"}
              >
                {currentTheme === "dark" ? (
                  <Sun size={16} className="text-yellow-400" />
                ) : (
                  <Moon size={16} />
                )}
                <span className="text-sm">{currentTheme === "dark" ? "亮色模式" : "暗色模式"}</span>
              </button>

              <button
                onClick={() => setCollapsed(true)}
                className={`p-2 rounded-md transition-all duration-200 ${
                  currentTheme === "dark"
                    ? "text-gray-400 hover:text-white hover:bg-[#3c3c3e]"
                    : "text-gray-600 hover:text-black hover:bg-gray-100"
                }`}
                title="折叠菜单"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg>
              </button>
            </div>

            <div className={`mt-2 text-xs text-center ${
              currentTheme === "dark" ? "text-gray-500" : "text-gray-400"
            }`}>
              © 2025 矿业公司综合管理系统 <br />
              <span className="text-[10px]">版本 1.0.0</span>
            </div>
          </div>
        )}
      </div>
    </aside>
  )
}

