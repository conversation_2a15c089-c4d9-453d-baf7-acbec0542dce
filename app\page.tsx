"use client"

import { MainLayout } from "@/components/main-layout"
import { Dashboard } from "@/components/dashboard"
import { useAuth } from "@/contexts/auth-context"
import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function Home() {
  const { isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // 确保未登录用户被重定向到登录页面
    if (!isAuthenticated) {
      router.replace("/login")
    }
  }, [isAuthenticated, router])

  return (
    <MainLayout>
      <Dashboard />
    </MainLayout>
  )
}

