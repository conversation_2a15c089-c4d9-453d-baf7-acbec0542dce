"use client"

import { useState } from "react"
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  FileText, 
  <PERSON>, 
  Heart, 
  Users, 
  Calendar,
  RefreshCw,
  Download,
  AlertTriangle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

interface FamilyPlanningRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  childrenCount: number
  marriageStatus: string
  policyCompliance: string
  subsidyAmount: number
  lastUpdateDate: string
  contactPhone?: string
  familySize?: number
  spouseName?: string
  spouseWorkplace?: string
  remarks?: string
  createdAt: string
  updatedAt: string
}

interface PolicyRecord {
  id: string
  policyName: string
  policyType: string
  issueDate: string
  effectiveDate: string
  description: string
  status: string
  createdAt: string
  updatedAt: string
}

export function FamilyPlanning() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [marriageStatusFilter, setMarriageStatusFilter] = useState("all")
  const [policyComplianceFilter, setPolicyComplianceFilter] = useState("all")
  const [dateRangeFilter, setDateRangeFilter] = useState("all")
  const [subsidyRangeFilter, setSubsidyRangeFilter] = useState("all")
  const [isAddRecordOpen, setIsAddRecordOpen] = useState(false)
  const [isEditRecordOpen, setIsEditRecordOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<FamilyPlanningRecord | null>(null)
  const [loading, setLoading] = useState(false)
  const [familyRecords, setFamilyRecords] = useState<FamilyPlanningRecord[]>([
    {
      id: "FP001",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "采矿部",
      childrenCount: 1,
      marriageStatus: "已婚",
      policyCompliance: "符合",
      subsidyAmount: 3000,
      lastUpdateDate: "2023-05-15",
      createdAt: "2023-05-15T10:00:00",
      updatedAt: "2023-05-15T10:00:00",
    },
    {
      id: "FP002",
      employeeName: "李四",
      employeeId: "EMP045",
      department: "安全部",
      childrenCount: 2,
      marriageStatus: "已婚",
      policyCompliance: "符合",
      subsidyAmount: 5000,
      lastUpdateDate: "2023-06-20",
      createdAt: "2023-06-20T10:00:00",
      updatedAt: "2023-06-20T10:00:00",
    },
    {
      id: "FP003",
      employeeName: "王五",
      employeeId: "EMP078",
      department: "机电部",
      childrenCount: 0,
      marriageStatus: "未婚",
      policyCompliance: "符合",
      subsidyAmount: 0,
      lastUpdateDate: "2023-04-10",
      createdAt: "2023-04-10T10:00:00",
      updatedAt: "2023-04-10T10:00:00",
    },
    {
      id: "FP004",
      employeeName: "赵六",
      employeeId: "EMP102",
      department: "运输部",
      childrenCount: 3,
      marriageStatus: "已婚",
      policyCompliance: "特殊情况",
      subsidyAmount: 6000,
      lastUpdateDate: "2023-07-05",
      createdAt: "2023-07-05T10:00:00",
      updatedAt: "2023-07-05T10:00:00",
    },
    {
      id: "FP005",
      employeeName: "钱七",
      employeeId: "EMP156",
      department: "通风部",
      childrenCount: 1,
      marriageStatus: "已婚",
      policyCompliance: "符合",
      subsidyAmount: 3000,
      lastUpdateDate: "2023-08-12",
      createdAt: "2023-08-12T10:00:00",
      updatedAt: "2023-08-12T10:00:00",
    },
  ])

  const [policies, setPolicies] = useState<PolicyRecord[]>([
    {
      id: "POL001",
      policyName: "矿山企业职工生育补贴政策",
      policyType: "补贴政策",
      issueDate: "2022-01-15",
      effectiveDate: "2022-02-01",
      description: "为鼓励符合条件的职工生育，对符合计划生育政策的职工提供生育补贴",
      status: "有效",
      createdAt: "2022-01-15T10:00:00",
      updatedAt: "2022-01-15T10:00:00",
    },
    {
      id: "POL002",
      policyName: "特殊岗位职工计划生育政策",
      policyType: "管理政策",
      issueDate: "2022-03-10",
      effectiveDate: "2022-04-01",
      description: "针对特殊岗位职工的计划生育管理规定",
      status: "有效",
      createdAt: "2022-03-10T10:00:00",
      updatedAt: "2022-03-10T10:00:00",
    },
    {
      id: "POL003",
      policyName: "职工生育保险报销指南",
      policyType: "指导文件",
      issueDate: "2022-05-20",
      effectiveDate: "2022-06-01",
      description: "详细说明职工生育保险的报销流程和标准",
      status: "有效",
      createdAt: "2022-05-20T10:00:00",
      updatedAt: "2022-05-20T10:00:00",
    },
    {
      id: "POL004",
      policyName: "计划生育奖励办法",
      policyType: "奖励政策",
      issueDate: "2021-11-05",
      effectiveDate: "2022-01-01",
      description: "对遵守计划生育政策的职工给予奖励的具体办法",
      status: "有效",
      createdAt: "2021-11-05T10:00:00",
      updatedAt: "2021-11-05T10:00:00",
    },
  ])

  // 筛选记录
  const filteredFamilyRecords = familyRecords.filter(record => {
    const matchesSearch = !searchTerm || 
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.department.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment = departmentFilter === "all" || record.department === departmentFilter;
    const matchesMarriage = marriageStatusFilter === "all" || record.marriageStatus === marriageStatusFilter;
    const matchesPolicy = policyComplianceFilter === "all" || record.policyCompliance === policyComplianceFilter;
    
    // 日期范围筛选
    let matchesDateRange = true;
    const recordDate = new Date(record.lastUpdateDate);
    const today = new Date();
    if (dateRangeFilter === "today") {
      matchesDateRange = recordDate.toDateString() === today.toDateString();
    } else if (dateRangeFilter === "week") {
      const weekAgo = new Date(today.setDate(today.getDate() - 7));
      matchesDateRange = recordDate >= weekAgo;
    } else if (dateRangeFilter === "month") {
      const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
      matchesDateRange = recordDate >= monthAgo;
    }

    // 补贴金额范围筛选
    let matchesSubsidyRange = true;
    if (subsidyRangeFilter === "0-2000") {
      matchesSubsidyRange = record.subsidyAmount <= 2000;
    } else if (subsidyRangeFilter === "2000-5000") {
      matchesSubsidyRange = record.subsidyAmount > 2000 && record.subsidyAmount <= 5000;
    } else if (subsidyRangeFilter === "5000+") {
      matchesSubsidyRange = record.subsidyAmount > 5000;
    }

    return matchesSearch && matchesDepartment && matchesMarriage && matchesPolicy && matchesDateRange && matchesSubsidyRange;
  });

  // 处理查看详情
  const handleViewDetail = (record: FamilyPlanningRecord) => {
    setSelectedRecord(record);
    setIsViewDetailOpen(true);
  };

  // 处理编辑记录
  const handleEditRecord = (record: FamilyPlanningRecord) => {
    setSelectedRecord(record);
    setIsEditRecordOpen(true);
  };

  // 处理删除记录
  const handleDeleteRecord = (record: FamilyPlanningRecord) => {
    setSelectedRecord(record);
    setIsDeleteDialogOpen(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast({
        title: "刷新成功",
        description: "数据已更新",
      });
    }, 1000);
  };

  // 导出数据
  const handleExport = () => {
    try {
      const csvContent = "data:text/csv;charset=utf-8," + 
        "员工姓名,员工编号,部门,子女数量,婚姻状况,政策符合性,补贴金额,更新日期\n" +
        familyRecords.map(record => 
          `${record.employeeName},${record.employeeId},${record.department},${record.childrenCount},${record.marriageStatus},${record.policyCompliance},${record.subsidyAmount},${record.lastUpdateDate}`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `计划生育记录_${new Date().toLocaleDateString()}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    }
  };

  // 添加记录
  const handleAddRecord = (formData: Partial<FamilyPlanningRecord>) => {
    const newRecord: FamilyPlanningRecord = {
      id: `FP${familyRecords.length + 1}`,
      employeeName: formData.employeeName || "",
      employeeId: formData.employeeId || "",
      department: formData.department || "",
      childrenCount: formData.childrenCount || 0,
      marriageStatus: formData.marriageStatus || "未婚",
      policyCompliance: formData.policyCompliance || "符合",
      subsidyAmount: formData.subsidyAmount || 0,
      lastUpdateDate: new Date().toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...formData
    };

    setFamilyRecords(prev => [...prev, newRecord]);
    toast({
      title: "添加成功",
      description: "已成功添加计划生育记录",
    });
    setIsAddRecordOpen(false);
  };

  // 更新记录
  const handleUpdateRecord = (formData: Partial<FamilyPlanningRecord>) => {
    if (!selectedRecord) return;

    setFamilyRecords(prev => prev.map(record => 
      record.id === selectedRecord.id 
        ? { 
            ...record, 
            ...formData,
            updatedAt: new Date().toISOString()
          }
        : record
    ));

    toast({
      title: "更新成功",
      description: "已成功更新计划生育记录",
    });
    setIsEditRecordOpen(false);
    setSelectedRecord(null);
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (!selectedRecord) return;

    setFamilyRecords(prev => prev.filter(record => record.id !== selectedRecord.id));
    toast({
      title: "删除成功",
      description: "已成功删除计划生育记录",
    });
    setIsDeleteDialogOpen(false);
    setSelectedRecord(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">计划生育管理</h1>
        <div className="flex items-center space-x-4">
          <Button onClick={handleRefresh} variant="outline" size="icon" disabled={loading}>
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          </Button>
          <Button onClick={handleExport} variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-rose-100 p-3">
                  <Heart className="h-6 w-6 text-rose-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">已婚员工</p>
                  <div className="text-2xl font-bold">
                    {familyRecords.filter(r => r.marriageStatus === "已婚").length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    占比 {((familyRecords.filter(r => r.marriageStatus === "已婚").length / familyRecords.length) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <Baby className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">有子女员工</p>
                  <div className="text-2xl font-bold">
                    {familyRecords.filter(r => r.childrenCount > 0).length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    平均 {(familyRecords.reduce((acc, r) => acc + r.childrenCount, 0) / familyRecords.length).toFixed(1)} 个/人
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <AlertTriangle className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">补贴总额</p>
                  <div className="text-2xl font-bold">
                    {familyRecords.reduce((acc, r) => acc + r.subsidyAmount, 0).toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' })}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    人均 {(familyRecords.reduce((acc, r) => acc + r.subsidyAmount, 0) / familyRecords.length).toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' })}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-purple-100 p-3">
                  <Calendar className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">政策符合率</p>
                  <div className="text-2xl font-bold">
                    {((familyRecords.filter(r => r.policyCompliance === "符合").length / familyRecords.length) * 100).toFixed(1)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {familyRecords.filter(r => r.policyCompliance === "符合").length} 人符合政策
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col space-y-4">
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Search className="w-4 h-4 text-gray-500" />
            <Input
              placeholder="搜索员工姓名、编号..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>

          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择部门" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部部门</SelectItem>
              <SelectItem value="采矿部">采矿部</SelectItem>
              <SelectItem value="安全部">安全部</SelectItem>
              <SelectItem value="机电部">机电部</SelectItem>
              <SelectItem value="运输部">运输部</SelectItem>
              <SelectItem value="通风部">通风部</SelectItem>
            </SelectContent>
          </Select>

          <Select value={marriageStatusFilter} onValueChange={setMarriageStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="婚姻状况" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="已婚">已婚</SelectItem>
              <SelectItem value="未婚">未婚</SelectItem>
            </SelectContent>
          </Select>

          <Select value={policyComplianceFilter} onValueChange={setPolicyComplianceFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="政策符合性" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="符合">符合</SelectItem>
              <SelectItem value="不符合">不符合</SelectItem>
              <SelectItem value="特殊情况">特殊情况</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateRangeFilter} onValueChange={setDateRangeFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="更新时间" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部时间</SelectItem>
              <SelectItem value="today">今天</SelectItem>
              <SelectItem value="week">最近一周</SelectItem>
              <SelectItem value="month">最近一月</SelectItem>
            </SelectContent>
          </Select>

          <Select value={subsidyRangeFilter} onValueChange={setSubsidyRangeFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="补贴金额" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部金额</SelectItem>
              <SelectItem value="0-2000">0-2000元</SelectItem>
              <SelectItem value="2000-5000">2000-5000元</SelectItem>
              <SelectItem value="5000+">5000元以上</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs defaultValue="records" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="records">
              <Users className="w-4 h-4 mr-2" />
              计划生育记录
            </TabsTrigger>
            <TabsTrigger value="policies">
              <FileText className="w-4 h-4 mr-2" />
              政策管理
            </TabsTrigger>
          </TabsList>

          <TabsContent value="records" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>计划生育记录</CardTitle>
                    <CardDescription>管理员工计划生育记录，包括婚姻状况、子女情况、政策符合性等</CardDescription>
                  </div>
                  <Button onClick={() => setIsAddRecordOpen(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    新增记录
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>记录编号</TableHead>
                        <TableHead>员工姓名</TableHead>
                        <TableHead>员工编号</TableHead>
                        <TableHead>部门</TableHead>
                        <TableHead>子女数量</TableHead>
                        <TableHead>婚姻状况</TableHead>
                        <TableHead>政策符合性</TableHead>
                        <TableHead>补贴金额</TableHead>
                        <TableHead>更新日期</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFamilyRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.id}</TableCell>
                          <TableCell>{record.employeeName}</TableCell>
                          <TableCell>{record.employeeId}</TableCell>
                          <TableCell>{record.department}</TableCell>
                          <TableCell>{record.childrenCount}</TableCell>
                          <TableCell>
                            <Badge variant={record.marriageStatus === "已婚" ? "default" : "secondary"}>
                              {record.marriageStatus}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={
                                record.policyCompliance === "符合" 
                                  ? "default"
                                  : record.policyCompliance === "特殊情况"
                                  ? "outline"
                                  : "destructive"
                              }
                            >
                              {record.policyCompliance}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {record.subsidyAmount.toLocaleString("zh-CN", { style: "currency", currency: "CNY" })}
                          </TableCell>
                          <TableCell>{record.lastUpdateDate}</TableCell>
                          <TableCell>
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleViewDetail(record)}
                              >
                                <FileText className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleEditRecord(record)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleDeleteRecord(record)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="policies">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>计划生育政策</CardTitle>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>新增政策</Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>新增计划生育政策</DialogTitle>
                        <DialogDescription>请填写计划生育政策信息，所有字段均为必填</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="policyName">政策名称</Label>
                          <Input id="policyName" />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="policyType">政策类型</Label>
                            <Input id="policyType" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="status">状态</Label>
                            <Input id="status" />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="issueDate">发布日期</Label>
                            <Input id="issueDate" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="effectiveDate">生效日期</Label>
                            <Input id="effectiveDate" type="date" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description">政策描述</Label>
                          <Input id="description" />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit">提交</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
                <CardDescription>管理计划生育相关政策，包括补贴政策、管理政策等</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>政策编号</TableHead>
                      <TableHead>政策名称</TableHead>
                      <TableHead>政策类型</TableHead>
                      <TableHead>发布日期</TableHead>
                      <TableHead>生效日期</TableHead>
                      <TableHead>政策描述</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {policies.map((policy) => (
                      <TableRow key={policy.id}>
                        <TableCell>{policy.id}</TableCell>
                        <TableCell>{policy.policyName}</TableCell>
                        <TableCell>{policy.policyType}</TableCell>
                        <TableCell>{policy.issueDate}</TableCell>
                        <TableCell>{policy.effectiveDate}</TableCell>
                        <TableCell className="max-w-xs truncate">{policy.description}</TableCell>
                        <TableCell>
                          <Badge variant={policy.status === "有效" ? "default" : "destructive"}>{policy.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              查看
                            </Button>
                            <Button variant="outline" size="sm">
                              编辑
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Dialog open={isAddRecordOpen} onOpenChange={setIsAddRecordOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>新增计划生育记录</DialogTitle>
            <DialogDescription>请填写员工计划生育信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-employeeName">员工姓名</Label>
                <Input 
                  id="add-employeeName" 
                  placeholder="请输入员工姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-employeeId">员工编号</Label>
                <Input 
                  id="add-employeeId" 
                  placeholder="请输入员工编号"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-department">所属部门</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿部">采矿部</SelectItem>
                    <SelectItem value="安全部">安全部</SelectItem>
                    <SelectItem value="机电部">机电部</SelectItem>
                    <SelectItem value="运输部">运输部</SelectItem>
                    <SelectItem value="通风部">通风部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-marriageStatus">婚姻状况</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择婚姻状况" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已婚">已婚</SelectItem>
                    <SelectItem value="未婚">未婚</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-childrenCount">子女数量</Label>
                <Input 
                  id="add-childrenCount" 
                  type="number"
                  min="0"
                  placeholder="请输入子女数量"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-policyCompliance">政策符合性</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择政策符合性" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="符合">符合</SelectItem>
                    <SelectItem value="不符合">不符合</SelectItem>
                    <SelectItem value="特殊情况">特殊情况</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-subsidyAmount">补贴金额</Label>
                <Input 
                  id="add-subsidyAmount" 
                  type="number"
                  min="0"
                  step="100"
                  placeholder="请输入补贴金额"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-contactPhone">联系电话</Label>
                <Input 
                  id="add-contactPhone" 
                  placeholder="请输入联系电话"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-spouseName">配偶姓名</Label>
                <Input 
                  id="add-spouseName" 
                  placeholder="请输入配偶姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-spouseWorkplace">配偶工作单位</Label>
                <Input 
                  id="add-spouseWorkplace" 
                  placeholder="请输入配偶工作单位"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-remarks">备注</Label>
              <Input 
                id="add-remarks" 
                placeholder="请输入备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddRecordOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              const formData: Partial<FamilyPlanningRecord> = {
                employeeName: (document.getElementById("add-employeeName") as HTMLInputElement).value,
                employeeId: (document.getElementById("add-employeeId") as HTMLInputElement).value,
                department: (document.querySelector("[data-value]") as HTMLElement)?.getAttribute("data-value") || "",
                marriageStatus: (document.querySelectorAll("[data-value]")[1] as HTMLElement)?.getAttribute("data-value") || "未婚",
                childrenCount: parseInt((document.getElementById("add-childrenCount") as HTMLInputElement).value) || 0,
                policyCompliance: (document.querySelectorAll("[data-value]")[2] as HTMLElement)?.getAttribute("data-value") || "符合",
                subsidyAmount: parseInt((document.getElementById("add-subsidyAmount") as HTMLInputElement).value) || 0,
                contactPhone: (document.getElementById("add-contactPhone") as HTMLInputElement).value,
                spouseName: (document.getElementById("add-spouseName") as HTMLInputElement).value,
                spouseWorkplace: (document.getElementById("add-spouseWorkplace") as HTMLInputElement).value,
                remarks: (document.getElementById("add-remarks") as HTMLInputElement).value,
              };
              handleAddRecord(formData);
            }}>
              添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditRecordOpen} onOpenChange={setIsEditRecordOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑计划生育记录</DialogTitle>
            <DialogDescription>请修改员工计划生育信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-employeeName">员工姓名</Label>
                <Input 
                  id="edit-employeeName" 
                  defaultValue={selectedRecord?.employeeName}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        employeeName: e.target.value
                      })
                    }
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-employeeId">员工编号</Label>
                <Input 
                  id="edit-employeeId" 
                  defaultValue={selectedRecord?.employeeId}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        employeeId: e.target.value
                      })
                    }
                  }}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-department">所属部门</Label>
                <Select 
                  defaultValue={selectedRecord?.department}
                  onValueChange={(value) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        department: value
                      })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿部">采矿部</SelectItem>
                    <SelectItem value="安全部">安全部</SelectItem>
                    <SelectItem value="机电部">机电部</SelectItem>
                    <SelectItem value="运输部">运输部</SelectItem>
                    <SelectItem value="通风部">通风部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-marriageStatus">婚姻状况</Label>
                <Select 
                  defaultValue={selectedRecord?.marriageStatus}
                  onValueChange={(value) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        marriageStatus: value
                      })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择婚姻状况" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已婚">已婚</SelectItem>
                    <SelectItem value="未婚">未婚</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-childrenCount">子女数量</Label>
                <Input 
                  id="edit-childrenCount" 
                  type="number"
                  min="0"
                  defaultValue={selectedRecord?.childrenCount}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        childrenCount: parseInt(e.target.value)
                      })
                    }
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-policyCompliance">政策符合性</Label>
                <Select 
                  defaultValue={selectedRecord?.policyCompliance}
                  onValueChange={(value) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        policyCompliance: value
                      })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择政策符合性" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="符合">符合</SelectItem>
                    <SelectItem value="不符合">不符合</SelectItem>
                    <SelectItem value="特殊情况">特殊情况</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-subsidyAmount">补贴金额</Label>
                <Input 
                  id="edit-subsidyAmount" 
                  type="number"
                  min="0"
                  step="100"
                  defaultValue={selectedRecord?.subsidyAmount}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        subsidyAmount: parseInt(e.target.value)
                      })
                    }
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-contactPhone">联系电话</Label>
                <Input 
                  id="edit-contactPhone" 
                  defaultValue={selectedRecord?.contactPhone}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        contactPhone: e.target.value
                      })
                    }
                  }}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-spouseName">配偶姓名</Label>
                <Input 
                  id="edit-spouseName" 
                  defaultValue={selectedRecord?.spouseName}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        spouseName: e.target.value
                      })
                    }
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-spouseWorkplace">配偶工作单位</Label>
                <Input 
                  id="edit-spouseWorkplace" 
                  defaultValue={selectedRecord?.spouseWorkplace}
                  onChange={(e) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        spouseWorkplace: e.target.value
                      })
                    }
                  }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-remarks">备注</Label>
              <Input 
                id="edit-remarks" 
                defaultValue={selectedRecord?.remarks}
                onChange={(e) => {
                  if (selectedRecord) {
                    setSelectedRecord({
                      ...selectedRecord,
                      remarks: e.target.value
                    })
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditRecordOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (selectedRecord) {
                handleUpdateRecord(selectedRecord)
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除该计划生育记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>计划生育记录详情</DialogTitle>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-6">
              <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                <div className="rounded-full bg-blue-100 p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedRecord.employeeName}</h3>
                  <p className="text-sm text-muted-foreground">员工编号：{selectedRecord.employeeId}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>所属部门</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.department}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>婚姻状况</Label>
                  <div className="mt-1">
                    <Badge variant={selectedRecord.marriageStatus === "已婚" ? "default" : "secondary"}>
                      {selectedRecord.marriageStatus}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>子女数量</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Baby className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.childrenCount} 名</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>政策符合性</Label>
                  <div className="mt-1">
                    <Badge 
                      variant={
                        selectedRecord.policyCompliance === "符合" 
                          ? "default"
                          : selectedRecord.policyCompliance === "特殊情况"
                          ? "outline"
                          : "destructive"
                      }
                    >
                      {selectedRecord.policyCompliance}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>补贴金额</Label>
                  <div className="text-lg font-semibold text-green-600 mt-1">
                    {selectedRecord.subsidyAmount.toLocaleString("zh-CN", {
                      style: "currency",
                      currency: "CNY",
                    })}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>最后更新日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.lastUpdateDate}</span>
                  </div>
                </div>
              </div>

              {(selectedRecord.contactPhone || selectedRecord.spouseName || selectedRecord.spouseWorkplace) && (
                <div className="border-t pt-6">
                  <h4 className="text-sm font-medium mb-4">附加信息</h4>
                  <div className="grid grid-cols-2 gap-6">
                    {selectedRecord.contactPhone && (
                      <div className="space-y-2">
                        <Label>联系电话</Label>
                        <div>{selectedRecord.contactPhone}</div>
                      </div>
                    )}
                    {selectedRecord.spouseName && (
                      <div className="space-y-2">
                        <Label>配偶姓名</Label>
                        <div>{selectedRecord.spouseName}</div>
                      </div>
                    )}
                    {selectedRecord.spouseWorkplace && (
                      <div className="space-y-2">
                        <Label>配偶工作单位</Label>
                        <div>{selectedRecord.spouseWorkplace}</div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {selectedRecord.remarks && (
                <div className="border-t pt-6">
                  <h4 className="text-sm font-medium mb-4">备注</h4>
                  <div className="p-4 bg-slate-50 rounded-lg text-sm">
                    {selectedRecord.remarks}
                  </div>
                </div>
              )}
            </div>
          )}
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsViewDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewDetailOpen(false)
              if (selectedRecord) {
                handleEditRecord(selectedRecord)
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

