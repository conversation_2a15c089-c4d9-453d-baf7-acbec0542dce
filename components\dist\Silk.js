"use strict";
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
exports.__esModule = true;
/* eslint-disable react/no-unknown-property */
var fiber_1 = require("@react-three/fiber");
var react_1 = require("react");
var three_1 = require("three");
var hexToNormalizedRGB = function (hex) {
    hex = hex.replace("#", "");
    return [
        parseInt(hex.slice(0, 2), 16) / 255,
        parseInt(hex.slice(2, 4), 16) / 255,
        parseInt(hex.slice(4, 6), 16) / 255,
    ];
};
var vertexShader = "\nvarying vec2 vUv;\nvarying vec3 vPosition;\n\nvoid main() {\n  vPosition = position;\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n";
var fragmentShader = "\nvarying vec2 vUv;\nvarying vec3 vPosition;\n\nuniform float uTime;\nuniform vec3  uColor;\nuniform float uSpeed;\nuniform float uScale;\nuniform float uRotation;\nuniform float uNoiseIntensity;\n\nconst float e = 2.71828182845904523536;\n\nfloat noise(vec2 texCoord) {\n  float G = e;\n  vec2  r = (G * sin(G * texCoord));\n  return fract(r.x * r.y * (1.0 + texCoord.x));\n}\n\nvec2 rotateUvs(vec2 uv, float angle) {\n  float c = cos(angle);\n  float s = sin(angle);\n  mat2  rot = mat2(c, -s, s, c);\n  return rot * uv;\n}\n\nvoid main() {\n  float rnd        = noise(gl_FragCoord.xy);\n  vec2  uv         = rotateUvs(vUv * uScale, uRotation);\n  vec2  tex        = uv * uScale;\n  float tOffset    = uSpeed * uTime;\n\n  tex.y += 0.03 * sin(8.0 * tex.x - tOffset);\n\n  float pattern = 0.6 +\n                  0.4 * sin(5.0 * (tex.x + tex.y +\n                                   cos(3.0 * tex.x + 5.0 * tex.y) +\n                                   0.02 * tOffset) +\n                           sin(20.0 * (tex.x + tex.y - 0.1 * tOffset)));\n\n  vec4 col = vec4(uColor, 1.0) * vec4(pattern) - rnd / 15.0 * uNoiseIntensity;\n  col.a = 0.5; // \u8BBE\u7F6E\u534A\u900F\u660E\u6548\u679C\n  gl_FragColor = col;\n}\n";
var SilkPlane = react_1.forwardRef(function SilkPlane(_a, ref) {
    var uniforms = _a.uniforms;
    var viewport = fiber_1.useThree().viewport;
    react_1.useLayoutEffect(function () {
        if (ref && 'current' in ref && ref.current) {
            ref.current.scale.set(viewport.width, viewport.height, 1);
        }
    }, [ref, viewport]);
    fiber_1.useFrame(function (_, delta) {
        if (ref && 'current' in ref && ref.current) {
            ref.current.material.uniforms.uTime.value += 0.1 * delta;
        }
    });
    return (React.createElement("mesh", { ref: ref },
        React.createElement("planeGeometry", { args: [1, 1, 1, 1] }),
        React.createElement("shaderMaterial", { transparent: true, uniforms: uniforms, vertexShader: vertexShader, fragmentShader: fragmentShader })));
});
var Silk = function (_a) {
    var _b = _a.speed, speed = _b === void 0 ? 5 : _b, _c = _a.scale, scale = _c === void 0 ? 1 : _c, _d = _a.color, color = _d === void 0 ? "#7B7481" : _d, _e = _a.noiseIntensity, noiseIntensity = _e === void 0 ? 1.5 : _e, _f = _a.rotation, rotation = _f === void 0 ? 0 : _f, _g = _a.className, className = _g === void 0 ? "" : _g;
    var meshRef = react_1.useRef(null);
    var uniforms = react_1.useMemo(function () { return ({
        uSpeed: { value: speed },
        uScale: { value: scale },
        uNoiseIntensity: { value: noiseIntensity },
        uColor: { value: new (three_1.Color.bind.apply(three_1.Color, __spreadArrays([void 0], hexToNormalizedRGB(color))))() },
        uRotation: { value: rotation },
        uTime: { value: 0 }
    }); }, [speed, scale, noiseIntensity, color, rotation]);
    return (React.createElement("div", { className: "absolute inset-0 z-0 " + className },
        React.createElement(fiber_1.Canvas, { dpr: [1, 2], frameloop: "always" },
            React.createElement(SilkPlane, { ref: meshRef, uniforms: uniforms }))));
};
exports["default"] = Silk;
