"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  MoreHorizontal,
  Filter,
  FileText,
  Calendar,
  AlertTriangle,
  Shield,
  FileCheck,
  Users,
  BarChart2,
  RefreshCcw,
  Eye,
  Clock,
  EyeOff,
  CheckCircle2,
  AlertCircle,
  Loader2,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import dayjs from "dayjs"
import * as XLSX from 'xlsx'

interface SafetyRecord {
  id: string
  title: string
  type: string
  level: string
  area: string
  department: string
  inspector: string
  checkDate: string
  nextCheck: string
  status: string
  description: string
  measures: string[]
  attachments: string[]
  isDisabled: boolean
  createdAt: string
  updatedAt: string
}

export function SafetyManagement() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string[]>([])
  const [selectedLevel, setSelectedLevel] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<SafetyRecord | null>(null)
  const [selectedRecords, setSelectedRecords] = useState<string[]>([])

  // 示例数据
  const [safetyRecords, setSafetyRecords] = useState<SafetyRecord[]>([
    {
      id: "1",
      title: "设备安全检查",
      type: "常规检查",
      level: "高",
      area: "生产车间",
      department: "安全部",
      inspector: "张三",
      checkDate: "2025-03-15",
      nextCheck: "2025-03-22",
      status: "正常",
      description: "对生产设备进行安全检查",
      measures: ["检查设备运行状态", "检查安全防护装置"],
      attachments: ["check_report_001.pdf"],
      isDisabled: false,
      createdAt: "2025-03-15 09:00:00",
      updatedAt: "2025-03-15 09:00:00"
    }
  ])

  // 处理导出
  const handleExport = async () => {
    try {
      setIsLoading(true)
      const exportData = safetyRecords.map(record => ({
        '标题': record.title,
        '类型': record.type,
        '风险等级': record.level,
        '区域': record.area,
        '部门': record.department,
        '检查人': record.inspector,
        '检查日期': record.checkDate,
        '下次检查': record.nextCheck,
        '状态': record.status,
        '描述': record.description,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      ws['!cols'] = [
        { wch: 30 }, // 标题
        { wch: 15 }, // 类型
        { wch: 15 }, // 风险等级
        { wch: 20 }, // 区域
        { wch: 15 }, // 部门
        { wch: 10 }, // 检查人
        { wch: 15 }, // 检查日期
        { wch: 15 }, // 下次检查
        { wch: 10 }, // 状态
        { wch: 50 }, // 描述
      ]

      XLSX.utils.book_append_sheet(wb, ws, '安全检查记录')
      XLSX.writeFile(wb, `安全检查记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`)

      toast({
        title: "导出成功",
        description: "文件已成功导出",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      setIsLoading(true)
      const reader = new FileReader()
      reader.onload = async (e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 转换导入的数据为记录格式
        const importedRecords: SafetyRecord[] = jsonData.map((item: any) => ({
          id: Math.random().toString(36).substr(2, 9),
          title: item['标题'],
          type: item['类型'],
          level: item['风险等级'],
          area: item['区域'],
          department: item['部门'],
          inspector: item['检查人'],
          checkDate: item['检查日期'],
          nextCheck: item['下次检查'],
          status: item['状态'],
          description: item['描述'],
          measures: [],
          attachments: [],
          isDisabled: false,
          createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        }))

        setSafetyRecords(prev => [...prev, ...importedRecords])
        toast({
          title: "导入成功",
          description: `成功导入 ${importedRecords.length} 条记录`,
        })
      }
      reader.readAsArrayBuffer(file)
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      if (event.target) event.target.value = ''
    }
  }

  // 处理添加记录
  const handleAdd = () => {
    setIsAddDialogOpen(true)
  }

  // 处理查看详情
  const handleView = (record: SafetyRecord) => {
    setCurrentRecord(record)
    setIsViewDetailsOpen(true)
  }

  // 处理编辑
  const handleEdit = (record: SafetyRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 处理删除
  const handleDelete = (record: SafetyRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (!currentRecord) return
    setSafetyRecords(prev => prev.filter(record => record.id !== currentRecord.id))
    setIsDeleteDialogOpen(false)
    setCurrentRecord(null)
    toast({
      title: "删除成功",
      description: "记录已成功删除",
    })
  }

  // 处理禁用/启用
  const handleToggleStatus = (record: SafetyRecord) => {
    setSafetyRecords(prev => prev.map(item => {
      if (item.id === record.id) {
        const updatedRecord = { ...item, isDisabled: !item.isDisabled };
        toast({
          title: updatedRecord.isDisabled ? "记录已禁用" : "记录已启用",
          description: `${record.title} 已${updatedRecord.isDisabled ? '禁用' : '启用'}`,
        });
        return updatedRecord;
      }
      return item;
    }));
  }

  // 筛选记录
  const filteredRecords = safetyRecords.filter(record => {
    const matchesSearch =
      record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.inspector.toLowerCase().includes(searchTerm.toLowerCase());

    // 检查是否选中了"all"或是空数组（表示所有类型）
    const typeFilter = selectedType.length === 0 || selectedType.includes("all");
    const matchesType = typeFilter || selectedType.includes(record.type);

    // 检查是否选中了"all"或是空数组（表示所有级别）
    const levelFilter = selectedLevel.length === 0 || selectedLevel.includes("all");
    const matchesLevel = levelFilter || selectedLevel.includes(record.level);

    // 检查是否选中了"all"或是空数组（表示所有状态）
    const statusFilter = selectedStatus.length === 0 || selectedStatus.includes("all");
    const matchesStatus = statusFilter || selectedStatus.includes(record.status);

    return matchesSearch && matchesType && matchesLevel && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center py-2 mb-4">
        <div className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-semibold text-gray-900">安全管理</h1>
        </div>
      </div>

      {/* 顶部操作区 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gradient-to-r from-blue-50 via-white to-blue-50 rounded-xl shadow-sm p-8 border border-blue-100/50">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-800">安全检查记录</h2>
          <p className="text-muted-foreground text-sm">管理和跟踪安全检查记录</p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleExport} className="hover:bg-green-50 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleImport}
              className="hidden"
              id="import-file"
            />
            <Button variant="outline" size="sm" onClick={() => document.getElementById('import-file')?.click()} className="hover:bg-blue-50 transition-colors">
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </div>
          <Button size="sm" onClick={handleAdd} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all">
            <Plus className="h-4 w-4 mr-2" />
            添加记录
          </Button>
        </div>
      </div>

      {/* 搜索和筛选区 */}
      <Card className="shadow-sm">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索记录..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select
              value={selectedType.join(",")}
              onValueChange={(value) => setSelectedType(value ? value.split(",") : [])}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="检查类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="常规检查">常规检查</SelectItem>
                <SelectItem value="专项检查">专项检查</SelectItem>
                <SelectItem value="临时检查">临时检查</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={selectedLevel.join(",")}
              onValueChange={(value) => setSelectedLevel(value ? value.split(",") : [])}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="风险等级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部等级</SelectItem>
                <SelectItem value="高">高风险</SelectItem>
                <SelectItem value="中">中风险</SelectItem>
                <SelectItem value="低">低风险</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={selectedStatus.join(",")}
              onValueChange={(value) => setSelectedStatus(value ? value.split(",") : [])}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="记录状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="正常">正常</SelectItem>
                <SelectItem value="警告">警告</SelectItem>
                <SelectItem value="异常">异常</SelectItem>
                <SelectItem value="已禁用">已禁用</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setSelectedType([]);
                setSelectedLevel([]);
                setSelectedStatus([]);
              }}
              className="hover:bg-blue-50"
            >
              <RefreshCcw className="h-4 w-4 mr-2" />
              重置筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 记录列表 */}
      <Card className="shadow-lg border-0">
        <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
          <CardTitle>安全检查记录</CardTitle>
          <CardDescription>
            共 {filteredRecords.length} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50/50 hover:bg-gray-50">
                <TableHead className="w-[300px]">标题</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>风险等级</TableHead>
                <TableHead>检查人</TableHead>
                <TableHead>检查日期</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow
                  key={record.id}
                  className={`${record.isDisabled ? 'opacity-50' : ''} hover:bg-blue-50/30 transition-colors`}
                >
                  <TableCell className="font-medium">
                    <Button variant="link" onClick={() => handleView(record)} className="h-auto p-0 hover:text-blue-600">
                      {record.title}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-normal bg-blue-50">
                      {record.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      record.level === "高" ? "destructive" :
                      record.level === "中" ? "secondary" : "default"
                    }>
                      {record.level}
                    </Badge>
                  </TableCell>
                  <TableCell>{record.inspector}</TableCell>
                  <TableCell>{record.checkDate}</TableCell>
                  <TableCell>
                    <Badge variant={
                      record.status === "正常" ? "default" :
                      record.status === "警告" ? "secondary" : "destructive"
                    }>
                      {record.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleView(record)} className="hover:bg-blue-50">
                        <Eye className="h-4 w-4 text-blue-600" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(record)} className="hover:bg-yellow-50">
                        <Edit className="h-4 w-4 text-yellow-600" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(record)} className="hover:bg-red-50">
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="hover:bg-gray-50">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleToggleStatus(record)}
                            className={record.isDisabled ? "text-green-600 hover:text-green-700" : "text-red-600 hover:text-red-700"}
                          >
                            {record.isDisabled ? (
                              <>
                                <Eye className="h-4 w-4 mr-2" />
                                启用记录
                              </>
                            ) : (
                              <>
                                <EyeOff className="h-4 w-4 mr-2" />
                                禁用记录
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 加载状态 */}
      {isLoading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <p className="text-sm text-gray-600">处理中...</p>
          </div>
        </div>
      )}

      {/* 添加记录对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加安全检查记录</DialogTitle>
            <DialogDescription>
              请填写安全检查记录的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-title">标题 <span className="text-red-500">*</span></Label>
                <Input id="add-title" placeholder="输入记录标题" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-type">检查类型 <span className="text-red-500">*</span></Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="常规检查">常规检查</SelectItem>
                    <SelectItem value="专项检查">专项检查</SelectItem>
                    <SelectItem value="临时检查">临时检查</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-level">风险等级 <span className="text-red-500">*</span></Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高风险</SelectItem>
                    <SelectItem value="中">中风险</SelectItem>
                    <SelectItem value="低">低风险</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-area">检查区域</Label>
                <Input id="add-area" placeholder="输入检查区域" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-department">责任部门</Label>
                <Input id="add-department" placeholder="输入责任部门" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-inspector">检查人 <span className="text-red-500">*</span></Label>
                <Input id="add-inspector" placeholder="输入检查人姓名" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-check-date">检查日期 <span className="text-red-500">*</span></Label>
                <Input id="add-check-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-next-check">下次检查日期</Label>
                <Input id="add-next-check" type="date" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-description">检查描述</Label>
              <Textarea
                id="add-description"
                placeholder="请详细描述检查内容和发现的问题"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
            <Button type="submit">保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑记录对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑安全检查记录</DialogTitle>
            <DialogDescription>
              修改安全检查记录的信息
            </DialogDescription>
          </DialogHeader>
          {currentRecord && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-title">标题 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-title"
                    value={currentRecord.title}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-type">检查类型 <span className="text-red-500">*</span></Label>
                  <Select
                    value={currentRecord.type}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="常规检查">常规检查</SelectItem>
                      <SelectItem value="专项检查">专项检查</SelectItem>
                      <SelectItem value="临时检查">临时检查</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-level">风险等级 <span className="text-red-500">*</span></Label>
                  <Select
                    value={currentRecord.level}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, level: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择等级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="高">高风险</SelectItem>
                      <SelectItem value="中">中风险</SelectItem>
                      <SelectItem value="低">低风险</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-area">检查区域</Label>
                  <Input
                    id="edit-area"
                    value={currentRecord.area}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, area: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-department">责任部门</Label>
                  <Input
                    id="edit-department"
                    value={currentRecord.department}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-inspector">检查人 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-inspector"
                    value={currentRecord.inspector}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, inspector: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-check-date">检查日期 <span className="text-red-500">*</span></Label>
                  <Input
                    id="edit-check-date"
                    type="date"
                    value={currentRecord.checkDate}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, checkDate: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-next-check">下次检查日期</Label>
                  <Input
                    id="edit-next-check"
                    type="date"
                    value={currentRecord.nextCheck}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, nextCheck: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态 <span className="text-red-500">*</span></Label>
                <Select
                  value={currentRecord.status}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="正常">正常</SelectItem>
                    <SelectItem value="警告">警告</SelectItem>
                    <SelectItem value="异常">异常</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">检查描述</Label>
                <Textarea
                  id="edit-description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                  rows={4}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button type="submit">保存更改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>安全检查记录详情</DialogTitle>
            <DialogDescription>
              查看安全检查记录的详细信息
            </DialogDescription>
          </DialogHeader>
          {currentRecord && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>标题</Label>
                  <p className="mt-1">{currentRecord.title}</p>
                </div>
                <div>
                  <Label>检查类型</Label>
                  <p className="mt-1">
                    <Badge variant="outline" className="font-normal">
                      {currentRecord.type}
                    </Badge>
                  </p>
                </div>
                <div>
                  <Label>风险等级</Label>
                  <p className="mt-1">
                    <Badge
                      variant={
                        currentRecord.level === "高" ? "destructive" :
                        currentRecord.level === "中" ? "secondary" : "default"
                      }
                    >
                      {currentRecord.level}
                    </Badge>
                  </p>
                </div>
                <div>
                  <Label>检查区域</Label>
                  <p className="mt-1">{currentRecord.area}</p>
                </div>
                <div>
                  <Label>责任部门</Label>
                  <p className="mt-1">{currentRecord.department}</p>
                </div>
                <div>
                  <Label>检查人</Label>
                  <p className="mt-1">{currentRecord.inspector}</p>
                </div>
                <div>
                  <Label>检查日期</Label>
                  <p className="mt-1">{currentRecord.checkDate}</p>
                </div>
                <div>
                  <Label>下次检查</Label>
                  <p className="mt-1">{currentRecord.nextCheck}</p>
                </div>
              </div>

              <div>
                <Label>检查描述</Label>
                <p className="mt-1 text-sm text-muted-foreground whitespace-pre-wrap">
                  {currentRecord.description}
                </p>
              </div>

              {currentRecord.measures && currentRecord.measures.length > 0 && (
                <div>
                  <Label>整改措施</Label>
                  <ul className="mt-2 space-y-2">
                    {currentRecord.measures.map((measure, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-500">•</span>
                        <span>{measure}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {currentRecord.attachments && currentRecord.attachments.length > 0 && (
                <div>
                  <Label>附件</Label>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {currentRecord.attachments.map((file, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded">
                        <FileText className="h-4 w-4 text-blue-500" />
                        <span className="text-sm truncate">{file}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewDetailsOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条安全检查记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}