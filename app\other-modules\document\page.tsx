"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { MainLayout } from "@/components/main-layout"
import { FileText, Search, Plus, Folder, File, ChevronLeft, Calendar, User, Download, Eye, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

interface Document {
  id: string
  name: string
  type: string
  size: string
  category: string
  uploadDate: string
  uploader: string
}

export default function DocumentPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: "1",
      name: "安全生产管理制度.docx",
      type: "Word文档",
      size: "2.5MB",
      category: "规章制度",
      uploadDate: "2025-03-05",
      uploader: "管理员"
    },
    {
      id: "2",
      name: "2025年第一季度安全检查报告.pdf",
      type: "PDF文档",
      size: "4.8MB",
      category: "安全检查",
      uploadDate: "2025-03-10",
      uploader: "安全部门"
    },
    {
      id: "3",
      name: "员工培训计划.xlsx",
      type: "Excel表格",
      size: "1.2MB",
      category: "培训资料",
      uploadDate: "2025-03-20",
      uploader: "人事部门"
    }
  ])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [activeTab, setActiveTab] = useState("all")

  const viewDocument = (document: Document) => {
    setSelectedDocument(document)
    setIsViewDialogOpen(true)
  }

  const filteredDocuments = documents.filter(document => {
    if (activeTab === "all") return true
    return document.category.toLowerCase() === activeTab
  })

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => router.push("/")}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h2 className="text-2xl font-bold">文档管理</h2>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            上传文档
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>文档列表</CardTitle>
                <CardDescription>管理系统文档资料</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="搜索文档..." className="pl-8 w-[250px]" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-4">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="规章制度">规章制度</TabsTrigger>
                <TabsTrigger value="安全检查">安全检查</TabsTrigger>
                <TabsTrigger value="培训资料">培训资料</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="space-y-4">
              {filteredDocuments.length > 0 ? (
                <div className="border rounded-md">
                  <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 font-medium text-sm">
                    <div className="col-span-5">文件名</div>
                    <div className="col-span-2">类型</div>
                    <div className="col-span-2">大小</div>
                    <div className="col-span-2">上传日期</div>
                    <div className="col-span-1">操作</div>
                  </div>
                  {filteredDocuments.map(doc => (
                    <div key={doc.id} className="grid grid-cols-12 gap-4 p-4 border-b last:border-0 items-center text-sm hover:bg-muted/30">
                      <div className="col-span-5 flex items-center gap-2">
                        <FileText className="h-4 w-4 text-blue-500" />
                        <span className="truncate">{doc.name}</span>
                      </div>
                      <div className="col-span-2">{doc.type}</div>
                      <div className="col-span-2">{doc.size}</div>
                      <div className="col-span-2">{doc.uploadDate}</div>
                      <div className="col-span-1 flex items-center gap-1">
                        <Button variant="ghost" size="icon" onClick={() => viewDocument(doc)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-medium">暂无文档</h3>
                  <p className="mt-2 text-sm text-muted-foreground">当前没有符合条件的文档</p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">共 {filteredDocuments.length} 个文档</div>
          </CardFooter>
        </Card>

        {/* 查看文档对话框 */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>文档详情</DialogTitle>
              <DialogDescription>查看文档信息</DialogDescription>
            </DialogHeader>
            {selectedDocument && (
              <div className="space-y-4 py-4">
                <div className="flex items-center justify-center p-6 bg-muted rounded-md">
                  <FileText className="h-16 w-16 text-blue-500" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">文件名</Label>
                    <p className="font-medium mt-1">{selectedDocument.name}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">类型</Label>
                    <p className="font-medium mt-1">{selectedDocument.type}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">大小</Label>
                    <p className="font-medium mt-1">{selectedDocument.size}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">分类</Label>
                    <p className="font-medium mt-1">{selectedDocument.category}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">上传日期</Label>
                    <p className="font-medium mt-1">{selectedDocument.uploadDate}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">上传者</Label>
                    <p className="font-medium mt-1">{selectedDocument.uploader}</p>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>关闭</Button>
              <Button>
                <Download className="h-4 w-4 mr-2" />
                下载文档
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 添加文档对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>上传文档</DialogTitle>
              <DialogDescription>添加新的文档到系统</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="file">选择文件</Label>
                <Input id="file" type="file" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">文档分类</Label>
                <Select defaultValue="规章制度">
                  <SelectTrigger id="category">
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="规章制度">规章制度</SelectItem>
                    <SelectItem value="安全检查">安全检查</SelectItem>
                    <SelectItem value="培训资料">培训资料</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">文档描述</Label>
                <Input id="description" placeholder="请输入文档描述（可选）" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={() => {
                setIsAddDialogOpen(false)
                toast({
                  title: "上传成功",
                  description: "文档已成功上传到系统"
                })
              }}>上传</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}