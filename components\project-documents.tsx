"use client"

import { useState } from "react"
import {
  Bar<PERSON>hart2,
  Download,
  Filter,
  Search,
  Settings,
  Upload,
  FileText,
  File,
  FileIcon as FilePdf,
  FileImage,
  FileSpreadsheet,
  Eye,
  Edit,
  Trash2,
  Share2,
  CheckCircle2,
  Clock,
  AlertCircle,
  Plus,
  <PERSON><PERSON><PERSON>riangle,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

// 添加类型定义
interface Document {
  id: string
  name: string
  project: string
  category: string
  type: string
  size: string
  uploadedBy: string
  uploadDate: string
  version: string
  status: string
  description?: string
  tags?: string[]
  reviewedBy?: string
  reviewDate?: string
}

interface FormData {
  name: string
  project: string
  category: string
  type: string
  description: string
  version: string
  status: string
  tags: string[]
}

interface ChartData {
  name: string
  value: number
}

export function ProjectDocuments() {
  // 状态管理
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedProject, setSelectedProject] = useState("all")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null)
  const { toast } = useToast()
  const [showAnalysis, setShowAnalysis] = useState(false)

  const [formData, setFormData] = useState<FormData>({
    name: "",
    project: "",
    category: "",
    type: "",
    description: "",
    version: "",
    status: "",
    tags: []
  })

  const [documents, setDocuments] = useState<Document[]>([
    {
      id: "1",
      name: "矿区A3开发项目计划书.pdf",
      project: "矿区A3开发项目",
      category: "计划文档",
      type: "PDF",
      size: "2.5 MB",
      uploadedBy: "张三",
      uploadDate: "2025-01-15",
      version: "1.2",
      status: "已审核",
    },
    {
      id: "2",
      name: "设备更新技术规范.docx",
      project: "设备更新计划",
      category: "技术文档",
      type: "Word",
      size: "1.8 MB",
      uploadedBy: "李四",
      uploadDate: "2025-02-01",
      version: "2.0",
      status: "已审核",
    },
    {
      id: "3",
      name: "安全系统升级测试报告.xlsx",
      project: "安全系统升级",
      category: "测试文档",
      type: "Excel",
      size: "3.2 MB",
      uploadedBy: "王五",
      uploadDate: "2025-02-20",
      version: "1.0",
      status: "待审核",
    },
    {
      id: "4",
      name: "新矿区勘探数据分析.pptx",
      project: "新矿区勘探",
      category: "分析报告",
      type: "PowerPoint",
      size: "5.7 MB",
      uploadedBy: "赵六",
      uploadDate: "2025-03-05",
      version: "1.1",
      status: "已审核",
    },
    {
      id: "5",
      name: "环保设施改造设计图.jpg",
      project: "环保设施改造",
      category: "设计文档",
      type: "图片",
      size: "4.3 MB",
      uploadedBy: "钱七",
      uploadDate: "2025-03-10",
      version: "2.1",
      status: "待审核",
    },
  ])

  // 获取文件类型对应的图标
  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case "PDF":
        return <FilePdf className="h-4 w-4 text-red-500" />
      case "Word":
        return <FileText className="h-4 w-4 text-blue-500" />
      case "Excel":
        return <FileSpreadsheet className="h-4 w-4 text-green-500" />
      case "PowerPoint":
        return <FileText className="h-4 w-4 text-orange-500" />
      case "图片":
        return <FileImage className="h-4 w-4 text-purple-500" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已审核":
        return <Badge className="bg-green-500">已审核</Badge>
      case "待审核":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            待审核
          </Badge>
        )
      case "已驳回":
        return <Badge variant="destructive">已驳回</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理查看详情
  const handleViewDetails = (doc: Document) => {
    setCurrentDocument(doc)
    setIsViewDetailsOpen(true)
  }

  // 处理编辑文档
  const handleEditDocument = (doc: Document) => {
    setFormData({
      name: doc.name,
      project: doc.project,
      category: doc.category,
      type: doc.type,
      description: doc.description || "",
      version: doc.version,
      status: doc.status,
      tags: doc.tags || []
    })
    setCurrentDocument(doc)
    setIsViewDetailsOpen(false)
    setIsEditDialogOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = async () => {
    setIsLoading(true)
    try {
      // 这里添加更新文档的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "更新成功",
        description: "文档信息已更新",
      })
      setIsEditDialogOpen(false)
    } catch (error) {
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理删除文档
  const handleDeleteDocument = (doc: Document) => {
    setDocumentToDelete(doc)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!documentToDelete) return

    setIsLoading(true)
    try {
      // 这里添加删除文档的API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新本地状态
      setDocuments(prevDocuments =>
        prevDocuments.filter(doc => doc.id !== documentToDelete.id)
      )

      toast({
        title: "删除成功",
        description: "文档已成功删除",
      })
      setIsDeleteDialogOpen(false)
      setDocumentToDelete(null)
    } catch (error) {
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsLoading(true)
    try {
      const text = await file.text()
      const rows = text.split('\n').map(row => row.split(','))
      const headers = rows[0]

      const importedDocuments = rows.slice(1).map(row => ({
        id: Math.random().toString(36).substr(2, 9),
        name: row[0],
        project: row[1],
        category: row[2],
        type: row[3],
        size: row[4],
        uploadedBy: row[5],
        uploadDate: row[6],
        version: row[7],
        status: row[8]
      }))

      setDocuments(prev => [...prev, ...importedDocuments])

      toast({
        title: "导入成功",
        description: `成功导入 ${importedDocuments.length} 个文档`,
      })

      // 重置文件输入
      event.target.value = ''
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导出
  const handleExport = async () => {
    setIsLoading(true)
    try {
      const headers = ['文档名称', '工程项目', '类别', '类型', '大小', '上传人', '上传日期', '版本', '状态']
      const csvData = documents.map(doc => [
        doc.name,
        doc.project,
        doc.category,
        doc.type,
        doc.size,
        doc.uploadedBy,
        doc.uploadDate,
        doc.version,
        doc.status
      ])

      const csvContent = [headers, ...csvData].map(row => row.join(',')).join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `工程文档管理_${new Date().toLocaleDateString()}.csv`
      link.click()

      toast({
        title: "导出成功",
        description: "数据已成功导出为CSV文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 筛选记录
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.uploadedBy.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesProject = selectedProject === "all" ? true : doc.project === selectedProject
    const matchesCategory = selectedCategory === "all" ? true : doc.category === selectedCategory

    return matchesSearch && matchesProject && matchesCategory
  })

  // 获取统计数据
  const getAnalysisData = () => {
    // 文档类型分布
    const typeDistribution = documents.reduce((acc: { [key: string]: number }, doc) => {
      acc[doc.category] = (acc[doc.category] || 0) + 1
      return acc
    }, {})

    const typeChartData = Object.entries(typeDistribution).map(([name, value]) => ({
      name,
      value
    }))

    // 项目文档数量
    const projectDistribution = documents.reduce((acc: { [key: string]: number }, doc) => {
      acc[doc.project] = (acc[doc.project] || 0) + 1
      return acc
    }, {})

    const projectChartData = Object.entries(projectDistribution).map(([name, value]) => ({
      name,
      value
    }))

    // 文档状态分布
    const statusDistribution = documents.reduce((acc: { [key: string]: number }, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1
      return acc
    }, {})

    const statusChartData = Object.entries(statusDistribution).map(([name, value]) => ({
      name,
      value
    }))

    // 按月份统计上传趋势
    const monthlyTrend = documents.reduce((acc: { [key: string]: number }, doc) => {
      const month = doc.uploadDate.substring(0, 7)
      acc[month] = (acc[month] || 0) + 1
      return acc
    }, {})

    const trendChartData = Object.entries(monthlyTrend)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([name, value]) => ({
        name,
        数量: value
      }))

    return {
      typeChartData,
      projectChartData,
      statusChartData,
      trendChartData
    }
  }

  // 渲染统计分析视图
  const AnalysisView = () => {
    const { typeChartData, projectChartData, statusChartData, trendChartData } = getAnalysisData()
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>文档类型分布</CardTitle>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={typeChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {typeChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>项目文档分布</CardTitle>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={projectChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>文档状态分布</CardTitle>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {statusChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>文档上传趋势</CardTitle>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={trendChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="数量" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">工程文档管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport} disabled={isLoading}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowAnalysis(!showAnalysis)} disabled={isLoading}>
            <BarChart2 className="h-4 w-4 mr-2" />
            {showAnalysis ? "返回列表" : "统计分析"}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" disabled={isLoading}>
                <Upload className="h-4 w-4 mr-2" />
                上传文档
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>上传工程文档</DialogTitle>
                <DialogDescription>上传并分类工程相关文档</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目</Label>
                    <Select>
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mining-a3">矿区A3开发项目</SelectItem>
                        <SelectItem value="equipment-update">设备更新计划</SelectItem>
                        <SelectItem value="safety-upgrade">安全系统升级</SelectItem>
                        <SelectItem value="exploration">新矿区勘探</SelectItem>
                        <SelectItem value="environmental">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">文档类别</Label>
                    <Select>
                      <SelectTrigger id="category">
                        <SelectValue placeholder="选择文档类别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="plan">计划文档</SelectItem>
                        <SelectItem value="technical">技术文档</SelectItem>
                        <SelectItem value="test">测试文档</SelectItem>
                        <SelectItem value="report">分析报告</SelectItem>
                        <SelectItem value="design">设计文档</SelectItem>
                        <SelectItem value="contract">合同文档</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="file">选择文件</Label>
                  <Input id="file" type="file" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="version">版本号</Label>
                    <Input id="version" placeholder="输入版本号，例如：1.0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="approved">已审核</SelectItem>
                        <SelectItem value="pending">待审核</SelectItem>
                        <SelectItem value="rejected">已驳回</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">文档描述</Label>
                  <Textarea id="description" placeholder="输入文档描述" rows={3} />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="notify" />
                  <Label htmlFor="notify">通知相关人员</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>上传</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {showAnalysis ? (
        <AnalysisView />
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
                <h3 className="text-xl font-bold">{documents.length}</h3>
            <p className="text-sm text-muted-foreground">总文档数</p>
          </CardContent>
        </Card>

            <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
                  <CheckCircle2 className="h-6 w-6 text-green-600" />
            </div>
                <h3 className="text-xl font-bold">{documents.filter(doc => doc.status === "已审核").length}</h3>
                <p className="text-sm text-muted-foreground">生效文档</p>
          </CardContent>
        </Card>

            <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
                  <Clock className="h-6 w-6 text-yellow-600" />
            </div>
                <h3 className="text-xl font-bold">{documents.filter(doc => doc.status === "待审核").length}</h3>
                <p className="text-sm text-muted-foreground">待审核</p>
          </CardContent>
        </Card>

            <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
                <div className="rounded-full bg-red-100 p-3 mb-4">
                  <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
                <h3 className="text-xl font-bold">{documents.filter(doc => doc.status === "已驳回").length}</h3>
                <p className="text-sm text-muted-foreground">已驳回</p>
          </CardContent>
        </Card>
      </div>

          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索文档..."
                  className="pl-8 w-[250px] bg-white hover:bg-slate-50 transition-colors"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[150px] bg-white hover:bg-slate-50 transition-colors">
                  <SelectValue placeholder="文档分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  <SelectItem value="规章制度">规章制度</SelectItem>
                  <SelectItem value="技术文档">技术文档</SelectItem>
                  <SelectItem value="操作手册">操作手册</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                onClick={handleExport}
              >
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              <div className="relative">
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleImport}
                  className="hidden"
                  id="import-file"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="hover:bg-green-50 hover:text-green-600 transition-colors"
                  onClick={() => document.getElementById('import-file')?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  导入
                </Button>
              </div>
              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white transition-colors"
                onClick={() => setIsAddDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                新建文档
              </Button>
            </div>
          </div>

          <Card className="shadow-sm">
            <CardHeader className="border-b bg-slate-50">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>文档列表</CardTitle>
                  <CardDescription>管理所有工程文档</CardDescription>
                </div>
                <Tabs defaultValue="all" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="all" className="hover:bg-blue-50">全部</TabsTrigger>
                    <TabsTrigger value="mine" className="hover:bg-blue-50">我的文档</TabsTrigger>
                    <TabsTrigger value="shared" className="hover:bg-blue-50">共享文档</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent className="p-0">
            <Table>
                <TableHeader className="bg-slate-50">
                <TableRow>
                  <TableHead>文档名称</TableHead>
                    <TableHead>所属项目</TableHead>
                    <TableHead>分类</TableHead>
                  <TableHead>大小</TableHead>
                    <TableHead>上传者</TableHead>
                    <TableHead>上传时间</TableHead>
                  <TableHead>版本</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {filteredDocuments.map((doc) => (
                    <TableRow key={doc.id} className="hover:bg-slate-50">
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {getFileTypeIcon(doc.type)}
                        <span>{doc.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{doc.project}</TableCell>
                    <TableCell>{doc.category}</TableCell>
                    <TableCell>{doc.size}</TableCell>
                    <TableCell>{doc.uploadedBy}</TableCell>
                    <TableCell>{doc.uploadDate}</TableCell>
                    <TableCell>{doc.version}</TableCell>
                    <TableCell>{getStatusBadge(doc.status)}</TableCell>
                    <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-blue-50 hover:text-blue-600"
                            onClick={() => handleViewDetails(doc)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            查看
                      </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-amber-50 hover:text-amber-600"
                            onClick={() => handleEditDocument(doc)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            编辑
                      </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-red-50 hover:text-red-600"
                            onClick={() => handleDeleteDocument(doc)}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            删除
                      </Button>
                        </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </CardContent>
            <CardFooter className="flex justify-between py-4 bg-slate-50">
              <div className="text-sm text-muted-foreground">
                共 {filteredDocuments.length} 条记录
          </div>
          <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled
                  className="hover:bg-slate-100"
                >
              上一页
            </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 hover:bg-blue-50 hover:text-blue-600"
                >
              1
            </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled
                  className="hover:bg-slate-100"
                >
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>
        </>
      )}

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              文档详情
            </DialogTitle>
            <DialogDescription>查看文档的详细信息</DialogDescription>
          </DialogHeader>

          {currentDocument && (
            <div className="space-y-6 py-4">
              <Card className="border-2 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="bg-slate-50/80">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-blue-500 rounded-full mr-3" />
                    基本信息
                  </CardTitle>
          </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">文档名称</Label>
                    <p className="text-sm font-medium">{currentDocument.name}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">工程项目</Label>
                    <p className="text-sm font-medium">{currentDocument.project}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">文档类别</Label>
                    <p className="text-sm font-medium">{currentDocument.category}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">文件类型</Label>
                    <p className="text-sm font-medium">{currentDocument.type}</p>
            </div>
          </CardContent>
        </Card>

              <Card className="border-2 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="bg-slate-50/80">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-green-500 rounded-full mr-3" />
                    文档信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">版本</Label>
                    <p className="text-sm font-medium">{currentDocument.version}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">文件大小</Label>
                    <p className="text-sm font-medium">{currentDocument.size}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">上传人</Label>
                    <p className="text-sm font-medium">{currentDocument.uploadedBy}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">上传日期</Label>
                    <p className="text-sm font-medium">{currentDocument.uploadDate}</p>
                  </div>
                  {currentDocument.description && (
                    <div className="col-span-2 space-y-1">
                      <Label className="text-sm font-medium text-muted-foreground">文档描述</Label>
                      <p className="text-sm font-medium">{currentDocument.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border-2 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="bg-slate-50/80">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-purple-500 rounded-full mr-3" />
                    审核信息
                  </CardTitle>
          </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-muted-foreground">状态</Label>
                    <div className="mt-1">{getStatusBadge(currentDocument.status)}</div>
                  </div>
                  {currentDocument.reviewedBy && (
                    <div className="space-y-1">
                      <Label className="text-sm font-medium text-muted-foreground">审核人</Label>
                      <p className="text-sm font-medium">{currentDocument.reviewedBy}</p>
                    </div>
                  )}
                  {currentDocument.reviewDate && (
                    <div className="space-y-1">
                      <Label className="text-sm font-medium text-muted-foreground">审核日期</Label>
                      <p className="text-sm font-medium">{currentDocument.reviewDate}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          <DialogFooter className="sticky bottom-0 bg-white z-10 pt-4 mt-6 border-t">
            <div className="flex items-center justify-end gap-4 w-full">
              <Button
                variant="outline"
                onClick={() => setIsViewDetailsOpen(false)}
                className="w-24 hover:bg-slate-100"
              >
                关闭
              </Button>
              <Button
                onClick={() => handleEditDocument(currentDocument!)}
                className="w-24 bg-blue-600 hover:bg-blue-700"
              >
                编辑
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <Edit className="h-5 w-5 text-blue-600" />
              编辑文档
            </DialogTitle>
            <DialogDescription>修改文档的详细信息</DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => { e.preventDefault(); handleSaveEdit(); }} className="py-4">
            <div className="space-y-6">
              <Card className="border-2 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="bg-slate-50/80">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-blue-500 rounded-full mr-3" />
                    基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      文档名称 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="border-slate-200 focus:border-blue-500 hover:bg-slate-50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="project" className="text-sm font-medium">
                      工程项目 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.project}
                      onValueChange={(value) => setFormData({ ...formData, project: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500 hover:bg-slate-50">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                        <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                        <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                        <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                        <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category" className="text-sm font-medium">
                      文档类别 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({ ...formData, category: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500 hover:bg-slate-50">
                        <SelectValue placeholder="选择文档类别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="计划文档">计划文档</SelectItem>
                        <SelectItem value="技术文档">技术文档</SelectItem>
                        <SelectItem value="测试文档">测试文档</SelectItem>
                        <SelectItem value="分析报告">分析报告</SelectItem>
                        <SelectItem value="设计文档">设计文档</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="version" className="text-sm font-medium">
                      版本 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="version"
                      value={formData.version}
                      onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                      className="border-slate-200 focus:border-blue-500 hover:bg-slate-50"
                      placeholder="例如：1.0.0"
                    />
                  </div>
                  <div className="col-span-2 space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">
                      文档描述
                    </Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="min-h-[100px] border-slate-200 focus:border-blue-500 hover:bg-slate-50"
                      placeholder="请输入文档的详细描述..."
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="bg-slate-50/80">
                  <CardTitle className="flex items-center text-base font-medium">
                    <div className="h-8 w-1 bg-purple-500 rounded-full mr-3" />
                    状态信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-6 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="status" className="text-sm font-medium">
                      状态 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => setFormData({ ...formData, status: value })}
                    >
                      <SelectTrigger className="border-slate-200 focus:border-blue-500 hover:bg-slate-50">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="已审核">已审核</SelectItem>
                        <SelectItem value="待审核">待审核</SelectItem>
                        <SelectItem value="已驳回">已驳回</SelectItem>
                      </SelectContent>
                    </Select>
            </div>
          </CardContent>
        </Card>
      </div>

            <DialogFooter className="sticky bottom-0 bg-white z-10 pt-4 mt-6 border-t">
              <div className="flex items-center justify-end gap-4 w-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="w-24 hover:bg-slate-100"
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-24 bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-1">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      <span>保存中</span>
                    </div>
                  ) : (
                    "保存"
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-[400px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              确认删除
            </DialogTitle>
            <DialogDescription className="text-base">
              您确定要删除文档 <span className="font-medium text-foreground">"{documentToDelete?.name}"</span> 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <div className="flex justify-end gap-4 w-full">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
                disabled={isLoading}
                className="hover:bg-slate-100"
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                disabled={isLoading}
                className="hover:bg-red-700"
              >
                {isLoading ? (
                  <div className="flex items-center gap-1">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    <span>删除中</span>
                  </div>
                ) : (
                  "删除"
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

