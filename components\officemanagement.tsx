"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  Building2,
  Briefcase,
  CheckCircle2,
  XCircle,
  AlertCircle
} from "lucide-react"

interface OfficeRecord {
  id: string
  title: string
  type: string
  requester: string
  department: string
  submitDate: string
  deadline: string
  priority: string
  status: string
  description: string
  attachments: string
}

export function OfficeManagement() {
  // 初始办公记录数据
  const initialRecords: OfficeRecord[] = [
    {
      id: "1",
      title: "办公设备采购申请",
      type: "设备申请",
      requester: "张三",
      department: "行政部",
      submitDate: "2025-03-15",
      deadline: "2025-03-20",
      priority: "高",
      status: "待审批",
      description: "申请采购10台办公电脑，用于新员工入职配置",
      attachments: "采购清单.xlsx"
    },
    {
      id: "2",
      title: "会议室预订",
      type: "场地预订",
      requester: "李四",
      department: "人力资源部",
      submitDate: "2025-03-16",
      deadline: "2025-03-16",
      priority: "中",
      status: "已完成",
      description: "预订大会议室，用于月度部门会议",
      attachments: "会议议程.docx"
    },
    {
      id: "3",
      title: "办公用品补充",
      type: "物资申请",
      requester: "王五",
      department: "财务部",
      submitDate: "2025-03-14",
      deadline: "2025-03-17",
      priority: "低",
      status: "处理中",
      description: "申请补充打印纸、文具等办公用品",
      attachments: "物品清单.xlsx"
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<OfficeRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<OfficeRecord>({
    id: "",
    title: "",
    type: "",
    requester: "",
    department: "",
    submitDate: "",
    deadline: "",
    priority: "",
    status: "",
    description: "",
    attachments: ""
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch =
        record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.requester.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.department.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "pending") return matchesSearch && record.status === "待审批";
      if (activeTab === "processing") return matchesSearch && record.status === "处理中";
      if (activeTab === "completed") return matchesSearch && record.status === "已完成";

      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      submitDate: new Date().toISOString().split('T')[0]
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      title: "",
      type: "",
      requester: "",
      department: "",
      submitDate: "",
      deadline: "",
      priority: "",
      status: "",
      description: "",
      attachments: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      title: "",
      type: "",
      requester: "",
      department: "",
      submitDate: "",
      deadline: "",
      priority: "",
      status: "",
      description: "",
      attachments: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      title: "",
      type: "",
      requester: "",
      department: "",
      submitDate: "",
      deadline: "",
      priority: "",
      status: "",
      description: "",
      attachments: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (record: OfficeRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: OfficeRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待审批":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">待审批</Badge>
      case "处理中":
        return <Badge variant="secondary">处理中</Badge>
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "已拒绝":
        return <Badge variant="destructive">已拒绝</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取优先级对应的样式
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "高":
        return <Badge variant="destructive">高</Badge>
      case "中":
        return <Badge variant="secondary">中</Badge>
      case "低":
        return <Badge variant="outline">低</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">办公管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新建申请
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>新建办公申请</DialogTitle>
              <DialogDescription>
                请填写申请的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">申请标题</Label>
                <Input
                  id="title"
                  value={currentRecord.title}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">申请类型</Label>
                  <Select
                    value={currentRecord.type}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择申请类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="设备申请">设备申请</SelectItem>
                      <SelectItem value="场地预订">场地预订</SelectItem>
                      <SelectItem value="物资申请">物资申请</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">优先级</Label>
                  <Select
                    value={currentRecord.priority}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, priority: value })}
                  >
                    <SelectTrigger id="priority">
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="中">中</SelectItem>
                      <SelectItem value="低">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="requester">申请人</Label>
                  <Input
                    id="requester"
                    value={currentRecord.requester}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, requester: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">所属部门</Label>
                  <Input
                    id="department"
                    value={currentRecord.department}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="deadline">截止日期</Label>
                  <Input
                    id="deadline"
                    type="date"
                    value={currentRecord.deadline}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, deadline: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={currentRecord.status}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, status: value })}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="待审批">待审批</SelectItem>
                      <SelectItem value="处理中">处理中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                      <SelectItem value="已拒绝">已拒绝</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">申请描述</Label>
                <Textarea
                  id="description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="attachments">附件</Label>
                <Input
                  id="attachments"
                  value={currentRecord.attachments}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, attachments: e.target.value })}
                  placeholder="附件名称，多个附件用逗号分隔"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认提交</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索申请记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="pending">待审批</TabsTrigger>
            <TabsTrigger value="processing">处理中</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <Building2 className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{record.title}</CardTitle>
              </div>
              {getStatusBadge(record.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                    {record.type}
                  </div>
                  {getPriorityBadge(record.priority)}
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  申请人: {record.requester} ({record.department})
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  截止日期: {record.deadline}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">申请描述:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.description}</div>
                </div>
                {record.attachments && (
                  <div className="flex items-center text-sm">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    附件: {record.attachments}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>申请记录列表</CardTitle>
          <CardDescription>管理所有办公申请记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>申请标题</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>申请人</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>提交日期</TableHead>
                <TableHead>截止日期</TableHead>
                <TableHead>优先级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.title}</TableCell>
                  <TableCell>{record.type}</TableCell>
                  <TableCell>{record.requester}</TableCell>
                  <TableCell>{record.department}</TableCell>
                  <TableCell>{record.submitDate}</TableCell>
                  <TableCell>{record.deadline}</TableCell>
                  <TableCell>{getPriorityBadge(record.priority)}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑申请记录</DialogTitle>
            <DialogDescription>
              修改申请记录的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">申请标题</Label>
              <Input
                id="edit-title"
                value={currentRecord.title}
                onChange={(e) => setCurrentRecord({ ...currentRecord, title: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">申请类型</Label>
                <Select
                  value={currentRecord.type}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择申请类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="设备申请">设备申请</SelectItem>
                    <SelectItem value="场地预订">场地预订</SelectItem>
                    <SelectItem value="物资申请">物资申请</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-priority">优先级</Label>
                <Select
                  value={currentRecord.priority}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, priority: value })}
                >
                  <SelectTrigger id="edit-priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-requester">申请人</Label>
                <Input
                  id="edit-requester"
                  value={currentRecord.requester}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, requester: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-department">所属部门</Label>
                <Input
                  id="edit-department"
                  value={currentRecord.department}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, department: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-deadline">截止日期</Label>
                <Input
                  id="edit-deadline"
                  type="date"
                  value={currentRecord.deadline}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, deadline: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select
                  value={currentRecord.status}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, status: value })}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="待审批">待审批</SelectItem>
                    <SelectItem value="处理中">处理中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="已拒绝">已拒绝</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">申请描述</Label>
              <Textarea
                id="edit-description"
                value={currentRecord.description}
                onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-attachments">附件</Label>
              <Input
                id="edit-attachments"
                value={currentRecord.attachments}
                onChange={(e) => setCurrentRecord({ ...currentRecord, attachments: e.target.value })}
                placeholder="附件名称，多个附件用逗号分隔"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.title}" 的申请记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
