import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import styles from "./visualDashboard.module.css";
import { BuildOutlined, SafetyOutlined, AndroidOutlined, StarOutlined } from "@ant-design/icons";

interface StatCardsProps {
  stats: {
    totalProjects: number;
    ongoingProjects: number;
    totalSafetyChecks: number;
    normal: number;
    total: number;
  };
}

export const StatCards = ({ stats }: StatCardsProps) => {
  const rootRef = useRef<HTMLDivElement>(null);
  const fadeRef = useRef<HTMLDivElement>(null);
  const setX = useRef<gsap.QuickSetter | null>(null);
  const setY = useRef<gsap.QuickSetter | null>(null);
  const pos = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const el = rootRef.current;
    if (!el) return;
    setX.current = gsap.quickSetter(el, "--x", "px");
    setY.current = gsap.quickSetter(el, "--y", "px");
    const { width, height } = el.getBoundingClientRect();
    pos.current = { x: width / 2, y: height / 2 };
    setX.current(pos.current.x);
    setY.current(pos.current.y);
  }, []);

  const moveTo = (x: number, y: number) => {
    gsap.to(pos.current, {
      x,
      y,
      duration: 0.45,
      ease: "power3.out",
      onUpdate: () => {
        setX.current?.(pos.current.x);
        setY.current?.(pos.current.y);
      },
      overwrite: true,
    });
  };

  const handleMove = (e: React.PointerEvent) => {
    const r = rootRef.current?.getBoundingClientRect();
    if (!r) return;
    moveTo(e.clientX - r.left, e.clientY - r.top);
    gsap.to(fadeRef.current, { opacity: 0, duration: 0.25, overwrite: true });
  };

  const handleLeave = () => {
    gsap.to(fadeRef.current, {
      opacity: 1,
      duration: 0.6,
      overwrite: true,
    });
  };

  const handleCardMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    card.style.setProperty("--mouse-x", `${x}px`);
    card.style.setProperty("--mouse-y", `${y}px`);
  };

  const items = [
    {
      title: "项目总数",
      value: stats.totalProjects,
      icon: <BuildOutlined />,
      borderColor: "#3B82F6",
      gradient: "linear-gradient(145deg, #3B82F6, #000)",
    },
    {
      title: "进行中项目",
      value: stats.ongoingProjects,
      icon: <StarOutlined />,
      borderColor: "#10B981",
      gradient: "linear-gradient(180deg, #10B981, #000)",
    },
    {
      title: "安全检查",
      value: stats.totalSafetyChecks,
      icon: <SafetyOutlined />,
      borderColor: "#F59E0B",
      gradient: "linear-gradient(165deg, #F59E0B, #000)",
    },
    {
      title: "物资总数",
      value: stats.total,
      icon: <AndroidOutlined />,
      borderColor: "#8B5CF6",
      gradient: "linear-gradient(225deg, #8B5CF6, #000)",
    },
  ];

  return (
    <div
      ref={rootRef}
      className={styles.chromaGrid}
      style={{
        "--r": "300px",
        "--cols": "4",
        "--rows": "1",
      } as React.CSSProperties}
      onPointerMove={handleMove}
      onPointerLeave={handleLeave}
    >
      {items.map((item, i) => (
        <article
          key={i}
          className={styles.chromaCard}
          onMouseMove={handleCardMove}
          style={
            {
              "--card-border": item.borderColor,
              "--card-gradient": item.gradient,
            } as React.CSSProperties
          }
        >
          <div className={styles.statIcon}>{item.icon}</div>
          <footer className={styles.chromaInfo}>
            <h3 className={styles.statValue}>{item.value}</h3>
            <p className={styles.statLabel}>{item.title}</p>
          </footer>
        </article>
      ))}
      <div className={styles.chromaOverlay} />
      <div ref={fadeRef} className={styles.chromaFade} />
    </div>
  );
};

export default StatCards; 