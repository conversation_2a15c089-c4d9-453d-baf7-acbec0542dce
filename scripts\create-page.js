const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);
const pagePath = args[0];
const componentName = args[1] || path.basename(pagePath);

// 验证参数
if (!pagePath) {
  console.error('请提供页面路径！');
  console.error('使用方法: node scripts/create-page.js <页面路径> [组件名]');
  console.error('示例: node scripts/create-page.js financial-management/new-page NewComponent');
  process.exit(1);
}

// 生成页面内容
const pageContent = `import { MainLayout } from "@/components/main-layout"
import { ${componentName} } from "@/components/${componentName.toLowerCase()}"

export default function ${componentName}Page() {
  return (
    <MainLayout>
      <${componentName} />
    </MainLayout>
  )
}
`;

// 确保目录存在
const fullPath = path.join('app', pagePath, 'page.tsx');
fs.mkdirSync(path.dirname(fullPath), { recursive: true });

// 写入文件
fs.writeFileSync(fullPath, pageContent);

console.log(`✅ 成功创建页面: ${fullPath}`);

// 创建组件文件（如果不存在）
const componentPath = path.join('components', `${componentName.toLowerCase()}.tsx`);
if (!fs.existsSync(componentPath)) {
  const componentContent = `"use client"

export function ${componentName}() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">${componentName}</h1>
      {/* 在这里添加组件内容 */}
    </div>
  )
}
`;
  fs.writeFileSync(componentPath, componentContent);
  console.log(`✅ 成功创建组件: ${componentPath}`);
} 