"use client"

import { useState, useEffect } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  FileText,
  Clock,
  DollarSign,
  FileCheck,
  Trash2,
  FileUp,
  Eye,
  Edit,
  RefreshCw,
  CheckCircle2,
  AlertCircle,
  CheckSquare,
  BanIcon,
  FileInput,
  XCircle,
  BarChart,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer"
import { Progress } from "@/components/ui/progress"
import { message } from "@/components/ui/use-message"
import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { format } from "date-fns"

// 定义合同接口
interface Contract {
  id: string
  contractNo: string
  project: string
  contractType: string
  party: string
  signDate: string
  startDate: string
  endDate: string
  amount: number
  status: "执行中" | "待执行" | "已完成" | "已终止"
  paymentStatus: "已支付" | "部分支付" | "未支付"
  manager: string
  description?: string
  contactPerson?: string
  contactPhone?: string
  attachments?: string[]
  remarks?: string
  createdAt?: string
  updatedAt?: string
  disabled?: boolean
}

// 定义导出设置接口
interface ExportSettings {
  format: "xlsx" | "csv" | "pdf"
  range: "all" | "selected" | "filtered"
  includeDetails: boolean
}

// 定义统计数据接口
interface Statistics {
  total: number
  executing: number
  pending: number
  completed: number
  terminated: number
  expiringContract: number
  totalAmount: number
  paidAmount: number
  typeDistribution: Record<string, number>
  statusDistribution: Record<string, number>
  paymentDistribution: Record<string, number>
  amountTrend: {
    month: string
    amount: number
  }[]
}

export function ProjectContractManagement() {
  // 状态管理
  const [contracts, setContracts] = useState<Contract[]>([])
  const [statistics, setStatistics] = useState<Statistics>({
    total: 0,
    executing: 0,
    pending: 0,
    completed: 0,
    terminated: 0,
    expiringContract: 0,
    totalAmount: 0,
    paidAmount: 0,
    typeDistribution: {},
    statusDistribution: {},
    paymentDistribution: {},
    amountTrend: []
  })
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isFilterDropdownOpen, setIsFilterDropdownOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [currentContract, setCurrentContract] = useState<Contract | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedContractType, setSelectedContractType] = useState("all")
  const [selectedContracts, setSelectedContracts] = useState<string[]>([])
  const [formData, setFormData] = useState<Partial<Contract>>({})
  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: "xlsx",
    range: "all",
    includeDetails: true
  })
  const [isLoading, setIsLoading] = useState(false)

  // 示例数据
  const contractsData: Contract[] = [
    {
      id: "1",
      contractNo: "HT-2023-001",
      project: "矿区A3开发项目",
      contractType: "工程承包合同",
      party: "山东矿业工程有限公司",
      signDate: "2025-01-15",
      startDate: "2025-02-01",
      endDate: "2025-04-30",
      amount: 5800000,
      status: "执行中",
      paymentStatus: "部分支付",
      manager: "张三",
      description: "矿区A3区域开发的总承包合同，包括基础设施建设和设备安装等工作。",
      contactPerson: "刘经理",
      contactPhone: "13800138000",
      attachments: ["合同正本.pdf", "技术附件.docx"],
      remarks: "重要战略合作项目",
      createdAt: "2025-01-15 14:30:25",
      updatedAt: "2025-02-01 09:15:43",
      disabled: false
    },
    {
      id: "2",
      contractNo: "HT-2023-002",
      project: "设备更新计划",
      contractType: "设备采购合同",
      party: "北京重型机械制造有限公司",
      signDate: "2025-01-05",
      startDate: "2025-01-15",
      endDate: "2025-03-15",
      amount: 2300000,
      status: "执行中",
      paymentStatus: "部分支付",
      manager: "李四",
      description: "更新采矿设备，包括5台挖掘机和3台运输车辆。",
      contactPerson: "王总",
      contactPhone: "13900139000",
      attachments: ["采购合同.pdf", "设备清单.xlsx"],
      remarks: "设备需按时到货，确保项目进度",
      createdAt: "2025-01-05 10:22:15",
      updatedAt: "2025-01-15 16:43:55",
      disabled: false
    },
    {
      id: "3",
      contractNo: "HT-2023-003",
      project: "安全系统升级",
      contractType: "技术服务合同",
      party: "上海安全科技有限公司",
      signDate: "2025-01-20",
      startDate: "2025-02-01",
      endDate: "2025-04-15",
      amount: 1200000,
      status: "执行中",
      paymentStatus: "未支付",
      manager: "王五",
      description: "升级矿区安全监控系统，包括瓦斯监测和人员定位功能。",
      contactPerson: "陈工",
      contactPhone: "13700137000",
      attachments: ["服务协议.pdf"],
      remarks: "安全重点项目",
      createdAt: "2025-01-20 09:35:12",
      updatedAt: "2025-02-01 14:20:38",
      disabled: false
    },
    {
      id: "4",
      contractNo: "HT-2023-004",
      project: "新矿区勘探",
      contractType: "勘探服务合同",
      party: "地质勘探研究院",
      signDate: "2025-02-01",
      startDate: "2025-02-15",
      endDate: "2025-04-15",
      amount: 1800000,
      status: "待执行",
      paymentStatus: "未支付",
      manager: "赵六",
      description: "对B区进行地质勘探，评估资源储量和开采可行性。",
      contactPerson: "郑教授",
      contactPhone: "13600136000",
      attachments: ["勘探方案.pdf"],
      remarks: "重要的前期勘探工作",
      createdAt: "2025-02-01 11:15:30",
      updatedAt: "2025-02-01 11:15:30",
      disabled: false
    },
    {
      id: "5",
      contractNo: "HT-2023-005",
      project: "环保设施改造",
      contractType: "工程承包合同",
      party: "绿色环保工程有限公司",
      signDate: "2025-02-10",
      startDate: "2025-03-05",
      endDate: "2025-04-10",
      amount: 3500000,
      status: "待执行",
      paymentStatus: "未支付",
      manager: "钱七",
      description: "对矿区排水系统和废气处理设施进行技术改造。",
      contactPerson: "林工",
      contactPhone: "13500135000",
      attachments: ["技术方案.pdf", "施工计划.xlsx"],
      remarks: "环保部门重点关注项目",
      createdAt: "2025-02-10 15:40:22",
      updatedAt: "2025-02-10 15:40:22",
      disabled: false
    },
    {
      id: "6",
      contractNo: "HT-2023-006",
      project: "矿山电力系统更新",
      contractType: "工程承包合同",
      party: "华东电力建设有限公司",
      signDate: "2025-01-12",
      startDate: "2025-01-20",
      endDate: "2025-03-20",
      amount: 4200000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "郑八",
      description: "更新矿区变电站和配电系统，提高供电可靠性和安全性。",
      contactPerson: "杨工",
      contactPhone: "13400134000",
      attachments: ["电力系统合同.pdf", "验收报告.pdf"],
      remarks: "按期完成，效果良好",
      createdAt: "2025-01-12 08:20:15",
      updatedAt: "2025-03-25 16:30:22",
      disabled: false
    },
    {
      id: "7",
      contractNo: "HT-2023-007",
      project: "矿区道路维修",
      contractType: "工程承包合同",
      party: "公路工程建设集团",
      signDate: "2025-01-05",
      startDate: "2025-01-15",
      endDate: "2025-03-30",
      amount: 2800000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "孙九",
      description: "对矿区主要运输道路进行修缮和加固，提高运输效率。",
      contactPerson: "赵总",
      contactPhone: "13300133000",
      attachments: ["道路工程合同.pdf"],
      remarks: "工期提前完成",
      createdAt: "2025-01-05 10:45:33",
      updatedAt: "2025-03-05 14:20:18",
      disabled: false
    },
    {
      id: "8",
      contractNo: "HT-2023-008",
      project: "矿区通风系统改造",
      contractType: "技术服务合同",
      party: "安全通风工程有限公司",
      signDate: "2025-01-18",
      startDate: "2025-02-01",
      endDate: "2025-04-10",
      amount: 3100000,
      status: "执行中",
      paymentStatus: "部分支付",
      manager: "周十",
      description: "改进矿井通风系统，增强瓦斯排放能力，提高安全生产水平。",
      contactPerson: "李总",
      contactPhone: "13200132000",
      attachments: ["通风系统设计.pdf", "技术协议.docx"],
      remarks: "关系矿工安全的重要项目",
      createdAt: "2025-01-18 13:25:45",
      updatedAt: "2025-02-05 11:30:40",
      disabled: false
    },
    {
      id: "9",
      contractNo: "HT-2023-009",
      project: "矿区排水系统升级",
      contractType: "设备采购合同",
      party: "水泵设备制造有限公司",
      signDate: "2025-01-22",
      startDate: "2025-02-10",
      endDate: "2025-04-15",
      amount: 1650000,
      status: "执行中",
      paymentStatus: "部分支付",
      manager: "吴一",
      description: "更换老旧排水设备，提高矿区排水能力和可靠性。",
      contactPerson: "张经理",
      contactPhone: "13100131000",
      attachments: ["排水设备合同.pdf"],
      remarks: "防汛期前必须完成",
      createdAt: "2025-01-22 09:10:25",
      updatedAt: "2025-02-15 14:35:50",
      disabled: false
    },
    {
      id: "10",
      contractNo: "HT-2023-010",
      project: "矿区办公楼翻新",
      contractType: "工程承包合同",
      party: "建筑装修工程有限公司",
      signDate: "2025-01-05",
      startDate: "2025-01-20",
      endDate: "2025-03-20",
      amount: 1850000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "郭二",
      description: "对矿区办公楼进行内外翻新，改善办公环境。",
      contactPerson: "王工",
      contactPhone: "13000130000",
      attachments: ["装修合同.pdf", "验收单.pdf"],
      remarks: "员工满意度高",
      createdAt: "2025-01-05 11:20:15",
      updatedAt: "2025-03-25 10:45:30",
      disabled: false
    },
    {
      id: "11",
      contractNo: "HT-2023-011",
      project: "矿区绿化工程",
      contractType: "工程承包合同",
      party: "园林绿化有限公司",
      signDate: "2025-01-20",
      startDate: "2025-02-05",
      endDate: "2025-03-30",
      amount: 980000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "陈三",
      description: "进行矿区环境绿化，改善生态环境，提升企业形象。",
      contactPerson: "林总",
      contactPhone: "13800138001",
      attachments: ["绿化方案.pdf"],
      remarks: "获得环保部门表彰",
      createdAt: "2025-01-20 09:30:20",
      updatedAt: "2025-03-05 15:20:10",
      disabled: false
    },
    {
      id: "12",
      contractNo: "HT-2023-012",
      project: "矿区信息系统升级",
      contractType: "技术服务合同",
      party: "智能信息技术有限公司",
      signDate: "2023-07-05",
      startDate: "2023-07-20",
      endDate: "2024-01-20",
      amount: 2750000,
      status: "执行中",
      paymentStatus: "部分支付",
      manager: "李四",
      description: "升级矿区信息管理系统，提高数据处理效率和决策支持能力。",
      contactPerson: "陈总",
      contactPhone: "13900139001",
      attachments: ["系统升级合同.pdf", "需求分析.docx"],
      remarks: "数字化转型关键项目",
      createdAt: "2023-07-05 14:25:30",
      updatedAt: "2023-08-10 11:15:45",
      disabled: false
    },
    {
      id: "13",
      contractNo: "HT-2023-013",
      project: "矿区安全培训",
      contractType: "服务合同",
      party: "安全培训咨询机构",
      signDate: "2023-11-12",
      startDate: "2023-12-01",
      endDate: "2024-03-31",
      amount: 560000,
      status: "执行中",
      paymentStatus: "未支付",
      manager: "赵六",
      description: "为矿区员工提供安全生产培训，提高安全意识和应急处理能力。",
      contactPerson: "黄老师",
      contactPhone: "13700137001",
      attachments: ["培训计划.pdf"],
      remarks: "季度培训项目",
      createdAt: "2023-11-12 10:30:25",
      updatedAt: "2023-12-05 16:40:30",
      disabled: false
    },
    {
      id: "14",
      contractNo: "HT-2023-014",
      project: "矿区食堂改造",
      contractType: "工程承包合同",
      party: "餐饮设施工程有限公司",
      signDate: "2025-02-18",
      startDate: "2025-03-01",
      endDate: "2025-04-10",
      amount: 1450000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "钱七",
      description: "对矿区职工食堂进行改造扩建，提升就餐环境和服务质量。",
      contactPerson: "周经理",
      contactPhone: "13600136001",
      attachments: ["食堂改造合同.pdf", "验收报告.pdf"],
      remarks: "员工满意度提升明显",
      createdAt: "2025-02-18 08:50:15",
      updatedAt: "2025-03-05 14:20:35",
      disabled: false
    },
    {
      id: "15",
      contractNo: "HT-2023-015",
      project: "矿区消防系统更新",
      contractType: "设备采购合同",
      party: "消防设备有限公司",
      signDate: "2025-01-25",
      startDate: "2025-02-10",
      endDate: "2025-04-15",
      amount: 950000,
      status: "已完成",
      paymentStatus: "已支付",
      manager: "孙九",
      description: "更新矿区消防设备和系统，提高火灾防控能力。",
      contactPerson: "刘经理",
      contactPhone: "13500135001",
      attachments: ["消防设备合同.pdf", "验收证明.pdf"],
      remarks: "通过消防部门验收",
      createdAt: "2025-01-25 13:40:20",
      updatedAt: "2025-02-20 09:30:15",
      disabled: false
    }
  ]

  // 初始化数据
  useEffect(() => {
    setContracts(contractsData)
    calculateStatistics(contractsData)
  }, [])

  // 计算统计数据
  const calculateStatistics = (contractsList: Contract[]) => {
    const activeContracts = contractsList.filter(c => !c.disabled)
    const today = new Date()
    const nextMonth = new Date()
    nextMonth.setMonth(today.getMonth() + 1)

    // 计算基础统计数据
    const stats: Statistics = {
      total: activeContracts.length,
      executing: activeContracts.filter(c => c.status === "执行中").length,
      pending: activeContracts.filter(c => c.status === "待执行").length,
      completed: activeContracts.filter(c => c.status === "已完成").length,
      terminated: activeContracts.filter(c => c.status === "已终止").length,
      expiringContract: activeContracts.filter(c => {
        const endDate = new Date(c.endDate)
        return c.status === "执行中" && endDate <= nextMonth && endDate >= today
      }).length,
      totalAmount: activeContracts.reduce((sum, c) => sum + c.amount, 0),
      paidAmount: activeContracts.reduce((sum, c) => {
        if (c.paymentStatus === "已支付") return sum + c.amount
        if (c.paymentStatus === "部分支付") return sum + (c.amount * 0.5) // 假设部分支付为50%
        return sum
      }, 0),
      typeDistribution: {},
      statusDistribution: {},
      paymentDistribution: {},
      amountTrend: []
    }

    // 计算分布统计
    activeContracts.forEach(contract => {
      // 合同类型分布
      if (stats.typeDistribution[contract.contractType]) {
        stats.typeDistribution[contract.contractType]++
      } else {
        stats.typeDistribution[contract.contractType] = 1
      }

      // 状态分布
      if (stats.statusDistribution[contract.status]) {
        stats.statusDistribution[contract.status]++
      } else {
        stats.statusDistribution[contract.status] = 1
      }

      // 支付状态分布
      if (stats.paymentDistribution[contract.paymentStatus]) {
        stats.paymentDistribution[contract.paymentStatus]++
      } else {
        stats.paymentDistribution[contract.paymentStatus] = 1
      }
    })

    // 真实数据：生成过去12个月的合同金额趋势，并确保数据有明显的趋势变化
    const monthNames = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
    const currentMonth = today.getMonth()
    const currentYear = today.getFullYear()
    const lastYear = currentYear - 1

    // 为过去12个月创建模拟数据，具有明显的季节性趋势和增长
    const mockTrend = [
      { month: `一月 ${currentMonth < 1 ? currentYear : lastYear}`, amount: 2400000 },  // 春节前签订高峰
      { month: `二月 ${currentMonth < 2 ? currentYear : lastYear}`, amount: 1200000 },  // 春节期间低谷
      { month: `三月 ${currentMonth < 3 ? currentYear : lastYear}`, amount: 2800000 },  // 开工复工增长
      { month: `四月 ${currentMonth < 4 ? currentYear : lastYear}`, amount: 3500000 },  // 稳步增长
      { month: `五月 ${currentMonth < 5 ? currentYear : lastYear}`, amount: 4200000 },  // 五月淡季小高峰
      { month: `六月 ${currentMonth < 6 ? currentYear : lastYear}`, amount: 3800000 },  // 略有回落
      { month: `七月 ${currentMonth < 7 ? currentYear : lastYear}`, amount: 3200000 },  // 季度末回落
      { month: `八月 ${currentMonth < 8 ? currentYear : lastYear}`, amount: 5100000 },  // 新预算年度开始，大幅增长
      { month: `九月 ${currentMonth < 9 ? currentYear : lastYear}`, amount: 5800000 },  // 继续上升
      { month: `十月 ${currentMonth < 10 ? currentYear : lastYear}`, amount: 3900000 }, // 国庆期间回落
      { month: `十一月 ${currentMonth < 11 ? currentYear : lastYear}`, amount: 6500000 }, // 年底前冲刺
      { month: `十二月 ${currentMonth < 12 ? currentYear : lastYear}`, amount: 7800000 }, // 年终高峰
    ]

    // 只保留过去6个月的趋势数据
    let startIndex = Math.max(0, 12 - 6)

    // 获取真实签约数据（如果有的话）
    for (let i = 0; i < 6; i++) {
      const targetMonthIndex = (currentMonth - 5 + i + 12) % 12
      const targetYear = targetMonthIndex > currentMonth ? lastYear : currentYear
      const monthYear = `${monthNames[targetMonthIndex]} ${targetYear}`

      // 从真实合同数据中计算当月签约额
      const realSigningAmount = activeContracts
        .filter(c => {
          const signDate = new Date(c.signDate)
          return signDate.getMonth() === targetMonthIndex && signDate.getFullYear() === targetYear
        })
        .reduce((sum, c) => sum + c.amount, 0)

      // 如果真实签约额为0，则使用模拟数据
      const monthlyAmount = realSigningAmount > 0 ? realSigningAmount : mockTrend[startIndex + i].amount

      stats.amountTrend.push({
        month: monthYear,
        amount: monthlyAmount
      })
    }

    setStatistics(stats)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "执行中":
        return <Badge className="bg-green-500">执行中</Badge>
      case "待执行":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            待执行
          </Badge>
        )
      case "已完成":
        return <Badge variant="secondary">已完成</Badge>
      case "已终止":
        return <Badge variant="destructive">已终止</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取支付状态对应的徽章样式
  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "已支付":
        return <Badge className="bg-green-500">已支付</Badge>
      case "部分支付":
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            部分支付
          </Badge>
        )
      case "未支付":
        return (
          <Badge variant="outline" className="text-red-500 border-red-500">
            未支付
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理添加合同
  const handleAddContract = () => {
    if (!formData.contractNo?.trim() || !formData.project?.trim() || !formData.contractType?.trim() || !formData.party?.trim()) {
      message.error("请填写必要的合同信息")
      return
    }

    try {
      const newContract: Contract = {
        id: Date.now().toString(),
        contractNo: formData.contractNo,
        project: formData.project,
        contractType: formData.contractType,
        party: formData.party,
        signDate: formData.signDate || new Date().toISOString().split('T')[0],
        startDate: formData.startDate || new Date().toISOString().split('T')[0],
        endDate: formData.endDate || new Date().toISOString().split('T')[0],
        amount: formData.amount || 0,
        status: formData.status as "执行中" | "待执行" | "已完成" | "已终止" || "待执行",
        paymentStatus: formData.paymentStatus as "已支付" | "部分支付" | "未支付" || "未支付",
        manager: formData.manager || "",
        description: formData.description,
        contactPerson: formData.contactPerson,
        contactPhone: formData.contactPhone,
        attachments: formData.attachments || [],
        remarks: formData.remarks,
        createdAt: new Date().toLocaleString(),
        updatedAt: new Date().toLocaleString(),
        disabled: false
      }

      const updatedContracts = [...contracts, newContract]
      setContracts(updatedContracts)
      calculateStatistics(updatedContracts)
      setIsAddDialogOpen(false)
      setFormData({})
      message.success("合同添加成功")
    } catch (error) {
      console.error("添加合同失败:", error)
      message.error("添加合同失败，请检查输入数据")
    }
  }

  // 处理编辑合同
  const handleEditContract = () => {
    if (!currentContract) return

    try {
      const updatedContracts = contracts.map(contract => {
        if (contract.id === currentContract.id) {
          return {
            ...contract,
            ...formData,
            updatedAt: new Date().toLocaleString()
          }
        }
        return contract
      })

      setContracts(updatedContracts)
      calculateStatistics(updatedContracts)
      setIsEditDialogOpen(false)
      setCurrentContract(null)
      setFormData({})
      message.success("合同更新成功")
    } catch (error) {
      console.error("更新合同失败:", error)
      message.error("更新合同失败，请检查输入数据")
    }
  }

  // 处理查看合同详情
  const handleViewContractDetails = (contract: Contract) => {
    setCurrentContract(contract)
    setIsDetailDialogOpen(true)
  }

  // 处理删除合同
  const handleDeleteContract = (id: string) => {
    try {
      const updatedContracts = contracts.map(contract =>
        contract.id === id ? { ...contract, disabled: true } : contract
      )
      setContracts(updatedContracts)
      calculateStatistics(updatedContracts)
      message.success("合同已删除")
    } catch (error) {
      console.error("删除合同失败:", error)
      message.error("删除合同失败")
    }
  }

  // 处理批量删除合同
  const handleBatchDelete = () => {
    if (selectedContracts.length === 0) {
      message.error("请选择要删除的合同")
      return
    }

    try {
      const updatedContracts = contracts.map(contract =>
        selectedContracts.includes(contract.id) ? { ...contract, disabled: true } : contract
      )
      setContracts(updatedContracts)
      calculateStatistics(updatedContracts)
      setSelectedContracts([])
      message.success(`已删除 ${selectedContracts.length} 个合同`)
    } catch (error) {
      console.error("批量删除失败:", error)
      message.error("批量删除失败")
    }
  }

  // 处理刷新数据
  const handleRefresh = () => {
    setIsLoading(true)

    try {
      // 模拟API调用延迟
      setTimeout(() => {
        setContracts(contractsData)
        calculateStatistics(contractsData)
        setSearchText("")
        setSelectedContractType("all")
        setActiveTab("all")
        setIsLoading(false)
        message.success("数据刷新成功")
      }, 1000)
    } catch (error) {
      console.error("刷新数据失败:", error)
      message.error("刷新数据失败")
      setIsLoading(false)
    }
  }

  // 处理导出数据
  const handleExportData = () => {
    try {
      // 根据导出设置确定要导出的合同
      let exportData = contracts.filter(c => !c.disabled)

      if (exportSettings.range === "selected") {
        if (selectedContracts.length === 0) {
          message.error("未选择任何合同，无法导出")
          return
        }
        exportData = exportData.filter(c => selectedContracts.includes(c.id))
      } else if (exportSettings.range === "filtered") {
        exportData = filteredContracts
      }

      // 模拟导出过程
      message.success(`已导出 ${exportData.length} 个合同到${exportSettings.format.toUpperCase()}文件`)
      setIsExportDialogOpen(false)
    } catch (error) {
      console.error("导出数据失败:", error)
      message.error("导出数据失败")
    }
  }

  // 处理导入数据
  const handleImportData = () => {
    // 模拟导入过程
    const randomCount = Math.floor(Math.random() * 5) + 1
    message.success(`已成功导入 ${randomCount} 个合同`)
    setIsImportDialogOpen(false)

    // 在实际应用中，这里应该处理文件上传和解析
  }

  // 处理选择合同
  const handleSelectContract = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedContracts([...selectedContracts, id])
    } else {
      setSelectedContracts(selectedContracts.filter(contractId => contractId !== id))
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedContracts(filteredContracts.map(c => c.id))
    } else {
      setSelectedContracts([])
    }
  }

  // 处理状态变更
  const handleChangeStatus = (id: string, status: "执行中" | "待执行" | "已完成" | "已终止") => {
    try {
      const updatedContracts = contracts.map(contract => {
        if (contract.id === id) {
          return {
            ...contract,
            status,
            updatedAt: new Date().toLocaleString()
          }
        }
        return contract
      })

      setContracts(updatedContracts)
      calculateStatistics(updatedContracts)
      message.success("状态更新成功")
    } catch (error) {
      console.error("更新状态失败:", error)
      message.error("更新状态失败")
    }
  }

  // 筛选合同
  const filteredContracts = contracts
    .filter(contract => !contract.disabled)
    .filter(contract => {
      // 根据标签页筛选
      if (activeTab !== "all") {
        if (activeTab === "in-progress" && contract.status !== "执行中") return false
        if (activeTab === "pending" && contract.status !== "待执行") return false
        if (activeTab === "completed" && contract.status !== "已完成") return false
      }

      // 根据搜索文本筛选
      if (searchText.trim() !== "") {
        const searchLower = searchText.toLowerCase()
        const isMatch =
          contract.contractNo.toLowerCase().includes(searchLower) ||
          contract.project.toLowerCase().includes(searchLower) ||
          contract.party.toLowerCase().includes(searchLower) ||
          contract.manager.toLowerCase().includes(searchLower) ||
          (contract.description && contract.description.toLowerCase().includes(searchLower))

        if (!isMatch) return false
      }

      // 根据合同类型筛选
      if (selectedContractType !== "all" && contract.contractType !== selectedContractType) {
        return false
      }

      return true
    })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">工程合同管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>选择导出选项</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsExportDialogOpen(true)}>
                <FileInput className="h-4 w-4 mr-2" />
                导出为Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                setExportSettings({...exportSettings, format: "pdf"})
                handleExportData()
              }}>
                <FileText className="h-4 w-4 mr-2" />
                导出为PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
            <FileUp className="h-4 w-4 mr-2" />
            导入数据
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
            刷新
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加合同
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加工程合同</DialogTitle>
                <DialogDescription>添加新的工程相关合同信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contract-no">合同编号</Label>
                    <Input
                      id="contract-no"
                      placeholder="输入合同编号"
                      value={formData.contractNo || ''}
                      onChange={(e) => setFormData({...formData, contractNo: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目</Label>
                    <Select
                      value={formData.project}
                      onValueChange={(value) => setFormData({...formData, project: value})}
                    >
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                        <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                        <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                        <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                        <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contract-type">合同类型</Label>
                    <Select
                      value={formData.contractType}
                      onValueChange={(value) => setFormData({...formData, contractType: value})}
                    >
                      <SelectTrigger id="contract-type">
                        <SelectValue placeholder="选择合同类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="工程承包合同">工程承包合同</SelectItem>
                        <SelectItem value="设备采购合同">设备采购合同</SelectItem>
                        <SelectItem value="技术服务合同">技术服务合同</SelectItem>
                        <SelectItem value="勘探服务合同">勘探服务合同</SelectItem>
                        <SelectItem value="劳务合同">劳务合同</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="party">合同方</Label>
                    <Input
                      id="party"
                      placeholder="输入合同对方名称"
                      value={formData.party || ''}
                      onChange={(e) => setFormData({...formData, party: e.target.value})}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sign-date">签订日期</Label>
                    <Input id="sign-date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="start-date">开始日期</Label>
                    <Input id="start-date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date">结束日期</Label>
                    <Input id="end-date" type="date" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">合同金额 (元)</Label>
                    <Input id="amount" type="number" min="0" placeholder="输入合同金额" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="manager">合同负责人</Label>
                    <Input id="manager" placeholder="输入合同负责人姓名" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">合同状态</Label>
                    <Select>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择合同状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="in-progress">执行中</SelectItem>
                        <SelectItem value="pending">待执行</SelectItem>
                        <SelectItem value="completed">已完成</SelectItem>
                        <SelectItem value="terminated">已终止</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="payment-status">支付状态</Label>
                    <Select>
                      <SelectTrigger id="payment-status">
                        <SelectValue placeholder="选择支付状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="paid">已支付</SelectItem>
                        <SelectItem value="partial">部分支付</SelectItem>
                        <SelectItem value="unpaid">未支付</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">合同描述</Label>
                  <Textarea id="description" placeholder="输入合同详细描述" rows={3} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="file">上传合同文件</Label>
                  <Input id="file" type="file" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false)
                  setFormData({})
                }}>
                  取消
                </Button>
                <Button onClick={handleAddContract}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">总合同数</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <FileCheck className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.executing}</h3>
            <p className="text-sm text-muted-foreground">执行中合同</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.expiringContract}</h3>
            <p className="text-sm text-muted-foreground">即将到期合同</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-purple-100 p-3 mb-4">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.totalAmount.toLocaleString()}</h3>
            <p className="text-sm text-muted-foreground">合同总金额</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>合同列表</CardTitle>
              <CardDescription>查看和管理所有工程合同</CardDescription>
            </div>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="in-progress">执行中</TabsTrigger>
                <TabsTrigger value="pending">待执行</TabsTrigger>
                <TabsTrigger value="completed">已完成</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索合同..."
                  className="pl-8 w-[250px]"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </div>
              <Select
                value={selectedContractType}
                onValueChange={setSelectedContractType}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="合同类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="工程承包合同">工程承包合同</SelectItem>
                  <SelectItem value="设备采购合同">设备采购合同</SelectItem>
                  <SelectItem value="技术服务合同">技术服务合同</SelectItem>
                  <SelectItem value="勘探服务合同">勘探服务合同</SelectItem>
                  <SelectItem value="劳务合同">劳务合同</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={() => {
                setSearchText("")
                setSelectedContractType("all")
                setActiveTab("all")
              }}>
                <XCircle className="h-4 w-4" />
              </Button>
            </div>
            {selectedContracts.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">已选择 {selectedContracts.length} 项</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchDelete}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </div>
            )}
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40px]">
                    <Checkbox
                      checked={
                        filteredContracts.length > 0 &&
                        selectedContracts.length === filteredContracts.length
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>合同编号</TableHead>
                  <TableHead>工程项目</TableHead>
                  <TableHead>合同类型</TableHead>
                  <TableHead>合同方</TableHead>
                  <TableHead>签订日期</TableHead>
                  <TableHead>结束日期</TableHead>
                  <TableHead>合同金额</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>支付状态</TableHead>
                  <TableHead>负责人</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContracts.length > 0 ? (
                  filteredContracts.map((contract) => (
                    <TableRow key={contract.id} className="group">
                      <TableCell>
                        <Checkbox
                          checked={selectedContracts.includes(contract.id)}
                          onCheckedChange={(checked) => handleSelectContract(contract.id, checked as boolean)}
                        />
                      </TableCell>
                    <TableCell className="font-medium">{contract.contractNo}</TableCell>
                    <TableCell>{contract.project}</TableCell>
                    <TableCell>{contract.contractType}</TableCell>
                    <TableCell>{contract.party}</TableCell>
                    <TableCell>{contract.signDate}</TableCell>
                    <TableCell>{contract.endDate}</TableCell>
                    <TableCell>{contract.amount.toLocaleString()} 元</TableCell>
                    <TableCell>{getStatusBadge(contract.status)}</TableCell>
                    <TableCell>{getPaymentStatusBadge(contract.paymentStatus)}</TableCell>
                    <TableCell>{contract.manager}</TableCell>
                    <TableCell className="text-right">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewContractDetails(contract)}
                          >
                            <Eye className="h-4 w-4" />
                      </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCurrentContract(contract)
                              setFormData(contract)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                                <Filter className="h-4 w-4" />
                      </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>状态操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleChangeStatus(contract.id, "执行中")}>
                                <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                                标记为执行中
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleChangeStatus(contract.id, "已完成")}>
                                <CheckSquare className="h-4 w-4 mr-2 text-blue-500" />
                                标记为已完成
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleChangeStatus(contract.id, "已终止")}>
                                <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                                标记为已终止
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteContract(contract.id)}
                                className="text-red-500 focus:text-red-500"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除合同
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                    </TableCell>
                  </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={12} className="h-24 text-center">
                      未找到符合条件的合同
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredContracts.length} 个合同</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 合同详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>合同详情</DialogTitle>
            <DialogDescription>查看合同详细信息</DialogDescription>
          </DialogHeader>
          {currentContract && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">合同编号</h3>
                  <p className="text-base">{currentContract.contractNo}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">工程项目</h3>
                  <p className="text-base">{currentContract.project}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">合同类型</h3>
                  <p className="text-base">{currentContract.contractType}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">合同方</h3>
                  <p className="text-base">{currentContract.party}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">签订日期</h3>
                  <p className="text-base">{currentContract.signDate}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">开始日期</h3>
                  <p className="text-base">{currentContract.startDate}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">结束日期</h3>
                  <p className="text-base">{currentContract.endDate}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">合同金额</h3>
                  <p className="text-base">{currentContract.amount.toLocaleString()} 元</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">状态</h3>
                  <div className="mt-1">{getStatusBadge(currentContract.status)}</div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">支付状态</h3>
                  <div className="mt-1">{getPaymentStatusBadge(currentContract.paymentStatus)}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">负责人</h3>
                  <p className="text-base">{currentContract.manager}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">联系人</h3>
                  <p className="text-base">{currentContract.contactPerson || '未指定'}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">合同描述</h3>
                <p className="text-base">{currentContract.description || '无描述信息'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">备注</h3>
                <p className="text-base">{currentContract.remarks || '无备注信息'}</p>
              </div>

              {currentContract.attachments && currentContract.attachments.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">附件</h3>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    {currentContract.attachments.map((file, index) => (
                      <li key={index} className="text-sm">{file}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                <div>创建时间: {currentContract.createdAt}</div>
                <div>更新时间: {currentContract.updatedAt}</div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                if (currentContract) {
                  setFormData(currentContract)
                  setIsDetailDialogOpen(false)
                  setIsEditDialogOpen(true)
                }
              }}
            >
              编辑
            </Button>
            <Button onClick={() => setIsDetailDialogOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑合同对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑合同</DialogTitle>
            <DialogDescription>修改合同信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-contract-no">合同编号</Label>
                <Input
                  id="edit-contract-no"
                  placeholder="输入合同编号"
                  value={formData.contractNo || ''}
                  onChange={(e) => setFormData({...formData, contractNo: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project">工程项目</Label>
                <Select
                  value={formData.project}
                  onValueChange={(value) => setFormData({...formData, project: value})}
                >
                  <SelectTrigger id="edit-project">
                    <SelectValue placeholder="选择工程项目" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                    <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                    <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                    <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                    <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-status">合同状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "执行中" | "待执行" | "已完成" | "已终止") => setFormData({...formData, status: value})}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择合同状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="执行中">执行中</SelectItem>
                    <SelectItem value="待执行">待执行</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="已终止">已终止</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-payment-status">支付状态</Label>
                <Select
                  value={formData.paymentStatus}
                  onValueChange={(value: "已支付" | "部分支付" | "未支付") => setFormData({...formData, paymentStatus: value})}
                >
                  <SelectTrigger id="edit-payment-status">
                    <SelectValue placeholder="选择支付状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已支付">已支付</SelectItem>
                    <SelectItem value="部分支付">部分支付</SelectItem>
                    <SelectItem value="未支付">未支付</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">合同描述</Label>
              <Textarea
                id="edit-description"
                placeholder="输入合同详细描述"
                rows={3}
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditDialogOpen(false)
              setFormData({})
            }}>
              取消
            </Button>
            <Button onClick={handleEditContract}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>导出合同数据</DialogTitle>
            <DialogDescription>选择导出格式和范围</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="format-xlsx"
                    checked={exportSettings.format === "xlsx"}
                    onCheckedChange={() => setExportSettings({...exportSettings, format: "xlsx"})}
                  />
                  <label htmlFor="format-xlsx" className="text-sm cursor-pointer">Excel (XLSX)</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="format-csv"
                    checked={exportSettings.format === "csv"}
                    onCheckedChange={() => setExportSettings({...exportSettings, format: "csv"})}
                  />
                  <label htmlFor="format-csv" className="text-sm cursor-pointer">CSV</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="format-pdf"
                    checked={exportSettings.format === "pdf"}
                    onCheckedChange={() => setExportSettings({...exportSettings, format: "pdf"})}
                  />
                  <label htmlFor="format-pdf" className="text-sm cursor-pointer">PDF</label>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label>导出范围</Label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="range-all"
                    checked={exportSettings.range === "all"}
                    onCheckedChange={() => setExportSettings({...exportSettings, range: "all"})}
                  />
                  <label htmlFor="range-all" className="text-sm cursor-pointer">所有合同 ({contracts.filter(c => !c.disabled).length} 项)</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="range-filtered"
                    checked={exportSettings.range === "filtered"}
                    onCheckedChange={() => setExportSettings({...exportSettings, range: "filtered"})}
                  />
                  <label htmlFor="range-filtered" className="text-sm cursor-pointer">当前筛选结果 ({filteredContracts.length} 项)</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="range-selected"
                    checked={exportSettings.range === "selected"}
                    disabled={selectedContracts.length === 0}
                    onCheckedChange={() => setExportSettings({...exportSettings, range: "selected"})}
                  />
                  <label
                    htmlFor="range-selected"
                    className={cn(
                      "text-sm cursor-pointer",
                      selectedContracts.length === 0 && "text-muted-foreground"
                    )}
                  >
                    已选择的合同 ({selectedContracts.length} 项)
                  </label>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-details"
                checked={exportSettings.includeDetails}
                onCheckedChange={(checked) => setExportSettings({...exportSettings, includeDetails: !!checked})}
              />
              <label htmlFor="include-details" className="text-sm cursor-pointer">包含详细信息（描述、备注等）</label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>取消</Button>
            <Button onClick={handleExportData}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>导入合同数据</DialogTitle>
            <DialogDescription>选择Excel或CSV文件导入</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="import-file">选择文件</Label>
              <Input id="import-file" type="file" accept=".xlsx,.xls,.csv" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="replace-existing" />
                <label htmlFor="replace-existing" className="text-sm cursor-pointer">
                  替换现有数据
                </label>
              </div>
              <p className="text-xs text-muted-foreground">
                选中此项将会删除现有的所有合同数据，然后导入新数据。未选中则会将导入数据添加到现有数据中。
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>取消</Button>
            <Button onClick={handleImportData}>导入</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计分析抽屉 */}
      <Drawer open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <DrawerContent>
          <DrawerHeader className="border-b">
            <DrawerTitle>合同统计分析</DrawerTitle>
            <DrawerDescription>查看工程合同数据统计</DrawerDescription>
          </DrawerHeader>
          <ScrollArea className="h-[70vh] p-6">
            <div className="space-y-8">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">基本统计</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm text-muted-foreground">总合同数</p>
                          <h4 className="text-2xl font-bold">{statistics.total}</h4>
                        </div>
                        <FileText className="h-5 w-5 text-primary" />
            </div>
          </CardContent>
        </Card>
        <Card>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm text-muted-foreground">执行中</p>
                          <h4 className="text-2xl font-bold">{statistics.executing}</h4>
                        </div>
                        <CheckCircle2 className="h-5 w-5 text-green-500" />
            </div>
          </CardContent>
        </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm text-muted-foreground">总金额</p>
                          <h4 className="text-2xl font-bold">{(statistics.totalAmount / 10000).toFixed(0)}万</h4>
      </div>
                        <DollarSign className="h-5 w-5 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm text-muted-foreground">支付比例</p>
                          <h4 className="text-2xl font-bold">
                            {statistics.totalAmount ? Math.round((statistics.paidAmount / statistics.totalAmount) * 100) : 0}%
                          </h4>
                        </div>
                        <BarChart className="h-5 w-5 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">合同状态分布</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">执行中</span>
                    <span className="text-sm font-medium">{statistics.executing} ({statistics.total ? Math.round((statistics.executing / statistics.total) * 100) : 0}%)</span>
                  </div>
                  <Progress value={statistics.total ? (statistics.executing / statistics.total) * 100 : 0} className="h-2"/>

                  <div className="flex justify-between mt-2">
                    <span className="text-sm">待执行</span>
                    <span className="text-sm font-medium">{statistics.pending} ({statistics.total ? Math.round((statistics.pending / statistics.total) * 100) : 0}%)</span>
                  </div>
                  <Progress value={statistics.total ? (statistics.pending / statistics.total) * 100 : 0} className="h-2 bg-muted"/>

                  <div className="flex justify-between mt-2">
                    <span className="text-sm">已完成</span>
                    <span className="text-sm font-medium">{statistics.completed} ({statistics.total ? Math.round((statistics.completed / statistics.total) * 100) : 0}%)</span>
                  </div>
                  <Progress value={statistics.total ? (statistics.completed / statistics.total) * 100 : 0} className="h-2 bg-muted"/>

                  <div className="flex justify-between mt-2">
                    <span className="text-sm">已终止</span>
                    <span className="text-sm font-medium">{statistics.terminated} ({statistics.total ? Math.round((statistics.terminated / statistics.total) * 100) : 0}%)</span>
                  </div>
                  <Progress value={statistics.total ? (statistics.terminated / statistics.total) * 100 : 0} className="h-2 bg-muted"/>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">合同金额趋势</h3>
                <div className="border rounded-md">
                  {/* 趋势图 - 使用SVG实现可视化图表，固定宽高比 */}
                  <div className="relative w-full" style={{ aspectRatio: '16/9' }}>
                    <svg width="100%" height="100%" viewBox="0 0 800 450" preserveAspectRatio="xMidYMid meet">
                      {/* 定义模拟数据 */}
                      {(() => {
                        // 更多样化的模拟数据，有起伏变化
                        const mockData = [
                          { month: "一月 2023", amount: 1800000 },
                          { month: "二月 2023", amount: 1200000 },
                          { month: "三月 2023", amount: 3500000 },
                          { month: "四月 2023", amount: 2700000 },
                          { month: "五月 2023", amount: 4200000 },
                          { month: "六月 2023", amount: 3800000 },
                          { month: "七月 2023", amount: 5600000 },
                          { month: "八月 2023", amount: 4800000 },
                          { month: "九月 2023", amount: 6200000 },
                          { month: "十月 2023", amount: 5400000 },
                          { month: "十一月 2023", amount: 7800000 },
                          { month: "十二月 2023", amount: 8500000 }
                        ];

                        // 显示最近6个月的数据
                        const last6MonthsData = mockData.slice(-6);

                        // 是否使用模拟数据
                        const trendData = statistics.amountTrend.length > 0 ? statistics.amountTrend : last6MonthsData;

                        // 计算最大值，确保Y轴适应数据
                        const maxAmount = Math.max(...trendData.map(item => item.amount));
                        // 为Y轴选择合适的刻度最大值，让图表有一些顶部空间
                        const yAxisMax = Math.ceil(maxAmount / 1000000) * 1000000 * 1.2;

                        // 设置图表区域
                        const margin = { top: 40, right: 30, bottom: 60, left: 70 };
                        const chartWidth = 800 - margin.left - margin.right;
                        const chartHeight = 450 - margin.top - margin.bottom;

                        // 计算Y轴刻度分段
                        const ySteps = 5;
                        const yTicks = Array.from({ length: ySteps + 1 }, (_, i) =>
                          yAxisMax / ySteps * i
                        );

                        // 绘制图表元素
                        return (
                          <>
                            {/* 绘制渐变背景 */}
                            <defs>
                              <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.7" />
                                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05" />
                              </linearGradient>
                              <filter id="shadow" x="-2" y="-2" width="110%" height="110%">
                                <feDropShadow dx="1" dy="2" stdDeviation="3" floodOpacity="0.2" floodColor="#000" />
                              </filter>
                              <linearGradient id="lineGradient" x1="0" y1="0" x2="1" y2="0">
                                <stop offset="0%" stopColor="#3b82f6" />
                                <stop offset="100%" stopColor="#8b5cf6" />
                              </linearGradient>
                            </defs>

                            {/* 图表标题 */}
                            <text
                              x="400"
                              y="25"
                              fontSize="16"
                              fontWeight="600"
                              fill="#374151"
                              textAnchor="middle"
                            >
                              最近六个月合同签订金额趋势
                            </text>

                            {/* 图表坐标轴组 */}
                            <g transform={`translate(${margin.left}, ${margin.top})`}>
                              {/* 绘制Y轴网格线 */}
                              {yTicks.map((tick, i) => (
                                <g key={`grid-${i}`}>
                                  <line
                                    x1="0"
                                    y1={chartHeight - (tick / yAxisMax) * chartHeight}
                                    x2={chartWidth}
                                    y2={chartHeight - (tick / yAxisMax) * chartHeight}
                                    stroke={i === 0 ? "#d1d5db" : "#e5e7eb"}
                                    strokeWidth={i === 0 ? 1 : 1}
                                    strokeDasharray={i === 0 ? "0" : "4 4"}
                                  />
                                  <text
                                    x="-10"
                                    y={chartHeight - (tick / yAxisMax) * chartHeight}
                                    fontSize="12"
                                    textAnchor="end"
                                    dominantBaseline="middle"
                                    fill="#6b7280"
                                  >
                                    {(tick / 10000).toLocaleString()}万
                                  </text>
                                </g>
                              ))}

                              {/* 绘制X轴 */}
                              <line
                                x1="0"
                                y1={chartHeight}
                                x2={chartWidth}
                                y2={chartHeight}
                                stroke="#d1d5db"
                                strokeWidth="1"
                              />

                              {/* 绘制X轴标签 */}
                              {trendData.map((item, i) => (
                                <g key={`x-label-${i}`}>
                                  <text
                                    x={i * (chartWidth / (trendData.length - 1))}
                                    y={chartHeight + 25}
                                    fontSize="12"
                                    textAnchor="middle"
                                    fill="#6b7280"
                                    transform={`rotate(-15, ${i * (chartWidth / (trendData.length - 1))}, ${chartHeight + 25})`}
                                  >
                                    {item.month}
                                  </text>
                                </g>
                              ))}

                              {/* 坐标轴标题 */}
                              <text
                                x="-45"
                                y={chartHeight / 2}
                                fontSize="14"
                                textAnchor="middle"
                                transform={`rotate(-90, -45, ${chartHeight / 2})`}
                                fill="#6b7280"
                              >
                                金额 (万元)
                              </text>

                              {/* 添加明显的折线连接 - 基础折线 */}
                              <polyline
                                points={trendData.map((item, i) => {
                                  const x = i * (chartWidth / (trendData.length - 1));
                                  const y = chartHeight - (item.amount / yAxisMax) * chartHeight;
                                  return `${x},${y}`;
                                }).join(' ')}
                                fill="none"
                                stroke="#4f46e5"
                                strokeWidth="2"
                                strokeLinejoin="round"
                              />

                              {/* 绘制区域填充 */}
                              <path
                                d={`
                                  M ${trendData.map((item, i) => {
                                    const x = i * (chartWidth / (trendData.length - 1));
                                    const y = chartHeight - (item.amount / yAxisMax) * chartHeight;
                                    return (i === 0 ? 'M' : 'L') + `${x},${y}`;
                                  }).join(' ')}
                                  L ${(trendData.length - 1) * (chartWidth / (trendData.length - 1))},${chartHeight}
                                  L 0,${chartHeight} Z
                                `}
                                fill="url(#areaGradient)"
                                opacity="0.6"
                              />

                              {/* 添加清晰的折线连接 - 直线版本 */}
                              <path
                                d={`M ${trendData.map((item, i) => {
                                  const x = i * (chartWidth / (trendData.length - 1));
                                  const y = chartHeight - (item.amount / yAxisMax) * chartHeight;
                                  return (i === 0 ? '' : ' L') + `${x},${y}`;
                                }).join('')}`}
                                stroke="#3b82f6"
                                strokeWidth="1.5"
                                strokeDasharray="2,1"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                fill="none"
                                opacity="0.8"
                              />

                              {/* 绘制平滑曲线 */}
                              <path
                                d={(() => {
                                  // 曲线点
                                  const points = trendData.map((item, i) => {
                                    const x = i * (chartWidth / (trendData.length - 1));
                                    const y = chartHeight - (item.amount / yAxisMax) * chartHeight;
                                    return [x, y];
                                  });

                                  // 贝塞尔曲线平滑
                                  if (points.length < 2) return "";

                                  let path = `M ${points[0][0]},${points[0][1]}`;

                                  for (let i = 0; i < points.length - 1; i++) {
                                    const x1 = points[i][0];
                                    const y1 = points[i][1];
                                    const x2 = points[i+1][0];
                                    const y2 = points[i+1][1];

                                    // 控制点计算 - 改进的贝塞尔曲线
                                    const tension = 0.3; // 控制曲线的平滑度

                                    const p0 = points[i-1] || points[i];
                                    const p3 = points[i+2] || points[i+1];

                                    const cp1x = x1 + (x2 - p0[0]) * tension;
                                    const cp1y = y1 + (y2 - p0[1]) * tension;
                                    const cp2x = x2 - (p3[0] - x1) * tension;
                                    const cp2y = y2 - (p3[1] - y1) * tension;

                                    path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${x2},${y2}`;
                                  }

                                  return path;
                                })()}
                                stroke="url(#lineGradient)"
                                strokeWidth="3.5"
                                strokeLinecap="round"
                                fill="none"
                                filter="url(#shadow)"
                                opacity="0.7"
                              />

                              {/* 数据点 */}
                              {trendData.map((item, i) => {
                                const x = i * (chartWidth / (trendData.length - 1));
                                const y = chartHeight - (item.amount / yAxisMax) * chartHeight;
                                return (
                                  <g key={`point-${i}`} className="chart-point">
                                    {/* 背景高亮 */}
                                    <circle
                                      cx={x}
                                      cy={y}
                                      r="8"
                                      fill="rgba(59, 130, 246, 0.2)"
                                      className="opacity-0 hover:opacity-100 transition-opacity duration-200"
                                    />

                                    {/* 数据点外圈 */}
                                    <circle
                                      cx={x}
                                      cy={y}
                                      r="6"
                                      fill="white"
                                      stroke="#4f46e5"
                                      strokeWidth="1.5"
                                    />

                                    {/* 数据点内核 */}
                                    <circle
                                      cx={x}
                                      cy={y}
                                      r="3"
                                      fill="#4f46e5"
                                    />

                                    {/* 悬停工具提示 */}
                                    <g className="opacity-0 hover:opacity-100 transition-opacity duration-200">
                                      <rect
                                        x={x - 45}
                                        y={y - 40}
                                        width="90"
                                        height="30"
                                        rx="5"
                                        fill="#1e40af"
                                        filter="url(#shadow)"
                                      />
                                      <text
                                        x={x}
                                        y={y - 22}
                                        fontSize="13"
                                        fontWeight="500"
                                        textAnchor="middle"
                                        fill="white"
                                      >
                                        {(item.amount / 10000).toLocaleString()}万元
                                      </text>
                                      <path
                                        d={`M ${x-5} ${y-10} L ${x} ${y-5} L ${x+5} ${y-10}`}
                                        fill="#1e40af"
                                      />
                                    </g>
                                  </g>
                                );
                              })}

                              {/* 汇总信息 */}
                              <g transform="translate(0, -25)">
                                <rect
                                  x={chartWidth - 150}
                                  y="0"
                                  width="150"
                                  height="50"
                                  rx="5"
                                  fill="white"
                                  stroke="#e5e7eb"
                                  strokeWidth="1"
                                />
                                <text
                                  x={chartWidth - 75}
                                  y="20"
                                  fontSize="12"
                                  textAnchor="middle"
                                  fill="#374151"
                                >
                                  总金额: {(trendData.reduce((sum, item) => sum + item.amount, 0) / 10000).toLocaleString()}万
                                </text>
                                <text
                                  x={chartWidth - 75}
                                  y="40"
                                  fontSize="12"
                                  textAnchor="middle"
                                  fill="#374151"
                                >
                                  平均: {(trendData.reduce((sum, item) => sum + item.amount, 0) / (trendData.length * 10000)).toLocaleString()}万
                                </text>
                              </g>
                            </g>

                            {/* 图例 */}
                            <g transform={`translate(${margin.left}, ${margin.top + chartHeight + 35})`}>
                              <rect width="190" height="30" rx="5" fill="white" stroke="#e5e7eb" strokeWidth="1" />
                              <rect x="15" y="15" width="30" height="10" fill="url(#areaGradient)" rx="2" opacity="0.6" />
                              <polyline points="15,10 45,10" stroke="#4f46e5" strokeWidth="2" strokeLinejoin="round" />
                              <circle cx="30" cy="10" r="4" fill="white" stroke="#4f46e5" strokeWidth="1.5" />
                              <circle cx="30" cy="10" r="2" fill="#4f46e5" />
                              <text x="60" y="15" fontSize="12" fill="#6b7280" dominantBaseline="middle">月度合同签订金额趋势</text>
                            </g>
                          </>
                        );
                      })()}
                    </svg>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground p-3 bg-slate-50 rounded-md">
                  <div className="font-medium mb-2">金额明细</div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                    {(() => {
                      // 同样使用模拟数据
                      const mockData = [
                        { month: "七月 2023", amount: 5600000 },
                        { month: "八月 2023", amount: 4800000 },
                        { month: "九月 2023", amount: 6200000 },
                        { month: "十月 2023", amount: 5400000 },
                        { month: "十一月 2023", amount: 7800000 },
                        { month: "十二月 2023", amount: 8500000 }
                      ];

                      const trendData = statistics.amountTrend.length > 0 ? statistics.amountTrend : mockData;

                      return trendData.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-1.5 rounded bg-white shadow-sm">
                          <span className="font-medium text-gray-600">{item.month}</span>
                          <span className="font-bold text-blue-600">{(item.amount / 10000).toLocaleString()} 万元</span>
                        </div>
                      ))
                    })()}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">合同类型分布</h3>
                <div className="h-64 p-4 border rounded-md">
                  {/* 饼图 - 使用SVG实现合同类型分布 */}
                  <div className="relative h-full w-full">
                    <svg width="100%" height="100%" viewBox="0 0 240 240">
                      {/* 分组数据来源于statistics.typeDistribution，模拟数据 */}
                      {(() => {
                        // 从statistics获取真实数据，如果为空则使用模拟数据
                        let typeData = []

                        // 检查statistics中是否有类型分布数据
                        const hasRealData = statistics.typeDistribution &&
                          Object.keys(statistics.typeDistribution).length > 0

                        if (hasRealData) {
                          // 使用真实数据
                          const colors = ["#3b82f6", "#10b981", "#f59e0b", "#8b5cf6", "#ec4899", "#6366f1", "#0ea5e9", "#14b8a6"]
                          typeData = Object.entries(statistics.typeDistribution).map(([type, value], index) => ({
                            type,
                            value,
                            color: colors[index % colors.length]
                          }))
                        } else {
                          // 使用模拟数据
                          typeData = [
                            { type: "工程承包合同", value: 35, color: "#3b82f6" },
                            { type: "设备采购合同", value: 25, color: "#10b981" },
                            { type: "技术服务合同", value: 18, color: "#f59e0b" },
                            { type: "勘探服务合同", value: 12, color: "#8b5cf6" },
                            { type: "劳务合同", value: 10, color: "#ec4899" }
                          ]
                        }

                        let startAngle = 0
                        const total = typeData.reduce((sum, item) => sum + item.value, 0)
                        const centerX = 120
                        const centerY = 110
                        const radius = 80

                        return (
                          <g>
                            {/* 圆饼图背景光晕 */}
                            <circle
                              cx={centerX}
                              cy={centerY}
                              r={radius + 5}
                              fill="rgba(243, 244, 246, 0.7)"
                            />

                            {typeData.map((item, i) => {
                              const percentage = item.value / total
                              const endAngle = startAngle + percentage * 360

                              // 计算SVG路径
                              const startRad = (startAngle - 90) * (Math.PI / 180)
                              const endRad = (endAngle - 90) * (Math.PI / 180)

                              const x1 = centerX + radius * Math.cos(startRad)
                              const y1 = centerY + radius * Math.sin(startRad)
                              const x2 = centerX + radius * Math.cos(endRad)
                              const y2 = centerY + radius * Math.sin(endRad)

                              // 计算标签位置 (标签在扇区中间)
                              const midAngle = startAngle + (endAngle - startAngle) / 2
                              const midRad = (midAngle - 90) * (Math.PI / 180)
                              const labelRadius = radius * 0.7
                              const labelX = centerX + labelRadius * Math.cos(midRad)
                              const labelY = centerY + labelRadius * Math.sin(midRad)

                              // 计算引导线终点
                              const lineEndRadius = radius * 1.2
                              const lineEndX = centerX + lineEndRadius * Math.cos(midRad)
                              const lineEndY = centerY + lineEndRadius * Math.sin(midRad)

                              // 创建扇区路径
                              const largeArcFlag = percentage > 0.5 ? 1 : 0
                              const pathData = [
                                `M ${centerX},${centerY}`,
                                `L ${x1},${y1}`,
                                `A ${radius},${radius} 0 ${largeArcFlag} 1 ${x2},${y2}`,
                                'Z'
                              ].join(' ')

                              // 更新起始角度为下一个扇区
                              const result = (
                                <g key={`slice-${i}`}>
                                  <path d={pathData} fill={item.color} stroke="white" strokeWidth="1.5">
                                    <title>{item.type}: {Math.round(percentage * 100)}%</title>
                                  </path>
                                  <line
                                    x1={labelX}
                                    y1={labelY}
                                    x2={lineEndX}
                                    y2={lineEndY}
                                    stroke="#6b7280"
                                    strokeWidth="1"
                                  />
                                  <text
                                    x={lineEndX + (lineEndX > centerX ? 5 : -5)}
                                    y={lineEndY}
                                    fontSize="10"
                                    textAnchor={lineEndX > centerX ? "start" : "end"}
                                    dominantBaseline="middle"
                                    fill="#374151"
                                    fontWeight="500"
                                  >
                                    {item.type} ({Math.round(percentage * 100)}%)
                                  </text>
                                </g>
                              )

                              startAngle = endAngle
                              return result
                            })}

                            {/* 中心圆点 */}
                            <circle
                              cx={centerX}
                              cy={centerY}
                              r="5"
                              fill="white"
                              stroke="#d1d5db"
                              strokeWidth="1"
                            />

                            {/* 饼图标题 */}
                            <text
                              x={centerX}
                              y="30"
                              fontSize="12"
                              fontWeight="500"
                              textAnchor="middle"
                              fill="#374151"
                            >
                              合同类型分布占比
                            </text>
                          </g>
                        )
                      })()}
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
          <DrawerFooter className="border-t">
            <Button variant="outline" onClick={() => setIsStatsDrawerOpen(false)}>关闭</Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  )
}

