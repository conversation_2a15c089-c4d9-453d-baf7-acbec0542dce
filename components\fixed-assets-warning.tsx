"use client"

import React, { useState, use<PERSON>emo } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import * as XLSX from "xlsx"
import ReactECharts from "echarts-for-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { Label } from "@/components/ui/label"
import { Search, Trash2, Edit, Download, Plus, AlertTriangle, Clock, BarChart, Filter, ArrowLeft, CheckSquare, ChevronDown, Calendar, Bell, History, ListFilter, PlusCircle, CheckCircle2, XCircle, AlarmClock, FileEdit, RefreshCw, PieChart, MoreHorizontal, SearchX, Eye, Users, FileText, User as LucideUser } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// 预警数据接口
interface WarningRecord {
  id: number,
  assetName: string,
  assetCode: string,
  warningType: string,
  warningDate: string,
  dueDate: string,
  description: string,
  level: "高" | "中" | "低",
  status: "已处理" | "处理中" | "待处理",
  department?: string,
  responsible?: string,
  processingRecords?: ProcessingRecord[],
  createdAt?: string,
  createdBy?: string,
  updatedAt?: string,
  updatedBy?: string,
}

// 处理记录接口
interface ProcessingRecord {
  processingDate: string,
  processor: string,
  processingMethod: string,
  processingResult: string,
  processingDetails: string,
  processingCost?: number,
  nextActionDate?: string,
}

// 数据过滤条件
interface FilterCondition {
  warningType: string,
  level: string,
  status: string,
  dateRange: {
    start: string,
    end: string
  } | null
}

// 模拟预警数据
const warningData: WarningRecord[] = [
  {
    id: 1,
    assetName: "采矿设备A型",
    assetCode: "ZC2023001",
    warningType: "维护预警",
    warningDate: "2025-01-10",
    dueDate: "2025-01-15",
    description: "设备需要进行定期保养，距离上次保养已经接近3个月",
    level: "中",
    status: "已处理",
    department: "生产部",
    responsible: "张工",
    processingRecords: [
      {
        processingDate: "2025-01-12",
        processor: "张工",
        processingMethod: "进行设备全面检查和保养",
        processingResult: "更换了磨损部件，添加了润滑油",
        processingDetails: "详细描述了保养过程和结果",
        processingCost: 100,
        nextActionDate: "2025-01-15"
      }
    ],
    createdAt: "2025-01-10",
    createdBy: "系统",
    updatedAt: "2025-01-12",
    updatedBy: "张工"
  },
  {
    id: 2,
    assetName: "运输车辆B型",
    assetCode: "ZC2023002",
    warningType: "使用寿命预警",
    warningDate: "2025-01-05",
    dueDate: "2025-02-05",
    description: "车辆使用时间接近5年，需要进行全面检查",
    level: "低",
    status: "待处理",
    department: "物流部",
    responsible: "李工",
    createdAt: "2025-01-05",
    createdBy: "系统"
  },
  {
    id: 3,
    assetName: "安全监控系统",
    assetCode: "ZC2022045",
    warningType: "系统升级预警",
    warningDate: "2025-01-25",
    dueDate: "2025-02-25",
    description: "系统软件版本过低，需要升级到最新版本以确保安全性",
    level: "高",
    status: "处理中",
    department: "安全部",
    responsible: "王工",
    processingRecords: [
      {
        processingDate: "2025-02-01",
        processor: "王工",
        processingMethod: "联系软件供应商，安排系统升级",
        processingResult: "已预约供应商上门升级",
        processingDetails: "详细描述了升级过程和结果",
        processingCost: 200,
        nextActionDate: "2025-02-05"
      }
    ],
    createdAt: "2025-01-25",
    createdBy: "系统",
    updatedAt: "2025-02-01",
    updatedBy: "王工"
  },
  {
    id: 4,
    assetName: "旧采矿设备",
    assetCode: "ZC2010005",
    warningType: "报废预警",
    warningDate: "2025-02-15",
    dueDate: "2025-03-15",
    description: "设备已超过使用年限，建议进行报废处理",
    level: "高",
    status: "待处理",
    department: "生产部",
    responsible: "赵工",
    createdAt: "2025-02-15",
    createdBy: "系统"
  },
  {
    id: 5,
    assetName: "办公楼",
    assetCode: "ZC2015001",
    warningType: "维护预警",
    warningDate: "2025-03-01",
    dueDate: "2025-04-01",
    description: "建筑需要进行年度安全检查",
    level: "中",
    status: "待处理",
    department: "行政部",
    responsible: "刘工",
    createdAt: "2025-03-01",
    createdBy: "系统"
  },
];

export function FixedAssetsWarning() {
  const { toast } = useToast()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddSheetOpen, setIsAddSheetOpen] = useState(false)
  const [isProcessingSheetOpen, setIsProcessingSheetOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [selectedWarning, setSelectedWarning] = useState<WarningRecord | null>(null)
  const [selectedRecords, setSelectedRecords] = useState<number[]>([])
  const [data, setData] = useState<WarningRecord[]>(warningData)

  // 过滤条件
  const [filters, setFilters] = useState<FilterCondition>({
    warningType: "all",
    level: "all",
    status: "all",
    dateRange: null
  })

  // 计算各类统计数据
  const statistics = useMemo(() => {
    return {
      total: data.length,
      pending: data.filter(item => item.status === "待处理").length,
      processing: data.filter(item => item.status === "处理中").length,
      completed: data.filter(item => item.status === "已处理").length,
      highLevel: data.filter(item => item.level === "高").length,
      expiringSoon: data.filter(item => {
        if (item.status === "已处理") return false;
        const today = new Date();
        const dueDate = new Date(item.dueDate);
        const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return diffDays >= 0 && diffDays <= 7;
      }).length
    }
  }, [data]);

  // 根据筛选条件过滤数据
  const filteredData = useMemo(() => {
    return data.filter(item => {
      // 搜索条件过滤
      const matchesSearch = !searchTerm ||
        item.assetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.assetCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase());

      // 类型过滤
      const matchesType = filters.warningType === "all" || item.warningType === filters.warningType;

      // 级别过滤
      const matchesLevel = filters.level === "all" || item.level === filters.level;

      // 状态过滤
      const matchesStatus = filters.status === "all" || item.status === filters.status;

      // 日期范围过滤
      const matchesDateRange = !filters.dateRange || (
        new Date(item.warningDate) >= new Date(filters.dateRange.start) &&
        new Date(item.warningDate) <= new Date(filters.dateRange.end)
      );

      return matchesSearch && matchesType && matchesLevel && matchesStatus && matchesDateRange;
    });
  }, [data, searchTerm, filters]);

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已处理":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            已处理
          </Badge>
        )
      case "处理中":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            <Clock className="h-3 w-3 mr-1" />
            处理中
          </Badge>
        )
      case "待处理":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
            <AlertTriangle className="h-3 w-3 mr-1" />
            待处理
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取级别徽章
  const getLevelBadge = (level: string) => {
    switch (level) {
      case "高":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
            高
          </Badge>
        )
      case "中":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
            中
          </Badge>
        )
      case "低":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            低
          </Badge>
        )
      default:
        return <Badge variant="outline">{level}</Badge>
    }
  }

  // 处理查看详情
  const handleViewDetails = (warning: WarningRecord) => {
    setSelectedWarning(warning);
    setIsViewDetailsOpen(true);
  }

  // 处理预警
  const handleProcessWarning = (warning: WarningRecord) => {
    setSelectedWarning(warning);
    setIsProcessingSheetOpen(true);
  }

  // 处理新增预警
  const handleAddWarning = () => {
    setIsAddSheetOpen(true);
  }

  // 处理删除预警
  const handleDeleteWarning = (id: number) => {
    setData(prevData => prevData.filter(item => item.id !== id));
    toast({
      title: "已删除预警",
      description: "预警记录已成功删除",
    });
  }

  // 处理导出数据
  const handleExport = () => {
    try {
      // 准备导出数据
      const exportData = filteredData.map(item => ({
        '资产名称': item.assetName,
        '资产编号': item.assetCode,
        '预警类型': item.warningType,
        '预警日期': item.warningDate,
        '截止日期': item.dueDate,
        '预警级别': item.level,
        '状态': item.status,
        '描述': item.description,
        '部门': item.department || '',
        '负责人': item.responsible || '',
      }));

      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      ws['!cols'] = [
        { wch: 20 }, // 资产名称
        { wch: 15 }, // 资产编号
        { wch: 15 }, // 预警类型
        { wch: 12 }, // 预警日期
        { wch: 12 }, // 截止日期
        { wch: 10 }, // 预警级别
        { wch: 10 }, // 状态
        { wch: 40 }, // 描述
        { wch: 15 }, // 部门
        { wch: 15 }, // 负责人
      ];

      XLSX.utils.book_append_sheet(wb, ws, '固定资产预警');
      XLSX.writeFile(wb, `固定资产预警_${new Date().toISOString().split('T')[0]}.xlsx`);

      toast({
        title: "导出成功",
        description: "预警数据已成功导出为Excel文件",
      });
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出过程中出现错误",
        variant: "destructive",
      });
    }
  }

  // 生成预警趋势图表配置
  const getWarningTrendOptions = () => {
    // 模拟生成最近7天的日期
    const dates = Array(7).fill(0).map((_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toISOString().split('T')[0];
    });

    // 模拟数据
    const pending = [2, 3, 4, 3, 5, 4, 3];
    const processing = [1, 2, 1, 3, 2, 3, 2];
    const completed = [1, 0, 2, 1, 0, 2, 3];

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['待处理', '处理中', '已处理']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: dates
        }
      ],
      yAxis: [
        {
          type: 'value',
          minInterval: 1
        }
      ],
      series: [
        {
          name: '待处理',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#fbbf24'
          },
          data: pending
        },
        {
          name: '处理中',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#3b82f6'
          },
          data: processing
        },
        {
          name: '已处理',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#22c55e'
          },
          data: completed
        }
      ]
    };
  };

  // 生成预警类型分布图表配置
  const getWarningTypeOptions = () => {
    // 获取不同预警类型的数量
    const typeMap: Record<string, number> = {};
    data.forEach(item => {
      typeMap[item.warningType] = (typeMap[item.warningType] || 0) + 1;
    });

    const typeData = Object.entries(typeMap).map(([name, value]) => ({ name, value }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: Object.keys(typeMap)
      },
      series: [
        {
          name: '预警类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: typeData
        }
      ]
    };
  };

  return (
    <div className="space-y-6">
      {/* 顶部区域 - 更新样式与首页保持一致 */}
      <div className="flex items-start justify-between py-2 mb-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-amber-600" />
            <h1 className="text-2xl font-semibold text-gray-900">固定资产预警管理</h1>
          </div>
          <p className="text-muted-foreground">全面监控与处理资产预警信息</p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-green-50 transition-colors"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button
            variant="default"
            size="sm"
            className="gap-2 bg-gradient-to-r from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600 shadow-md transition-all"
            onClick={handleAddWarning}
          >
            <Plus className="h-4 w-4" />
            添加预警
          </Button>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card className="shadow-sm border border-amber-100/50 overflow-hidden bg-gradient-to-r from-amber-50 via-white to-amber-50">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索资产名称、编号或描述..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <Filter className="h-4 w-4" />
                  筛选
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <DropdownMenuItem
                  onClick={() => setFilters({...filters, warningType: "all"})}
                  className={filters.warningType === "all" || !filters.warningType ? "bg-accent text-accent-foreground" : ""}
                >
                  全部类型
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setFilters({...filters, warningType: "维护预警"})}
                  className={filters.warningType === "维护预警" ? "bg-accent text-accent-foreground" : ""}
                >
                  维护预警
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setFilters({...filters, warningType: "使用寿命预警"})}
                  className={filters.warningType === "使用寿命预警" ? "bg-accent text-accent-foreground" : ""}
                >
                  使用寿命预警
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setFilters({...filters, warningType: "系统升级预警"})}
                  className={filters.warningType === "系统升级预警" ? "bg-accent text-accent-foreground" : ""}
                >
                  系统升级预警
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setFilters({...filters, warningType: "报废预警"})}
                  className={filters.warningType === "报废预警" ? "bg-accent text-accent-foreground" : ""}
                >
                  报废预警
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Select
              value={filters.level}
              onValueChange={(value) => setFilters({...filters, level: value})}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="预警级别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部级别</SelectItem>
                <SelectItem value="高">高</SelectItem>
                <SelectItem value="中">中</SelectItem>
                <SelectItem value="低">低</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.status}
              onValueChange={(value) => setFilters({...filters, status: value})}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="预警状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="待处理">待处理</SelectItem>
                <SelectItem value="处理中">处理中</SelectItem>
                <SelectItem value="已处理">已处理</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilters({
                warningType: "all",
                level: "all",
                status: "all",
                dateRange: null
              })}
              className="hover:bg-amber-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重置筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 统计卡片区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-amber-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">预警总数</CardTitle>
              <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.total}</div>
              <div className="text-sm text-muted-foreground">条预警</div>
            </div>
            <div className="flex items-center justify-between mt-3">
              <div className="text-sm text-muted-foreground">处理进度</div>
              <div className="text-sm font-medium">{Math.round((statistics.completed / statistics.total) * 100)}%</div>
            </div>
            <Progress
              value={(statistics.completed / statistics.total) * 100}
              className="h-1.5 mt-1"
            />
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-amber-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">待处理预警</CardTitle>
              <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                <Clock className="h-4 w-4 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.pending}</div>
              <div className="text-sm text-muted-foreground">条待处理</div>
            </div>
            <div className="flex items-center justify-between mt-3">
              <div className="text-sm text-muted-foreground">处理中</div>
              <div className="text-sm font-medium">{statistics.processing} 条</div>
            </div>
            <Progress
              value={(statistics.processing / (statistics.pending + statistics.processing)) * 100}
              className="h-1.5 mt-1"
            />
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-red-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">高级别预警</CardTitle>
              <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.highLevel}</div>
              <div className="text-sm text-muted-foreground">条高级预警</div>
            </div>
            <div className="flex items-center justify-between mt-3">
              <div className="text-sm text-muted-foreground">占比</div>
              <div className="text-sm font-medium">{Math.round((statistics.highLevel / statistics.total) * 100)}%</div>
            </div>
            <Progress
              value={(statistics.highLevel / statistics.total) * 100}
              className="h-1.5 mt-1 bg-red-100"
              indicatorClassName="bg-red-500"
            />
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-blue-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">即将到期</CardTitle>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <AlarmClock className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.expiringSoon}</div>
              <div className="text-sm text-muted-foreground">条即将到期</div>
            </div>
            <div className="flex items-center justify-between mt-3">
              <div className="text-sm text-muted-foreground">7天内到期</div>
              <div className="text-sm font-medium text-blue-600">需优先处理</div>
            </div>
            <Progress
              value={(statistics.expiringSoon / statistics.pending) * 100}
              className="h-1.5 mt-1 bg-blue-100"
              indicatorClassName="bg-blue-500"
            />
          </CardContent>
        </Card>
      </div>

      {/* 数据可视化图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-md border-0">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <BarChart className="h-5 w-5 mr-2 text-amber-500" />
              预警趋势（近7天）
            </CardTitle>
            <CardDescription>
              显示近7天内不同状态预警的数量变化趋势
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts
              option={getWarningTrendOptions()}
              style={{ height: 300 }}
            />
          </CardContent>
        </Card>

        <Card className="shadow-md border-0">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <PieChart className="h-5 w-5 mr-2 text-amber-500" />
              预警类型分布
            </CardTitle>
            <CardDescription>
              显示不同类型预警的数量分布情况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReactECharts
              option={getWarningTypeOptions()}
              style={{ height: 300 }}
            />
          </CardContent>
        </Card>
      </div>

      {/* 预警记录表格 */}
      <Card className="shadow-md border-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center text-lg">
              <Bell className="h-5 w-5 mr-2 text-amber-500" />
              预警记录
            </CardTitle>
            <div className="flex items-center gap-2">
              {selectedRecords.length > 0 && (
                <>
                  <Button variant="outline" size="sm" className="h-8 gap-1" onClick={() => setSelectedRecords([])}>
                    <XCircle className="h-4 w-4" />
                    取消选择
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="outline" className="h-8 gap-1">
                        <CheckSquare className="h-4 w-4" />
                        批量操作
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          // TODO: 实现批量标记为处理中
                          toast({
                            title: "批量操作",
                            description: `已将 ${selectedRecords.length} 条预警标记为处理中`,
                          });
                        }}
                      >
                        标记为处理中
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          // TODO: 实现批量标记为已处理
                          toast({
                            title: "批量操作",
                            description: `已将 ${selectedRecords.length} 条预警标记为已处理`,
                          });
                        }}
                      >
                        标记为已处理
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-red-500 focus:text-red-500"
                        onClick={() => {
                          // 删除选中的记录
                          setData(prevData => prevData.filter(item => !selectedRecords.includes(item.id)));
                          toast({
                            title: "批量删除",
                            description: `已删除 ${selectedRecords.length} 条预警记录`,
                          });
                          setSelectedRecords([]);
                        }}
                      >
                        删除所选记录
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="w-[40px]">
                    <Checkbox
                      checked={
                        filteredData.length > 0 &&
                        selectedRecords.length === filteredData.length
                      }
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRecords(filteredData.map(item => item.id));
                        } else {
                          setSelectedRecords([]);
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>资产信息</TableHead>
                  <TableHead>预警类型</TableHead>
                  <TableHead>预警日期</TableHead>
                  <TableHead>截止日期</TableHead>
                  <TableHead>级别</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right w-[120px]">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <SearchX className="h-8 w-8 mb-2" />
                        <p>未找到任何记录</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredData.map((warning) => (
                    <TableRow
                      key={warning.id}
                      className={selectedRecords.includes(warning.id) ? "bg-muted/20" : undefined}
                    >
                      <TableCell>
                        <Checkbox
                          checked={selectedRecords.includes(warning.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedRecords(prev => [...prev, warning.id]);
                            } else {
                              setSelectedRecords(prev => prev.filter(id => id !== warning.id));
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{warning.assetName}</div>
                        <div className="text-xs text-muted-foreground">{warning.assetCode}</div>
                        {warning.department && (
                          <div className="text-xs text-muted-foreground mt-1">
                            部门：{warning.department}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {warning.warningType}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>{warning.warningDate}</div>
                      </TableCell>
                      <TableCell>
                        <div className={
                          new Date(warning.dueDate) < new Date() && warning.status !== "已处理"
                            ? "text-red-500 font-medium"
                            : ""
                        }>
                          {warning.dueDate}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getLevelBadge(warning.level)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(warning.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">打开菜单</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(warning)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                            {warning.status !== "已处理" && (
                              <DropdownMenuItem onClick={() => handleProcessWarning(warning)}>
                                <FileEdit className="h-4 w-4 mr-2" />
                                处理预警
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-500 focus:text-red-500"
                              onClick={() => handleDeleteWarning(warning.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 将添加预警对话框改为滑动窗口 */}
      <Sheet open={isAddSheetOpen} onOpenChange={setIsAddSheetOpen}>
        <SheetContent className="sm:max-w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>添加预警信息</SheetTitle>
            <SheetDescription>
              请填写新增预警的详细信息
            </SheetDescription>
          </SheetHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="assetName">资产名称 <span className="text-red-500">*</span></Label>
                <Input id="assetName" placeholder="输入资产名称" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="assetCode">资产编号 <span className="text-red-500">*</span></Label>
                <Input id="assetCode" placeholder="例如：ZC2023001" required />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="department">所属部门 <span className="text-red-500">*</span></Label>
                <Select required>
                  <SelectTrigger id="department">
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="production">生产部</SelectItem>
                    <SelectItem value="logistics">物流部</SelectItem>
                    <SelectItem value="security">安全管理部</SelectItem>
                    <SelectItem value="engineering">工程部</SelectItem>
                    <SelectItem value="it">信息技术部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="responsible">负责人 <span className="text-red-500">*</span></Label>
                <Input id="responsible" placeholder="输入负责人姓名" required />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="warningType">预警类型 <span className="text-red-500">*</span></Label>
                <Select required>
                  <SelectTrigger id="warningType">
                    <SelectValue placeholder="选择预警类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="维护预警">维护预警</SelectItem>
                    <SelectItem value="使用寿命预警">使用寿命预警</SelectItem>
                    <SelectItem value="系统升级预警">系统升级预警</SelectItem>
                    <SelectItem value="报废预警">报废预警</SelectItem>
                    <SelectItem value="闲置预警">闲置预警</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="level">预警级别 <span className="text-red-500">*</span></Label>
                <Select required>
                  <SelectTrigger id="level">
                    <SelectValue placeholder="选择预警级别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="warningDate">预警日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="warningDate"
                  type="date"
                  defaultValue={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dueDate">截止日期 <span className="text-red-500">*</span></Label>
                <Input id="dueDate" type="date" required />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">预警描述 <span className="text-red-500">*</span></Label>
              <Textarea
                id="description"
                placeholder="请输入预警的详细描述..."
                className="min-h-[100px]"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select defaultValue="待处理">
                <SelectTrigger id="status">
                  <SelectValue placeholder="选择预警状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="待处理">待处理</SelectItem>
                  <SelectItem value="处理中">处理中</SelectItem>
                  <SelectItem value="已处理">已处理</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <SheetFooter className="pt-4">
            <Button variant="outline" onClick={() => setIsAddSheetOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              // 模拟添加预警
              const newId = Math.max(...data.map(item => item.id)) + 1;
              const newWarning: WarningRecord = {
                id: newId,
                assetName: "新添加资产", // 实际应从表单获取
                assetCode: "ZC2023099", // 实际应从表单获取
                warningType: "维护预警", // 实际应从表单获取
                warningDate: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                level: "中", // 实际应从表单获取
                status: "待处理", // 实际应从表单获取
                description: "这是一条新添加的预警记录", // 实际应从表单获取
                department: "信息技术部", // 实际应从表单获取
                responsible: "张工", // 实际应从表单获取
                processingRecords: []
              };

              setData([...data, newWarning]);
              toast({
                title: "添加成功",
                description: "新的预警记录已成功添加",
              });
              setIsAddSheetOpen(false);
            }}>
              保存
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 预警处理弹出窗口 */}
      <Sheet open={isProcessingSheetOpen} onOpenChange={setIsProcessingSheetOpen}>
        <SheetContent className="sm:max-w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-amber-600 to-amber-500">处理预警</SheetTitle>
            <SheetDescription>
              请输入预警处理的相关信息
            </SheetDescription>
          </SheetHeader>
          <div className="space-y-5 py-6">
            {selectedWarning && (
              <>
                <div className="bg-gradient-to-r from-amber-50 to-white p-4 rounded-xl space-y-3 shadow-sm border border-amber-100/50">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="font-medium text-lg text-gray-900">{selectedWarning.assetName}</h4>
                      <p className="text-sm text-muted-foreground">编号：{selectedWarning.assetCode}</p>
                    </div>
                    <div>
                      {getLevelBadge(selectedWarning.level)}
                    </div>
                  </div>
                  <div className="flex flex-col gap-1 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-amber-500" />
                      <span className="text-muted-foreground">预警日期：</span>
                      <span>{selectedWarning.warningDate}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlarmClock className="h-4 w-4 text-amber-500" />
                      <span className="text-muted-foreground">截止日期：</span>
                      <span className={
                        new Date(selectedWarning.dueDate) < new Date()
                          ? "text-red-500 font-medium"
                          : ""
                      }>{selectedWarning.dueDate}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-amber-500" />
                      <span className="text-muted-foreground">预警类型：</span>
                      <span>{selectedWarning.warningType}</span>
                    </div>
                  </div>
                  <div className="text-sm">
                    <p className="text-muted-foreground">描述：</p>
                    <p className="mt-1 text-gray-700">{selectedWarning.description}</p>
                  </div>
                </div>

                <div className="space-y-4 mt-4">
                  <h3 className="font-medium text-base flex items-center">
                    <FileEdit className="h-5 w-5 mr-2 text-amber-500" />
                    处理信息
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="processingDate">处理日期 <span className="text-red-500">*</span></Label>
                      <Input
                        id="processingDate"
                        type="date"
                        defaultValue={new Date().toISOString().split('T')[0]}
                        className="transition-all hover:border-amber-300 focus-visible:ring-amber-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="processor">处理人 <span className="text-red-500">*</span></Label>
                      <Input
                        id="processor"
                        placeholder="请输入处理人姓名"
                        className="transition-all hover:border-amber-300 focus-visible:ring-amber-500"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="processingMethod">处理方式 <span className="text-red-500">*</span></Label>
                    <Select>
                      <SelectTrigger className="transition-all hover:border-amber-300 focus:ring-amber-500">
                        <SelectValue placeholder="请选择处理方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="regular">例行维护</SelectItem>
                        <SelectItem value="repair">维修处理</SelectItem>
                        <SelectItem value="upgrade">系统升级</SelectItem>
                        <SelectItem value="replace">更换部件</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="processingResult">处理结果 <span className="text-red-500">*</span></Label>
                    <Select>
                      <SelectTrigger className="transition-all hover:border-amber-300 focus:ring-amber-500">
                        <SelectValue placeholder="请选择处理结果" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="resolved">已解决</SelectItem>
                        <SelectItem value="partially">部分解决</SelectItem>
                        <SelectItem value="continued">继续监控</SelectItem>
                        <SelectItem value="needMore">需要进一步处理</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="processingDetails">处理详情 <span className="text-red-500">*</span></Label>
                    <Textarea
                      id="processingDetails"
                      placeholder="请输入详细的处理过程和结果..."
                      className="min-h-[100px] transition-all hover:border-amber-300 focus-visible:ring-amber-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="processingCost">处理费用（￥）</Label>
                      <Input
                        id="processingCost"
                        type="number"
                        placeholder="请输入处理费用"
                        className="transition-all hover:border-amber-300 focus-visible:ring-amber-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="nextActionDate">下次操作日期</Label>
                      <Input
                        id="nextActionDate"
                        type="date"
                        className="transition-all hover:border-amber-300 focus-visible:ring-amber-500"
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
          <SheetFooter className="mt-6 gap-2">
            <Button variant="outline" className="transition-all hover:bg-red-50 hover:text-red-600 hover:border-red-200" onClick={() => setIsProcessingSheetOpen(false)}>
              取消
            </Button>
            <Button
              className="bg-gradient-to-r from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600 shadow-md transition-all"
              onClick={() => {
                // 模拟处理预警
                if (selectedWarning) {
                  const updatedData = data.map(item => {
                    if (item.id === selectedWarning.id) {
                      return {
                        ...item,
                        status: "已处理" as const
                      };
                    }
                    return item;
                  });
                  setData(updatedData);
                  toast({
                    title: "处理成功",
                    description: "预警已成功标记为已处理",
                  });
                  setIsProcessingSheetOpen(false);
                }
              }}
            >
              提交处理
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 查看详情弹出窗口 */}
      <Sheet open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <SheetContent className="sm:max-w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-amber-600 to-amber-500">预警详情</SheetTitle>
            <SheetDescription>
              查看预警的详细信息和处理记录
            </SheetDescription>
          </SheetHeader>

          <div className="py-6 space-y-6">
            {selectedWarning && (
              <>
                <div className="bg-gradient-to-r from-amber-50 to-white p-4 rounded-xl space-y-4 shadow-sm border border-amber-100/50">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-lg text-gray-900">{selectedWarning.assetName}</h3>
                      <p className="text-sm text-muted-foreground">编号：{selectedWarning.assetCode}</p>
                    </div>
                    <div className="flex gap-2">
                      {getLevelBadge(selectedWarning.level)}
                      {getStatusBadge(selectedWarning.status)}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="space-y-1">
                      <p className="text-muted-foreground flex items-center gap-1">
                        <Bell className="h-3.5 w-3.5 text-amber-500" />
                        预警类型
                      </p>
                      <p className="font-medium">{selectedWarning.warningType}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground flex items-center gap-1">
                        <Users className="h-3.5 w-3.5 text-amber-500" />
                        所属部门
                      </p>
                      <p className="font-medium">{selectedWarning.department || "未指定"}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5 text-amber-500" />
                        预警日期
                      </p>
                      <p className="font-medium">{selectedWarning.warningDate}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground flex items-center gap-1">
                        <AlarmClock className="h-3.5 w-3.5 text-amber-500" />
                        截止日期
                      </p>
                      <p className={`font-medium ${
                        new Date(selectedWarning.dueDate) < new Date() && selectedWarning.status !== "已处理"
                          ? "text-red-500"
                          : ""
                      }`}>{selectedWarning.dueDate}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground flex items-center gap-1">
                        <LucideUser className="h-3.5 w-3.5 text-amber-500" />
                        负责人
                      </p>
                      <p className="font-medium">{selectedWarning.responsible || "未指定"}</p>
                    </div>
                  </div>

                  <div className="space-y-1 pt-2 border-t border-dashed border-amber-100">
                    <p className="text-muted-foreground flex items-center gap-1">
                      <FileText className="h-3.5 w-3.5 text-amber-500" />
                      预警描述
                    </p>
                    <p className="text-gray-700">{selectedWarning.description}</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium flex items-center text-gray-900">
                    <History className="h-5 w-5 mr-2 text-amber-500" />
                    处理记录
                  </h4>

                  {selectedWarning.processingRecords && selectedWarning.processingRecords.length > 0 ? (
                    <div className="space-y-4">
                      {selectedWarning.processingRecords.map((record, index) => (
                        <div key={index} className="border border-gray-100 rounded-lg p-4 space-y-3 bg-white shadow-sm hover:shadow-md transition-all">
                          <div className="flex justify-between items-start">
                            <div className="font-medium text-gray-900 flex items-center gap-2">
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                              {record.processingMethod}
                            </div>
                            <div className="text-sm text-muted-foreground bg-gray-50 px-2 py-0.5 rounded">
                              {record.processingDate}
                            </div>
                          </div>
                          <div className="grid grid-cols-2 text-sm gap-3">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">处理人：</span>
                              <span className="font-medium">{record.processor}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">处理结果：</span>
                              <span className="font-medium">{record.processingResult}</span>
                            </div>
                            {record.processingCost && (
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">处理费用：</span>
                                <span className="font-medium">￥{record.processingCost}</span>
                              </div>
                            )}
                            {record.nextActionDate && (
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">下次操作日期：</span>
                                <span className="font-medium">{record.nextActionDate}</span>
                              </div>
                            )}
                          </div>
                          <div className="text-sm bg-gradient-to-r from-gray-50 to-white p-3 rounded-md mt-2">
                            <div className="text-muted-foreground mb-1">处理详情：</div>
                            <p className="text-gray-700">{record.processingDetails}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground bg-gradient-to-r from-gray-50 to-white rounded-lg">
                      <History className="h-12 w-12 mx-auto mb-3 opacity-30" />
                      <p>暂无处理记录</p>
                      <p className="text-sm mt-1">当预警被处理后，处理记录将显示在这里</p>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>

          <SheetFooter className="gap-2 mt-4">
            {selectedWarning && selectedWarning.status !== "已处理" && (
              <Button
                className="bg-gradient-to-r from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600 shadow-md transition-all"
                onClick={() => {
                  setIsViewDetailsOpen(false);
                  setIsProcessingSheetOpen(true);
                }}
              >
                处理此预警
              </Button>
            )}
            <Button variant="outline" className="transition-all hover:bg-gray-50" onClick={() => setIsViewDetailsOpen(false)}>
              关闭
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  )
}

