"use client"

import React, { useState, useEffect, useRef } from "react"
import {
  Search,
  Filter, 
  Plus,
  Download,
  Refresh<PERSON><PERSON>, 
  Printer, 
  BarChart2, 
  Warehouse as WarehouseIcon, 
  Package,
  AlertCircle,
  Edit,
  Trash2,
  X,
  CheckCircle,
  Save,
  Calendar,
  FileText
} from "lucide-react"

interface InventoryCheck {
  id: string
  warehouseId: string
  date: string
  checkType: string
  result: string
  responsible: string
  status: string
  description?: string
}

interface Alert {
  id: string
  warehouseId: string
  alertType: string
  severity: string
  message: string
  date: string
  status: string
}

interface WarehouseSection {
  id: string
  name: string
  code: string
  capacity: number
  usedCapacity: number
  materialTypes: string[]
  status: string
}

interface MaintenanceSchedule {
  id: string
  warehouseId: string
  date: string
  maintenanceType: string
  description: string
  responsible: string
  status: string
  nextMaintenanceDate: string
}

interface Warehouse {
  id: string
  name: string
  code: string
  type: string
  area: number
  usedArea: number
  location: string
  manager: string
  contact: string
  email: string
  status: string
  materialCount: number
  lastCheckDate: string
  nextCheckDate: string
  description?: string
  temperature?: number
  humidity?: number
  sections: WarehouseSection[]
  turnoverRate: number
  safetyLevel: string
  maintenanceSchedule: MaintenanceSchedule[]
  inventoryChecks: InventoryCheck[]
  alerts: Alert[]
  hasTemperatureControl: boolean
  hasHumidityControl: boolean
  hasCameraSystem: boolean
  hasFireProtection: boolean
  materialCategories: string[]
  usageRate: number
}

// 统计数据类型
interface WarehouseStats {
  totalWarehouses: number
  totalMaterials: number
  warningWarehouses: number
  averageUsage: number
}

export function WarehouseManagement() {
  // 状态定义
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isLoading, setIsLoading] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [newWarehouse, setNewWarehouse] = useState<Partial<Warehouse>>({
    name: "",
    code: "",
    type: "综合仓库",
    area: 0,
    usedArea: 0,
    location: "",
    manager: "",
    contact: "",
    email: "",
    status: "正常",
    materialCount: 0,
    lastCheckDate: new Date().toISOString().split('T')[0],
    nextCheckDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    description: "",
    sections: [],
    turnoverRate: 0,
    safetyLevel: "中",
    maintenanceSchedule: [],
    inventoryChecks: [],
    alerts: [],
    hasTemperatureControl: false,
    hasHumidityControl: false,
    hasCameraSystem: false,
    hasFireProtection: true,
    materialCategories: [],
    usageRate: 0
  })
  const [newSection, setNewSection] = useState<Partial<WarehouseSection>>({
    name: "",
    code: "",
    capacity: 0,
    usedCapacity: 0,
    materialTypes: [],
    status: "正常"
  })
  const [exportFormat, setExportFormat] = useState<"csv" | "excel" | "pdf">("excel")
  const exportOptionsRef = useRef<HTMLDivElement>(null)
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [searchErrors, setSearchErrors] = useState<Record<string, string>>({})
  const [editedWarehouse, setEditedWarehouse] = useState<Warehouse | null>(null)
  const [successMessage, setSuccessMessage] = useState("")
  const [errorMessage, setErrorMessage] = useState("")

  // 示例数据
  const [warehouses, setWarehouses] = useState<Warehouse[]>([
    {
      id: "1",
      name: "主仓库",
      code: "WH001",
      type: "综合仓库",
      area: 2000,
      usedArea: 1500,
      location: "工业园区A区",
      manager: "张三",
      contact: "13800138000",
      email: "<EMAIL>",
      status: "正常",
      materialCount: 1200,
      lastCheckDate: "2024-03-15",
      nextCheckDate: "2024-04-15",
      sections: [
        {
          id: "1-1",
          name: "A区",
          code: "WH001-A",
          capacity: 500,
          usedCapacity: 400,
          materialTypes: ["建筑材料", "五金工具"],
          status: "正常"
        },
        {
          id: "1-2",
          name: "B区",
          code: "WH001-B",
          capacity: 500,
          usedCapacity: 350,
          materialTypes: ["电气设备", "安全设备"],
          status: "正常"
        }
      ],
      turnoverRate: 0.85,
      safetyLevel: "高",
      maintenanceSchedule: [],
      inventoryChecks: [],
      alerts: [
        {
          id: "alert-1",
          warehouseId: "1",
          alertType: "温度异常",
          severity: "中",
          message: "温度超过设定阈值",
          date: "2024-03-20",
          status: "已处理"
        }
      ],
      hasTemperatureControl: true,
      hasHumidityControl: true,
      hasCameraSystem: true,
      hasFireProtection: true,
      materialCategories: ["建筑材料", "电气设备", "安全设备", "五金工具"],
      usageRate: 0.75,
      description: "公司主要仓储设施，存放多种物资"
    },
    {
      id: "2",
      name: "临时仓库",
      code: "WH002",
      type: "临时仓库",
      area: 800,
      usedArea: 400,
      location: "工业园区B区",
      manager: "李四",
      contact: "13900139000",
      email: "<EMAIL>",
      status: "维护中",
      materialCount: 300,
      lastCheckDate: "2024-03-10",
      nextCheckDate: "2024-04-10",
      sections: [
        {
          id: "2-1",
          name: "临时区",
          code: "WH002-A",
          capacity: 800,
          usedCapacity: 400,
          materialTypes: ["周转材料"],
          status: "正常"
        }
      ],
      turnoverRate: 0.65,
      safetyLevel: "中",
      maintenanceSchedule: [
        {
          id: "maint-1",
          warehouseId: "2",
          date: "2024-03-25",
          maintenanceType: "消防设施检查",
          description: "消防设施检查与维护",
          responsible: "王五",
          status: "计划中",
          nextMaintenanceDate: "2024-04-20"
        }
      ],
      inventoryChecks: [],
      alerts: [],
      hasTemperatureControl: false,
      hasHumidityControl: false,
      hasCameraSystem: true,
      hasFireProtection: true,
      materialCategories: ["周转材料"],
      usageRate: 0.5,
      description: "临时存放周转材料"
    },
    {
      id: "3",
      name: "原材料仓库",
      code: "WH003",
      type: "原材料仓库",
      area: 1500,
      usedArea: 1350,
      location: "工业园区B区",
      manager: "赵六",
      contact: "13700137000",
      email: "<EMAIL>",
      status: "已满",
      materialCount: 850,
      lastCheckDate: "2024-03-05",
      nextCheckDate: "2024-04-05",
      sections: [
        {
          id: "3-1",
          name: "钢材区",
          code: "WH003-A",
          capacity: 800,
          usedCapacity: 780,
          materialTypes: ["钢材"],
          status: "已满"
        },
        {
          id: "3-2",
          name: "水泥区",
          code: "WH003-B",
          capacity: 700,
          usedCapacity: 570,
          materialTypes: ["水泥"],
          status: "正常"
        }
      ],
      turnoverRate: 0.92,
      safetyLevel: "高",
      maintenanceSchedule: [],
      inventoryChecks: [],
      alerts: [
        {
          id: "alert-2",
          warehouseId: "3",
          alertType: "容量预警",
          severity: "高",
          message: "仓库容量即将达到上限",
          date: "2024-03-18",
          status: "未处理"
        }
      ],
      hasTemperatureControl: false,
      hasHumidityControl: true,
      hasCameraSystem: true,
      hasFireProtection: true,
      materialCategories: ["钢材", "水泥"],
      usageRate: 0.9,
      description: "存放建筑原材料"
    }
  ])

  // 计算统计数据
  const calculateStats = (): WarehouseStats => {
    const totalWarehouses = warehouses.length
    const totalMaterials = warehouses.reduce((sum, w) => sum + w.materialCount, 0)
    const warningWarehouses = warehouses.filter(w => 
      w.status === "已满" || 
      w.alerts.some(a => a.status === "未处理") ||
      (w.usedArea / w.area) > 0.9
    ).length
    
    const totalArea = warehouses.reduce((sum, w) => sum + w.area, 0)
    const usedArea = warehouses.reduce((sum, w) => sum + w.usedArea, 0)
    const averageUsage = totalArea > 0 ? Math.round((usedArea / totalArea) * 100) : 0
    
    return {
      totalWarehouses,
      totalMaterials,
      warningWarehouses,
      averageUsage
    }
  }
  
  // 统计数据
  const stats = calculateStats()

  // 模拟数据加载
  const handleRefresh = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // 表单字段验证
  const validateForm = (data: Partial<Warehouse>) => {
    const errors: Record<string, string> = {}
    
    if (!data.name?.trim()) errors.name = "仓库名称不能为空"
    if (!data.code?.trim()) errors.code = "仓库编码不能为空"
    if (!data.location?.trim()) errors.location = "位置不能为空"
    if (!data.manager?.trim()) errors.manager = "负责人不能为空"
    if (!data.contact?.trim()) errors.contact = "联系方式不能为空"
    if (data.area === undefined || data.area <= 0) errors.area = "面积必须大于0"
    if (data.usedArea === undefined) errors.usedArea = "已用面积不能为空"
    if (data.usedArea !== undefined && data.area !== undefined && data.usedArea > data.area) {
      errors.usedArea = "已用面积不能大于总面积"
    }
    
    return errors
  }
  
  // 添加新仓库处理
  const handleAddNewWarehouse = () => {
    const errors = validateForm(newWarehouse)
    if (Object.keys(errors).length > 0) {
      setSearchErrors(errors)
      return
    }
    
    setIsLoading(true)
    // 模拟API请求延迟
    setTimeout(() => {
      // 生成新ID
      const id = (Math.max(0, ...warehouses.map(w => parseInt(w.id))) + 1).toString()
      
      // 创建完整的仓库对象
      const completeWarehouse: Warehouse = {
        id,
        name: newWarehouse.name || "",
        code: newWarehouse.code || "",
        type: newWarehouse.type || "综合仓库",
        area: newWarehouse.area || 0,
        usedArea: newWarehouse.usedArea || 0,
        location: newWarehouse.location || "",
        manager: newWarehouse.manager || "",
        contact: newWarehouse.contact || "",
        email: newWarehouse.email || "",
        status: newWarehouse.status || "正常",
        materialCount: newWarehouse.materialCount || 0,
        lastCheckDate: newWarehouse.lastCheckDate || new Date().toISOString().split('T')[0],
        nextCheckDate: newWarehouse.nextCheckDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        description: newWarehouse.description || "",
        sections: newWarehouse.sections || [],
        turnoverRate: newWarehouse.turnoverRate || 0,
        safetyLevel: newWarehouse.safetyLevel || "中",
        maintenanceSchedule: newWarehouse.maintenanceSchedule || [],
        inventoryChecks: newWarehouse.inventoryChecks || [],
        alerts: newWarehouse.alerts || [],
        hasTemperatureControl: newWarehouse.hasTemperatureControl || false,
        hasHumidityControl: newWarehouse.hasHumidityControl || false,
        hasCameraSystem: newWarehouse.hasCameraSystem || false,
        hasFireProtection: newWarehouse.hasFireProtection || true,
        materialCategories: newWarehouse.materialCategories || [],
        usageRate: newWarehouse.usedArea && newWarehouse.area ? newWarehouse.usedArea / newWarehouse.area : 0
      }
      
      // 更新仓库列表
      setWarehouses([...warehouses, completeWarehouse])
      
      // 重置表单
      setNewWarehouse({
        name: "",
        code: "",
        type: "综合仓库",
        area: 0,
        usedArea: 0,
        location: "",
        manager: "",
        contact: "",
        email: "",
        status: "正常",
        materialCount: 0,
        lastCheckDate: new Date().toISOString().split('T')[0],
        nextCheckDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        description: "",
        sections: [],
        turnoverRate: 0,
        safetyLevel: "中",
        maintenanceSchedule: [],
        inventoryChecks: [],
        alerts: [],
        hasTemperatureControl: false,
        hasHumidityControl: false,
        hasCameraSystem: false,
        hasFireProtection: true,
        materialCategories: [],
        usageRate: 0
      })
      setSearchErrors({})
      setShowAddForm(false)
      setIsLoading(false)
      
      // 显示成功消息
      setSuccessMessage("仓库添加成功！")
      setTimeout(() => setSuccessMessage(""), 3000)
    }, 1000)
  }
  
  // 编辑仓库
  const handleEditWarehouse = (warehouse: Warehouse) => {
    setEditedWarehouse({...warehouse})
    setShowEditForm(true)
  }
  
  // 保存编辑
  const handleSaveEdit = () => {
    if (!editedWarehouse) return
    
    const errors = validateForm(editedWarehouse)
    if (Object.keys(errors).length > 0) {
      setSearchErrors(errors)
      return
    }
    
    setIsLoading(true)
    // 模拟API请求延迟
    setTimeout(() => {
      // 更新仓库列表
      const updatedWarehouses = warehouses.map(w => 
        w.id === editedWarehouse.id ? {
          ...editedWarehouse,
          usageRate: editedWarehouse.usedArea / editedWarehouse.area
        } : w
      )
      
      setWarehouses(updatedWarehouses)
      setSearchErrors({})
      setShowEditForm(false)
      setEditedWarehouse(null)
      setIsLoading(false)
      
      // 显示成功消息
      setSuccessMessage("仓库更新成功！")
      setTimeout(() => setSuccessMessage(""), 3000)
    }, 1000)
  }
  
  // 删除仓库
  const handleDeleteWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse)
    setShowDeleteModal(true)
  }
  
  // 确认删除
  const confirmDelete = () => {
    if (!selectedWarehouse) return
    
    setIsLoading(true)
    // 模拟API请求延迟
    setTimeout(() => {
      // 更新仓库列表
      const filteredWarehouses = warehouses.filter(w => w.id !== selectedWarehouse.id)
      
      setWarehouses(filteredWarehouses)
      setShowDeleteModal(false)
      setSelectedWarehouse(null)
      setIsLoading(false)
      
      // 显示成功消息
      setSuccessMessage("仓库删除成功！")
      setTimeout(() => setSuccessMessage(""), 3000)
    }, 1000)
  }
  
  // 添加分区
  const handleAddSection = () => {
    if (!editedWarehouse) return
    
    const sectionId = editedWarehouse.sections.length > 0 
      ? (Math.max(0, ...editedWarehouse.sections.map(s => parseInt(s.id.split('-')[1]))) + 1).toString()
      : "1"
      
    const newSectionComplete: WarehouseSection = {
      id: `${editedWarehouse.id}-${sectionId}`,
      name: newSection.name || "",
      code: newSection.code || "",
      capacity: newSection.capacity || 0,
      usedCapacity: newSection.usedCapacity || 0,
      materialTypes: newSection.materialTypes || [],
      status: newSection.status || "正常"
    }
    
    // 更新编辑中的仓库
    setEditedWarehouse({
      ...editedWarehouse,
      sections: [...editedWarehouse.sections, newSectionComplete]
    })
    
    // 重置分区表单
    setNewSection({
      name: "",
      code: "",
      capacity: 0,
      usedCapacity: 0,
      materialTypes: [],
      status: "正常"
    })
  }
  
  // 删除分区
  const handleRemoveSection = (sectionId: string) => {
    if (!editedWarehouse) return
    
    // 更新编辑中的仓库
    setEditedWarehouse({
      ...editedWarehouse,
      sections: editedWarehouse.sections.filter(s => s.id !== sectionId)
    })
  }
  
  // 导出数据
  const handleExport = () => {
    setShowExportOptions(!showExportOptions)
  }
  
  // 关闭导出选项
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (exportOptionsRef.current && !exportOptionsRef.current.contains(event.target as Node)) {
        setShowExportOptions(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  
  // 执行导出
  const executeExport = (format: "csv" | "excel" | "pdf") => {
    setIsLoading(true)
    
    // 模拟导出延迟
    setTimeout(() => {
      setIsLoading(false)
      setShowExportOptions(false)
      
      // 根据格式生成不同的文件名
      const timestamp = new Date().toISOString().replace(/[-:\.]/g, "").split("T")[0]
      const fileName = `仓库数据_${timestamp}`
      
      // 在实际应用中，这里会调用API或生成文件
      // 这里只是模拟下载操作
      alert(`已导出为 ${format.toUpperCase()} 格式：${fileName}.${format}`)
      
      // 显示成功消息
      setSuccessMessage(`成功导出${filteredWarehouses.length}条数据为${format.toUpperCase()}格式！`)
      setTimeout(() => setSuccessMessage(""), 3000)
    }, 1000)
  }
  
  // 打印数据
  const handlePrint = () => {
    window.print()
  }
  
  // 查看仓库详情
  const handleViewWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse)
    setShowDetailModal(true)
  }
  
  // 添加新仓库
  const handleAddWarehouse = () => {
    setShowAddForm(true)
  }
  
  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>, setState: any) => {
    const { name, value, type } = e.target
    
    // 处理不同类型的输入
    if (type === 'number') {
      setState((prev: any) => ({ ...prev, [name]: parseFloat(value) || 0 }))
    } else if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      setState((prev: any) => ({ ...prev, [name]: checkbox.checked }))
    } else {
      setState((prev: any) => ({ ...prev, [name]: value }))
    }
    
    // 清除相关字段的错误
    if (searchErrors[name]) {
      setSearchErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  // 处理物资类型标签输入
  const handleMaterialTypesChange = (value: string, setState: any) => {
    if (!value.trim()) return
    
    setState((prev: any) => ({
      ...prev,
      materialTypes: [...(prev.materialTypes || []), value.trim()]
    }))
  }
  
  // 移除物资类型标签
  const handleRemoveMaterialType = (index: number, setState: any, stateKey: string) => {
    setState((prev: any) => ({
      ...prev,
      [stateKey]: prev[stateKey].filter((_: any, i: number) => i !== index)
    }))
  }

  // 过滤数据
  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = 
      warehouse.name.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse.code.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || warehouse.type === selectedType
    const matchesStatus = selectedStatus === "all" || warehouse.status === selectedStatus
    return matchesSearch && matchesType && matchesStatus
  })

  // 状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <span className="px-2 py-1 rounded-full bg-green-100 text-green-800">正常</span>
      case "维护中":
        return <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">维护中</span>
      case "已满":
        return <span className="px-2 py-1 rounded-full bg-red-100 text-red-800">已满</span>
      case "已关闭":
        return <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800">已关闭</span>
      default:
        return <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800">{status}</span>
    }
  }

  // 统计卡片属性类型
  interface StatCardProps {
    title: string
    value: number | string
    icon: React.ReactNode
    color: string
    suffix?: string
  }

  // 统计组件
  const StatCard = ({ title, value, icon, color, suffix = "" }: StatCardProps) => (
    <div className={`bg-white p-4 rounded-lg shadow-sm border-l-4 ${color}`}>
      <div className="flex items-center">
        <div className="mr-4">{icon}</div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-2xl font-bold">
            {value}{suffix}
          </p>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6 bg-gray-50 p-6 min-h-screen">
      {/* 成功/错误消息 */}
      {successMessage && (
        <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50 flex items-center shadow-lg">
          <CheckCircle className="h-5 w-5 mr-2" />
          <span>{successMessage}</span>
          <button 
            onClick={() => setSuccessMessage("")}
            className="ml-4 text-green-700 hover:text-green-900"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
      
      {errorMessage && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50 flex items-center shadow-lg">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>{errorMessage}</span>
          <button 
            onClick={() => setErrorMessage("")}
            className="ml-4 text-red-700 hover:text-red-900"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
      
      {/* 页面标题和操作按钮 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between bg-white p-4 rounded-lg shadow-sm">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">仓库管理</h2>
          <p className="text-gray-500">管理和监控所有仓库状态</p>
        </div>
        <div className="flex flex-wrap gap-2 mt-4 sm:mt-0">
          <button 
            onClick={handleRefresh}
            className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            <span>{isLoading ? "加载中..." : "刷新"}</span>
          </button>
          <div className="relative">
            <button 
              onClick={handleExport}
              className="flex items-center px-3 py-2 bg-green-50 hover:bg-green-100 text-green-700 rounded-md"
            >
            <Download className="h-4 w-4 mr-2" />
              <span>导出</span>
            </button>
            
            {showExportOptions && (
              <div 
                ref={exportOptionsRef}
                className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border"
              >
                <div className="py-1">
                  <button 
                    onClick={() => executeExport("excel")}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <FileText className="h-4 w-4 mr-2 text-green-600" />
                    Excel 格式 (.xlsx)
                  </button>
                  <button 
                    onClick={() => executeExport("csv")}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <FileText className="h-4 w-4 mr-2 text-blue-600" />
                    CSV 格式 (.csv)
                  </button>
                  <button 
                    onClick={() => executeExport("pdf")}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <FileText className="h-4 w-4 mr-2 text-red-600" />
                    PDF 格式 (.pdf)
                  </button>
                </div>
              </div>
            )}
          </div>
          <button 
            onClick={handlePrint}
            className="flex items-center px-3 py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md"
          >
            <Printer className="h-4 w-4 mr-2" />
            <span>打印</span>
          </button>
          <button 
            onClick={handleAddWarehouse}
            className="flex items-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span>添加仓库</span>
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard 
          title="仓库总数" 
          value={stats.totalWarehouses} 
          icon={<WarehouseIcon className="h-8 w-8 text-blue-500" />} 
          color="border-blue-500" 
        />
        <StatCard 
          title="物资总数" 
          value={stats.totalMaterials} 
          icon={<Package className="h-8 w-8 text-green-500" />} 
          color="border-green-500" 
        />
        <StatCard 
          title="预警仓库" 
          value={stats.warningWarehouses} 
          icon={<AlertCircle className="h-8 w-8 text-red-500" />} 
          color="border-red-500" 
        />
        <StatCard 
          title="平均使用率" 
          value={stats.averageUsage} 
          icon={<BarChart2 className="h-8 w-8 text-orange-500" />} 
          color="border-orange-500"
          suffix="%" 
        />
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input 
              type="text" 
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              placeholder="搜索仓库名称或编码..."
              className="pl-10 pr-4 py-2 border rounded-md w-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="all">所有类型</option>
            <option value="综合仓库">综合仓库</option>
            <option value="原材料仓库">原材料仓库</option>
            <option value="成品仓库">成品仓库</option>
            <option value="临时仓库">临时仓库</option>
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="all">所有状态</option>
            <option value="正常">正常</option>
            <option value="维护中">维护中</option>
            <option value="已满">已满</option>
            <option value="已关闭">已关闭</option>
          </select>
          
          <button
            onClick={() => {
              setSearchText("")
              setSelectedType("all")
              setSelectedStatus("all")
            }}
            className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md"
          >
            <Filter className="h-4 w-4 mr-2" />
            <span>重置筛选</span>
          </button>
        </div>
      </div>
      
      {/* 仓库列表 */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-800">仓库列表</h3>
          <p className="text-sm text-gray-500">
            共 <span className="font-semibold">{filteredWarehouses.length}</span> 个仓库
          </p>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-60">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : filteredWarehouses.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <WarehouseIcon className="h-16 w-16 text-gray-300 mb-4" />
            <h4 className="text-lg font-medium text-gray-700">没有找到符合条件的仓库</h4>
            <p className="mt-2 text-gray-500 max-w-md">
              尝试调整搜索或筛选条件，或者添加一个新的仓库
            </p>
            <button 
              onClick={handleAddWarehouse}
              className="mt-4 flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md"
            >
                <Plus className="h-4 w-4 mr-2" />
              <span>添加仓库</span>
            </button>
                  </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredWarehouses.map(warehouse => (
              <div 
                key={warehouse.id} 
                className="border p-5 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer bg-white"
                onClick={() => handleViewWarehouse(warehouse)}
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-800">{warehouse.name}</h4>
                    <p className="text-sm text-gray-500 mt-1">{warehouse.code}</p>
                  </div>
                  {getStatusBadge(warehouse.status)}
                  </div>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <span className="w-20 text-gray-500">类型:</span>
                    <span>{warehouse.type}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-20 text-gray-500">位置:</span>
                    <span>{warehouse.location}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-20 text-gray-500">负责人:</span>
                    <span>{warehouse.manager}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-20 text-gray-500">库容:</span>
                    <div className="flex-grow">
                      <div className="flex justify-between text-xs mb-1">
                        <span>{warehouse.usedArea}/{warehouse.area} m²</span>
                        <span>{Math.round((warehouse.usedArea / warehouse.area) * 100)}%</span>
                  </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            (warehouse.usedArea / warehouse.area) > 0.9 ? 'bg-red-500' : 
                            (warehouse.usedArea / warehouse.area) > 0.7 ? 'bg-yellow-500' : 
                            'bg-green-500'
                          }`}
                          style={{ width: `${Math.min(100, Math.round((warehouse.usedArea / warehouse.area) * 100))}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="w-20 text-gray-500">物资数量:</span>
                    <span>{warehouse.materialCount}</span>
                  </div>
                </div>
                
                <div className="mt-4 pt-3 border-t flex justify-end gap-2">
                  <button 
                    className="px-3 py-1.5 bg-blue-50 text-blue-700 rounded hover:bg-blue-100 text-sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleViewWarehouse(warehouse)
                    }}
                  >
                    查看详情
                  </button>
                  <button 
                    className="px-3 py-1.5 bg-indigo-50 text-indigo-700 rounded hover:bg-indigo-100 text-sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEditWarehouse(warehouse)
                    }}
                  >
                    编辑
                  </button>
                  <button 
                    className="px-3 py-1.5 bg-red-50 text-red-700 rounded hover:bg-red-100 text-sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteWarehouse(warehouse)
                    }}
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* 仓库详情模态框 - 在实际实现中需要添加 */}
      {showDetailModal && selectedWarehouse && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-2xl font-bold text-gray-800">{selectedWarehouse.name}</h3>
                  <p className="text-gray-500">{selectedWarehouse.code}</p>
                </div>
                {getStatusBadge(selectedWarehouse.status)}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <h4 className="text-lg font-semibold mb-2">基本信息</h4>
                  <div className="space-y-2">
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">仓库类型:</span>
                      <span>{selectedWarehouse.type}</span>
                  </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">总面积:</span>
                      <span>{selectedWarehouse.area} m²</span>
                </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">已用面积:</span>
                      <span>{selectedWarehouse.usedArea} m²</span>
                    </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">位置:</span>
                      <span>{selectedWarehouse.location}</span>
                    </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">安全级别:</span>
                      <span>{selectedWarehouse.safetyLevel}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-2">联系信息</h4>
                <div className="space-y-2">
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">负责人:</span>
                      <span>{selectedWarehouse.manager}</span>
                </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">联系电话:</span>
                      <span>{selectedWarehouse.contact}</span>
              </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">电子邮箱:</span>
                      <span>{selectedWarehouse.email}</span>
                    </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">上次盘点:</span>
                      <span>{selectedWarehouse.lastCheckDate}</span>
                    </div>
                    <div className="flex">
                      <span className="w-1/3 text-gray-500">下次盘点:</span>
                      <span>{selectedWarehouse.nextCheckDate}</span>
                    </div>
                  </div>
        </div>
      </div>

              {selectedWarehouse.description && (
                <div className="mt-6">
                  <h4 className="text-lg font-semibold mb-2">描述</h4>
                  <p className="text-gray-700">{selectedWarehouse.description}</p>
                  </div>
              )}
              
              <div className="mt-6">
                <h4 className="text-lg font-semibold mb-2">分区信息</h4>
                {selectedWarehouse.sections.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedWarehouse.sections.map(section => (
                      <div key={section.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                  <div>
                            <h5 className="font-semibold">{section.name}</h5>
                            <p className="text-sm text-gray-500">{section.code}</p>
                  </div>
                          {getStatusBadge(section.status)}
                </div>
                        <div className="space-y-1 text-sm">
                          <p>容量: {section.usedCapacity}/{section.capacity} m²</p>
                          <p>物资类型: {section.materialTypes.join(", ")}</p>
              </div>
                      </div>
        ))}
                  </div>
                ) : (
                  <p className="text-gray-500">暂无分区信息</p>
                )}
      </div>

              <div className="mt-8 flex justify-end">
                <button 
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2"
                  onClick={() => setShowDetailModal(false)}
                >
                  关闭
                </button>
                <button 
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md mr-2"
                  onClick={() => {
                    setShowDetailModal(false)
                    handleEditWarehouse(selectedWarehouse)
                  }}
                >
                  编辑仓库
                </button>
                <button 
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                  onClick={() => {
                    setShowDetailModal(false)
                    handleDeleteWarehouse(selectedWarehouse)
                  }}
                >
                  删除仓库
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && selectedWarehouse && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4 text-red-600">
                <AlertCircle className="h-6 w-6 mr-2" />
                <h3 className="text-xl font-bold">确认删除</h3>
              </div>
              
              <p className="text-gray-700 mb-6">
                确定要删除仓库 <span className="font-semibold">{selectedWarehouse.name}</span> 吗？此操作无法撤销。
              </p>
              
              <div className="flex justify-end">
                <button 
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2"
                  onClick={() => setShowDeleteModal(false)}
                >
                  取消
                </button>
                <button 
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                  onClick={confirmDelete}
                  disabled={isLoading}
                >
                  {isLoading ? "删除中..." : "确认删除"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加仓库表单 */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center border-b pb-3 mb-4">
                <h3 className="text-xl font-bold text-gray-800">添加新仓库</h3>
                <button 
                  onClick={() => setShowAddForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                  <h4 className="text-lg font-semibold mb-4">基本信息</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={newWarehouse.name}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.name ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.name && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.name}</p>
                      )}
            </div>
                    
                    <div>
                      <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库编码 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="code"
                        name="code"
                        value={newWarehouse.code}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.code ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.code && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.code}</p>
                      )}
          </div>
                    
                    <div>
                      <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库类型
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={newWarehouse.type}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="综合仓库">综合仓库</option>
                        <option value="原材料仓库">原材料仓库</option>
                        <option value="成品仓库">成品仓库</option>
                        <option value="临时仓库">临时仓库</option>
                      </select>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="area" className="block text-sm font-medium text-gray-700 mb-1">
                          总面积 (m²) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          id="area"
                          name="area"
                          value={newWarehouse.area}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                            searchErrors.area ? 'border-red-500' : 'border-gray-300'
                          }`}
                        />
                        {searchErrors.area && (
                          <p className="mt-1 text-sm text-red-500">{searchErrors.area}</p>
                        )}
              </div>
                      
                      <div>
                        <label htmlFor="usedArea" className="block text-sm font-medium text-gray-700 mb-1">
                          已用面积 (m²) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          id="usedArea"
                          name="usedArea"
                          value={newWarehouse.usedArea}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                            searchErrors.usedArea ? 'border-red-500' : 'border-gray-300'
                          }`}
                        />
                        {searchErrors.usedArea && (
                          <p className="mt-1 text-sm text-red-500">{searchErrors.usedArea}</p>
                        )}
            </div>
          </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        位置 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={newWarehouse.location}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.location ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.location && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.location}</p>
                      )}
                      </div>
                    
                    <div>
                      <label htmlFor="safetyLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        安全级别
                      </label>
                      <select
                        id="safetyLevel"
                        name="safetyLevel"
                        value={newWarehouse.safetyLevel}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="高">高</option>
                        <option value="中">中</option>
                        <option value="低">低</option>
                      </select>
                      </div>
                    
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                        状态
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={newWarehouse.status}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="正常">正常</option>
                        <option value="维护中">维护中</option>
                        <option value="已满">已满</option>
                        <option value="已关闭">已关闭</option>
                      </select>
                      </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-4">联系与附加信息</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="manager" className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="manager"
                        name="manager"
                        value={newWarehouse.manager}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.manager ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.manager && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.manager}</p>
                      )}
                        </div>
                    
                    <div>
                      <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-1">
                        联系电话 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="contact"
                        name="contact"
                        value={newWarehouse.contact}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.contact ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.contact && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.contact}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        电子邮箱
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={newWarehouse.email}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="lastCheckDate" className="block text-sm font-medium text-gray-700 mb-1">
                          上次盘点日期
                        </label>
                        <input
                          type="date"
                          id="lastCheckDate"
                          name="lastCheckDate"
                          value={newWarehouse.lastCheckDate}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="nextCheckDate" className="block text-sm font-medium text-gray-700 mb-1">
                          下次盘点日期
                        </label>
                        <input
                          type="date"
                          id="nextCheckDate"
                          name="nextCheckDate"
                          value={newWarehouse.nextCheckDate}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="materialCount" className="block text-sm font-medium text-gray-700 mb-1">
                        物资数量
                      </label>
                      <input
                        type="number"
                        id="materialCount"
                        name="materialCount"
                        value={newWarehouse.materialCount}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        描述
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={3}
                        value={newWarehouse.description}
                        onChange={(e) => handleInputChange(e, setNewWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="hasTemperatureControl"
                          name="hasTemperatureControl"
                          checked={newWarehouse.hasTemperatureControl}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="hasTemperatureControl" className="ml-2 text-sm text-gray-700">
                          温度控制
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="hasHumidityControl"
                          name="hasHumidityControl"
                          checked={newWarehouse.hasHumidityControl}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="hasHumidityControl" className="ml-2 text-sm text-gray-700">
                          湿度控制
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="hasCameraSystem"
                          name="hasCameraSystem"
                          checked={newWarehouse.hasCameraSystem}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="hasCameraSystem" className="ml-2 text-sm text-gray-700">
                          监控系统
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="hasFireProtection"
                          name="hasFireProtection"
                          checked={newWarehouse.hasFireProtection}
                          onChange={(e) => handleInputChange(e, setNewWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="hasFireProtection" className="ml-2 text-sm text-gray-700">
                          消防系统
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end">
                <button 
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2"
                  onClick={() => {
                    setShowAddForm(false)
                    setSearchErrors({})
                  }}
                >
                  取消
                </button>
                <button 
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md"
                  onClick={handleAddNewWarehouse}
                  disabled={isLoading}
                >
                  {isLoading ? "添加中..." : "添加仓库"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 编辑仓库表单 */}
      {showEditForm && editedWarehouse && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center border-b pb-3 mb-4">
                <h3 className="text-xl font-bold text-gray-800">编辑仓库信息</h3>
                <button 
                  onClick={() => {
                    setShowEditForm(false)
                    setSearchErrors({})
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold mb-4">基本信息</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="edit-name"
                        name="name"
                        value={editedWarehouse.name}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.name ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.name && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.name}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="edit-code" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库编码 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="edit-code"
                        name="code"
                        value={editedWarehouse.code}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.code ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.code && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.code}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="edit-type" className="block text-sm font-medium text-gray-700 mb-1">
                        仓库类型
                      </label>
                      <select
                        id="edit-type"
                        name="type"
                        value={editedWarehouse.type}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="综合仓库">综合仓库</option>
                        <option value="原材料仓库">原材料仓库</option>
                        <option value="成品仓库">成品仓库</option>
                        <option value="临时仓库">临时仓库</option>
                      </select>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="edit-area" className="block text-sm font-medium text-gray-700 mb-1">
                          总面积 (m²) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          id="edit-area"
                          name="area"
                          value={editedWarehouse.area}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                            searchErrors.area ? 'border-red-500' : 'border-gray-300'
                          }`}
                        />
                        {searchErrors.area && (
                          <p className="mt-1 text-sm text-red-500">{searchErrors.area}</p>
                        )}
                      </div>
                      
                      <div>
                        <label htmlFor="edit-usedArea" className="block text-sm font-medium text-gray-700 mb-1">
                          已用面积 (m²) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          id="edit-usedArea"
                          name="usedArea"
                          value={editedWarehouse.usedArea}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                            searchErrors.usedArea ? 'border-red-500' : 'border-gray-300'
                          }`}
                        />
                        {searchErrors.usedArea && (
                          <p className="mt-1 text-sm text-red-500">{searchErrors.usedArea}</p>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="edit-location" className="block text-sm font-medium text-gray-700 mb-1">
                        位置 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="edit-location"
                        name="location"
                        value={editedWarehouse.location}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.location ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.location && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.location}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="edit-safetyLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        安全级别
                      </label>
                      <select
                        id="edit-safetyLevel"
                        name="safetyLevel"
                        value={editedWarehouse.safetyLevel}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="高">高</option>
                        <option value="中">中</option>
                        <option value="低">低</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="edit-status" className="block text-sm font-medium text-gray-700 mb-1">
                        状态
                      </label>
                      <select
                        id="edit-status"
                        name="status"
                        value={editedWarehouse.status}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="正常">正常</option>
                        <option value="维护中">维护中</option>
                        <option value="已满">已满</option>
                        <option value="已关闭">已关闭</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <h4 className="text-lg font-semibold mb-4">仓库分区</h4>
                    
                    {editedWarehouse.sections.length > 0 ? (
                      <div className="space-y-3 mb-4">
                        {editedWarehouse.sections.map((section, index) => (
                          <div key={section.id} className="flex items-start border p-3 rounded-md">
                            <div className="flex-grow">
                              <div className="flex items-center mb-1">
                                <h5 className="font-semibold">{section.name}</h5>
                                <span className="text-sm text-gray-500 ml-2">({section.code})</span>
                              </div>
                              <p className="text-sm text-gray-600">
                                容量: {section.usedCapacity}/{section.capacity} m²
                              </p>
                              <div className="flex flex-wrap mt-1">
                                {section.materialTypes.map((type, typeIndex) => (
                                  <span 
                                    key={typeIndex} 
                                    className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded mr-1 mb-1"
                                  >
                                    {type}
                        </span>
                                ))}
                      </div>
                            </div>
                            <button 
                              onClick={() => handleRemoveSection(section.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
          </div>
                    ) : (
                      <p className="text-gray-500 mb-4">暂无分区数据，请添加。</p>
                    )}
                    
                    <div className="border p-3 rounded-md bg-gray-50">
                      <h5 className="font-medium mb-2">添加新分区</h5>
                      
                      <div className="grid grid-cols-2 gap-3 mb-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">分区名称</label>
                          <input
                            type="text"
                            name="name"
                            value={newSection.name}
                            onChange={(e) => handleInputChange(e, setNewSection)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">分区编码</label>
                          <input
                            type="text"
                            name="code"
                            value={newSection.code}
                            onChange={(e) => handleInputChange(e, setNewSection)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3 mb-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">总容量 (m²)</label>
                          <input
                            type="number"
                            name="capacity"
                            value={newSection.capacity}
                            onChange={(e) => handleInputChange(e, setNewSection)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">已用容量 (m²)</label>
                          <input
                            type="number"
                            name="usedCapacity"
                            value={newSection.usedCapacity}
                            onChange={(e) => handleInputChange(e, setNewSection)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                          />
                        </div>
                      </div>
                      
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">物资类型</label>
                        <div className="flex flex-wrap mb-2">
                          {newSection.materialTypes?.map((type, index) => (
                            <span 
                              key={index} 
                              className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded mr-1 mb-1 flex items-center"
                            >
                              {type}
                              <button 
                                onClick={() => handleRemoveMaterialType(index, setNewSection, 'materialTypes')}
                                className="ml-1 text-gray-500 hover:text-gray-700"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                        
                        <div className="flex">
                          <input
                            type="text"
                            id="materialType"
                            placeholder="输入物资类型并按回车添加"
                            className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault()
                                const input = e.target as HTMLInputElement
                                handleMaterialTypesChange(input.value, setNewSection)
                                input.value = ''
                              }
                            }}
                          />
                          <button
                            className="px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-r-md"
                            onClick={() => {
                              const input = document.getElementById('materialType') as HTMLInputElement
                              handleMaterialTypesChange(input.value, setNewSection)
                              input.value = ''
                            }}
                          >
                            添加
                          </button>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                        <select
                          name="status"
                          value={newSection.status}
                          onChange={(e) => handleInputChange(e, setNewSection)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                        >
                          <option value="正常">正常</option>
                          <option value="维护中">维护中</option>
                          <option value="已满">已满</option>
                          <option value="已关闭">已关闭</option>
                        </select>
                      </div>
                      
                      <div className="mt-3 flex justify-end">
                        <button
                          className="px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm"
                          onClick={handleAddSection}
                        >
                          添加分区
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-4">联系与附加信息</h4>
                  
          <div className="space-y-4">
                    <div>
                      <label htmlFor="edit-manager" className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="edit-manager"
                        name="manager"
                        value={editedWarehouse.manager}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.manager ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.manager && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.manager}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="edit-contact" className="block text-sm font-medium text-gray-700 mb-1">
                        联系电话 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="edit-contact"
                        name="contact"
                        value={editedWarehouse.contact}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
                          searchErrors.contact ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {searchErrors.contact && (
                        <p className="mt-1 text-sm text-red-500">{searchErrors.contact}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="edit-email" className="block text-sm font-medium text-gray-700 mb-1">
                        电子邮箱
                      </label>
                      <input
                        type="email"
                        id="edit-email"
                        name="email"
                        value={editedWarehouse.email}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
                    </div>
                    
            <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="edit-lastCheckDate" className="block text-sm font-medium text-gray-700 mb-1">
                          上次盘点日期
                        </label>
                        <input
                          type="date"
                          id="edit-lastCheckDate"
                          name="lastCheckDate"
                          value={editedWarehouse.lastCheckDate}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
              </div>
                      
                      <div>
                        <label htmlFor="edit-nextCheckDate" className="block text-sm font-medium text-gray-700 mb-1">
                          下次盘点日期
                        </label>
                        <input
                          type="date"
                          id="edit-nextCheckDate"
                          name="nextCheckDate"
                          value={editedWarehouse.nextCheckDate}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
              </div>
              </div>
                    
                    <div>
                      <label htmlFor="edit-materialCount" className="block text-sm font-medium text-gray-700 mb-1">
                        物资数量
                      </label>
                      <input
                        type="number"
                        id="edit-materialCount"
                        name="materialCount"
                        value={editedWarehouse.materialCount}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
              </div>
                    
                    <div>
                      <label htmlFor="edit-description" className="block text-sm font-medium text-gray-700 mb-1">
                        描述
                      </label>
                      <textarea
                        id="edit-description"
                        name="description"
                        rows={3}
                        value={editedWarehouse.description}
                        onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      />
            </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="edit-hasTemperatureControl"
                          name="hasTemperatureControl"
                          checked={editedWarehouse.hasTemperatureControl}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="edit-hasTemperatureControl" className="ml-2 text-sm text-gray-700">
                          温度控制
                        </label>
            </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="edit-hasHumidityControl"
                          name="hasHumidityControl"
                          checked={editedWarehouse.hasHumidityControl}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="edit-hasHumidityControl" className="ml-2 text-sm text-gray-700">
                          湿度控制
                        </label>
          </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="edit-hasCameraSystem"
                          name="hasCameraSystem"
                          checked={editedWarehouse.hasCameraSystem}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="edit-hasCameraSystem" className="ml-2 text-sm text-gray-700">
                          监控系统
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="edit-hasFireProtection"
                          name="hasFireProtection"
                          checked={editedWarehouse.hasFireProtection}
                          onChange={(e) => handleInputChange(e, setEditedWarehouse)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label htmlFor="edit-hasFireProtection" className="ml-2 text-sm text-gray-700">
                          消防系统
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end">
                <button 
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md mr-2"
                  onClick={() => {
                    setShowEditForm(false)
                    setSearchErrors({})
                  }}
                >
                  取消
                </button>
                <button 
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md"
                  onClick={handleSaveEdit}
                  disabled={isLoading}
                >
                  {isLoading ? "保存中..." : "保存修改"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 
