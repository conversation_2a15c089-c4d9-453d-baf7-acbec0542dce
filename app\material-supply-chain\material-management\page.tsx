"use client"

import { MaterialManagement } from "@/components/material-management"
import { MainLayout } from "@/components/main-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"
import { useRouter } from "next/navigation"

export default function MaterialManagementPage() {
  const router = useRouter()

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={() => router.push("/")}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-2xl font-bold">物资维护和周转材料管理</h2>
        </div>
        <MaterialManagement />
      </div>
    </MainLayout>
  )
} 