"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  AlertTriangle,
  CheckCircle
} from "lucide-react"

interface InspectionRecord {
  id: string
  date: string
  location: string
  inspector: string
  type: string
  status: string
  findings: string
  actions: string
}

export function SecurityDeptInspection() {
  // 初始检查记录数据
  const initialInspections: InspectionRecord[] = [
    {
      id: "1",
      date: "2025-03-01",
      location: "A区生产车间",
      inspector: "张三",
      type: "日常检查",
      status: "已完成",
      findings: "发现安全通道堵塞问题",
      actions: "已要求相关部门立即清理"
    },
    {
      id: "2",
      date: "2025-03-02",
      location: "B区办公楼",
      inspector: "李四",
      type: "专项检查",
      status: "待处理",
      findings: "消防设备未按时检修",
      actions: "安排下周进行检修"
    },
    {
      id: "3",
      date: "2025-03-03",
      location: "C区仓库",
      inspector: "王五",
      type: "安全隐患排查",
      status: "已完成",
      findings: "无重大安全隐患",
      actions: "继续保持"
    },
    {
      id: "4",
      date: "2025-03-04",
      location: "D区宿舍",
      inspector: "赵六",
      type: "消防检查",
      status: "进行中",
      findings: "部分消防栓水压不足",
      actions: "正在联系维修"
    },
  ]

  // 状态管理
  const [inspections, setInspections] = useState<InspectionRecord[]>(initialInspections)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentInspection, setCurrentInspection] = useState<InspectionRecord>({
    id: "",
    date: "",
    location: "",
    inspector: "",
    type: "",
    status: "",
    findings: "",
    actions: ""
  })

  // 过滤检查记录
  const filteredInspections = inspections.filter(
    (inspection) =>
      inspection.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      inspection.inspector.toLowerCase().includes(searchTerm.toLowerCase()) ||
      inspection.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      inspection.findings.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 添加检查记录
  const handleAddInspection = () => {
    const newInspection = {
      ...currentInspection,
      id: (inspections.length + 1).toString()
    }
    setInspections([...inspections, newInspection])
    setCurrentInspection({
      id: "",
      date: "",
      location: "",
      inspector: "",
      type: "",
      status: "",
      findings: "",
      actions: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑检查记录
  const handleEditInspection = () => {
    const updatedInspections = inspections.map((inspection) =>
      inspection.id === currentInspection.id ? currentInspection : inspection
    )
    setInspections(updatedInspections)
    setCurrentInspection({
      id: "",
      date: "",
      location: "",
      inspector: "",
      type: "",
      status: "",
      findings: "",
      actions: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除检查记录
  const handleDeleteInspection = () => {
    const updatedInspections = inspections.filter(
      (inspection) => inspection.id !== currentInspection.id
    )
    setInspections(updatedInspections)
    setCurrentInspection({
      id: "",
      date: "",
      location: "",
      inspector: "",
      type: "",
      status: "",
      findings: "",
      actions: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (inspection: InspectionRecord) => {
    setCurrentInspection(inspection)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (inspection: InspectionRecord) => {
    setCurrentInspection(inspection)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "待处理":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">待处理</Badge>
      case "进行中":
        return <Badge variant="secondary">进行中</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">保卫部检查管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加检查记录
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加新检查记录</DialogTitle>
              <DialogDescription>
                请填写新检查记录的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">检查日期</Label>
                  <Input
                    id="date"
                    type="date"
                    value={currentInspection.date}
                    onChange={(e) => setCurrentInspection({ ...currentInspection, date: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">检查地点</Label>
                  <Input
                    id="location"
                    value={currentInspection.location}
                    onChange={(e) => setCurrentInspection({ ...currentInspection, location: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="inspector">检查人员</Label>
                  <Input
                    id="inspector"
                    value={currentInspection.inspector}
                    onChange={(e) => setCurrentInspection({ ...currentInspection, inspector: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">检查类型</Label>
                  <Select
                    value={currentInspection.type}
                    onValueChange={(value) => setCurrentInspection({ ...currentInspection, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择检查类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="日常检查">日常检查</SelectItem>
                      <SelectItem value="专项检查">专项检查</SelectItem>
                      <SelectItem value="安全隐患排查">安全隐患排查</SelectItem>
                      <SelectItem value="消防检查">消防检查</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select
                  value={currentInspection.status}
                  onValueChange={(value) => setCurrentInspection({ ...currentInspection, status: value })}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="待处理">待处理</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="findings">检查发现</Label>
                <Textarea
                  id="findings"
                  value={currentInspection.findings}
                  onChange={(e) => setCurrentInspection({ ...currentInspection, findings: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="actions">处理措施</Label>
                <Textarea
                  id="actions"
                  value={currentInspection.actions}
                  onChange={(e) => setCurrentInspection({ ...currentInspection, actions: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddInspection}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索检查记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredInspections.map((inspection) => (
          <Card key={inspection.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{inspection.location}</CardTitle>
              {getStatusBadge(inspection.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  {inspection.date}
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  {inspection.inspector}
                </div>
                <div className="flex items-center text-sm">
                  <FileText className="h-4 w-4 mr-2 text-gray-500" />
                  {inspection.type}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">检查发现:</div>
                  <div className="text-gray-500 text-xs mt-1">{inspection.findings}</div>
                </div>
              </div>
            </CardContent>
            <div className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(inspection)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(inspection)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>检查记录列表</CardTitle>
          <CardDescription>管理所有保卫部检查记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>检查地点</TableHead>
                <TableHead>检查人员</TableHead>
                <TableHead>检查类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>检查发现</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInspections.map((inspection) => (
                <TableRow key={inspection.id}>
                  <TableCell>{inspection.date}</TableCell>
                  <TableCell>{inspection.location}</TableCell>
                  <TableCell>{inspection.inspector}</TableCell>
                  <TableCell>{inspection.type}</TableCell>
                  <TableCell>{getStatusBadge(inspection.status)}</TableCell>
                  <TableCell className="max-w-xs truncate">{inspection.findings}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(inspection)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(inspection)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑检查记录</DialogTitle>
            <DialogDescription>
              修改检查记录的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-date">检查日期</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={currentInspection.date}
                  onChange={(e) => setCurrentInspection({ ...currentInspection, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">检查地点</Label>
                <Input
                  id="edit-location"
                  value={currentInspection.location}
                  onChange={(e) => setCurrentInspection({ ...currentInspection, location: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-inspector">检查人员</Label>
                <Input
                  id="edit-inspector"
                  value={currentInspection.inspector}
                  onChange={(e) => setCurrentInspection({ ...currentInspection, inspector: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">检查类型</Label>
                <Select
                  value={currentInspection.type}
                  onValueChange={(value) => setCurrentInspection({ ...currentInspection, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择检查类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="日常检查">日常检查</SelectItem>
                    <SelectItem value="专项检查">专项检查</SelectItem>
                    <SelectItem value="安全隐患排查">安全隐患排查</SelectItem>
                    <SelectItem value="消防检查">消防检查</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-status">状态</Label>
              <Select
                value={currentInspection.status}
                onValueChange={(value) => setCurrentInspection({ ...currentInspection, status: value })}
              >
                <SelectTrigger id="edit-status">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="已完成">已完成</SelectItem>
                  <SelectItem value="待处理">待处理</SelectItem>
                  <SelectItem value="进行中">进行中</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-findings">检查发现</Label>
              <Textarea
                id="edit-findings"
                value={currentInspection.findings}
                onChange={(e) => setCurrentInspection({ ...currentInspection, findings: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-actions">处理措施</Label>
              <Textarea
                id="edit-actions"
                value={currentInspection.actions}
                onChange={(e) => setCurrentInspection({ ...currentInspection, actions: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditInspection}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentInspection.location}" 的检查记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteInspection}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
