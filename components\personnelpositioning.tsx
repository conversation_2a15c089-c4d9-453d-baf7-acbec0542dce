"use client"

import { useState } from "react"
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash, 
  Search, 
  MapPin,
  User,
  Clock,
  AlertTriangle,
  CheckCircle,
  Wifi,
  Battery,
  RefreshCw
} from "lucide-react"

interface PersonnelPosition {
  id: string
  name: string
  employeeId: string
  department: string
  position: string
  area: string
  deviceId: string
  batteryStatus: string
  signalStrength: string
  lastUpdate: string
  status: string
}

export function PersonnelPositioning() {
  // 初始人员定位数据
  const initialPositions: PersonnelPosition[] = [
    { 
      id: "1", 
      name: "张三", 
      employeeId: "EMP001", 
      department: "采矿部", 
      position: "矿工", 
      area: "A区矿井", 
      deviceId: "DEV-A001", 
      batteryStatus: "85%", 
      signalStrength: "强", 
      lastUpdate: "2024-03-13 08:30:45", 
      status: "在线" 
    },
    { 
      id: "2", 
      name: "李四", 
      employeeId: "EMP002", 
      department: "安全部", 
      position: "安全员", 
      area: "B区隧道", 
      deviceId: "DEV-B002", 
      batteryStatus: "62%", 
      signalStrength: "中", 
      lastUpdate: "2024-03-13 08:25:12", 
      status: "在线" 
    },
    { 
      id: "3", 
      name: "王五", 
      employeeId: "EMP003", 
      department: "机电部", 
      position: "机修工", 
      area: "C区维修间", 
      deviceId: "DEV-C003", 
      batteryStatus: "45%", 
      signalStrength: "弱", 
      lastUpdate: "2024-03-13 07:50:33", 
      status: "离线" 
    },
    { 
      id: "4", 
      name: "赵六", 
      employeeId: "EMP004", 
      department: "运输部", 
      position: "司机", 
      area: "D区运输通道", 
      deviceId: "DEV-D004", 
      batteryStatus: "90%", 
      signalStrength: "强", 
      lastUpdate: "2024-03-13 08:28:55", 
      status: "在线" 
    },
    { 
      id: "5", 
      name: "钱七", 
      employeeId: "EMP005", 
      department: "通风部", 
      position: "通风工", 
      area: "E区风井", 
      deviceId: "DEV-E005", 
      batteryStatus: "30%", 
      signalStrength: "弱", 
      lastUpdate: "2024-03-13 08:10:22", 
      status: "警告" 
    },
  ]

  // 状态管理
  const [positions, setPositions] = useState<PersonnelPosition[]>(initialPositions)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentPosition, setCurrentPosition] = useState<PersonnelPosition>({
    id: "",
    name: "",
    employeeId: "",
    department: "",
    position: "",
    area: "",
    deviceId: "",
    batteryStatus: "",
    signalStrength: "",
    lastUpdate: "",
    status: ""
  })

  // 过滤人员定位数据
  const filteredPositions = positions.filter(
    (position) =>
      position.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      position.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      position.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      position.area.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 添加人员定位记录
  const handleAddPosition = () => {
    const newPosition = {
      ...currentPosition,
      id: (positions.length + 1).toString(),
      lastUpdate: new Date().toLocaleString()
    }
    setPositions([...positions, newPosition])
    setCurrentPosition({
      id: "",
      name: "",
      employeeId: "",
      department: "",
      position: "",
      area: "",
      deviceId: "",
      batteryStatus: "",
      signalStrength: "",
      lastUpdate: "",
      status: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑人员定位记录
  const handleEditPosition = () => {
    const updatedPositions = positions.map((position) =>
      position.id === currentPosition.id ? {
        ...currentPosition,
        lastUpdate: new Date().toLocaleString()
      } : position
    )
    setPositions(updatedPositions)
    setCurrentPosition({
      id: "",
      name: "",
      employeeId: "",
      department: "",
      position: "",
      area: "",
      deviceId: "",
      batteryStatus: "",
      signalStrength: "",
      lastUpdate: "",
      status: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除人员定位记录
  const handleDeletePosition = () => {
    const updatedPositions = positions.filter(
      (position) => position.id !== currentPosition.id
    )
    setPositions(updatedPositions)
    setCurrentPosition({
      id: "",
      name: "",
      employeeId: "",
      department: "",
      position: "",
      area: "",
      deviceId: "",
      batteryStatus: "",
      signalStrength: "",
      lastUpdate: "",
      status: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (position: PersonnelPosition) => {
    setCurrentPosition(position)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (position: PersonnelPosition) => {
    setCurrentPosition(position)
    setIsDeleteDialogOpen(true)
  }

  // 刷新所有人员位置
  const refreshAllPositions = () => {
    // 在实际应用中，这里会调用API获取最新位置
    // 这里仅模拟更新时间
    const updatedPositions = positions.map(position => ({
      ...position,
      lastUpdate: new Date().toLocaleString()
    }))
    setPositions(updatedPositions)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "在线":
        return <Badge className="bg-green-500">在线</Badge>
      case "离线":
        return <Badge variant="outline" className="text-gray-500 border-gray-500">离线</Badge>
      case "警告":
        return <Badge variant="destructive">警告</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取电池状态对应的图标
  const getBatteryIcon = (status: string) => {
    const percentage = parseInt(status.replace('%', ''))
    if (percentage > 70) {
      return <Battery className="h-4 w-4 text-green-500" />
    } else if (percentage > 30) {
      return <Battery className="h-4 w-4 text-yellow-500" />
    } else {
      return <Battery className="h-4 w-4 text-red-500" />
    }
  }

  // 获取信号强度对应的图标
  const getSignalIcon = (strength: string) => {
    switch (strength) {
      case "强":
        return <Wifi className="h-4 w-4 text-green-500" />
      case "中":
        return <Wifi className="h-4 w-4 text-yellow-500" />
      case "弱":
        return <Wifi className="h-4 w-4 text-red-500" />
      default:
        return <Wifi className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">人员定位管理</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={refreshAllPositions}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新位置
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加人员
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加人员定位</DialogTitle>
                <DialogDescription>
                  添加新的人员定位信息
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">姓名</Label>
                    <Input
                      id="name"
                      value={currentPosition.name}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="employeeId">工号</Label>
                    <Input
                      id="employeeId"
                      value={currentPosition.employeeId}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, employeeId: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="department">部门</Label>
                    <Input
                      id="department"
                      value={currentPosition.department}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, department: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="position">职位</Label>
                    <Input
                      id="position"
                      value={currentPosition.position}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, position: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="area">区域</Label>
                    <Input
                      id="area"
                      value={currentPosition.area}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, area: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deviceId">设备ID</Label>
                    <Input
                      id="deviceId"
                      value={currentPosition.deviceId}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, deviceId: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="batteryStatus">电池状态</Label>
                    <Input
                      id="batteryStatus"
                      value={currentPosition.batteryStatus}
                      onChange={(e) => setCurrentPosition({ ...currentPosition, batteryStatus: e.target.value })}
                      placeholder="例如: 85%"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="signalStrength">信号强度</Label>
                    <Select
                      value={currentPosition.signalStrength}
                      onValueChange={(value) => setCurrentPosition({ ...currentPosition, signalStrength: value })}
                    >
                      <SelectTrigger id="signalStrength">
                        <SelectValue placeholder="选择信号强度" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="强">强</SelectItem>
                        <SelectItem value="中">中</SelectItem>
                        <SelectItem value="弱">弱</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={currentPosition.status}
                    onValueChange={(value) => setCurrentPosition({ ...currentPosition, status: value })}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="在线">在线</SelectItem>
                      <SelectItem value="离线">离线</SelectItem>
                      <SelectItem value="警告">警告</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddPosition}>确认添加</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索人员..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredPositions.map((position) => (
          <Card key={position.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <User className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{position.name}</CardTitle>
              </div>
              {getStatusBadge(position.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                  {position.area}
                </div>
                <div className="flex items-center text-sm">
                  <div className="text-gray-500 mr-2">工号:</div>
                  {position.employeeId}
                </div>
                <div className="flex items-center text-sm">
                  <div className="text-gray-500 mr-2">部门:</div>
                  {position.department} - {position.position}
                </div>
                <div className="flex items-center text-sm">
                  <div className="text-gray-500 mr-2">设备:</div>
                  {position.deviceId}
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <div className="flex items-center">
                    {getBatteryIcon(position.batteryStatus)}
                    <span className="ml-1">{position.batteryStatus}</span>
                  </div>
                  <div className="flex items-center">
                    {getSignalIcon(position.signalStrength)}
                    <span className="ml-1">{position.signalStrength}</span>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  <Clock className="h-3 w-3 inline mr-1" />
                  最后更新: {position.lastUpdate}
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(position)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(position)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>人员定位列表</CardTitle>
          <CardDescription>实时监控所有人员位置信息</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>姓名</TableHead>
                <TableHead>工号</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>职位</TableHead>
                <TableHead>区域</TableHead>
                <TableHead>设备ID</TableHead>
                <TableHead>电池状态</TableHead>
                <TableHead>信号强度</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>最后更新</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPositions.map((position) => (
                <TableRow key={position.id}>
                  <TableCell>{position.name}</TableCell>
                  <TableCell>{position.employeeId}</TableCell>
                  <TableCell>{position.department}</TableCell>
                  <TableCell>{position.position}</TableCell>
                  <TableCell>{position.area}</TableCell>
                  <TableCell>{position.deviceId}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {getBatteryIcon(position.batteryStatus)}
                      <span className="ml-1">{position.batteryStatus}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {getSignalIcon(position.signalStrength)}
                      <span className="ml-1">{position.signalStrength}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(position.status)}</TableCell>
                  <TableCell>{position.lastUpdate}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(position)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(position)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑人员定位</DialogTitle>
            <DialogDescription>
              修改人员定位信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">姓名</Label>
                <Input
                  id="edit-name"
                  value={currentPosition.name}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-employeeId">工号</Label>
                <Input
                  id="edit-employeeId"
                  value={currentPosition.employeeId}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, employeeId: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-department">部门</Label>
                <Input
                  id="edit-department"
                  value={currentPosition.department}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, department: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-position">职位</Label>
                <Input
                  id="edit-position"
                  value={currentPosition.position}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, position: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-area">区域</Label>
                <Input
                  id="edit-area"
                  value={currentPosition.area}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, area: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-deviceId">设备ID</Label>
                <Input
                  id="edit-deviceId"
                  value={currentPosition.deviceId}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, deviceId: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-batteryStatus">电池状态</Label>
                <Input
                  id="edit-batteryStatus"
                  value={currentPosition.batteryStatus}
                  onChange={(e) => setCurrentPosition({ ...currentPosition, batteryStatus: e.target.value })}
                  placeholder="例如: 85%"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-signalStrength">信号强度</Label>
                <Select
                  value={currentPosition.signalStrength}
                  onValueChange={(value) => setCurrentPosition({ ...currentPosition, signalStrength: value })}
                >
                  <SelectTrigger id="edit-signalStrength">
                    <SelectValue placeholder="选择信号强度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="强">强</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="弱">弱</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-status">状态</Label>
              <Select
                value={currentPosition.status}
                onValueChange={(value) => setCurrentPosition({ ...currentPosition, status: value })}
              >
                <SelectTrigger id="edit-status">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="在线">在线</SelectItem>
                  <SelectItem value="离线">离线</SelectItem>
                  <SelectItem value="警告">警告</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditPosition}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentPosition.name}" 的定位信息吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePosition}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
