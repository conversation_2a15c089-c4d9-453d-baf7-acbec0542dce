"use strict";
exports.__esModule = true;
exports.SiteGallery = void 0;
var theme_context_1 = require("@/contexts/theme-context");
var button_1 = require("@/components/ui/button");
var lucide_react_1 = require("lucide-react");
var navigation_1 = require("next/navigation");
var CircularGallery_1 = require("./CircularGallery");
function SiteGallery() {
    var themeMode = theme_context_1.useTheme().themeMode;
    var router = navigation_1.useRouter();
    return (React.createElement("div", { className: "mb-8 rounded-2xl p-6 " + (themeMode === "dark"
            ? "bg-gradient-to-br from-[#2c2c2e]/50 to-[#1a1a1c]/50 border border-[#3a3a3c]"
            : "bg-gradient-to-br from-white to-gray-50/50 border border-gray-100") },
        React.createElement("div", { className: "flex items-center justify-between mb-6" },
            React.createElement("div", { className: "flex items-center gap-3" },
                React.createElement("h2", { className: "text-2xl font-bold " + (themeMode === "dark" ? "text-white" : "text-gray-900") }, "\u65BD\u5DE5\u73B0\u573A\u5C55\u793A"),
                React.createElement("div", { className: "px-2.5 py-1 text-xs rounded-full flex items-center gap-1.5 transition-colors " + (themeMode === "dark"
                        ? "bg-[#2c2c2e] text-blue-400 border border-[#3a3a3c]"
                        : "bg-blue-50 text-blue-600 border border-blue-100") },
                    React.createElement("span", { className: "relative flex h-2 w-2" },
                        React.createElement("span", { className: "animate-ping absolute inline-flex h-full w-full rounded-full opacity-75 " + (themeMode === "dark" ? "bg-blue-400" : "bg-blue-500") }),
                        React.createElement("span", { className: "relative inline-flex rounded-full h-2 w-2 " + (themeMode === "dark" ? "bg-blue-400" : "bg-blue-500") })),
                    "\u5B9E\u65F6\u66F4\u65B0")),
            React.createElement(button_1.Button, { variant: "outline", size: "sm", className: "transition-all duration-300 hover:scale-105 " + (themeMode === "dark"
                    ? "bg-[#2c2c2e] border-[#3a3a3c] hover:bg-[#3a3a3c] text-white"
                    : "bg-white border-gray-200 hover:bg-gray-50"), onClick: function () { return router.push('/project-management/site-gallery'); } },
                "\u67E5\u770B\u66F4\u591A",
                React.createElement(lucide_react_1.ChevronRight, { className: "ml-1 h-4 w-4" }))),
        React.createElement("div", { className: "relative h-[600px] rounded-2xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl " + (themeMode === "dark"
                ? "bg-[#1c1c1e] shadow-black/20 hover:shadow-black/30"
                : "bg-gray-50/50 shadow-gray-200/50 hover:shadow-gray-300/50") },
            React.createElement(CircularGallery_1["default"], { bend: 6.5, textColor: themeMode === "dark" ? "#ffffff" : "#1d1d1f", borderRadius: 0.2, font: "bold 28px 'DM Sans'", items: [
                    { image: "/zhanshi/1.jpg", text: "主井施工现场" },
                    { image: "/zhanshi/2.jpg", text: "采掘工作面" },
                    { image: "/zhanshi/3.jpg", text: "通风系统" },
                    { image: "/zhanshi/4.jpg", text: "运输巷道" },
                    { image: "/zhanshi/5.jpg", text: "安全监控室" },
                    { image: "/zhanshi/6.jpg", text: "机电设备间" },
                    { image: "/zhanshi/7.jpg", text: "排水系统" },
                    { image: "/zhanshi/8.jpg", text: "地面设施" }
                ] })),
        React.createElement("style", { jsx: true }, "\n        @keyframes float {\n          0%, 100% { transform: translateY(0); }\n          50% { transform: translateY(-10px); }\n        }\n      ")));
}
exports.SiteGallery = SiteGallery;
