"use client"

import { useState } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  Shield,
  Users,
  Camera,
  AlertTriangle,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

export function SecurityManagement() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  // 示例数据
  const securityRecords = [
    {
      id: "1",
      date: "2025-03-01",
      time: "08:00",
      type: "门禁记录",
      location: "正门",
      person: "李明",
      department: "生产部",
      status: "正常",
      notes: "正常上班打卡",
    },
    {
      id: "2",
      date: "2025-03-01",
      time: "09:30",
      type: "巡逻记录",
      location: "厂区西侧",
      person: "张保安",
      department: "保卫部",
      status: "异常",
      notes: "发现可疑人员，已处理",
    },
    {
      id: "3",
      date: "2025-03-01",
      time: "12:00",
      type: "监控记录",
      location: "仓库区",
      person: "王安",
      department: "保卫部",
      status: "正常",
      notes: "例行检查",
    },
    {
      id: "4",
      date: "2025-03-01",
      time: "15:00",
      type: "访客登记",
      location: "接待室",
      person: "赵访客",
      department: "外部访客",
      status: "正常",
      notes: "商务拜访",
    },
  ]

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "异常":
        return <Badge variant="destructive">异常</Badge>
      case "警告":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            警告
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">保卫管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Camera className="h-4 w-4 mr-2" />
            监控中心
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加记录
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加安保记录</DialogTitle>
                <DialogDescription>记录新的安保相关事项</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">日期</Label>
                    <Input id="date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="time">时间</Label>
                    <Input id="time" type="time" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">记录类型</Label>
                    <Select>
                      <SelectTrigger id="type">
                        <SelectValue placeholder="选择记录类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="access">门禁记录</SelectItem>
                        <SelectItem value="patrol">巡逻记录</SelectItem>
                        <SelectItem value="monitor">监控记录</SelectItem>
                        <SelectItem value="visitor">访客登记</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">位置</Label>
                    <Input id="location" placeholder="输入位置" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="person">人员</Label>
                    <Input id="person" placeholder="输入人员姓名" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">部门</Label>
                    <Input id="department" placeholder="输入部门名称" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">备注</Label>
                  <Textarea id="notes" placeholder="输入备注信息" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>确认</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="flex gap-4">
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日门禁记录</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">324</div>
            <p className="text-xs text-muted-foreground">较昨日增加 45 次</p>
          </CardContent>
        </Card>
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在岗保安</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">当前班次</p>
          </CardContent>
        </Card>
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">监控设备</CardTitle>
            <Camera className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">48/50</div>
            <p className="text-xs text-muted-foreground">正常运行</p>
          </CardContent>
        </Card>
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">安全警报</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">需要处理</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>安保记录</CardTitle>
          <CardDescription>查看和管理安保相关记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <Input placeholder="搜索..." className="max-w-sm" />
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              筛选
            </Button>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>时间</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>位置</TableHead>
                <TableHead>人员</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>备注</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {securityRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.date}</TableCell>
                  <TableCell>{record.time}</TableCell>
                  <TableCell>{record.type}</TableCell>
                  <TableCell>{record.location}</TableCell>
                  <TableCell>{record.person}</TableCell>
                  <TableCell>{record.department}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell>{record.notes}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}