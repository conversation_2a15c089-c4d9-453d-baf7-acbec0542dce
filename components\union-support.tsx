"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"

interface UnionSupportRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  supportType: string
  supportAmount: number
  supportDate: string
  supportReason: string
  status: string
}

interface UnionActivityRecord {
  id: string
  activityName: string
  organizer: string
  startDate: string
  endDate: string
  location: string
  participants: number
  budget: number
  status: string
}

export function UnionSupport() {
  const [searchTerm, setSearchTerm] = useState("")
  const [supportRecords, setSupportRecords] = useState<UnionSupportRecord[]>([
    {
      id: "SP001",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "采矿部",
      supportType: "困难帮扶",
      supportAmount: 5000,
      supportDate: "2023-05-15",
      supportReason: "家庭成员重病住院",
      status: "已发放",
    },
    {
      id: "SP002",
      employeeName: "李四",
      employeeId: "EMP045",
      department: "安全部",
      supportType: "子女教育",
      supportAmount: 3000,
      supportDate: "2023-06-20",
      supportReason: "子女大学入学资助",
      status: "审核中",
    },
    {
      id: "SP003",
      employeeName: "王五",
      employeeId: "EMP078",
      department: "机电部",
      supportType: "医疗帮扶",
      supportAmount: 8000,
      supportDate: "2023-04-10",
      supportReason: "重大疾病治疗",
      status: "已发放",
    },
    {
      id: "SP004",
      employeeName: "赵六",
      employeeId: "EMP102",
      department: "运输部",
      supportType: "灾害帮扶",
      supportAmount: 10000,
      supportDate: "2023-07-05",
      supportReason: "家庭遭遇自然灾害",
      status: "已发放",
    },
    {
      id: "SP005",
      employeeName: "钱七",
      employeeId: "EMP156",
      department: "通风部",
      supportType: "困难帮扶",
      supportAmount: 4000,
      supportDate: "2023-08-12",
      supportReason: "家庭经济困难",
      status: "审核中",
    },
  ])

  const [unionActivities, setUnionActivities] = useState<UnionActivityRecord[]>([
    {
      id: "UA001",
      activityName: "职工文化节",
      organizer: "矿山工会",
      startDate: "2023-09-01",
      endDate: "2023-09-07",
      location: "矿区文化中心",
      participants: 350,
      budget: 50000,
      status: "已完成",
    },
    {
      id: "UA002",
      activityName: "安全知识竞赛",
      organizer: "安全部工会小组",
      startDate: "2023-10-15",
      endDate: "2023-10-15",
      location: "培训中心",
      participants: 120,
      budget: 15000,
      status: "筹备中",
    },
    {
      id: "UA003",
      activityName: "职工健康体检",
      organizer: "矿山工会医疗组",
      startDate: "2023-11-01",
      endDate: "2023-11-10",
      location: "矿区医院",
      participants: 500,
      budget: 100000,
      status: "筹备中",
    },
    {
      id: "UA004",
      activityName: "职工家属联谊会",
      organizer: "矿山工会",
      startDate: "2023-08-20",
      endDate: "2023-08-20",
      location: "矿区礼堂",
      participants: 200,
      budget: 30000,
      status: "已完成",
    },
  ])

  const filteredSupportRecords = supportRecords.filter(
    (record) =>
      record.employeeName.includes(searchTerm) ||
      record.employeeId.includes(searchTerm) ||
      record.department.includes(searchTerm) ||
      record.supportType.includes(searchTerm),
  )

  const filteredActivities = unionActivities.filter(
    (activity) =>
      activity.activityName.includes(searchTerm) ||
      activity.organizer.includes(searchTerm) ||
      activity.location.includes(searchTerm),
  )

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">工会与帮扶管理</h1>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="搜索..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </div>
      </div>

      <Tabs defaultValue="support">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="support">帮扶管理</TabsTrigger>
          <TabsTrigger value="activities">工会活动</TabsTrigger>
        </TabsList>

        <TabsContent value="support" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>帮扶记录</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>新增帮扶</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增帮扶记录</DialogTitle>
                      <DialogDescription>请填写帮扶信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="employeeName">员工姓名</Label>
                          <Input id="employeeName" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employeeId">员工编号</Label>
                          <Input id="employeeId" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="department">所属部门</Label>
                          <Input id="department" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="supportType">帮扶类型</Label>
                          <Input id="supportType" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="supportAmount">帮扶金额</Label>
                          <Input id="supportAmount" type="number" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="supportDate">帮扶日期</Label>
                          <Input id="supportDate" type="date" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="supportReason">帮扶原因</Label>
                        <Input id="supportReason" />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理员工帮扶记录，包括困难帮扶、医疗帮扶、子女教育等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>帮扶编号</TableHead>
                    <TableHead>员工姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>帮扶类型</TableHead>
                    <TableHead>帮扶金额</TableHead>
                    <TableHead>帮扶日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSupportRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>{record.id}</TableCell>
                      <TableCell>{record.employeeName}</TableCell>
                      <TableCell>{record.employeeId}</TableCell>
                      <TableCell>{record.department}</TableCell>
                      <TableCell>{record.supportType}</TableCell>
                      <TableCell>
                        {record.supportAmount.toLocaleString("zh-CN", { style: "currency", currency: "CNY" })}
                      </TableCell>
                      <TableCell>{record.supportDate}</TableCell>
                      <TableCell>
                        <Badge variant={record.status === "已发放" ? "success" : "warning"}>{record.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            查看
                          </Button>
                          <Button variant="outline" size="sm">
                            编辑
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>帮扶统计</CardTitle>
              <CardDescription>帮扶类型和金额统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">30</div>
                    <p className="text-xs text-muted-foreground">帮扶总人次</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">¥150,000</div>
                    <p className="text-xs text-muted-foreground">帮扶总金额</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">12</div>
                    <p className="text-xs text-muted-foreground">困难帮扶人次</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">8</div>
                    <p className="text-xs text-muted-foreground">医疗帮扶人次</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>工会活动</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>新增活动</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增工会活动</DialogTitle>
                      <DialogDescription>请填写工会活动信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="activityName">活动名称</Label>
                        <Input id="activityName" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="organizer">组织者</Label>
                          <Input id="organizer" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="location">活动地点</Label>
                          <Input id="location" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="startDate">开始日期</Label>
                          <Input id="startDate" type="date" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endDate">结束日期</Label>
                          <Input id="endDate" type="date" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="participants">预计参与人数</Label>
                          <Input id="participants" type="number" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="budget">预算</Label>
                          <Input id="budget" type="number" />
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理工会组织的各类活动，包括文化活动、健康活动、知识竞赛等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>活动编号</TableHead>
                    <TableHead>活动名称</TableHead>
                    <TableHead>组织者</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>结束日期</TableHead>
                    <TableHead>活动地点</TableHead>
                    <TableHead>参与人数</TableHead>
                    <TableHead>预算</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>{activity.id}</TableCell>
                      <TableCell>{activity.activityName}</TableCell>
                      <TableCell>{activity.organizer}</TableCell>
                      <TableCell>{activity.startDate}</TableCell>
                      <TableCell>{activity.endDate}</TableCell>
                      <TableCell>{activity.location}</TableCell>
                      <TableCell>{activity.participants}</TableCell>
                      <TableCell>
                        {activity.budget.toLocaleString("zh-CN", { style: "currency", currency: "CNY" })}
                      </TableCell>
                      <TableCell>
                        <Badge variant={activity.status === "已完成" ? "success" : "warning"}>{activity.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            查看
                          </Button>
                          <Button variant="outline" size="sm">
                            编辑
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

