"use client"

import { useState, useEffect } from "react"
import { Search, Plus, Edit, Trash2, Download, MoreHorizontal, Users, FileText, Mail, Phone, Upload, Filter, Settings2, UserCheck, AlertCircle, UserPlus, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Personnel, usePersonnelData } from "@/hooks/use-personnel-data"

export function EmployeeManagement() {
  const { toast } = useToast()
  const {
    personnel,
    loading,
    filterPersonnel,
    addPersonnel,
    updatePersonnel,
    deletePersonnel,
    batchDeletePersonnel,
    getExpiringContracts
  } = usePersonnelData()

  // 状态管理
  const [isAddEmployeeOpen, setIsAddEmployeeOpen] = useState(false)
  const [isEditEmployeeOpen, setIsEditEmployeeOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Personnel | null>(null)
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [contractTypeFilter, setContractTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [formData, setFormData] = useState<Partial<Personnel>>({})
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)

  // 筛选后的员工数据
  const filteredEmployees = filterPersonnel({
    search: searchTerm,
    department: departmentFilter,
    contractType: contractTypeFilter,
    status: statusFilter
  })

  // 分页数据
  const totalPages = Math.ceil(filteredEmployees.length / pageSize)
  const paginatedEmployees = filteredEmployees.slice(
    (page - 1) * pageSize,
    page * pageSize
  )

  // 检查合同到期提醒
  useEffect(() => {
    const expiringContracts = getExpiringContracts()
    if (expiringContracts.length > 0) {
      toast({
        title: "合同到期提醒",
        description: `有 ${expiringContracts.length} 名员工的合同即将在30天内到期`,
        variant: "destructive",
      })
    }
  }, [])

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id.replace('employee-', '')]: value
    }))
  }

  // 处理选择框变化
  const handleSelectChange = (field: string) => (value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 重置表单数据
  const resetFormData = () => {
    setFormData({
      name: "",
      employeeId: "",
      department: "",
      position: "",
      hireDate: new Date().toISOString().split('T')[0],
      contractType: "全职",
      contractEnd: "",
      status: "在职",
      email: "",
      phone: "",
      address: "",
      emergencyContact: "",
      emergencyPhone: "",
      education: "本科"
    })
  }

  // 处理添加员工
  const handleAddEmployee = () => {
    if (!formData.name || !formData.employeeId || !formData.department || !formData.position) {
      toast({
        title: "请填写必要信息",
        description: "姓名、员工编号、部门和职位为必填项",
        variant: "destructive",
      })
      return
    }

    try {
      addPersonnel(formData as Omit<Personnel, 'id'>)
      toast({
        title: "添加成功",
        description: `已成功添加员工：${formData.name}`,
      })
      setIsAddEmployeeOpen(false)
      resetFormData()
    } catch (error) {
      toast({
        title: "添加失败",
        description: "添加员工时发生错误",
        variant: "destructive",
      })
    }
  }

  // 处理更新员工
  const handleUpdateEmployee = () => {
    if (!selectedEmployee || !formData.name || !formData.department || !formData.position) {
      toast({
        title: "请填写必要信息",
        description: "姓名、部门和职位为必填项",
        variant: "destructive",
      })
      return
    }

    try {
      updatePersonnel(selectedEmployee.id, formData)
      toast({
        title: "更新成功",
        description: `已成功更新员工：${formData.name}`,
      })
      setIsEditEmployeeOpen(false)
      setSelectedEmployee(null)
      resetFormData()
    } catch (error) {
      toast({
        title: "更新失败",
        description: "更新员工信息时发生错误",
        variant: "destructive",
      })
    }
  }

  // 处理删除员工
  const handleDeleteEmployee = () => {
    if (!selectedEmployee) return

    try {
      deletePersonnel(selectedEmployee.id)
      toast({
        title: "删除成功",
        description: `已成功删除员工：${selectedEmployee.name}`,
      })
      setIsDeleteDialogOpen(false)
      setSelectedEmployee(null)
    } catch (error) {
      toast({
        title: "删除失败",
        description: "删除员工时发生错误",
        variant: "destructive",
      })
    }
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedEmployees.length === 0) return

    try {
      batchDeletePersonnel(selectedEmployees)
      toast({
        title: "批量删除成功",
        description: `已成功删除 ${selectedEmployees.length} 名员工`,
      })
      setSelectedEmployees([])
    } catch (error) {
      toast({
        title: "批量删除失败",
        description: "删除员工时发生错误",
        variant: "destructive",
      })
    }
  }

  // 处理选择员工
  const handleSelectEmployee = (employeeId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmployees([...selectedEmployees, employeeId])
    } else {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId))
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmployees(paginatedEmployees.map(emp => emp.id))
    } else {
      setSelectedEmployees([])
    }
  }

  // 导出数据
  const handleExportData = () => {
    const dataStr = JSON.stringify(
      selectedEmployees.length > 0
        ? personnel.filter(emp => selectedEmployees.includes(emp.id))
        : personnel,
      null,
      2
    )
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`
    const exportFileDefaultName = `employees-${new Date().toISOString().slice(0, 10)}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">员工管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExportData} className="hover:bg-blue-50 hover:text-blue-600">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".csv,.json"
              onChange={(e) => {
                // 处理文件导入
                const file = e.target.files?.[0]
                if (file) {
                  // 这里添加文件导入逻辑
                }
              }}
              className="hidden"
              id="import-file"
            />
            <Button variant="outline" size="sm" onClick={() => document.getElementById('import-file')?.click()} className="hover:bg-green-50 hover:text-green-600">
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">员工总数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-blue-100 p-3">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-blue-600">{personnel.length}</div>
                <p className="text-xs text-muted-foreground">
                  较上月{personnel.length > 156 ? "增加" : "减少"} {Math.abs(personnel.length - 156)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow duration-200 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">在职员工</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-green-100 p-3">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-green-600">
                  {personnel.filter(emp => emp.status === "在职").length}
                </div>
                <p className="text-xs text-muted-foreground">
                  占总数 {((personnel.filter(emp => emp.status === "在职").length / personnel.length) * 100).toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow duration-200 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">合同即将到期</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-amber-100 p-3">
                <AlertCircle className="h-6 w-6 text-amber-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-amber-600">{getExpiringContracts().length}</div>
                <p className="text-xs text-muted-foreground">未来30天内</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow duration-200 bg-gradient-to-br from-white to-slate-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">本月新入职</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-purple-100 p-3">
                <UserPlus className="h-6 w-6 text-purple-600" />
              </div>
              <div className="space-y-1 text-right">
                <div className="text-2xl font-bold text-purple-600">
                  {personnel.filter(emp => {
                    const hireDate = new Date(emp.hireDate)
                    const now = new Date()
                    return hireDate.getMonth() === now.getMonth() && hireDate.getFullYear() === now.getFullYear()
                  }).length}
                </div>
                <p className="text-xs text-muted-foreground">本月新增</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader>
          <CardTitle>员工列表</CardTitle>
          <CardDescription>管理公司所有员工信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    type="search" 
                    placeholder="搜索员工..." 
                    className="pl-8 w-[250px] border-slate-200 focus-visible:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="工程管理部">工程管理部</SelectItem>
                    <SelectItem value="人事管理部">人事管理部</SelectItem>
                    <SelectItem value="财务管理部">财务管理部</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={contractTypeFilter} onValueChange={setContractTypeFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="合同类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="全职">全职</SelectItem>
                    <SelectItem value="兼职">兼职</SelectItem>
                    <SelectItem value="实习">实习</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="在职">在职</SelectItem>
                    <SelectItem value="离职">离职</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleExportData} 
                  className="hover:bg-blue-50 hover:text-blue-600 border-slate-200"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <div className="relative">
                  <input
                    type="file"
                    accept=".csv,.json"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        // 处理文件导入
                      }
                    }}
                    className="hidden"
                    id="import-file"
                  />
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => document.getElementById('import-file')?.click()} 
                    className="hover:bg-green-50 hover:text-green-600 border-slate-200"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    导入
                  </Button>
                </div>
                <Button 
                  onClick={() => setIsAddEmployeeOpen(true)} 
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加员工
                </Button>
              </div>
            </div>

            <div className="rounded-md border border-slate-200">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedEmployees.length === paginatedEmployees.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>职位</TableHead>
                    <TableHead>入职日期</TableHead>
                    <TableHead>合同类型</TableHead>
                    <TableHead>合同到期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedEmployees.map((employee) => (
                    <TableRow key={employee.id} className="hover:bg-slate-50">
                      <TableCell>
                        <Checkbox
                          checked={selectedEmployees.includes(employee.id)}
                          onCheckedChange={(checked) => handleSelectEmployee(employee.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2 text-slate-400" />
                          <span className="font-medium">{employee.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{employee.employeeId}</TableCell>
                      <TableCell>{employee.department}</TableCell>
                      <TableCell>{employee.position}</TableCell>
                      <TableCell>{employee.hireDate}</TableCell>
                      <TableCell>{employee.contractType}</TableCell>
                      <TableCell>{employee.contractEnd}</TableCell>
                      <TableCell>
                        <Badge variant={employee.status === "在职" ? "default" : "secondary"} className={
                          employee.status === "在职" ? "bg-green-100 text-green-700" : "bg-slate-100 text-slate-700"
                        }>
                          {employee.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedEmployee(employee)
                              setFormData(employee)
                              setIsEditEmployeeOpen(true)
                            }}
                            className="hover:bg-amber-50 hover:text-amber-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedEmployee(employee)
                              setIsDeleteDialogOpen(true)
                            }}
                            className="hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t bg-slate-50 px-6 py-3">
          <div className="flex items-center gap-4">
            <div className="text-sm text-slate-600">
              共 {filteredEmployees.length} 条记录
            </div>
            {selectedEmployees.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBatchDelete}
                className="bg-red-500 hover:bg-red-600"
              >
                删除选中 ({selectedEmployees.length})
              </Button>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className="border-slate-200 hover:bg-slate-100"
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="px-3 border-slate-200 bg-white hover:bg-slate-100"
            >
              {page} / {totalPages}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
              className="border-slate-200 hover:bg-slate-100"
            >
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 添加员工对话框 */}
      <Dialog open={isAddEmployeeOpen} onOpenChange={setIsAddEmployeeOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-blue-500" />
              <span>添加员工</span>
            </DialogTitle>
            <DialogDescription>
              添加新员工信息到系统，带 <span className="text-red-500">*</span> 为必填项
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-3 bg-slate-100">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white data-[state=active]:text-blue-600">
                基本信息
              </TabsTrigger>
              <TabsTrigger value="contract" className="data-[state=active]:bg-white data-[state=active]:text-blue-600">
                合同信息
              </TabsTrigger>
              <TabsTrigger value="contact" className="data-[state=active]:bg-white data-[state=active]:text-blue-600">
                联系方式
              </TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-name">
                    姓名 <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="employee-name"
                    placeholder="请输入姓名"
                    value={formData.name || ''}
                    onChange={handleInputChange}
                    className="border-slate-200 focus-visible:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-id">员工编号</Label>
                  <Input
                    id="employee-employeeId"
                    placeholder="请输入员工编号"
                    value={formData.employeeId || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-department">部门</Label>
                  <Select
                    value={formData.department}
                    onValueChange={handleSelectChange('department')}
                  >
                    <SelectTrigger id="employee-department">
                      <SelectValue placeholder="选择部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="工程管理部">工程管理部</SelectItem>
                      <SelectItem value="人事管理部">人事管理部</SelectItem>
                      <SelectItem value="财务管理部">财务管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-position">职位</Label>
                  <Input
                    id="employee-position"
                    placeholder="请输入职位"
                    value={formData.position || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-hireDate">入职日期</Label>
                  <Input
                    id="employee-hireDate"
                    type="date"
                    value={formData.hireDate || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-status">状态</Label>
                  <Select
                    value={formData.status}
                    onValueChange={handleSelectChange('status')}
                  >
                    <SelectTrigger id="employee-status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="在职">在职</SelectItem>
                      <SelectItem value="离职">离职</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="contract" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-contractType">合同类型</Label>
                  <Select
                    value={formData.contractType}
                    onValueChange={handleSelectChange('contractType')}
                  >
                    <SelectTrigger id="employee-contractType">
                      <SelectValue placeholder="选择合同类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="全职">全职</SelectItem>
                      <SelectItem value="兼职">兼职</SelectItem>
                      <SelectItem value="实习">实习</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-contractEnd">合同结束日期</Label>
                  <Input
                    id="employee-contractEnd"
                    type="date"
                    value={formData.contractEnd || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="contact" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-email">邮箱</Label>
                  <Input
                    id="employee-email"
                    type="email"
                    placeholder="请输入邮箱"
                    value={formData.email || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-phone">电话</Label>
                  <Input
                    id="employee-phone"
                    placeholder="请输入电话"
                    value={formData.phone || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-address">地址</Label>
                  <Input
                    id="employee-address"
                    placeholder="请输入地址"
                    value={formData.address || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-emergencyContact">紧急联系人</Label>
                  <Input
                    id="employee-emergencyContact"
                    placeholder="请输入紧急联系人"
                    value={formData.emergencyContact || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-emergencyPhone">紧急联系电话</Label>
                  <Input
                    id="employee-emergencyPhone"
                    placeholder="请输入紧急联系电话"
                    value={formData.emergencyPhone || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsAddEmployeeOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              onClick={handleAddEmployee}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              确认添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑员工对话框 */}
      <Dialog open={isEditEmployeeOpen} onOpenChange={setIsEditEmployeeOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-amber-500" />
              <span>编辑员工</span>
            </DialogTitle>
            <DialogDescription>修改员工信息</DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="contract">合同信息</TabsTrigger>
              <TabsTrigger value="contact">联系方式</TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-name">姓名</Label>
                  <Input
                    id="employee-name"
                    placeholder="请输入姓名"
                    value={formData.name || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-id">员工编号</Label>
                  <Input
                    id="employee-employeeId"
                    placeholder="请输入员工编号"
                    value={formData.employeeId || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-department">部门</Label>
                  <Select
                    value={formData.department}
                    onValueChange={handleSelectChange('department')}
                  >
                    <SelectTrigger id="employee-department">
                      <SelectValue placeholder="选择部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="工程管理部">工程管理部</SelectItem>
                      <SelectItem value="人事管理部">人事管理部</SelectItem>
                      <SelectItem value="财务管理部">财务管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-position">职位</Label>
                  <Input
                    id="employee-position"
                    placeholder="请输入职位"
                    value={formData.position || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-hireDate">入职日期</Label>
                  <Input
                    id="employee-hireDate"
                    type="date"
                    value={formData.hireDate || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-status">状态</Label>
                  <Select
                    value={formData.status}
                    onValueChange={handleSelectChange('status')}
                  >
                    <SelectTrigger id="employee-status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="在职">在职</SelectItem>
                      <SelectItem value="离职">离职</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="contract" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-contractType">合同类型</Label>
                  <Select
                    value={formData.contractType}
                    onValueChange={handleSelectChange('contractType')}
                  >
                    <SelectTrigger id="employee-contractType">
                      <SelectValue placeholder="选择合同类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="全职">全职</SelectItem>
                      <SelectItem value="兼职">兼职</SelectItem>
                      <SelectItem value="实习">实习</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-contractEnd">合同结束日期</Label>
                  <Input
                    id="employee-contractEnd"
                    type="date"
                    value={formData.contractEnd || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="contact" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee-email">邮箱</Label>
                  <Input
                    id="employee-email"
                    type="email"
                    placeholder="请输入邮箱"
                    value={formData.email || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-phone">电话</Label>
                  <Input
                    id="employee-phone"
                    placeholder="请输入电话"
                    value={formData.phone || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-address">地址</Label>
                  <Input
                    id="employee-address"
                    placeholder="请输入地址"
                    value={formData.address || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-emergencyContact">紧急联系人</Label>
                  <Input
                    id="employee-emergencyContact"
                    placeholder="请输入紧急联系人"
                    value={formData.emergencyContact || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee-emergencyPhone">紧急联系电话</Label>
                  <Input
                    id="employee-emergencyPhone"
                    placeholder="请输入紧急联系电话"
                    value={formData.emergencyPhone || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsEditEmployeeOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              onClick={handleUpdateEmployee}
              className="bg-amber-500 hover:bg-amber-600 text-white"
            >
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>确认删除</span>
            </DialogTitle>
            <DialogDescription>
              您确定要删除 <span className="font-medium text-slate-900">{selectedEmployee?.name}</span> 的信息吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-slate-200 hover:bg-slate-100"
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteEmployee}
              className="bg-red-500 hover:bg-red-600"
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

