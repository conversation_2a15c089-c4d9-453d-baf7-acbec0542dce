"use client"

import { useState, useEffect } from "react"
import {
  Bar<PERSON>hart2,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  FileText,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  UploadCloud,
  X,
  SlidersHorizontal,
  Calendar,
  SearchX,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import * as XLSX from 'xlsx'
import * as echarts from 'echarts/core'
import ReactECharts from 'echarts-for-react'

// 定义费用记录接口
interface CostRecord {
  id: string
  project: string
  costType: string
  budgetAmount: number
  actualAmount: number
  variance: number
  variancePercent: number
  status: string
  paymentStatus: string
  approver: string
  lastUpdate: string
  description?: string
  invoice?: string
  createdAt?: string
  updatedAt?: string
  attachments?: string[]
  disabled?: boolean
}

// 定义添加费用表单接口
interface CostForm {
  project: string
  costType: string
  budgetAmount: string
  actualAmount: string
  paymentStatus: string
  approver: string
  description: string
  invoice: string
}

export function ProjectCost() {
  // 界面状态管理
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 数据筛选状态
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCostType, setSelectedCostType] = useState("all")
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState("all")
  const [selectedProject, setSelectedProject] = useState("all")
  const [currentTab, setCurrentTab] = useState("all")

  // 表单数据
  const [costForm, setCostForm] = useState<CostForm>({
    project: "",
    costType: "",
    budgetAmount: "",
    actualAmount: "",
    paymentStatus: "",
    approver: "",
    description: "",
    invoice: ""
  })

  // 编辑/查看的记录
  const [currentRecord, setCurrentRecord] = useState<CostRecord | null>(null)

  // 选中的记录
  const [selectedRows, setSelectedRows] = useState<string[]>([])

  // 示例数据
  const [costs, setCosts] = useState<CostRecord[]>([
    {
      id: "1",
      project: "矿区A3开发项目",
      costType: "设备采购",
      budgetAmount: 1500000,
      actualAmount: 1450000,
      variance: -50000,
      variancePercent: -3.33,
      status: "正常",
      paymentStatus: "部分支付",
      approver: "张三",
      lastUpdate: "2025-03-10",
    },
    {
      id: "2",
      project: "矿区A3开发项目",
      costType: "人工费用",
      budgetAmount: 800000,
      actualAmount: 820000,
      variance: 20000,
      variancePercent: 2.5,
      status: "超支",
      paymentStatus: "已支付",
      approver: "李四",
      lastUpdate: "2025-03-08",
    },
    {
      id: "3",
      project: "设备更新计划",
      costType: "材料采购",
      budgetAmount: 500000,
      actualAmount: 480000,
      variance: -20000,
      variancePercent: -4,
      status: "正常",
      paymentStatus: "已支付",
      approver: "王五",
      lastUpdate: "2025-03-05",
    },
    {
      id: "4",
      project: "安全系统升级",
      costType: "系统集成",
      budgetAmount: 300000,
      actualAmount: 350000,
      variance: 50000,
      variancePercent: 16.67,
      status: "超支",
      paymentStatus: "部分支付",
      approver: "赵六",
      lastUpdate: "2025-03-12",
    },
    {
      id: "5",
      project: "新矿区勘探",
      costType: "勘探费用",
      budgetAmount: 600000,
      actualAmount: 580000,
      variance: -20000,
      variancePercent: -3.33,
      status: "正常",
      paymentStatus: "部分支付",
      approver: "钱七",
      lastUpdate: "2025-03-11",
    },
  ])

  // 重置表单
  const resetCostForm = () => {
    setCostForm({
      project: "",
      costType: "",
      budgetAmount: "",
      actualAmount: "",
      paymentStatus: "",
      approver: "",
      description: "",
      invoice: ""
    })
  }

  // 处理添加费用
  const handleAddCost = () => {
    setIsLoading(true)
    try {
      // 验证必填字段
      if (!costForm.project || !costForm.costType || !costForm.budgetAmount || !costForm.actualAmount) {
        toast.error("请填写必填字段")
        setIsLoading(false)
        return
      }

      // 计算差异和差异百分比
      const budgetAmount = parseFloat(costForm.budgetAmount)
      const actualAmount = parseFloat(costForm.actualAmount)
      const variance = actualAmount - budgetAmount
      const variancePercent = budgetAmount > 0 ? Number(((variance / budgetAmount) * 100).toFixed(2)) : 0

      // 确定状态
      let status = "正常"
      if (variance > 0) {
        status = "超支"
      } else if (variance < 0) {
        status = "节约"
      }

      // 创建新记录
      const newCost: CostRecord = {
        id: `${Date.now()}`,
        project: costForm.project,
        costType: costForm.costType,
        budgetAmount: budgetAmount,
        actualAmount: actualAmount,
        variance: variance,
        variancePercent: variancePercent,
        status: status,
        paymentStatus: costForm.paymentStatus,
        approver: costForm.approver,
        lastUpdate: "2025-03-15",
        description: costForm.description,
        invoice: costForm.invoice,
        createdAt: "2025-03-15T10:30:00.000Z",
        updatedAt: "2025-03-15T10:30:00.000Z",
        disabled: false
      }

      // 更新状态
      setCosts([...costs, newCost])
      resetCostForm()
      setIsAddDialogOpen(false)
      toast.success("费用添加成功")
    } catch (error) {
      toast.error("添加失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理编辑费用
  const handleEditCost = () => {
    setIsLoading(true)
    try {
      if (!currentRecord) return

      // 验证必填字段
      if (!costForm.project || !costForm.costType || !costForm.budgetAmount || !costForm.actualAmount) {
        toast.error("请填写必填字段")
        setIsLoading(false)
        return
      }

      // 计算差异和差异百分比
      const budgetAmount = parseFloat(costForm.budgetAmount)
      const actualAmount = parseFloat(costForm.actualAmount)
      const variance = actualAmount - budgetAmount
      const variancePercent = budgetAmount > 0 ? Number(((variance / budgetAmount) * 100).toFixed(2)) : 0

      // 确定状态
      let status = "正常"
      if (variance > 0) {
        status = "超支"
      } else if (variance < 0) {
        status = "节约"
      }

      // 创建编辑后的记录
      const editedCost: CostRecord = {
        ...currentRecord,
        project: costForm.project,
        costType: costForm.costType,
        budgetAmount: budgetAmount,
        actualAmount: actualAmount,
        variance: variance,
        variancePercent: variancePercent,
        status: status,
        paymentStatus: costForm.paymentStatus,
        approver: costForm.approver,
        lastUpdate: "2025-03-15",
        description: costForm.description,
        invoice: costForm.invoice,
        updatedAt: "2025-03-15T10:30:00.000Z"
      }

      // 更新状态
      setCosts(costs.map(cost => cost.id === currentRecord.id ? editedCost : cost))
      resetCostForm()
      setIsEditDialogOpen(false)
      toast.success("费用更新成功")
    } catch (error) {
      toast.error("更新失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理删除费用
  const handleDeleteCost = () => {
    if (!currentRecord) return

    setIsLoading(true)
    try {
      setCosts(costs.filter(cost => cost.id !== currentRecord.id))
      setCurrentRecord(null)
      setIsDeleteDialogOpen(false)
      toast.success("费用删除成功")
    } catch (error) {
      toast.error("删除失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRows.length === 0) return

    setIsLoading(true)
    try {
      setCosts(costs.filter(cost => !selectedRows.includes(cost.id)))
      setSelectedRows([])
      toast.success(`成功删除 ${selectedRows.length} 条记录`)
    } catch (error) {
      toast.error("批量删除失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理查看详情
  const handleViewDetails = (record: CostRecord) => {
    setCurrentRecord(record)
    setIsViewDetailsOpen(true)
  }

  // 处理编辑
  const prepareEdit = (record: CostRecord) => {
    setCurrentRecord(record)
    setCostForm({
      project: record.project,
      costType: record.costType,
      budgetAmount: record.budgetAmount.toString(),
      actualAmount: record.actualAmount.toString(),
      paymentStatus: record.paymentStatus,
      approver: record.approver,
      description: record.description || "",
      invoice: record.invoice || ""
    })
    setIsEditDialogOpen(true)
  }

  // 处理删除确认
  const confirmDelete = (record: CostRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 处理导出数据
  const handleExportData = (format: string) => {
    setIsLoading(true)
    try {
      // 准备导出数据
      const exportData = costs.map(cost => ({
        '工程项目': cost.project,
        '费用类型': cost.costType,
        '预算金额 (元)': cost.budgetAmount,
        '实际金额 (元)': cost.actualAmount,
        '差异 (元)': cost.variance,
        '差异百分比 (%)': cost.variancePercent,
        '状态': cost.status,
        '支付状态': cost.paymentStatus,
        '审批人': cost.approver,
        '最后更新': cost.lastUpdate,
        '费用说明': cost.description || '',
        '发票信息': cost.invoice || ''
      }))

      if (format === 'excel') {
        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)

        // 设置列宽
        ws['!cols'] = [
          { wch: 20 }, // 工程项目
          { wch: 15 }, // 费用类型
          { wch: 15 }, // 预算金额
          { wch: 15 }, // 实际金额
          { wch: 15 }, // 差异
          { wch: 15 }, // 差异百分比
          { wch: 10 }, // 状态
          { wch: 10 }, // 支付状态
          { wch: 10 }, // 审批人
          { wch: 15 }, // 最后更新
          { wch: 30 }, // 费用说明
          { wch: 20 }, // 发票信息
        ]

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '工程费用')

        // 导出文件
        XLSX.writeFile(wb, `工程费用_${new Date().toISOString().split('T')[0]}.xlsx`)
      }

      toast.success("导出成功")
      setIsExportDialogOpen(false)
    } catch (error) {
      toast.error("导出失败，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入文件
  const handleImportFile = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    try {
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]]
        const rows = XLSX.utils.sheet_to_json(firstSheet)

        // 转换导入的数据格式
        const importedCosts: CostRecord[] = rows.map((row: any, index) => {
          const budgetAmount = parseFloat(row['预算金额 (元)'] || row['预算金额'] || 0)
          const actualAmount = parseFloat(row['实际金额 (元)'] || row['实际金额'] || 0)
          const variance = actualAmount - budgetAmount
          const variancePercent = budgetAmount > 0 ? Number(((variance / budgetAmount) * 100).toFixed(2)) : 0

          // 确定状态
          let status = "正常"
          if (variance > 0) {
            status = "超支"
          } else if (variance < 0) {
            status = "节约"
          }

          return {
            id: `import-${Date.now()}-${index}`,
            project: row['工程项目'] || '',
            costType: row['费用类型'] || '',
            budgetAmount: budgetAmount,
            actualAmount: actualAmount,
            variance: variance,
            variancePercent: variancePercent,
            status: row['状态'] || status,
            paymentStatus: row['支付状态'] || '未支付',
            approver: row['审批人'] || '',
            lastUpdate: row['最后更新'] || new Date().toISOString().split('T')[0],
            description: row['费用说明'] || '',
            invoice: row['发票信息'] || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            disabled: false
          }
        })

        if (importedCosts.length > 0) {
          setCosts([...costs, ...importedCosts])
          toast.success(`成功导入 ${importedCosts.length} 条记录`)
        } else {
          toast.error("未找到有效数据")
        }
      }
      reader.readAsArrayBuffer(file)
    } catch (error) {
      toast.error("导入失败，请检查文件格式")
    } finally {
      setIsImporting(false)
      if (event.target) event.target.value = ''
    }
  }

  // 处理表单输入变化
  const handleFormChange = (field: keyof CostForm, value: string) => {
    setCostForm({
      ...costForm,
      [field]: value
    })
  }

  // 筛选数据
  const filteredCosts = costs.filter(cost => {
    // 按Tab筛选
    if (currentTab !== 'all') {
      const statusMap: Record<string, string> = {
        'normal': '正常',
        'over': '超支',
        'under': '节约'
      }
      if (cost.status !== statusMap[currentTab]) return false
    }

    // 按搜索词筛选
    if (searchTerm && !cost.project.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !cost.costType.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !cost.approver.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }

    // 按费用类型筛选
    if (selectedCostType !== 'all' && cost.costType !== selectedCostType) {
      return false
    }

    // 按支付状态筛选
    if (selectedPaymentStatus !== 'all' && cost.paymentStatus !== selectedPaymentStatus) {
      return false
    }

    // 按项目筛选
    if (selectedProject !== 'all' && cost.project !== selectedProject) {
      return false
    }

    return true
  })

  // 汇总数据
  const totalBudget = filteredCosts.reduce((sum, item) => sum + item.budgetAmount, 0)
  const totalActual = filteredCosts.reduce((sum, item) => sum + item.actualAmount, 0)
  const totalVariance = totalActual - totalBudget
  const totalVariancePercent = totalBudget > 0 ? Number(((totalVariance / totalBudget) * 100).toFixed(2)) : 0

  // 预算与实际对比图表数据
  const getBudgetVsActualChartOption = () => {
    // 按项目分组汇总数据
    const projectData: {[key: string]: {budget: number, actual: number}} = {}

    filteredCosts.forEach(cost => {
      if (!projectData[cost.project]) {
        projectData[cost.project] = {
          budget: 0,
          actual: 0
        }
      }

      projectData[cost.project].budget += cost.budgetAmount
      projectData[cost.project].actual += cost.actualAmount
    })

    // 转换为图表数据
    const projects = Object.keys(projectData)
    const budgetData = projects.map(p => projectData[p].budget)
    const actualData = projects.map(p => projectData[p].actual)

    // 确保有数据时才显示图表
    if (projects.length === 0) {
      projects.push('暂无数据')
      budgetData.push(0)
      actualData.push(0)
    }

    return {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const project = params[0].name
          const budget = params[0].value.toLocaleString()
          const actual = params[1].value.toLocaleString()
          const variance = (params[1].value - params[0].value).toLocaleString()
          const varPercent = params[0].value > 0
            ? (((params[1].value - params[0].value) / params[0].value) * 100).toFixed(2)
            : 0

          return `<div style="font-weight:bold">${project}</div>
                  <div>预算: ${budget} 元</div>
                  <div>实际: ${actual} 元</div>
                  <div>差异: ${variance} 元 (${varPercent}%)</div>`
        }
      },
      legend: {
        data: ['预算金额', '实际支出'],
        right: 10,
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: '金额 (元)',
        axisLabel: {
          formatter: function(value: number) {
            return value >= 10000
              ? (value / 10000) + '万'
              : value
          }
        }
      },
      yAxis: {
        type: 'category',
        data: projects,
        axisLabel: {
          formatter: function(value: string) {
            return value.length > 10 ? value.slice(0, 10) + '...' : value
          }
        }
      },
      series: [
        {
          name: '预算金额',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#8884d8'
          },
          label: {
            show: true,
            formatter: function(params: any) {
              return params.value >= 10000
                ? (params.value / 10000).toFixed(1) + '万'
                : params.value
            }
          },
          data: budgetData
        },
        {
          name: '实际支出',
          type: 'bar',
          stack: 'compare',
          itemStyle: {
            color: '#82ca9d'
          },
          label: {
            show: true,
            formatter: function(params: any) {
              return params.value >= 10000
                ? (params.value / 10000).toFixed(1) + '万'
                : params.value
            }
          },
          data: actualData
        }
      ]
    }
  }

  // 费用分布图表数据
  const getCostDistributionChartOption = () => {
    // 按费用类型分组
    const costTypeData: {[key: string]: number} = {}

    filteredCosts.forEach(cost => {
      if (!costTypeData[cost.costType]) {
        costTypeData[cost.costType] = 0
      }

      costTypeData[cost.costType] += cost.actualAmount
    })

    // 转换为饼图数据格式
    const pieData = Object.keys(costTypeData).map(type => ({
      name: type,
      value: costTypeData[type]
    }))

    // 确保有数据
    if (pieData.length === 0) {
      pieData.push({
        name: '暂无数据',
        value: 100
      })
    }

    return {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const percent = params.percent.toFixed(1)
          return `${params.name}: ${params.value.toLocaleString()} 元 (${percent}%)`
        }
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 20,
        bottom: 20,
      },
      series: [
        {
          name: '费用分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pieData
        }
      ]
    }
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "超支":
        return <Badge variant="destructive">超支</Badge>
      case "节约":
        return <Badge className="bg-blue-500">节约</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取支付状态对应的徽章样式
  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "已支付":
        return <Badge className="bg-green-500">已支付</Badge>
      case "部分支付":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            部分支付
          </Badge>
        )
      case "未支付":
        return (
          <Badge variant="outline" className="text-red-500 border-red-500">
            未支付
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取差异对应的图标和样式
  const getVarianceDisplay = (variance: number) => {
    if (variance < 0) {
      return (
        <div className="flex items-center text-green-500">
          <TrendingDown className="h-4 w-4 mr-1" />
          <span>{Math.abs(variance).toLocaleString()} 元</span>
        </div>
      )
    } else if (variance > 0) {
      return (
        <div className="flex items-center text-red-500">
          <TrendingUp className="h-4 w-4 mr-1" />
          <span>{variance.toLocaleString()} 元</span>
        </div>
      )
    } else {
      return <span>0 元</span>
    }
  }

  // 获取差异百分比对应的样式
  const getVariancePercentDisplay = (percent: number) => {
    if (percent < 0) {
      return <span className="text-green-500">{percent}%</span>
    } else if (percent > 0) {
      return <span className="text-red-500">+{percent}%</span>
    } else {
      return <span>0%</span>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center py-2 mb-2">
        <div className="flex items-center gap-2">
          <DollarSign className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold">工程费用管理</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-blue-50"
            onClick={() => setIsExportDialogOpen(true)}
          >
            <FileText className="h-4 w-4 mr-2" />
            费用报表
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-green-50"
            onClick={() => setIsExportDialogOpen(true)}
          >
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
          <div className="relative">
            <input
              type="file"
              id="import-excel"
              className="hidden"
              accept=".xlsx,.xls"
              onChange={handleImportFile}
              disabled={isImporting}
            />
            <Button
              variant="outline"
              size="sm"
              className="hover:bg-yellow-50"
              onClick={() => document.getElementById('import-excel')?.click()}
              disabled={isImporting}
            >
              <UploadCloud className="h-4 w-4 mr-2" />
              {isImporting ? "导入中..." : "导入数据"}
          </Button>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-gray-100"
            onClick={() => setIsFilterDrawerOpen(true)}
          >
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            高级筛选
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            if (!open) resetCostForm();
          }}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all">
                <Plus className="h-4 w-4 mr-2" />
                添加费用
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加工程费用</DialogTitle>
                <DialogDescription>记录工程项目的费用支出</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目 <span className="text-red-500">*</span></Label>
                    <Select
                      value={costForm.project}
                      onValueChange={(value) => handleFormChange("project", value)}
                    >
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                        <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                        <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                        <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                        <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cost-type">费用类型 <span className="text-red-500">*</span></Label>
                    <Select
                      value={costForm.costType}
                      onValueChange={(value) => handleFormChange("costType", value)}
                    >
                      <SelectTrigger id="cost-type">
                        <SelectValue placeholder="选择费用类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="设备采购">设备采购</SelectItem>
                        <SelectItem value="人工费用">人工费用</SelectItem>
                        <SelectItem value="材料采购">材料采购</SelectItem>
                        <SelectItem value="系统集成">系统集成</SelectItem>
                        <SelectItem value="勘探费用">勘探费用</SelectItem>
                        <SelectItem value="其他费用">其他费用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budget-amount">预算金额 (元) <span className="text-red-500">*</span></Label>
                    <Input
                      id="budget-amount"
                      type="number"
                      min="0"
                      placeholder="输入预算金额"
                      value={costForm.budgetAmount}
                      onChange={(e) => handleFormChange("budgetAmount", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="actual-amount">实际金额 (元) <span className="text-red-500">*</span></Label>
                    <Input
                      id="actual-amount"
                      type="number"
                      min="0"
                      placeholder="输入实际金额"
                      value={costForm.actualAmount}
                      onChange={(e) => handleFormChange("actualAmount", e.target.value)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="payment-status">支付状态 <span className="text-red-500">*</span></Label>
                    <Select
                      value={costForm.paymentStatus}
                      onValueChange={(value) => handleFormChange("paymentStatus", value)}
                    >
                      <SelectTrigger id="payment-status">
                        <SelectValue placeholder="选择支付状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="已支付">已支付</SelectItem>
                        <SelectItem value="部分支付">部分支付</SelectItem>
                        <SelectItem value="未支付">未支付</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="approver">审批人 <span className="text-red-500">*</span></Label>
                    <Input
                      id="approver"
                      placeholder="输入审批人姓名"
                      value={costForm.approver}
                      onChange={(e) => handleFormChange("approver", e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">费用说明</Label>
                  <Textarea
                    id="description"
                    placeholder="输入费用详细说明"
                    rows={3}
                    value={costForm.description}
                    onChange={(e) => handleFormChange("description", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoice">发票信息</Label>
                  <Input
                    id="invoice"
                    placeholder="输入发票号码或相关信息"
                    value={costForm.invoice}
                    onChange={(e) => handleFormChange("invoice", e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddCost} disabled={isLoading}>
                  {isLoading ? "保存中..." : "保存"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <DollarSign className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{totalBudget.toLocaleString()} 元</h3>
            <p className="text-sm text-muted-foreground">总预算</p>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{totalActual.toLocaleString()} 元</h3>
            <p className="text-sm text-muted-foreground">总实际支出</p>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              {totalVariance < 0 ? (
                <TrendingDown className="h-6 w-6 text-green-600" />
              ) : (
                <TrendingUp className="h-6 w-6 text-red-600" />
              )}
            </div>
            <h3 className="text-xl font-bold">
              {totalVariance < 0 ? (
                <span className="text-green-600">-{Math.abs(totalVariance).toLocaleString()} 元</span>
              ) : (
                <span className="text-red-600">+{totalVariance.toLocaleString()} 元</span>
              )}
            </h3>
            <p className="text-sm text-muted-foreground">总差异</p>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">
              {Number(totalVariancePercent) < 0 ? (
                <span className="text-green-600">{totalVariancePercent}%</span>
              ) : (
                <span className="text-red-600">+{totalVariancePercent}%</span>
              )}
            </h3>
            <p className="text-sm text-muted-foreground">差异百分比</p>
          </CardContent>
        </Card>
      </div>

      <Card className="border-0 shadow-lg">
        <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>工程费用明细</CardTitle>
              <CardDescription>查看和管理所有工程费用</CardDescription>
            </div>
            <Tabs defaultValue="all" onValueChange={(value) => setCurrentTab(value)}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="normal">正常</TabsTrigger>
                <TabsTrigger value="over">超支</TabsTrigger>
                <TabsTrigger value="under">节约</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索费用..."
                  className="pl-8 w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={selectedCostType} onValueChange={setSelectedCostType}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="费用类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="设备采购">设备采购</SelectItem>
                  <SelectItem value="人工费用">人工费用</SelectItem>
                  <SelectItem value="材料采购">材料采购</SelectItem>
                  <SelectItem value="系统集成">系统集成</SelectItem>
                  <SelectItem value="勘探费用">勘探费用</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedPaymentStatus} onValueChange={setSelectedPaymentStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="支付状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="已支付">已支付</SelectItem>
                  <SelectItem value="部分支付">部分支付</SelectItem>
                  <SelectItem value="未支付">未支付</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                className="hover:bg-blue-50"
                onClick={() => setIsFilterDrawerOpen(true)}
              >
                <Filter className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="hover:bg-blue-50 ml-2"
                onClick={() => {
                  setSearchTerm("")
                  setSelectedCostType("all")
                  setSelectedPaymentStatus("all")
                  setSelectedProject("all")
                  setCurrentTab("all")
                }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                重置筛选
              </Button>
            </div>
            {selectedRows.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBatchDelete}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                批量删除 ({selectedRows.length})
              </Button>
            )}
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50/50 hover:bg-gray-50">
                  <TableHead className="w-[40px]">
                    <Checkbox
                      checked={selectedRows.length > 0 && selectedRows.length === filteredCosts.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRows(filteredCosts.map(cost => cost.id))
                        } else {
                          setSelectedRows([])
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>工程项目</TableHead>
                  <TableHead>费用类型</TableHead>
                  <TableHead>预算金额</TableHead>
                  <TableHead>实际金额</TableHead>
                  <TableHead>差异</TableHead>
                  <TableHead>差异百分比</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>支付状态</TableHead>
                  <TableHead>审批人</TableHead>
                  <TableHead>最后更新</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCosts.map((cost) => (
                  <TableRow
                    key={cost.id}
                    className="hover:bg-blue-50/30 transition-colors cursor-pointer"
                    onClick={() => handleViewDetails(cost)}
                  >
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedRows.includes(cost.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRows([...selectedRows, cost.id])
                          } else {
                            setSelectedRows(selectedRows.filter(id => id !== cost.id))
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{cost.project}</TableCell>
                    <TableCell>{cost.costType}</TableCell>
                    <TableCell>{cost.budgetAmount.toLocaleString()} 元</TableCell>
                    <TableCell>{cost.actualAmount.toLocaleString()} 元</TableCell>
                    <TableCell>{getVarianceDisplay(cost.variance)}</TableCell>
                    <TableCell>{getVariancePercentDisplay(cost.variancePercent)}</TableCell>
                    <TableCell>{getStatusBadge(cost.status)}</TableCell>
                    <TableCell>{getPaymentStatusBadge(cost.paymentStatus)}</TableCell>
                    <TableCell>{cost.approver}</TableCell>
                    <TableCell>{cost.lastUpdate}</TableCell>
                    <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(cost)}
                        className="hover:bg-blue-100"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        详情
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => prepareEdit(cost)}
                        className="hover:bg-yellow-100"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => confirmDelete(cost)}
                        className="hover:bg-red-100"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredCosts.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={12} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <SearchX className="h-8 w-8 mb-2" />
                        <p>没有找到匹配的费用记录</p>
                        <Button
                          variant="link"
                          onClick={() => {
                            setSearchTerm("")
                            setSelectedCostType("all")
                            setSelectedPaymentStatus("all")
                            setSelectedProject("all")
                            setCurrentTab("all")
                          }}
                          className="mt-2"
                        >
                          清除筛选条件
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredCosts.length} 条费用记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>费用分布</CardTitle>
            <CardDescription>按费用类型的支出分布</CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            <ReactECharts
              option={getCostDistributionChartOption()}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'canvas' }}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>预算与实际对比</CardTitle>
            <CardDescription>各项目预算与实际支出的对比</CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            <ReactECharts
              option={getBudgetVsActualChartOption()}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'canvas' }}
            />
          </CardContent>
        </Card>
      </div>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>费用详情</DialogTitle>
            <DialogDescription>查看费用记录的详细信息</DialogDescription>
          </DialogHeader>
          {currentRecord && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">工程项目</Label>
                  <p className="font-medium">{currentRecord.project}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">费用类型</Label>
                  <p className="font-medium">{currentRecord.costType}</p>
                </div>
              </div>

              <div className="border-t pt-4">
                <Label className="text-xs text-muted-foreground mb-2 block">费用状态</Label>
                <div className="flex items-center gap-2">
                  {getStatusBadge(currentRecord.status)}
                  <span className="text-sm text-muted-foreground ml-2">支付状态: </span>
                  {getPaymentStatusBadge(currentRecord.paymentStatus)}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 border-t pt-4">
                <div>
                  <Label className="text-xs text-muted-foreground">预算金额</Label>
                  <p className="font-medium">{currentRecord.budgetAmount.toLocaleString()} 元</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">实际金额</Label>
                  <p className="font-medium">{currentRecord.actualAmount.toLocaleString()} 元</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">差异</Label>
                  <div>{getVarianceDisplay(currentRecord.variance)}</div>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">差异百分比</Label>
                  <div>{getVariancePercentDisplay(currentRecord.variancePercent)}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 border-t pt-4">
                <div>
                  <Label className="text-xs text-muted-foreground">审批人</Label>
                  <p className="font-medium">{currentRecord.approver}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">最后更新</Label>
                  <p className="font-medium">{currentRecord.lastUpdate}</p>
                </div>
              </div>

              {currentRecord.description && (
                <div className="border-t pt-4">
                  <Label className="text-xs text-muted-foreground">费用说明</Label>
                  <p className="text-sm whitespace-pre-wrap">{currentRecord.description}</p>
                </div>
              )}

              {currentRecord.invoice && (
                <div className="border-t pt-4">
                  <Label className="text-xs text-muted-foreground">发票信息</Label>
                  <p className="text-sm">{currentRecord.invoice}</p>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewDetailsOpen(false)}>关闭</Button>
            {currentRecord && (
              <Button variant="outline" onClick={() => {
                setIsViewDetailsOpen(false);
                prepareEdit(currentRecord);
              }}>
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) resetCostForm();
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑费用</DialogTitle>
            <DialogDescription>修改费用记录的信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-project">工程项目 <span className="text-red-500">*</span></Label>
                <Select
                  value={costForm.project}
                  onValueChange={(value) => handleFormChange("project", value)}
                >
                  <SelectTrigger id="edit-project">
                    <SelectValue placeholder="选择工程项目" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                    <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                    <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                    <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                    <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-cost-type">费用类型 <span className="text-red-500">*</span></Label>
                <Select
                  value={costForm.costType}
                  onValueChange={(value) => handleFormChange("costType", value)}
                >
                  <SelectTrigger id="edit-cost-type">
                    <SelectValue placeholder="选择费用类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="设备采购">设备采购</SelectItem>
                    <SelectItem value="人工费用">人工费用</SelectItem>
                    <SelectItem value="材料采购">材料采购</SelectItem>
                    <SelectItem value="系统集成">系统集成</SelectItem>
                    <SelectItem value="勘探费用">勘探费用</SelectItem>
                    <SelectItem value="其他费用">其他费用</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-budget-amount">预算金额 (元) <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-budget-amount"
                  type="number"
                  min="0"
                  placeholder="输入预算金额"
                  value={costForm.budgetAmount}
                  onChange={(e) => handleFormChange("budgetAmount", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-actual-amount">实际金额 (元) <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-actual-amount"
                  type="number"
                  min="0"
                  placeholder="输入实际金额"
                  value={costForm.actualAmount}
                  onChange={(e) => handleFormChange("actualAmount", e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-payment-status">支付状态 <span className="text-red-500">*</span></Label>
                <Select
                  value={costForm.paymentStatus}
                  onValueChange={(value) => handleFormChange("paymentStatus", value)}
                >
                  <SelectTrigger id="edit-payment-status">
                    <SelectValue placeholder="选择支付状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已支付">已支付</SelectItem>
                    <SelectItem value="部分支付">部分支付</SelectItem>
                    <SelectItem value="未支付">未支付</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-approver">审批人 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-approver"
                  placeholder="输入审批人姓名"
                  value={costForm.approver}
                  onChange={(e) => handleFormChange("approver", e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">费用说明</Label>
              <Textarea
                id="edit-description"
                placeholder="输入费用详细说明"
                rows={3}
                value={costForm.description}
                onChange={(e) => handleFormChange("description", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-invoice">发票信息</Label>
              <Input
                id="edit-invoice"
                placeholder="输入发票号码或相关信息"
                value={costForm.invoice}
                onChange={(e) => handleFormChange("invoice", e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditCost} disabled={isLoading}>
              {isLoading ? "保存中..." : "保存更改"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条费用记录吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCost}
              className="bg-red-500 hover:bg-red-600"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 导出数据对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>导出费用数据</DialogTitle>
            <DialogDescription>选择要导出的格式</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  className="flex-1 h-20 flex flex-col items-center justify-center hover:bg-green-50 border-green-100"
                  onClick={() => handleExportData('excel')}
                >
                  <FileText className="h-8 w-8 text-green-600 mb-2" />
                  <span>导出为Excel</span>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground text-center mt-2">
                导出{filteredCosts.length}条费用记录，包含所有字段和详细信息
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 高级筛选抽屉 */}
      <Sheet open={isFilterDrawerOpen} onOpenChange={setIsFilterDrawerOpen}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>高级筛选</SheetTitle>
            <SheetDescription>设置详细筛选条件</SheetDescription>
          </SheetHeader>
          <div className="py-6 space-y-6">
            <div className="space-y-2">
              <Label>工程项目</Label>
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger>
                  <SelectValue placeholder="选择工程项目" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有项目</SelectItem>
                  <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                  <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                  <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                  <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                  <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>费用类型</Label>
              <Select value={selectedCostType} onValueChange={setSelectedCostType}>
                <SelectTrigger>
                  <SelectValue placeholder="选择费用类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="设备采购">设备采购</SelectItem>
                  <SelectItem value="人工费用">人工费用</SelectItem>
                  <SelectItem value="材料采购">材料采购</SelectItem>
                  <SelectItem value="系统集成">系统集成</SelectItem>
                  <SelectItem value="勘探费用">勘探费用</SelectItem>
                  <SelectItem value="其他费用">其他费用</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>支付状态</Label>
              <Select value={selectedPaymentStatus} onValueChange={setSelectedPaymentStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="选择支付状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="已支付">已支付</SelectItem>
                  <SelectItem value="部分支付">部分支付</SelectItem>
                  <SelectItem value="未支付">未支付</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>费用状态</Label>
              <Select value={currentTab} onValueChange={setCurrentTab}>
                <SelectTrigger>
                  <SelectValue placeholder="选择费用状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="normal">正常</SelectItem>
                  <SelectItem value="over">超支</SelectItem>
                  <SelectItem value="under">节约</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>关键词搜索</Label>
              <Input
                placeholder="输入关键词搜索"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                搜索项目名称、费用类型或审批人
              </p>
            </div>
          </div>

          <SheetFooter className="pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("")
                setSelectedCostType("all")
                setSelectedPaymentStatus("all")
                setSelectedProject("all")
                setCurrentTab("all")
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button onClick={() => setIsFilterDrawerOpen(false)}>
              应用筛选
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 加载状态 */}
      {isLoading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p className="text-sm text-gray-600">处理中...</p>
          </div>
        </div>
      )}
    </div>
  )
}

