"use client"

import { useState } from "react"
import { <PERSON>, <PERSON>fresh<PERSON><PERSON>, AlertTriangle, Settings, Mail, Database, Clock, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "sonner"
import { message } from "antd"

export function SystemSettings() {
  const [language, setLanguage] = useState("zh-CN")
  const [sessionTimeout, setSessionTimeout] = useState("30")
  const [loading, setLoading] = useState(false)
  const [lastModified, setLastModified] = useState({
    time: "2025-03-20 15:30:45",
    user: "管理员"
  })

  const handleSave = async () => {
    setLoading(true)
    try {
      // 这里添加实际的保存逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新最后修改信息
      setLastModified({
        time: new Date().toLocaleString(),
        user: "当前用户"
      })

      message.success("设置保存成功！")
    } catch (error) {
      message.error("保存失败，请重试！")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50/30 py-12">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="bg-white rounded-xl shadow-sm p-8 mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">系统设置</h2>
              <p className="text-muted-foreground text-sm">配置系统的基本设置和参数</p>
            </div>
            <div className="flex flex-col sm:flex-row items-end sm:items-center gap-6">
              <div className="text-sm text-muted-foreground bg-gray-50 rounded-lg p-3">
                <div className="flex items-center gap-3 mb-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>最后修改时间：{lastModified.time}</span>
                </div>
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>修改人：{lastModified.user}</span>
                </div>
              </div>
              <Button onClick={handleSave} disabled={loading} size="lg" className="shadow-sm px-6">
                <Save className="h-5 w-5 mr-2" />
                {loading ? "保存中..." : "保存设置"}
              </Button>
            </div>
          </div>
        </div>

        <Card className="shadow-md border-0 rounded-xl overflow-hidden">
          <Tabs defaultValue="general" className="w-full">
            <TabsList className="w-full justify-start p-0 bg-gray-50/50 border-b sticky top-0 z-10">
              <TabsTrigger value="general" className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-primary px-8 py-4">
                <Settings className="h-5 w-5 mr-2" />
                基本设置
              </TabsTrigger>
              <TabsTrigger value="email" className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-primary px-8 py-4">
                <Mail className="h-5 w-5 mr-2" />
                邮件设置
              </TabsTrigger>
              <TabsTrigger value="backup" className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-primary px-8 py-4">
                <Database className="h-5 w-5 mr-2" />
                备份与恢复
              </TabsTrigger>
            </TabsList>

            <ScrollArea className="max-h-[70vh]">
              <div className="p-8">
                <TabsContent value="general" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="system-name">系统名称</Label>
                      <Input id="system-name" defaultValue="2025 矿业公司综合管理系统" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company-name">公司名称</Label>
                      <Input id="company-name" defaultValue="XX矿业有限公司" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="admin-email">管理员邮箱</Label>
                      <Input id="admin-email" type="email" defaultValue="<EMAIL>" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contact-phone">联系电话</Label>
                      <Input id="contact-phone" defaultValue="010-12345678" />
                    </div>
                  </div>

                  <Separator className="my-6" />

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="system-announcement">系统公告</Label>
                      <Textarea
                        id="system-announcement"
                        placeholder="输入系统公告内容，将显示在用户登录后的首页"
                        defaultValue="欢迎使用矿业公司综合管理系统，系统将于本周日凌晨2:00-4:00进行例行维护，请提前做好工作安排。"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label>系统语言</Label>
                        <Select value={language} onValueChange={setLanguage}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="选择系统语言" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="zh-CN">简体中文</SelectItem>
                            <SelectItem value="en-US">English (US)</SelectItem>
                            <SelectItem value="zh-TW">繁體中文</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>时区设置</Label>
                        <Select defaultValue="Asia/Shanghai">
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="选择时区" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Asia/Shanghai">中国标准时间 (UTC+8)</SelectItem>
                            <SelectItem value="America/New_York">美国东部时间 (UTC-5)</SelectItem>
                            <SelectItem value="Europe/London">格林威治标准时间 (UTC+0)</SelectItem>
                            <SelectItem value="Asia/Tokyo">日本标准时间 (UTC+9)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator className="my-6" />

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">安全设置</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">登录失败锁定</Label>
                            <p className="text-sm text-muted-foreground">连续登录失败后锁定账户</p>
                          </div>
                          <Switch defaultChecked />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="login-attempts">允许的登录失败次数</Label>
                          <Input
                            id="login-attempts"
                            type="number"
                            defaultValue="5"
                            min="1"
                            max="10"
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="session-timeout">会话超时时间（分钟）</Label>
                          <Select value={sessionTimeout} onValueChange={setSessionTimeout}>
                            <SelectTrigger id="session-timeout" className="w-full">
                              <SelectValue placeholder="选择超时时间" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="15">15 分钟</SelectItem>
                              <SelectItem value="30">30 分钟</SelectItem>
                              <SelectItem value="60">60 分钟</SelectItem>
                              <SelectItem value="120">120 分钟</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">强制密码复杂度</Label>
                            <p className="text-sm text-muted-foreground">要求用户设置复杂密码</p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="email" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="smtp-server">SMTP 服务器</Label>
                      <Input id="smtp-server" placeholder="smtp.example.com" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="smtp-port">SMTP 端口</Label>
                      <Input id="smtp-port" type="number" placeholder="587" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="smtp-username">SMTP 用户名</Label>
                      <Input id="smtp-username" type="email" placeholder="<EMAIL>" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="smtp-password">SMTP 密码</Label>
                      <Input id="smtp-password" type="password" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email-template">邮件模板</Label>
                    <Textarea
                      id="email-template"
                      placeholder="输入默认邮件模板内容"
                      rows={4}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">启用 SSL/TLS</Label>
                      <p className="text-sm text-muted-foreground">使用安全连接发送邮件</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </TabsContent>

                <TabsContent value="backup" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">自动备份</h3>
                        <p className="text-sm text-muted-foreground">配置系统自动备份策略</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label>备份频率</Label>
                        <Select defaultValue="daily">
                          <SelectTrigger>
                            <SelectValue placeholder="选择备份频率" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">每天</SelectItem>
                            <SelectItem value="weekly">每周</SelectItem>
                            <SelectItem value="monthly">每月</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>保留时间</Label>
                        <Select defaultValue="30">
                          <SelectTrigger>
                            <SelectValue placeholder="选择保留时间" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="7">7 天</SelectItem>
                            <SelectItem value="30">30 天</SelectItem>
                            <SelectItem value="90">90 天</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>备份路径</Label>
                      <Input defaultValue="/backup" />
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button variant="outline">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        立即备份
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </div>
            </ScrollArea>
          </Tabs>
        </Card>
      </div>
    </div>
  )
}

