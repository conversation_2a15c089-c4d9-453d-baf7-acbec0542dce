"use client"

import { useState, useMemo } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  Users,
  Truck,
  Wrench,
  DollarSign,
  Upload,
  Loader2,
  Edit,
  Eye,
  Trash2,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  ResponsiveContainer,
  BarChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Bar,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"

// 添加类型定义
interface Resource {
  id: string
  project: string
  resourceType: string
  name: string
  quantity: number
  unit: string
  allocated: number
  utilizationRate: number
  startDate: string
  endDate: string
  status: string
  isDisabled?: boolean
  department?: string
  description?: string
  location?: string
  manager?: string
}

interface FormData {
  project: string
  resourceType: string
  name: string
  quantity: number
  unit: string
  allocated: number
  startDate: string
  endDate: string
  department: string
  description: string
  location: string
  manager: string
}

interface ChartLabel {
  name: string
  percent: number
}

export function ProjectResources() {
  // 添加状态管理
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [currentResource, setCurrentResource] = useState<Resource | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [resourceToDelete, setResourceToDelete] = useState<Resource | null>(null)
  const { toast } = useToast()

  const [formData, setFormData] = useState<FormData>({
    project: "",
    resourceType: "",
    name: "",
    quantity: 0,
    unit: "",
    allocated: 0,
    startDate: "",
    endDate: "",
    department: "",
    description: "",
    location: "",
    manager: ""
  })

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)

  // 示例数据
  const resources = [
    {
      id: "1",
      project: "矿区A3开发项目",
      resourceType: "人力资源",
      name: "采矿工程师团队",
      quantity: 15,
      unit: "人",
      allocated: 12,
      utilizationRate: 80,
      startDate: "2023-10-15",
      endDate: "2023-12-30",
      status: "使用中",
    },
    {
      id: "2",
      project: "矿区A3开发项目",
      resourceType: "设备资源",
      name: "挖掘机",
      quantity: 5,
      unit: "台",
      allocated: 4,
      utilizationRate: 90,
      startDate: "2023-10-20",
      endDate: "2023-12-25",
      status: "使用中",
    },
    {
      id: "3",
      project: "设备更新计划",
      resourceType: "材料资源",
      name: "钢材",
      quantity: 200,
      unit: "吨",
      allocated: 150,
      utilizationRate: 75,
      startDate: "2023-11-01",
      endDate: "2024-01-15",
      status: "使用中",
    },
    {
      id: "4",
      project: "安全系统升级",
      resourceType: "人力资源",
      name: "安全工程师",
      quantity: 8,
      unit: "人",
      allocated: 8,
      utilizationRate: 100,
      startDate: "2023-11-15",
      endDate: "2023-12-10",
      status: "使用中",
    },
    {
      id: "5",
      project: "新矿区勘探",
      resourceType: "设备资源",
      name: "勘探设备",
      quantity: 10,
      unit: "套",
      allocated: 6,
      utilizationRate: 60,
      startDate: "2023-12-01",
      endDate: "2024-03-01",
      status: "使用中",
    },
    {
      id: "6",
      project: "环保设施改造",
      resourceType: "资金资源",
      name: "环保设备采购资金",
      quantity: 1000000,
      unit: "元",
      allocated: 350000,
      utilizationRate: 35,
      startDate: "2023-12-05",
      endDate: "2024-02-15",
      status: "使用中",
    },
  ]

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "使用中":
        return <Badge className="bg-green-500">使用中</Badge>
      case "已分配":
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            已分配
          </Badge>
        )
      case "未分配":
        return <Badge variant="secondary">未分配</Badge>
      case "已归还":
        return (
          <Badge variant="outline" className="text-gray-500 border-gray-500">
            已归还
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取资源类型对应的图标
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case "人力资源":
        return <Users className="h-4 w-4 text-blue-500" />
      case "设备资源":
        return <Wrench className="h-4 w-4 text-yellow-500" />
      case "材料资源":
        return <Truck className="h-4 w-4 text-green-500" />
      case "资金资源":
        return <DollarSign className="h-4 w-4 text-purple-500" />
      default:
        return null
    }
  }

  // 筛选资源
  const filteredResources = useMemo(() => {
    return resources.filter(resource => {
      const matchesSearch = 
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.resourceType.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesType = selectedType === "all" ? true : resource.resourceType === selectedType
      const matchesStatus = selectedStatus === "all" ? true : resource.status === selectedStatus
      
      return matchesSearch && matchesType && matchesStatus
    })
  }, [resources, searchTerm, selectedType, selectedStatus])

  // 更新统计数据
  const statistics = useMemo(() => {
    return {
      total: filteredResources.length,
      inUse: filteredResources.filter(r => r.status === "使用中").length,
      allocated: filteredResources.filter(r => r.status === "已分配").length,
      unallocated: filteredResources.filter(r => r.status === "未分配").length
    }
  }, [filteredResources])

  // 处理导出
  const handleExport = async () => {
    setIsLoading(true)
    try {
      const headers = ['工程项目', '资源类型', '资源名称', '数量', '单位', '已分配', '利用率', '开始日期', '结束日期', '状态']
      const csvData = resources.map(resource => [
        resource.project,
        resource.resourceType,
        resource.name,
        resource.quantity,
        resource.unit,
        resource.allocated,
        resource.utilizationRate,
        resource.startDate,
        resource.endDate,
        resource.status
      ])
      
      const csvContent = [headers, ...csvData].map(row => row.join(',')).join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `工程资源管理_${new Date().toLocaleDateString()}.csv`
      link.click()
      
      toast({
        title: "导出成功",
        description: "数据已成功导出为CSV文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    setIsLoading(true)
    try {
      const text = await file.text()
      const rows = text.split('\n').map(row => row.split(','))
      const headers = rows[0]
      
      const importedResources = rows.slice(1).map(row => ({
        id: Math.random().toString(36).substr(2, 9),
        project: row[0],
        resourceType: row[1],
        name: row[2],
        quantity: parseInt(row[3]),
        unit: row[4],
        allocated: parseInt(row[5]),
        utilizationRate: parseInt(row[6]),
        startDate: row[7],
        endDate: row[8],
        status: row[9],
        isDisabled: false
      }))
      
      // 这里应该调用API来保存导入的数据
      // setResources([...resources, ...importedResources])
      
      toast({
        title: "导入成功",
        description: `成功导入 ${importedResources.length} 条记录`,
      })
      
      event.target.value = ''
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理保存资源
  const handleSaveResource = async () => {
    setIsLoading(true)
    try {
      // 这里添加保存资源的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "保存成功",
        description: "资源信息已保存",
      })
      setIsAddDialogOpen(false)
      // 重置表单
      setFormData({
        project: "",
        resourceType: "",
        name: "",
        quantity: 0,
        unit: "",
        allocated: 0,
        startDate: "",
        endDate: "",
        department: "",
        description: "",
        location: "",
        manager: ""
      })
    } catch (error) {
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理删除资源
  const handleDeleteResource = (resource: Resource) => {
    setResourceToDelete(resource)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!resourceToDelete) return
    
    setIsLoading(true)
    try {
      // 这里添加删除资源的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "删除成功",
        description: "资源已删除",
      })
      setIsDeleteDialogOpen(false)
      setResourceToDelete(null)
    } catch (error) {
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 添加统计分析数据计算函数
  const getAnalysisData = () => {
    const typeData = resources.reduce((acc, resource) => {
      const type = resource.resourceType
      if (!acc[type]) {
        acc[type] = {
          数量: 0,
          已分配: 0,
          利用率: 0,
          count: 0
        }
      }
      acc[type].数量 += resource.quantity
      acc[type].已分配 += resource.allocated
      acc[type].利用率 += resource.utilizationRate
      acc[type].count += 1
      return acc
    }, {} as Record<string, { 数量: number; 已分配: number; 利用率: number; count: number }>)

    return Object.entries(typeData).map(([type, data]) => ({
      name: type,
      数量: data.数量,
      已分配: data.已分配,
      平均利用率: Math.round(data.利用率 / data.count)
    }))
  }

  // 处理查看详情
  const handleViewDetails = (resource: Resource) => {
    setCurrentResource(resource)
    setIsViewDetailsOpen(true)
  }

  // 处理编辑资源
  const handleEditResource = (resource: Resource) => {
    setFormData({
      project: resource.project,
      resourceType: resource.resourceType,
      name: resource.name,
      quantity: resource.quantity,
      unit: resource.unit,
      allocated: resource.allocated,
      startDate: resource.startDate,
      endDate: resource.endDate,
      department: resource.department || "",
      description: resource.description || "",
      location: resource.location || "",
      manager: resource.manager || ""
    })
    setCurrentResource(resource)
    setIsViewDetailsOpen(false)
    setIsEditDialogOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = async () => {
    setIsLoading(true)
    try {
      // 这里添加更新资源的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "更新成功",
        description: "资源信息已更新",
      })
      setIsEditDialogOpen(false)
    } catch (error) {
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理添加资源
  const handleAddResource = () => {
    setFormData({
      project: "",
      resourceType: "",
      name: "",
      quantity: 0,
      unit: "",
      allocated: 0,
      startDate: "",
      endDate: "",
      department: "",
      description: "",
      location: "",
      manager: ""
    })
    setIsAddDialogOpen(true)
  }

  // 处理保存新资源
  const handleSaveNewResource = async () => {
    setIsLoading(true)
    try {
      // 这里添加保存新资源的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "添加成功",
        description: "新资源已添加",
      })
      setIsAddDialogOpen(false)
    } catch (error) {
      toast({
        title: "添加失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6 max-w-[1600px] mx-auto px-4">
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold text-gray-800">工程资源管理</h2>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 bg-gray-50 p-1.5 rounded-lg">
            <Button variant="ghost" size="sm" onClick={handleExport} className="hover:bg-white hover:shadow-sm transition-all">
              <Download className="h-4 w-4 mr-2 text-gray-600" />
              导出数据
            </Button>
            <div className="relative">
              <input
                type="file"
                accept=".csv"
                onChange={handleImport}
                className="hidden"
                id="import-file"
              />
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => document.getElementById('import-file')?.click()}
                className="hover:bg-white hover:shadow-sm transition-all"
              >
                <Upload className="h-4 w-4 mr-2 text-gray-600" />
                导入数据
              </Button>
            </div>
            <Dialog open={isAnalysisDialogOpen} onOpenChange={setIsAnalysisDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="hover:bg-white hover:shadow-sm transition-all">
                  <BarChart2 className="h-4 w-4 mr-2 text-gray-600" />
                  统计分析
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
                <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
                  <DialogTitle>资源统计分析</DialogTitle>
                  <DialogDescription>查看资源使用情况的详细统计数据</DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4">
                  <div className="grid grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">{resources.length}</div>
                        <p className="text-xs text-muted-foreground">资源总数</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">
                          {Math.round(resources.reduce((acc, r) => acc + r.utilizationRate, 0) / resources.length)}%
                        </div>
                        <p className="text-xs text-muted-foreground">平均利用率</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">
                          {resources.filter(r => r.utilizationRate > 80).length}
                        </div>
                        <p className="text-xs text-muted-foreground">高利用率资源</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">
                          {resources.filter(r => r.utilizationRate < 30).length}
                        </div>
                        <p className="text-xs text-muted-foreground">低利用率资源</p>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader className="sticky top-0 bg-white z-10">
                      <CardTitle>资源类型分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={getAnalysisData()}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="数量" fill="#8884d8" />
                            <Bar dataKey="已分配" fill="#82ca9d" />
                            <Bar dataKey="平均利用率" fill="#ffc658" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="sticky top-0 bg-white z-10">
                        <CardTitle>资源状态分布</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[200px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Pie
                                data={[
                                  { name: '使用中', value: resources.filter(r => r.status === '使用中').length },
                                  { name: '已分配', value: resources.filter(r => r.status === '已分配').length },
                                  { name: '未分配', value: resources.filter(r => r.status === '未分配').length },
                                  { name: '已归还', value: resources.filter(r => r.status === '已归还').length }
                                ]}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={({ name, percent }: ChartLabel) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                              >
                                {resources.map((_, index) => (
                                  <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042'][index % 4]} />
                                ))}
                              </Pie>
                              <Tooltip />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="sticky top-0 bg-white z-10">
                        <CardTitle>利用率趋势</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[200px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={resources}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="name" />
                              <YAxis />
                              <Tooltip />
                              <Line type="monotone" dataKey="utilizationRate" stroke="#8884d8" />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="sticky top-0 bg-white z-10">
                        <CardTitle>资源分配情况</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={resources}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="name" />
                              <YAxis />
                              <Tooltip />
                              <Legend />
                              <Bar dataKey="quantity" name="总数量" fill="#8884d8" />
                              <Bar dataKey="allocated" name="已分配" fill="#82ca9d" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="sticky top-0 bg-white z-10">
                        <CardTitle>资源利用率趋势</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={resources}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="name" />
                              <YAxis domain={[0, 100]} />
                              <Tooltip />
                              <Legend />
                              <Line type="monotone" dataKey="utilizationRate" name="利用率" stroke="#8884d8" />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white shadow-sm" onClick={handleAddResource}>
                <Plus className="h-4 w-4 mr-2" />
                添加资源
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>添加工程资源</DialogTitle>
                <DialogDescription>添加新的工程资源</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目</Label>
                    <Select>
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mining-a3">矿区A3开发项目</SelectItem>
                        <SelectItem value="equipment-update">设备更新计划</SelectItem>
                        <SelectItem value="safety-upgrade">安全系统升级</SelectItem>
                        <SelectItem value="exploration">新矿区勘探</SelectItem>
                        <SelectItem value="environmental">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="resource-type">资源类型</Label>
                    <Select>
                      <SelectTrigger id="resource-type">
                        <SelectValue placeholder="选择资源类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="human">人力资源</SelectItem>
                        <SelectItem value="equipment">设备资源</SelectItem>
                        <SelectItem value="material">材料资源</SelectItem>
                        <SelectItem value="financial">资金资源</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="resource-name">资源名称</Label>
                  <Input id="resource-name" placeholder="输入资源名称" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">数量</Label>
                    <Input id="quantity" type="number" min="0" placeholder="输入资源数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">单位</Label>
                    <Input id="unit" placeholder="输入单位" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-date">开始日期</Label>
                    <Input id="start-date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date">结束日期</Label>
                    <Input id="end-date" type="date" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="allocated">已分配数量</Label>
                    <Input id="allocated" type="number" min="0" placeholder="输入已分配数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="in-use">使用中</SelectItem>
                        <SelectItem value="allocated">已分配</SelectItem>
                        <SelectItem value="unallocated">未分配</SelectItem>
                        <SelectItem value="returned">已归还</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">资源描述</Label>
                  <Textarea id="description" placeholder="输入资源详细描述" rows={3} />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleSaveNewResource}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-50 p-3 mb-4">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-blue-600">45</h3>
            <p className="text-sm text-gray-600">人力资源</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-50 p-3 mb-4">
              <Wrench className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-2xl font-bold text-yellow-600">28</h3>
            <p className="text-sm text-gray-600">设备资源</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-50 p-3 mb-4">
              <Truck className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-green-600">15</h3>
            <p className="text-sm text-gray-600">材料资源</p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-purple-50 p-3 mb-4">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-2xl font-bold text-purple-600">5</h3>
            <p className="text-sm text-gray-600">资金资源</p>
          </CardContent>
        </Card>
      </div>

      <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
        <CardHeader className="border-b bg-gray-50/50">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-xl text-gray-800">工程资源列表</CardTitle>
              <CardDescription>查看和管理所有工程资源</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  type="search" 
                  placeholder="搜索资源..." 
                  className="pl-9 w-[280px] bg-gray-50 border-gray-200 focus:bg-white transition-colors"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-[160px] bg-gray-50 border-gray-200">
                  <SelectValue placeholder="资源类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="人力资源">人力资源</SelectItem>
                  <SelectItem value="设备资源">设备资源</SelectItem>
                  <SelectItem value="材料资源">材料资源</SelectItem>
                  <SelectItem value="资金资源">资金资源</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[160px] bg-gray-50 border-gray-200">
                  <SelectValue placeholder="使用状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="使用中">使用中</SelectItem>
                  <SelectItem value="已分配">已分配</SelectItem>
                  <SelectItem value="未分配">未分配</SelectItem>
                  <SelectItem value="已归还">已归还</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 overflow-hidden">
            <Table>
              <TableHeader className="bg-gray-50/50">
                <TableRow>
                  <TableHead className="font-semibold">工程项目</TableHead>
                  <TableHead className="font-semibold">资源类型</TableHead>
                  <TableHead className="font-semibold">资源名称</TableHead>
                  <TableHead className="font-semibold">数量/单位</TableHead>
                  <TableHead className="font-semibold">已分配</TableHead>
                  <TableHead className="font-semibold">利用率</TableHead>
                  <TableHead className="font-semibold">开始日期</TableHead>
                  <TableHead className="font-semibold">结束日期</TableHead>
                  <TableHead className="font-semibold">状态</TableHead>
                  <TableHead className="text-right font-semibold">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResources.map((resource) => (
                  <TableRow key={resource.id} className="hover:bg-gray-50/50">
                    <TableCell className="font-medium">{resource.project}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getResourceTypeIcon(resource.resourceType)}
                        <span>{resource.resourceType}</span>
                      </div>
                    </TableCell>
                    <TableCell>{resource.name}</TableCell>
                    <TableCell>
                      {resource.quantity} {resource.unit}
                    </TableCell>
                    <TableCell>
                      {resource.allocated} {resource.unit}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress 
                          value={resource.utilizationRate} 
                          className="h-2 w-[60px]"
                          indicatorClassName={
                            resource.utilizationRate > 80 ? "bg-green-500" :
                            resource.utilizationRate > 50 ? "bg-blue-500" :
                            "bg-yellow-500"
                          }
                        />
                        <span className={
                          resource.utilizationRate > 80 ? "text-green-600" :
                          resource.utilizationRate > 50 ? "text-blue-600" :
                          "text-yellow-600"
                        }>{resource.utilizationRate}%</span>
                      </div>
                    </TableCell>
                    <TableCell>{resource.startDate}</TableCell>
                    <TableCell>{resource.endDate}</TableCell>
                    <TableCell>{getStatusBadge(resource.status)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-blue-50 hover:text-blue-600"
                        onClick={() => handleViewDetails(resource)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        详情
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-green-50 hover:text-green-600"
                        onClick={() => handleEditResource(resource)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-red-50 hover:text-red-600"
                        onClick={() => handleDeleteResource(resource)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between py-4 px-6 border-t bg-gray-50/50">
          <div className="text-sm text-gray-600">共 {filteredResources.length} 个资源</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled className="bg-white">
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3 bg-white">
              1
            </Button>
            <Button variant="outline" size="sm" disabled className="bg-white">
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个资源吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>资源详情</DialogTitle>
            <DialogDescription>查看资源的详细信息</DialogDescription>
          </DialogHeader>
          {currentResource && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>工程项目</Label>
                  <p className="text-base mt-1">{currentResource.project}</p>
                </div>
                <div>
                  <Label>资源类型</Label>
                  <p className="text-base mt-1">{currentResource.resourceType}</p>
                </div>
                <div>
                  <Label>资源名称</Label>
                  <p className="text-base mt-1">{currentResource.name}</p>
                </div>
                <div>
                  <Label>数量/单位</Label>
                  <p className="text-base mt-1">{currentResource.quantity} {currentResource.unit}</p>
                </div>
                <div>
                  <Label>已分配</Label>
                  <p className="text-base mt-1">{currentResource.allocated} {currentResource.unit}</p>
                </div>
                <div>
                  <Label>利用率</Label>
                  <p className="text-base mt-1">{currentResource.utilizationRate}%</p>
                </div>
                <div>
                  <Label>开始日期</Label>
                  <p className="text-base mt-1">{currentResource.startDate}</p>
                </div>
                <div>
                  <Label>结束日期</Label>
                  <p className="text-base mt-1">{currentResource.endDate}</p>
                </div>
                {currentResource.department && (
                  <div>
                    <Label>所属部门</Label>
                    <p className="text-base mt-1">{currentResource.department}</p>
                  </div>
                )}
                {currentResource.location && (
                  <div>
                    <Label>位置</Label>
                    <p className="text-base mt-1">{currentResource.location}</p>
                  </div>
                )}
                {currentResource.manager && (
                  <div>
                    <Label>负责人</Label>
                    <p className="text-base mt-1">{currentResource.manager}</p>
                  </div>
                )}
              </div>
              {currentResource.description && (
                <div>
                  <Label>资源描述</Label>
                  <p className="text-base mt-1 whitespace-pre-wrap">{currentResource.description}</p>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => handleEditResource(currentResource!)}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
            <Button onClick={() => setIsViewDetailsOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑资源对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑工程资源</DialogTitle>
            <DialogDescription>修改工程资源信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project">工程项目</Label>
                <Select>
                  <SelectTrigger id="project">
                    <SelectValue placeholder="选择工程项目" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mining-a3">矿区A3开发项目</SelectItem>
                    <SelectItem value="equipment-update">设备更新计划</SelectItem>
                    <SelectItem value="safety-upgrade">安全系统升级</SelectItem>
                    <SelectItem value="exploration">新矿区勘探</SelectItem>
                    <SelectItem value="environmental">环保设施改造</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="resource-type">资源类型</Label>
                <Select>
                  <SelectTrigger id="resource-type">
                    <SelectValue placeholder="选择资源类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="human">人力资源</SelectItem>
                    <SelectItem value="equipment">设备资源</SelectItem>
                    <SelectItem value="material">材料资源</SelectItem>
                    <SelectItem value="financial">资金资源</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="resource-name">资源名称</Label>
              <Input id="resource-name" placeholder="输入资源名称" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">数量</Label>
                <Input id="quantity" type="number" min="0" placeholder="输入资源数量" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="unit">单位</Label>
                <Input id="unit" placeholder="输入单位" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">开始日期</Label>
                <Input id="start-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date">结束日期</Label>
                <Input id="end-date" type="date" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="allocated">已分配数量</Label>
                <Input id="allocated" type="number" min="0" placeholder="输入已分配数量" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="in-use">使用中</SelectItem>
                    <SelectItem value="allocated">已分配</SelectItem>
                    <SelectItem value="unallocated">未分配</SelectItem>
                    <SelectItem value="returned">已归还</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">资源描述</Label>
              <Textarea id="description" placeholder="输入资源详细描述" rows={3} />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>更新</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isLoading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <p className="text-sm text-gray-600">处理中...</p>
          </div>
        </div>
      )}
    </div>
  )
}

