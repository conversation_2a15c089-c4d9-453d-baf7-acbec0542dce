"use client"

import { useState, useEffect } from "react"

// 定义安全检查数据类型
export interface SafetyCheck {
  id: string
  name: string
  type: string
  department: string
  inspector: string
  date: string
  status: string
  issues: number
  location?: string
  items?: string[]
  description?: string
}

// 初始数据
const initialSafetyChecks: SafetyCheck[] = [
  {
    id: "1",
    name: "矿区A3安全例行检查",
    type: "例行检查",
    department: "安全管理部",
    inspector: "张三",
    date: "2025-03-01",
    status: "已完成",
    issues: 0,
  },
  {
    id: "2",
    name: "设备安全检查",
    type: "专项检查",
    department: "设备管理部",
    inspector: "李四",
    date: "2025-02-28",
    status: "已完成",
    issues: 2,
  },
  {
    id: "3",
    name: "爆破区域安全检查",
    type: "专项检查",
    department: "安全管理部",
    inspector: "王五",
    date: "2025-02-25",
    status: "已完成",
    issues: 1,
  },
  {
    id: "4",
    name: "矿区B2安全例行检查",
    type: "例行检查",
    department: "安全管理部",
    inspector: "赵六",
    date: "2025-03-02",
    status: "进行中",
    issues: 0,
  },
  {
    id: "5",
    name: "消防设备检查",
    type: "专项检查",
    department: "安全管理部",
    inspector: "张三",
    date: "2025-03-05",
    status: "计划中",
    issues: 0,
  },
]

export function useSafetyCheckData() {
  // 使用localStorage持久化数据
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheck[]>([])
  const [loading, setLoading] = useState(true)

  // 初始化数据
  useEffect(() => {
    const storedData = localStorage.getItem("safety-check-data")
    if (storedData) {
      setSafetyChecks(JSON.parse(storedData))
    } else {
      setSafetyChecks(initialSafetyChecks)
      localStorage.setItem("safety-check-data", JSON.stringify(initialSafetyChecks))
    }
    setLoading(false)
  }, [])

  // 保存数据到localStorage
  const saveData = (data: SafetyCheck[]) => {
    localStorage.setItem("safety-check-data", JSON.stringify(data))
    setSafetyChecks(data)
  }

  // 添加安全检查
  const addSafetyCheck = (newCheck: Omit<SafetyCheck, "id">) => {
    const id = Date.now().toString()
    const checkWithId = { ...newCheck, id }
    const updatedChecks = [...safetyChecks, checkWithId]
    saveData(updatedChecks)
    return checkWithId
  }

  // 更新安全检查
  const updateSafetyCheck = (id: string, updatedData: Partial<SafetyCheck>) => {
    const updatedChecks = safetyChecks.map(check =>
      check.id === id ? { ...check, ...updatedData } : check
    )
    saveData(updatedChecks)
    return updatedChecks.find(check => check.id === id)
  }

  // 删除安全检查
  const deleteSafetyCheck = (id: string) => {
    const updatedChecks = safetyChecks.filter(check => check.id !== id)
    saveData(updatedChecks)
  }

  // 获取单个安全检查
  const getSafetyCheck = (id: string) => {
    return safetyChecks.find(check => check.id === id)
  }

  // 筛选安全检查
  const filterSafetyChecks = (filters: {
    search?: string
    type?: string
    status?: string
  }) => {
    let filtered = [...safetyChecks]

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(
        check =>
          check.name.toLowerCase().includes(searchLower) ||
          check.inspector.toLowerCase().includes(searchLower) ||
          check.department.toLowerCase().includes(searchLower)
      )
    }

    if (filters.type && filters.type !== "all") {
      filtered = filtered.filter(check =>
        check.type === (filters.type === "routine" ? "例行检查" : "专项检查")
      )
    }

    if (filters.status && filters.status !== "all") {
      const statusMap: Record<string, string> = {
        "completed": "已完成",
        "in-progress": "进行中",
        "planned": "计划中"
      }
      filtered = filtered.filter(check => check.status === statusMap[filters.status || ""])
    }

    return filtered
  }

  // 获取统计数据
  const getStatistics = () => {
    const completed = safetyChecks.filter(check => check.status === "已完成").length
    const totalIssues = safetyChecks.reduce((sum, check) => sum + check.issues, 0)
    const unresolvedIssues = 2 // 这里应该根据实际数据计算，暂时使用固定值

    return {
      completed,
      totalIssues,
      unresolvedIssues
    }
  }

  return {
    safetyChecks,
    loading,
    addSafetyCheck,
    updateSafetyCheck,
    deleteSafetyCheck,
    getSafetyCheck,
    filterSafetyChecks,
    getStatistics
  }
}