"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  Zap,
  Droplet,
  Flame,
  Wind,
  BarChart,
  TrendingUp,
  TrendingDown,
  LineChart as LucideLineChart,
  PieChart,
  AreaChart,
  ArrowUpRight,
  ArrowDownRight,
  Battery,
  FileText,
  Info,
  Edit,
  Trash,
  ChevronRight,
  AlertTriangle,
  RefreshCw,
  Loader2,
  Gauge,
  LinkIcon,
  PanelLeft,
  ListFilter,
  BatteryChargingIcon,
  FlameIcon,
  DropletIcon,
  CloudFogIcon,
  TrendingUpIcon,
  LeafIcon,
  Lightbulb,
  MoreHorizontal,
  Eye
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"
import {
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  BarChart as RechartsBarChart
} from "recharts"

// 能源消耗记录接口
interface EnergyRecord {
  id: string;
  date: string;
  type: string;
  location: string;
  consumption: number;
  unit: string;
  cost: number;
  status: string;
  trend: string;
  manager: string;
  description?: string;
  efficiency?: number;
}

// 月度数据接口
interface MonthlyData {
  month: string;
  "电力"?: number;
  "天然气"?: number;
  "水"?: number;
  "蒸汽"?: number;
  "电力同比"?: number;
  "天然气同比"?: number;
  "水同比"?: number;
  "蒸汽同比"?: number;
  "电力环比"?: number;
  "天然气环比"?: number;
  "水环比"?: number;
  "蒸汽环比"?: number;
  "电力预测"?: boolean;
  "天然气预测"?: boolean;
  "水预测"?: boolean;
  "蒸汽预测"?: boolean;
  [key: string]: string | number | boolean | undefined;
}

// 添加智能分析结果接口
interface AnalysisResult {
  id: string;
  type: "anomaly" | "trend" | "recommendation" | "alert";
  title: string;
  description: string;
  impact: "high" | "medium" | "low";
  date: string;
  source: string;
  relatedType?: string;
  relatedTime?: string;
  suggestedAction?: string;
  potentialSaving?: number;
  confidence?: number;
}

export function EnergyConsumptionManagement() {
  const { toast } = useToast()
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewSheetOpen, setIsViewSheetOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("table")
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split("T")[0])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [currentRecord, setCurrentRecord] = useState<EnergyRecord | null>(null)
  const [formData, setFormData] = useState<Partial<EnergyRecord>>({
    date: new Date().toISOString().split("T")[0],
    type: "",
    location: "",
    consumption: 0,
    unit: "",
    cost: 0,
    manager: "",
    description: "",
  })
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([])
  const [forecastData, setForecastData] = useState<MonthlyData[]>([])
  const [timeRange, setTimeRange] = useState<"6months" | "12months" | "2years">("12months")
  const [selectedEnergyType, setSelectedEnergyType] = useState<string>("全部")
  const [isDetailSheetOpen, setIsDetailSheetOpen] = useState(false)
  const [recordToDelete, setRecordToDelete] = useState<EnergyRecord | null>(null)

  // 添加智能分析结果数据
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([
    {
      id: "1",
      type: "anomaly",
      title: "B区用电量异常波动",
      description: "B区在3月15日至3月20日期间用电量比平均值高出37%，可能存在设备异常或能源浪费。",
      impact: "high",
      date: "2025-03-21",
      source: "智能能耗监测系统",
      relatedType: "电力",
      relatedTime: "2025-03-15 至 2025-03-20",
      suggestedAction: "检查B区主要用电设备运行状态，排查异常耗电设备",
      confidence: 92
    },
    {
      id: "2",
      type: "trend",
      title: "夏季空调能耗预测",
      description: "根据历史数据分析，预计今年夏季空调用电将增加25%，建议提前做好用电计划。",
      impact: "medium",
      date: "2025-04-10",
      source: "能源趋势分析系统",
      relatedType: "电力",
      relatedTime: "2025-03-01 至 2025-04-15",
      suggestedAction: "提前排查空调系统，优化设置温度，实施错峰用电",
      confidence: 85
    },
    {
      id: "3",
      type: "recommendation",
      title: "天然气使用优化建议",
      description: "分析显示工艺加热过程存在能量损失，优化加热时间和温度可节省15%的天然气消耗。",
      impact: "medium",
      date: "2025-03-30",
      source: "能源效率分析系统",
      relatedType: "天然气",
      suggestedAction: "调整加热工艺参数，增加保温措施",
      potentialSaving: 15000,
      confidence: 78
    },
    {
      id: "4",
      type: "alert",
      title: "用水量突增预警",
      description: "C区近三天用水量持续异常增长，累计高于正常值45%，可能存在管道泄漏。",
      impact: "high",
      date: "2025-04-05",
      source: "水资源监测系统",
      relatedType: "水",
      relatedTime: "2025-04-03 至 2025-04-05",
      suggestedAction: "立即检查C区管道系统，特别是地下管网",
      confidence: 89
    },
    {
      id: "5",
      type: "recommendation",
      title: "照明系统改造建议",
      description: "将现有照明系统更换为LED智能照明，预计可降低照明用电30%，投资回收期约14个月。",
      impact: "medium",
      date: "2025-03-25",
      source: "能源投资分析系统",
      relatedType: "电力",
      suggestedAction: "启动照明系统改造项目，优先改造高耗能区域",
      potentialSaving: 25000,
      confidence: 92
    }
  ]);

  // 添加假设分析相关状态
  const [showScenarioAnalysis, setShowScenarioAnalysis] = useState(false);
  const [scenarioParams, setScenarioParams] = useState({
    electricityReduction: 10,
    gasReduction: 15,
    waterReduction: 8,
    investmentAmount: 200000
  });

  // 假设分析结果
  const [scenarioResults, setScenarioResults] = useState({
    annualSaving: 0,
    paybackPeriod: 0,
    carbonReduction: 0,
    peakReduction: 0
  });

  // 计算假设分析结果
  useEffect(() => {
    // 模拟计算节省金额
    const electricitySaving = monthlyData.reduce((sum, item) => sum + (item["电力"] || 0), 0) * 0.8 * (scenarioParams.electricityReduction / 100);
    const gasSaving = monthlyData.reduce((sum, item) => sum + (item["天然气"] || 0), 0) * 3 * (scenarioParams.gasReduction / 100);
    const waterSaving = monthlyData.reduce((sum, item) => sum + (item["水"] || 0), 0) * 3 * (scenarioParams.waterReduction / 100);

    const totalAnnualSaving = (electricitySaving + gasSaving + waterSaving) * 12 / monthlyData.length;
    const paybackPeriod = scenarioParams.investmentAmount / totalAnnualSaving;
    const carbonReduction = electricitySaving * 0.5 + gasSaving * 2.1; // 简化的碳排放计算

    setScenarioResults({
      annualSaving: Math.round(totalAnnualSaving),
      paybackPeriod: Math.round(paybackPeriod * 10) / 10,
      carbonReduction: Math.round(carbonReduction),
      peakReduction: Math.round(electricitySaving * 0.3)
    });
  }, [scenarioParams, monthlyData]);

  // 获取分析结果图标
  const getAnalysisTypeIcon = (type: string) => {
    switch (type) {
      case "anomaly":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "trend":
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case "recommendation":
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      case "alert":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // 获取影响程度标签
  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "high":
        return <Badge className="bg-red-500">高影响</Badge>;
      case "medium":
        return <Badge className="bg-yellow-500">中等影响</Badge>;
      case "low":
        return <Badge className="bg-blue-500">低影响</Badge>;
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 示例数据
  const energyRecords = [
    {
      id: "1",
      date: "2025-03-01",
      type: "电力",
      location: "A区生产线",
      consumption: 15000,
      unit: "kWh",
      cost: 12000,
      status: "正常",
      trend: "上升",
      manager: "张工",
      description: "主要用于A区生产线设备运行和照明",
      efficiency: 85
    },
    {
      id: "2",
      date: "2025-03-01",
      type: "天然气",
      location: "B区锅炉房",
      consumption: 5000,
      unit: "m³",
      cost: 15000,
      status: "正常",
      trend: "平稳",
      manager: "李工",
      description: "用于B区锅炉加热和工艺处理",
      efficiency: 78
    },
    {
      id: "3",
      date: "2025-03-01",
      type: "水",
      location: "全厂区",
      consumption: 2000,
      unit: "吨",
      cost: 6000,
      status: "异常",
      trend: "下降",
      manager: "王工",
      description: "全厂区用水，包括生产用水和生活用水",
      efficiency: 65
    },
    {
      id: "4",
      date: "2025-03-01",
      type: "蒸汽",
      location: "C区加工车间",
      consumption: 800,
      unit: "吨",
      cost: 24000,
      status: "正常",
      trend: "平稳",
      manager: "赵工",
      description: "C区加工车间工艺处理用蒸汽",
      efficiency: 80
    },
    {
      id: "5",
      date: "2025-03-02",
      type: "电力",
      location: "D区办公楼",
      consumption: 1200,
      unit: "kWh",
      cost: 960,
      status: "正常",
      trend: "下降",
      manager: "张工",
      description: "D区办公楼照明和设备用电",
      efficiency: 90
    },
  ]

  const [records, setRecords] = useState<EnergyRecord[]>(energyRecords)
  const [filteredRecords, setFilteredRecords] = useState<EnergyRecord[]>([])

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500 hover:bg-green-600 transition-colors">正常</Badge>
      case "异常":
        return <Badge variant="destructive" className="hover:bg-red-600 transition-colors">异常</Badge>
      case "警告":
        return (
          <Badge variant="outline" className="text-amber-500 border-amber-500 hover:bg-amber-50 transition-colors">
            警告
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取趋势对应的徽章样式
  const getTrendBadge = (trend: string) => {
    switch (trend) {
      case "上升":
        return (
          <Badge variant="outline" className="text-red-500 border-red-500 hover:bg-red-50 transition-colors flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            上升
          </Badge>
        )
      case "下降":
        return (
          <Badge variant="outline" className="text-green-500 border-green-500 hover:bg-green-50 transition-colors flex items-center gap-1">
            <TrendingDown className="h-3 w-3" />
            下降
          </Badge>
        )
      case "平稳":
        return (
          <Badge variant="secondary" className="hover:bg-gray-200 transition-colors flex items-center gap-1">
            <LineChart className="h-3 w-3" />
            平稳
          </Badge>
        )
      default:
        return <Badge>{trend}</Badge>
    }
  }

  // 计算总统计数据
  const statistics = useMemo(() => {
    return {
      totalCost: records.reduce((sum, record) => sum + record.cost, 0),
      totalElectricity: records.filter(record => record.type === "电力").reduce((sum, record) => sum + record.consumption, 0),
      totalGas: records.filter(record => record.type === "天然气").reduce((sum, record) => sum + record.consumption, 0),
      totalWater: records.filter(record => record.type === "水").reduce((sum, record) => sum + record.consumption, 0),
      totalSteam: records.filter(record => record.type === "蒸汽").reduce((sum, record) => sum + record.consumption, 0),
      avgEfficiency: Math.round(records.reduce((sum, record) => sum + (record.efficiency || 0), 0) / records.length),
    }
  }, [records])

  // 计算能源消耗比例分布
  const energyDistribution = useMemo(() => {
    const types = ["电力", "天然气", "水", "蒸汽"];
    const totalConsumption = types.reduce((sum, type) =>
      sum + records.filter(record => record.type === type).reduce((typeSum, record) => typeSum + record.consumption, 0), 0);

    return types.map(type => {
      const typeConsumption = records.filter(record => record.type === type)
        .reduce((sum, record) => sum + record.consumption, 0);
      return {
        type,
        consumption: typeConsumption,
        percentage: Math.round((typeConsumption / totalConsumption) * 100) || 0
      };
    });
  }, [records]);

  // 计算能源成本分布
  const costDistribution = useMemo(() => {
    const types = ["电力", "天然气", "水", "蒸汽"];
    const totalCost = types.reduce((sum, type) =>
      sum + records.filter(record => record.type === type).reduce((typeSum, record) => typeSum + record.cost, 0), 0);

    return types.map(type => {
      const typeCost = records.filter(record => record.type === type)
        .reduce((sum, record) => sum + record.cost, 0);
      return {
        type,
        cost: typeCost,
        percentage: Math.round((typeCost / totalCost) * 100) || 0
      };
    });
  }, [records]);

  // 月度能源数据生成函数
  const generateMonthlyData = (months = 12): MonthlyData[] => {
    const currentDate = new Date();
    let index = 0;

    // 生成历史月份数组
    const historicalMonths: string[] = [];
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setMonth(currentDate.getMonth() - i);
      const monthName = date.toLocaleString('zh-CN', { month: 'long', year: 'numeric' });
      historicalMonths.push(monthName);
    }

    const data: MonthlyData[] = months <= 6
      ? historicalMonths.slice(-months).map((month) => ({
          month,
          "电力": Math.round(7000 + 3000 * Math.random() + 2000 * Math.sin(index++ / 5)),
          "天然气": Math.round(4000 + 1500 * Math.random() + 1000 * Math.sin(index / 3)),
          "水": Math.round(1500 + 500 * Math.random() + 300 * Math.cos(index / 4)),
          "蒸汽": Math.round(2000 + 1000 * Math.random() + 500 * Math.sin(index / 6))
        }))
      : historicalMonths.slice(-months).map((month) => ({
          month,
          "电力": Math.round(7000 + 3000 * Math.random() + 2000 * Math.sin(index++ / 5)),
          "天然气": Math.round(4000 + 1500 * Math.random() + 1000 * Math.sin(index / 3)),
          "水": Math.round(1500 + 500 * Math.random() + 300 * Math.cos(index / 4)),
          "蒸汽": Math.round(2000 + 1000 * Math.random() + 500 * Math.sin(index / 6))
        }));

    // 添加异常点标记
    // 在第5个月份添加电力异常
    if (data.length >= 5) {
      data[data.length - 5]["电力"] = Math.round(data[data.length - 5]["电力"] as number * 1.4);
      data[data.length - 5]["电力异常"] = true;
    }

    // 在第3个月份添加天然气异常
    if (data.length >= 3) {
      data[data.length - 3]["天然气"] = Math.round(data[data.length - 3]["天然气"] as number * 1.35);
      data[data.length - 3]["天然气异常"] = true;
    }

    // 计算环比和同比
    data.forEach((item, i) => {
      // 环比计算 (与上个月比较)
      if (i > 0) {
        data[i]["电力环比"] = parseFloat(((data[i]["电力"]! - data[i-1]["电力"]!) / data[i-1]["电力"]! * 100).toFixed(1));
        data[i]["天然气环比"] = parseFloat(((data[i]["天然气"]! - data[i-1]["天然气"]!) / data[i-1]["天然气"]! * 100).toFixed(1));
        data[i]["水环比"] = parseFloat(((data[i]["水"]! - data[i-1]["水"]!) / data[i-1]["水"]! * 100).toFixed(1));
        data[i]["蒸汽环比"] = parseFloat(((data[i]["蒸汽"]! - data[i-1]["蒸汽"]!) / data[i-1]["蒸汽"]! * 100).toFixed(1));
      }

      // 同比计算 (与去年同月比较)
      if (i >= 12) {
        data[i]["电力同比"] = parseFloat(((data[i]["电力"]! - data[i-12]["电力"]!) / data[i-12]["电力"]! * 100).toFixed(1));
        data[i]["天然气同比"] = parseFloat(((data[i]["天然气"]! - data[i-12]["天然气"]!) / data[i-12]["天然气"]! * 100).toFixed(1));
        data[i]["水同比"] = parseFloat(((data[i]["水"]! - data[i-12]["水"]!) / data[i-12]["水"]! * 100).toFixed(1));
        data[i]["蒸汽同比"] = parseFloat(((data[i]["蒸汽"]! - data[i-12]["蒸汽"]!) / data[i-12]["蒸汽"]! * 100).toFixed(1));
      }
    });

    return data;
  };

  // 生成预测数据函数
  const generateForecastData = (historicalData: MonthlyData[], forecastMonths = 3): MonthlyData[] => {
    if (historicalData.length < 3) return [];

    const currentDate = new Date();
    const forecastData: MonthlyData[] = [];

    // 基于最近3个月的趋势预测
    const recentMonths = historicalData.slice(-3);

    // 计算平均增长率
    const avgGrowthRate = {
      "电力": recentMonths.reduce((sum, m, i, arr) =>
        i > 0 ? sum + ((m["电力"]! - arr[i-1]["电力"]!) / arr[i-1]["电力"]!) : sum, 0) / (recentMonths.length - 1),
      "天然气": recentMonths.reduce((sum, m, i, arr) =>
        i > 0 ? sum + ((m["天然气"]! - arr[i-1]["天然气"]!) / arr[i-1]["天然气"]!) : sum, 0) / (recentMonths.length - 1),
      "水": recentMonths.reduce((sum, m, i, arr) =>
        i > 0 ? sum + ((m["水"]! - arr[i-1]["水"]!) / arr[i-1]["水"]!) : sum, 0) / (recentMonths.length - 1),
      "蒸汽": recentMonths.reduce((sum, m, i, arr) =>
        i > 0 ? sum + ((m["蒸汽"]! - arr[i-1]["蒸汽"]!) / arr[i-1]["蒸汽"]!) : sum, 0) / (recentMonths.length - 1)
    };

    const lastMonth = historicalData[historicalData.length - 1];

    // 生成预测数据
    for (let i = 1; i <= forecastMonths; i++) {
      const forecastDate = new Date(currentDate);
      forecastDate.setMonth(currentDate.getMonth() + i);

      const monthName = forecastDate.toLocaleString('zh-CN', { month: 'long', year: 'numeric' });
      const monthIndex = forecastDate.getMonth();

      // 应用季节性因素
      const seasonalFactors = {
        "电力": monthIndex >= 5 && monthIndex <= 8 ? 1.3 :
               (monthIndex >= 11 || monthIndex <= 1) ? 1.2 : 1,
        "天然气": (monthIndex >= 10 || monthIndex <= 2) ? 1.5 : 0.8,
        "水": (monthIndex >= 5 && monthIndex <= 8) ? 1.2 : 1,
        "蒸汽": (monthIndex >= 10 || monthIndex <= 3) ? 1.4 : 0.9
      };

      // 用上个月的数据和增长率预测
      let baseElectricity = i === 1 ? lastMonth["电力"]! : forecastData[i-2]["电力"]!;
      let baseGas = i === 1 ? lastMonth["天然气"]! : forecastData[i-2]["天然气"]!;
      let baseWater = i === 1 ? lastMonth["水"]! : forecastData[i-2]["水"]!;
      let baseSteam = i === 1 ? lastMonth["蒸汽"]! : forecastData[i-2]["蒸汽"]!;

      const forecastMonth: MonthlyData = {
        month: monthName,
        "电力": Math.round(baseElectricity * (1 + avgGrowthRate["电力"]) * seasonalFactors["电力"]),
        "天然气": Math.round(baseGas * (1 + avgGrowthRate["天然气"]) * seasonalFactors["天然气"]),
        "水": Math.round(baseWater * (1 + avgGrowthRate["水"]) * seasonalFactors["水"]),
        "蒸汽": Math.round(baseSteam * (1 + avgGrowthRate["蒸汽"]) * seasonalFactors["蒸汽"]),
        "电力预测": true,
        "天然气预测": true,
        "水预测": true,
        "蒸汽预测": true
      };

      forecastData.push(forecastMonth);
    }

    return forecastData;
  };

  // 获取能源类型对应的颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case "电力":
        return { bg: "bg-yellow-500", text: "text-yellow-600" };
      case "天然气":
        return { bg: "bg-orange-500", text: "text-orange-600" };
      case "水":
        return { bg: "bg-blue-500", text: "text-blue-600" };
      case "蒸汽":
        return { bg: "bg-gray-500", text: "text-gray-600" };
      default:
        return { bg: "bg-gray-500", text: "text-gray-600" };
    }
  };

  // 获取能源类型对应的图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "电力":
        return <Zap className="h-4 w-4 text-yellow-500" />;
      case "天然气":
        return <Flame className="h-4 w-4 text-orange-500" />;
      case "水":
        return <Droplet className="h-4 w-4 text-blue-500" />;
      case "蒸汽":
        return <Wind className="h-4 w-4 text-gray-500" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  // 过滤记录
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredRecords(records);
    } else {
      const filtered = records.filter(record =>
        record.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.manager.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRecords(filtered);
    }
  }, [searchTerm, records]);

  // 初始化月度数据和预测数据
  useEffect(() => {
    // 生成月度数据
    const historical = generateMonthlyData(12);
    setMonthlyData(historical);

    // 生成预测数据
    const forecast = generateForecastData(historical, 3);

    // 合并历史数据和预测数据
    setForecastData([...historical, ...forecast]);
  }, []);

  // 在组件开始时初始化 filteredRecords
  useEffect(() => {
    // 根据搜索和过滤条件筛选记录
    let filtered = [...records];
    
    // 根据搜索词筛选
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(record => 
        record.location.toLowerCase().includes(lowerSearchTerm) ||
        record.type.toLowerCase().includes(lowerSearchTerm) ||
        record.manager.toLowerCase().includes(lowerSearchTerm) ||
        record.description?.toLowerCase().includes(lowerSearchTerm)
      );
    }
    
    // 根据能源类型筛选
    if (selectedType !== "all") {
      filtered = filtered.filter(record => record.type === selectedType);
    }
    
    setFilteredRecords(filtered);
  }, [records, searchTerm, selectedType]);

  // 查看记录详情
  const handleViewDetails = (record: EnergyRecord) => {
    setCurrentRecord(record);
    setIsViewSheetOpen(true);
  }

  // 编辑记录
  const handleEditRecord = (record: EnergyRecord) => {
    setCurrentRecord(record);
    setFormData({ ...record });
    setIsEditDialogOpen(true);
  }

  // 删除记录
  const handleDeleteRecord = (record: EnergyRecord) => {
    setRecordToDelete(record);
    setIsDeleteDialogOpen(true);
  }

  // 确认删除
  const confirmDelete = () => {
    if (recordToDelete) {
      const updatedRecords = records.filter(r => r.id !== recordToDelete.id);
      setRecords(updatedRecords);
      toast({
        title: "删除成功",
        description: "能源消耗记录已成功删除",
      });
    }
    setIsDeleteDialogOpen(false);
    setRecordToDelete(null);
  }

  // 表单提交处理
  const handleSubmit = () => {
    // 验证表单数据
    if (!formData.date || !formData.type || !formData.location || !formData.consumption || !formData.unit || !formData.cost || !formData.manager) {
      toast({
        title: "表单不完整",
        description: "请填写所有必填字段",
        variant: "destructive",
      });
      return;
    }

    if (isEditDialogOpen) {
      // 编辑现有记录
      if (currentRecord) {
        const updatedRecords = records.map(record =>
          record.id === currentRecord.id ? { 
            ...record, 
            ...formData,
            consumption: Number(formData.consumption),
            cost: Number(formData.cost),
          } : record
        );
        setRecords(updatedRecords);
        setIsEditDialogOpen(false);
        toast({
          title: "更新成功",
          description: "能源消耗记录已更新",
        });
      }
    } else {
      // 添加新记录
      const newRecord: EnergyRecord = {
        id: `${Date.now()}`,
        date: formData.date || new Date().toISOString().split("T")[0],
        type: formData.type || "",
        location: formData.location || "",
        consumption: Number(formData.consumption) || 0,
        unit: formData.unit || "",
        cost: Number(formData.cost) || 0,
        status: "正常",
        trend: "平稳",
        manager: formData.manager || "",
        description: formData.description || "",
        efficiency: Math.round(70 + Math.random() * 20),
      };
      setRecords([...records, newRecord]);
      setIsAddDialogOpen(false);
      toast({
        title: "添加成功",
        description: "新能源消耗记录已添加",
      });
    }

    // 重置表单
    setFormData({
      date: new Date().toISOString().split("T")[0],
      type: "",
      location: "",
      consumption: 0,
      unit: "",
      cost: 0,
      manager: "",
      description: "",
    });
  }

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      // 模拟刷新
      setIsLoading(false);
      toast({
        title: "数据已刷新",
        description: "能源消耗数据已更新",
      });
    }, 1000);
  }

  // 导出数据
  const handleExport = () => {
    // 导出逻辑
    toast({
      title: "导出成功",
      description: "能源消耗数据已导出",
    });
  }

  // 添加自定义渲染异常点和预测线的函数
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;

      return (
        <div className="custom-tooltip" style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p className="label" style={{ margin: '0 0 5px', fontWeight: 'bold' }}>{`${label}`}</p>
          {payload.map((entry: any, index: number) => {
            const isAnomaly = item[`${entry.name}异常`];
            const isPrediction = item[`${entry.name}预测`];

            return (
              <p key={`item-${index}`} style={{
                color: entry.color,
                margin: '2px 0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <span style={{ display: 'flex', alignItems: 'center' }}>
                  {entry.name}:
                  {isAnomaly && (
                    <span style={{
                      marginLeft: '5px',
                      color: '#ff4d4f',
                      fontSize: '12px'
                    }}>
                      <AlertTriangle size={12} /> 异常
                    </span>
                  )}
                  {isPrediction && (
                    <span style={{
                      marginLeft: '5px',
                      color: '#1890ff',
                      fontSize: '12px'
                    }}>
                      <TrendingUp size={12} /> 预测
                    </span>
                  )}
                </span>
                <span style={{ marginLeft: '10px', fontWeight: '500' }}>
                  {entry.value} {entry.unit}
                </span>
              </p>
            );
          })}

          {(item["电力同比"] || item["天然气同比"] || item["水同比"] || item["蒸汽同比"]) && (
            <div style={{ borderTop: '1px dashed #ccc', marginTop: '5px', paddingTop: '5px' }}>
              <p style={{ fontSize: '12px', color: '#666', margin: '2px 0' }}>同比变化:</p>
              {item["电力同比"] && <p style={{ fontSize: '12px', color: '#666', margin: '2px 0' }}>电力: {item["电力同比"] > 0 ? '+' : ''}{item["电力同比"]}%</p>}
              {item["天然气同比"] && <p style={{ fontSize: '12px', color: '#666', margin: '2px 0' }}>天然气: {item["天然气同比"] > 0 ? '+' : ''}{item["天然气同比"]}%</p>}
              {item["水同比"] && <p style={{ fontSize: '12px', color: '#666', margin: '2px 0' }}>水: {item["水同比"] > 0 ? '+' : ''}{item["水同比"]}%</p>}
              {item["蒸汽同比"] && <p style={{ fontSize: '12px', color: '#666', margin: '2px 0' }}>蒸汽: {item["蒸汽同比"] > 0 ? '+' : ''}{item["蒸汽同比"]}%</p>}
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  // 自定义数据点渲染，用于突出显示异常和预测
  const renderCustomizedDot = (props: any) => {
    const { cx, cy, payload, dataKey } = props;

    // 检查是否为预测点
    if (payload[`${dataKey}预测`]) {
      // 对于预测点，使用方形而不是圆形，并使用虚线
      return (
        <svg x={cx - 5} y={cy - 5} width={10} height={10}>
          <rect x="0" y="0" width="10" height="10" fill="#1890ff" strokeDasharray="2 2" stroke="#1890ff" />
        </svg>
      );
    }

    // 检查是否为异常点
    if (payload[`${dataKey}异常`]) {
      return (
        <svg x={cx - 6} y={cy - 6} width={12} height={12} fill="red" viewBox="0 0 12 12">
          <circle cx="6" cy="6" r="6" />
        </svg>
      );
    }

    // 默认点
    return (
      <svg x={cx - 3} y={cy - 3} width={6} height={6} fill="currentColor" viewBox="0 0 6 6">
        <circle cx="3" cy="3" r="3" />
      </svg>
    );
  };

  return (
    <div className="energy-consumption-container">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">能源消耗管理</h2>
          <p className="text-muted-foreground">
            监控和管理公司各类能源消耗情况，提高能源使用效率
          </p>
          </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      <Tabs defaultValue="table" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="table">数据记录</TabsTrigger>
          <TabsTrigger value="charts">图表分析</TabsTrigger>
          <TabsTrigger value="intelligence">智能分析</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          {/* 原有的表格内容 */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <CardTitle>能源消耗记录</CardTitle>
                  <CardDescription>所有能源消耗数据记录和使用情况</CardDescription>
                </div>
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Button onClick={() => setIsAddDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加记录
                  </Button>
                  <Button variant="outline" onClick={handleRefresh}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    刷新
                  </Button>
                  <Button variant="outline" onClick={handleExport}>
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row justify-between gap-4 mb-4">
                <div className="flex flex-col sm:flex-row gap-2 w-full">
                  <div className="relative w-full sm:w-64">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="搜索..."
                      className="pl-8 w-full"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-full sm:w-40">
                      <SelectValue placeholder="类型筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="电力">电力</SelectItem>
                      <SelectItem value="天然气">天然气</SelectItem>
                      <SelectItem value="水">水</SelectItem>
                      <SelectItem value="蒸汽">蒸汽</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>日期</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>使用位置</TableHead>
                      <TableHead className="text-right">消耗量</TableHead>
                      <TableHead className="text-right">成本(元)</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>趋势</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.length > 0 ? (
                      filteredRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.date}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getTypeIcon(record.type)}
                              <span>{record.type}</span>
                            </div>
                          </TableCell>
                          <TableCell>{record.location}</TableCell>
                          <TableCell className="text-right">
                            {record.consumption.toLocaleString()} {record.unit}
                          </TableCell>
                          <TableCell className="text-right">
                            {record.cost.toLocaleString()}
                          </TableCell>
                          <TableCell>{getStatusBadge(record.status)}</TableCell>
                          <TableCell>{getTrendBadge(record.trend)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">打开菜单</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleViewDetails(record)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditRecord(record)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onClick={() => handleDeleteRecord(record)}
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          {isLoading ? (
                            <div className="flex justify-center items-center">
                              <Loader2 className="h-6 w-6 animate-spin mr-2" />
                              加载中...
                            </div>
                          ) : (
                            "没有找到匹配的记录"
                          )}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="charts" className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={(value: "6months" | "12months" | "2years") => setTimeRange(value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6months">近6个月</SelectItem>
                  <SelectItem value="12months">近12个月</SelectItem>
                  <SelectItem value="2years">近2年</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedEnergyType} onValueChange={setSelectedEnergyType}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="能源类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="全部">全部类型</SelectItem>
                  <SelectItem value="电力">电力</SelectItem>
                  <SelectItem value="天然气">天然气</SelectItem>
                  <SelectItem value="水">水</SelectItem>
                  <SelectItem value="蒸汽">蒸汽</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              导出图表
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>能源消耗趋势</CardTitle>
                <CardDescription>各类能源消耗量月度变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={forecastData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="电力"
                        name="电力"
                        stroke="#faad14"
                        strokeWidth={2}
                        dot={renderCustomizedDot}
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="天然气"
                        name="天然气"
                        stroke="#ff7a45"
                        strokeWidth={2}
                        dot={renderCustomizedDot}
                      />
                      <Line
                        type="monotone"
                        dataKey="水"
                        name="水"
                        stroke="#1890ff"
                        strokeWidth={2}
                        dot={renderCustomizedDot}
                      />
                      <Line
                        type="monotone"
                        dataKey="蒸汽"
                        name="蒸汽"
                        stroke="#8c8c8c"
                        strokeWidth={2}
                        dot={renderCustomizedDot}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>能源消耗分布</CardTitle>
                <CardDescription>各类能源消耗占比分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={energyDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="percentage"
                        nameKey="type"
                        label={({ type, percentage }) => `${type} ${percentage}%`}
                      >
                        {energyDistribution.map((entry, index) => {
                          const colors = ["#faad14", "#ff7a45", "#1890ff", "#8c8c8c"];
                          return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                        })}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>能源成本分析</CardTitle>
                <CardDescription>各类能源成本占比分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={costDistribution}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="type" />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="cost" name="成本 (元)" fill="#8884d8">
                        {costDistribution.map((entry, index) => {
                          const colors = ["#faad14", "#ff7a45", "#1890ff", "#8c8c8c"];
                          return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                        })}
                      </Bar>
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>能源效率分析</CardTitle>
                <CardDescription>各类能源使用效率评估</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex flex-col justify-center">
                  <div className="space-y-6">
                    {["电力", "天然气", "水", "蒸汽"].map((type) => {
                      const efficiency = type === "电力" ? 85 :
                                        type === "天然气" ? 78 :
                                        type === "水" ? 65 : 80;
                      const color = efficiency >= 80 ? "bg-green-500" :
                                   efficiency >= 70 ? "bg-yellow-500" : "bg-red-500";
                      return (
                        <div key={type} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(type)}
                              <span>{type}</span>
                            </div>
                            <span className="font-medium">{efficiency}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className={`h-2.5 rounded-full ${color}`}
                              style={{ width: `${efficiency}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 添加智能分析标签页 */}
        <TabsContent value="intelligence" className="space-y-4">
          <Card>
            <CardHeader>
                        <div className="flex justify-between items-center">
                <div>
                  <CardTitle>能源智能分析</CardTitle>
                  <CardDescription>使用AI分析能源消耗数据，发现异常和优化机会</CardDescription>
                        </div>
                <Button variant="outline" onClick={() => setShowScenarioAnalysis(!showScenarioAnalysis)}>
                  {showScenarioAnalysis ? "关闭假设分析" : "开启假设分析"}
                </Button>
                        </div>
                      </CardHeader>
            <CardContent>
              {showScenarioAnalysis ? (
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
                    <h3 className="text-lg font-medium text-blue-800 mb-2">节能假设分析</h3>
                    <p className="text-sm text-blue-600 mb-4">
                      通过调整参数，模拟不同节能措施的效果和投资回报
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="space-y-2">
                        <Label htmlFor="electricity-reduction">电力消耗降低比例 (%)</Label>
                        <div className="flex items-center space-x-2">
                <Input
                            id="electricity-reduction"
                            type="number"
                            value={scenarioParams.electricityReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, electricityReduction: Number(e.target.value)})}
                            className="max-w-[150px]"
                          />
                          <input
                            type="range"
                            min="0"
                            max="50"
                            value={scenarioParams.electricityReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, electricityReduction: Number(e.target.value)})}
                            className="flex-1"
                />
              </div>
              </div>

              <div className="space-y-2">
                        <Label htmlFor="gas-reduction">天然气消耗降低比例 (%)</Label>
                        <div className="flex items-center space-x-2">
                <Input
                            id="gas-reduction"
                  type="number"
                            value={scenarioParams.gasReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, gasReduction: Number(e.target.value)})}
                            className="max-w-[150px]"
                          />
                          <input
                            type="range"
                  min="0"
                            max="50"
                            value={scenarioParams.gasReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, gasReduction: Number(e.target.value)})}
                            className="flex-1"
                />
              </div>
              </div>

            <div className="space-y-2">
                        <Label htmlFor="water-reduction">水资源消耗降低比例 (%)</Label>
                        <div className="flex items-center space-x-2">
              <Input
                            id="water-reduction"
                type="number"
                            value={scenarioParams.waterReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, waterReduction: Number(e.target.value)})}
                            className="max-w-[150px]"
                          />
                          <input
                            type="range"
                min="0"
                            max="50"
                            value={scenarioParams.waterReduction}
                            onChange={(e) => setScenarioParams({...scenarioParams, waterReduction: Number(e.target.value)})}
                            className="flex-1"
              />
            </div>
            </div>

              <div className="space-y-2">
                        <Label htmlFor="investment-amount">预计投资金额 (元)</Label>
                <Input
                          id="investment-amount"
                          type="number"
                          value={scenarioParams.investmentAmount}
                          onChange={(e) => setScenarioParams({...scenarioParams, investmentAmount: Number(e.target.value)})}
                />
              </div>
              </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              ¥{scenarioResults.annualSaving.toLocaleString()}
            </div>
                            <p className="text-sm text-gray-500 mt-1">年度节省</p>
              </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {scenarioResults.paybackPeriod} 年
              </div>
                            <p className="text-sm text-gray-500 mt-1">投资回收期</p>
            </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {scenarioResults.carbonReduction} 吨
              </div>
                            <p className="text-sm text-gray-500 mt-1">碳减排量</p>
              </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-orange-600">
                              {scenarioResults.peakReduction} kW
            </div>
                            <p className="text-sm text-gray-500 mt-1">峰值负荷降低</p>
            </div>
                        </CardContent>
                      </Card>
            </div>

                    <div className="mt-6 text-sm text-blue-600 p-3 bg-blue-50 border border-blue-100 rounded">
                      <p className="flex items-center">
                        <Lightbulb className="h-4 w-4 mr-2" />
                        根据您的假设参数，该节能方案具有良好的经济效益，建议重点考虑实施。
                      </p>
                  </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <Card>
                  <CardHeader className="pb-2">
                        <CardTitle className="text-base">能源消耗预测</CardTitle>
                  </CardHeader>
                  <CardContent>
                        <div className="h-[200px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={forecastData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="month" />
                              <YAxis />
                              <Tooltip content={<CustomTooltip />} />
                              <Legend />

                              {/* 历史电力数据 */}
                              <Line
                                type="monotone"
                                name="电力（历史）"
                                dataKey="电力"
                                stroke="#8884d8"
                                strokeWidth={2}
                                dot={renderCustomizedDot}
                                connectNulls
                              />

                              {/* 历史天然气数据 */}
                              <Line
                                type="monotone"
                                name="天然气（历史）"
                                dataKey="天然气"
                                stroke="#82ca9d"
                                strokeWidth={2}
                                dot={renderCustomizedDot}
                                connectNulls
                              />
                            </LineChart>
                          </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                    <Card>
                  <CardHeader className="pb-2">
                        <CardTitle className="text-base">能源效率分析</CardTitle>
                  </CardHeader>
                  <CardContent>
                        <div className="h-[200px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsPieChart>
                              <Pie
                                data={[
                                  { name: '有效能耗', value: 65 },
                                  { name: '设备损耗', value: 15 },
                                  { name: '传输损耗', value: 12 },
                                  { name: '浪费能耗', value: 8 }
                                ]}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              >
                                {[
                                  { name: '有效能耗', value: 65, color: '#52c41a' },
                                  { name: '设备损耗', value: 15, color: '#faad14' },
                                  { name: '传输损耗', value: 12, color: '#1890ff' },
                                  { name: '浪费能耗', value: 8, color: '#ff4d4f' }
                                ].map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                              </Pie>
                              <Tooltip />
                            </RechartsPieChart>
                          </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>

                  <h3 className="text-lg font-medium mt-6 mb-3">智能分析结果</h3>
                  {analysisResults.map((result) => (
                    <Card key={result.id} className="mb-3">
                      <CardContent className="p-4">
                        <div className="flex items-start">
                          <div className="mr-3 mt-1">
                            {getAnalysisTypeIcon(result.type)}
                      </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-1">
                              <h4 className="font-medium text-base">{result.title}</h4>
                              <div className="flex items-center space-x-2">
                                {getImpactBadge(result.impact)}
                                <Badge variant="outline">可信度: {result.confidence}%</Badge>
                      </div>
                    </div>
                            <p className="text-sm text-gray-600 mb-3">{result.description}</p>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs text-gray-500 mb-2">
                              {result.relatedType && (
                    <div>
                                  <span className="font-medium">相关能源: </span>
                                  {result.relatedType}
                      </div>
                              )}
                              {result.relatedTime && (
                                <div>
                                  <span className="font-medium">时间范围: </span>
                                  {result.relatedTime}
                    </div>
                              )}
                    <div>
                                <span className="font-medium">发现日期: </span>
                                {result.date}
                        </div>
                            </div>
                            {result.suggestedAction && (
                              <div className="mt-2 p-2 bg-blue-50 border-l-4 border-blue-400 text-sm">
                                <span className="font-medium text-blue-700">建议措施: </span>
                                <span className="text-blue-600">{result.suggestedAction}</span>
                                {result.potentialSaving && (
                                  <span className="ml-2 text-green-600">
                                    (预计节省: ¥{result.potentialSaving.toLocaleString()})
                        </span>
                                )}
                              </div>
                            )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
                  ))}
              </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加记录对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加能源消耗记录</DialogTitle>
            <DialogDescription>
              添加新的能源消耗记录到系统
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date">日期</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">能源类型</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择能源类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="电力">电力</SelectItem>
                    <SelectItem value="天然气">天然气</SelectItem>
                    <SelectItem value="水">水</SelectItem>
                    <SelectItem value="蒸汽">蒸汽</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">使用位置</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="consumption">消耗量</Label>
                <Input
                  id="consumption"
                  type="number"
                  value={formData.consumption}
                  onChange={(e) => setFormData({ ...formData, consumption: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="unit">单位</Label>
                <Select
                  value={formData.unit}
                  onValueChange={(value) => setFormData({ ...formData, unit: value })}
                >
                  <SelectTrigger id="unit">
                    <SelectValue placeholder="选择单位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kWh">kWh (电力)</SelectItem>
                    <SelectItem value="m³">m³ (天然气)</SelectItem>
                    <SelectItem value="吨">吨 (水/蒸汽)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cost">成本 (元)</Label>
              <Input
                id="cost"
                type="number"
                value={formData.cost}
                onChange={(e) => setFormData({ ...formData, cost: Number(e.target.value) })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="manager">负责人</Label>
              <Input
                id="manager"
                value={formData.manager}
                onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
            <Button onClick={handleSubmit}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑记录对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑能源消耗记录</DialogTitle>
            <DialogDescription>
              修改能源消耗记录信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-date">日期</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">能源类型</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择能源类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="电力">电力</SelectItem>
                    <SelectItem value="天然气">天然气</SelectItem>
                    <SelectItem value="水">水</SelectItem>
                    <SelectItem value="蒸汽">蒸汽</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-location">使用位置</Label>
              <Input
                id="edit-location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-consumption">消耗量</Label>
                <Input
                  id="edit-consumption"
                  type="number"
                  value={formData.consumption}
                  onChange={(e) => setFormData({ ...formData, consumption: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-unit">单位</Label>
                <Select
                  value={formData.unit}
                  onValueChange={(value) => setFormData({ ...formData, unit: value })}
                >
                  <SelectTrigger id="edit-unit">
                    <SelectValue placeholder="选择单位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kWh">kWh (电力)</SelectItem>
                    <SelectItem value="m³">m³ (天然气)</SelectItem>
                    <SelectItem value="吨">吨 (水/蒸汽)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-cost">成本 (元)</Label>
              <Input
                id="edit-cost"
                type="number"
                value={formData.cost}
                onChange={(e) => setFormData({ ...formData, cost: Number(e.target.value) })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-manager">负责人</Label>
              <Input
                id="edit-manager"
                value={formData.manager}
                onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">描述</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleSubmit}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条能源消耗记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 查看详情侧边栏 */}
      <Sheet open={isViewSheetOpen} onOpenChange={setIsViewSheetOpen}>
        <SheetContent className="sm:max-w-[540px]">
          <SheetHeader>
            <SheetTitle>能源消耗详情</SheetTitle>
            <SheetDescription>
              查看能源消耗记录的详细信息
            </SheetDescription>
          </SheetHeader>
          {currentRecord && (
            <div className="py-6 space-y-6">
              <div className="flex items-center justify-center p-6 bg-muted rounded-md">
                {getTypeIcon(currentRecord.type)}
                <span className="ml-2 text-lg font-medium">{currentRecord.type}消耗记录</span>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">日期</Label>
                  <p className="font-medium mt-1">{currentRecord.date}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">使用位置</Label>
                  <p className="font-medium mt-1">{currentRecord.location}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">消耗量</Label>
                  <p className="font-medium mt-1">{currentRecord.consumption} {currentRecord.unit}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">成本</Label>
                  <p className="font-medium mt-1">{currentRecord.cost.toLocaleString()} 元</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">状态</Label>
                  <p className="font-medium mt-1">{getStatusBadge(currentRecord.status)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">趋势</Label>
                  <p className="font-medium mt-1">{getTrendBadge(currentRecord.trend)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">负责人</Label>
                  <p className="font-medium mt-1">{currentRecord.manager}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">能源效率</Label>
                  <p className="font-medium mt-1">{currentRecord.efficiency}%</p>
                </div>
              </div>

              {currentRecord.description && (
                <div>
                  <Label className="text-muted-foreground">描述</Label>
                  <p className="mt-1 text-sm">{currentRecord.description}</p>
                </div>
              )}

              <div className="p-4 bg-muted rounded-md">
                <h4 className="font-medium mb-2">能源使用分析</h4>
                <p className="text-sm text-muted-foreground">
                  该记录的能源使用效率为 {currentRecord?.efficiency ?? 0}%，
                  {currentRecord?.efficiency ? 
                    (currentRecord.efficiency >= 85 ? "处于良好水平" : 
                     currentRecord.efficiency >= 70 ? "处于一般水平，有优化空间" : 
                     "效率较低，建议进行能源审计") : 
                    "效率较低，建议进行能源审计"}
                </p>
              </div>
            </div>
          )}
          <SheetFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsViewSheetOpen(false)}>关闭</Button>
            {currentRecord && (
              <Button onClick={() => {
                setIsViewSheetOpen(false);
                handleEditRecord(currentRecord);
              }}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
            )}
          </SheetFooter>
        </SheetContent>
      </Sheet>
      </div>
  );
}