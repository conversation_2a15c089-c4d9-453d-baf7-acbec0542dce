# 矿业公司综合管理系统 - Mermaid图表代码集合

## 1. 系统技术架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web浏览器] --> B[React前端应用]
        C[移动设备] --> B
        D[平板设备] --> B
    end
    
    subgraph "应用服务层"
        B --> E[Nginx负载均衡]
        E --> F[Flask应用服务器1]
        E --> G[Flask应用服务器2]
        E --> H[Flask应用服务器N]
    end
    
    subgraph "业务逻辑层"
        F --> I[用户认证模块]
        F --> J[权限管理模块]
        F --> K[业务处理模块]
        F --> L[数据访问层]
    end
    
    subgraph "数据存储层"
        L --> M[MySQL主库]
        L --> N[MySQL从库]
        L --> O[Redis缓存]
        L --> P[文件存储系统]
    end
    
    subgraph "外部系统"
        K --> Q[第三方API]
        K --> R[监控设备接口]
        K --> S[邮件服务]
    end
```

## 2. 功能模块结构图

```mermaid
mindmap
  root((矿业公司综合管理系统))
    系统管理
      用户管理
      角色权限
      系统配置
      日志审计
    安全管理
      安全检查
      隐患管理
      应急预案
      安全培训
      事故管理
    工程管理
      项目规划
      进度管理
      质量控制
      成本管理
      合同管理
    人事管理
      员工档案
      考勤管理
      绩效考核
      培训管理
      薪资管理
    财务管理
      财务状况
      成本核算
      预算管理
      报表分析
    固定资产管理
      资产台账
      设备维护
      折旧管理
      预警系统
    能源管理
      能耗监控
      用量分析
      成本统计
      节能优化
    保卫管理
      门禁系统
      视频监控
      巡逻记录
      访客管理
    办公行政管理
      文档管理
      会议安排
      车辆管理
      后勤服务
    物资供应链管理
      采购管理
      库存控制
      供应商管理
      物流跟踪
    任务流程管理
      工作流引擎
      任务分配
      进度跟踪
      审批流程
    综合展示报表
      数据可视化
      实时大屏
      统计分析
      决策支持
```

## 3. 系统数据流图

```mermaid
flowchart TD
    A[用户登录] --> B{身份验证}
    B -->|成功| C[权限检查]
    B -->|失败| D[登录失败]
    C --> E[功能模块访问]
    E --> F[业务逻辑处理]
    F --> G[数据库操作]
    G --> H[数据返回]
    H --> I[前端渲染]
    I --> J[用户界面展示]
    
    F --> K[日志记录]
    F --> L[缓存更新]
    F --> M[实时通知]
    
    subgraph "数据处理流程"
        N[数据采集] --> O[数据验证]
        O --> P[数据存储]
        P --> Q[数据分析]
        Q --> R[报表生成]
    end
```

## 4. 数据库ER图

```mermaid
erDiagram
    USERS ||--o{ USER_ROLES : has
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : belongs_to
    
    USERS {
        int id PK
        string username UK
        string password
        string real_name
        string email
        string phone
        string department
        string position
        int role_id FK
        enum status
        datetime created_at
        datetime updated_at
    }
    
    ROLES {
        int id PK
        string name UK
        string description
        datetime created_at
        datetime updated_at
    }
    
    PERMISSIONS {
        int id PK
        string name UK
        string code UK
        string description
        string module
        datetime created_at
    }
    
    SAFETY_CHECKS ||--o{ SAFETY_ISSUES : contains
    SAFETY_CHECKS {
        int id PK
        string name
        string type
        string department
        int inspector_id FK
        date check_date
        enum status
        int issues_count
        text description
        datetime created_at
    }
    
    SAFETY_ISSUES {
        int id PK
        int check_id FK
        string title
        string description
        enum severity
        enum status
        string responsible_person
        date deadline
        datetime created_at
    }
    
    PROJECTS ||--o{ PROJECT_TASKS : contains
    PROJECTS {
        int id PK
        string name
        string description
        decimal budget
        date start_date
        date end_date
        enum status
        int manager_id FK
        datetime created_at
    }
    
    ASSETS ||--o{ ASSET_MAINTENANCE : has
    ASSETS {
        int id PK
        string asset_code UK
        string name
        string category
        decimal purchase_price
        date purchase_date
        string location
        enum status
        datetime created_at
    }
    
    ENERGY_RECORDS {
        int id PK
        string energy_type
        decimal consumption
        decimal cost
        date record_date
        string department
        datetime created_at
    }
```

## 5. 主题系统架构图

```mermaid
flowchart LR
    A[用户偏好] --> B[主题Context]
    B --> C[CSS变量系统]
    C --> D[组件样式]
    B --> E[localStorage持久化]
    F[系统偏好检测] --> B
    G[主题切换器] --> B
```

## 6. RBAC权限模型图

```mermaid
graph TD
    A[用户 User] --> B[角色 Role]
    B --> C[权限 Permission]
    C --> D[资源 Resource]
    
    E[用户组 Group] --> B
    F[部门 Department] --> E
    
    G[菜单权限] --> C
    H[操作权限] --> C
    I[数据权限] --> C
```

## 7. 测试环境架构图

```mermaid
graph TB
    subgraph "测试环境"
        A[测试数据库] --> B[应用服务器]
        B --> C[Web服务器]
        C --> D[负载均衡器]
        E[测试数据生成器] --> A
        F[自动化测试工具] --> B
    end
    
    subgraph "监控系统"
        G[性能监控]
        H[日志收集]
        I[错误追踪]
    end
    
    D --> G
    B --> H
    B --> I
```

## 8. 并发用户测试场景图

```mermaid
graph LR
    A[并发用户测试] --> B[100用户]
    A --> C[300用户]
    A --> D[500用户]
    A --> E[800用户]
    
    F[业务场景测试] --> G[登录认证]
    F --> H[数据查询]
    F --> I[报表生成]
    F --> J[文件上传]
```

## 9. 安全检查业务流程图

```mermaid
sequenceDiagram
    participant 安全员 as 安全员
    participant 系统 as 系统
    participant 责任人 as 责任人
    participant 管理员 as 管理员
    
    安全员->>系统: 创建安全检查计划
    系统->>安全员: 返回检查表单
    安全员->>系统: 执行检查并记录问题
    系统->>责任人: 发送隐患整改通知
    责任人->>系统: 提交整改报告
    系统->>安全员: 通知验收
    安全员->>系统: 验收并关闭隐患
    系统->>管理员: 生成检查报告
```

## 10. 安全管理配置流程图

```mermaid
flowchart TD
    A[安全管理配置] --> B[检查类型设置]
    A --> C[隐患等级定义]
    A --> D[应急预案模板]
    A --> E[安全培训课程]
    
    B --> F[日常检查]
    B --> G[专项检查]
    B --> H[季度检查]
    
    C --> I[低危隐患]
    C --> J[中危隐患]
    C --> K[高危隐患]
    C --> L[紧急隐患]
```

## 11. 项目开发甘特图

```mermaid
gantt
    title 矿业公司综合管理系统开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求调研           :done, req1, 2024-01-01, 2024-01-15
    需求分析           :done, req2, 2024-01-16, 2024-01-30
    原型设计           :done, req3, 2024-01-31, 2024-02-15
    
    section 系统设计
    架构设计           :done, design1, 2024-02-16, 2024-02-28
    数据库设计         :done, design2, 2024-03-01, 2024-03-15
    接口设计           :done, design3, 2024-03-16, 2024-03-30
    
    section 开发实施
    基础框架搭建       :done, dev1, 2024-04-01, 2024-04-15
    核心模块开发       :done, dev2, 2024-04-16, 2024-07-31
    集成测试           :done, dev3, 2024-08-01, 2024-08-31
    
    section 测试部署
    系统测试           :done, test1, 2024-09-01, 2024-09-30
    用户验收           :done, test2, 2024-10-01, 2024-10-15
    生产部署           :done, deploy1, 2024-10-16, 2024-10-31
```

## 12. 团队组织架构图

```mermaid
graph TD
    A[项目经理] --> B[前端开发团队]
    A --> C[后端开发团队]
    A --> D[UI/UX设计团队]
    A --> E[测试团队]
    A --> F[运维团队]
    
    B --> G[React开发工程师 x3]
    B --> H[前端架构师 x1]
    
    C --> I[Python开发工程师 x4]
    C --> J[数据库工程师 x1]
    C --> K[后端架构师 x1]
    
    D --> L[UI设计师 x2]
    D --> M[UX设计师 x1]
    
    E --> N[测试工程师 x3]
    E --> O[自动化测试工程师 x1]
    
    F --> P[运维工程师 x2]
    F --> Q[安全工程师 x1]
```

## 13. 平台化发展架构图

```mermaid
graph TB
    A[核心平台] --> B[基础服务层]
    A --> C[业务服务层]
    A --> D[应用服务层]
    
    B --> E[用户管理]
    B --> F[权限控制]
    B --> G[数据存储]
    B --> H[消息通知]
    
    C --> I[安全管理服务]
    C --> J[项目管理服务]
    C --> K[人事管理服务]
    C --> L[财务管理服务]
    
    D --> M[Web应用]
    D --> N[移动应用]
    D --> O[API接口]
    D --> P[第三方集成]
```

## 14. 市场竞争象限图

```mermaid
quadrantChart
    title 市场竞争象限图
    x-axis 低价格 --> 高价格
    y-axis 低功能 --> 高功能
    
    quadrant-1 明星产品
    quadrant-2 问题产品
    quadrant-3 瘦狗产品
    quadrant-4 金牛产品
    
    本系统: [0.3, 0.8]
    用友ERP: [0.7, 0.7]
    金蝶ERP: [0.6, 0.6]
    SAP: [0.9, 0.9]
    传统方式: [0.1, 0.1]
```

## 使用说明

1. 复制对应的Mermaid代码
2. 粘贴到支持Mermaid的编辑器中（如Typora、Notion、GitHub等）
3. 或使用在线Mermaid编辑器：https://mermaid.live/
4. 生成对应的图表并导出为图片格式

这些图表可以直接插入到您的项目报告中，提升报告的专业性和可读性。
