"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  Building2,
  Briefcase,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Package,
  Truck,
  Store,
  ClipboardList,
  DollarSign,
  BarChart
} from "lucide-react"

interface SupplyRecord {
  id: string
  name: string
  category: string
  specification: string
  unit: string
  price: number
  supplier: string
  contact: string
  phone: string
  email: string
  address: string
  status: string
  description: string
  lastSupplyDate: string
  nextSupplyDate: string
  minStock: number
  maxStock: number
  currentStock: number
}

export function SupplyManagement() {
  // 初始供应记录数据
  const initialRecords: SupplyRecord[] = [
    {
      id: "1",
      name: "安全帽",
      category: "安全防护",
      specification: "标准型",
      unit: "个",
      price: 50,
      supplier: "安全防护用品有限公司",
      contact: "张三",
      phone: "13800138000",
      email: "<EMAIL>",
      address: "北京市朝阳区安全路88号",
      status: "正常供应",
      description: "符合国家标准的安全防护帽",
      lastSupplyDate: "2025-03-01",
      nextSupplyDate: "2025-04-01",
      minStock: 100,
      maxStock: 500,
      currentStock: 300
    },
    {
      id: "2",
      name: "工作手套",
      category: "安全防护",
      specification: "防割型",
      unit: "双",
      price: 25,
      supplier: "安全防护用品有限公司",
      contact: "李四",
      phone: "13800138001",
      email: "<EMAIL>",
      address: "北京市朝阳区安全路88号",
      status: "库存不足",
      description: "防割防刺工作手套",
      lastSupplyDate: "2025-02-15",
      nextSupplyDate: "2025-03-15",
      minStock: 200,
      maxStock: 1000,
      currentStock: 150
    },
    {
      id: "3",
      name: "矿灯",
      category: "照明设备",
      specification: "LED型",
      unit: "个",
      price: 200,
      supplier: "矿山设备有限公司",
      contact: "王五",
      phone: "13800138002",
      email: "<EMAIL>",
      address: "北京市海淀区矿山路66号",
      status: "正常供应",
      description: "高亮度LED矿灯",
      lastSupplyDate: "2025-03-10",
      nextSupplyDate: "2025-04-10",
      minStock: 50,
      maxStock: 200,
      currentStock: 120
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<SupplyRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<SupplyRecord>({
    id: "",
    name: "",
    category: "",
    specification: "",
    unit: "",
    price: 0,
    supplier: "",
    contact: "",
    phone: "",
    email: "",
    address: "",
    status: "",
    description: "",
    lastSupplyDate: "",
    nextSupplyDate: "",
    minStock: 0,
    maxStock: 0,
    currentStock: 0
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch =
        record.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.supplier.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "normal") return matchesSearch && record.status === "正常供应";
      if (activeTab === "low") return matchesSearch && record.status === "库存不足";

      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      currentStock: 0
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      name: "",
      category: "",
      specification: "",
      unit: "",
      price: 0,
      supplier: "",
      contact: "",
      phone: "",
      email: "",
      address: "",
      status: "",
      description: "",
      lastSupplyDate: "",
      nextSupplyDate: "",
      minStock: 0,
      maxStock: 0,
      currentStock: 0
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      name: "",
      category: "",
      specification: "",
      unit: "",
      price: 0,
      supplier: "",
      contact: "",
      phone: "",
      email: "",
      address: "",
      status: "",
      description: "",
      lastSupplyDate: "",
      nextSupplyDate: "",
      minStock: 0,
      maxStock: 0,
      currentStock: 0
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      name: "",
      category: "",
      specification: "",
      unit: "",
      price: 0,
      supplier: "",
      contact: "",
      phone: "",
      email: "",
      address: "",
      status: "",
      description: "",
      lastSupplyDate: "",
      nextSupplyDate: "",
      minStock: 0,
      maxStock: 0,
      currentStock: 0
    })
    setIsDeleteDialogOpen(false)
  }

  // 更新库存
  const handleUpdateStock = (record: SupplyRecord, amount: number) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? {
        ...r,
        currentStock: r.currentStock + amount,
        status: r.currentStock + amount <= r.minStock ? "库存不足" : "正常供应",
        lastSupplyDate: amount > 0 ? new Date().toISOString().split('T')[0] : r.lastSupplyDate
      } : r
    )
    setRecords(updatedRecords)
  }

  // 打开编辑对话框
  const openEditDialog = (record: SupplyRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: SupplyRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常供应":
        return <Badge className="bg-green-500">正常供应</Badge>
      case "库存不足":
        return <Badge variant="destructive">库存不足</Badge>
      case "停止供应":
        return <Badge variant="secondary">停止供应</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">供应管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增物资
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>新增物资</DialogTitle>
              <DialogDescription>
                请填写物资的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">物资名称</Label>
                  <Input
                    id="name"
                    value={currentRecord.name}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">物资类别</Label>
                  <Select
                    value={currentRecord.category}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="选择物资类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全防护">安全防护</SelectItem>
                      <SelectItem value="照明设备">照明设备</SelectItem>
                      <SelectItem value="工具设备">工具设备</SelectItem>
                      <SelectItem value="办公用品">办公用品</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="specification">规格型号</Label>
                  <Input
                    id="specification"
                    value={currentRecord.specification}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, specification: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unit">单位</Label>
                  <Input
                    id="unit"
                    value={currentRecord.unit}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, unit: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">单价</Label>
                <Input
                  id="price"
                  type="number"
                  value={currentRecord.price}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, price: Number(e.target.value) })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="supplier">供应商</Label>
                  <Input
                    id="supplier"
                    value={currentRecord.supplier}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, supplier: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact">联系人</Label>
                  <Input
                    id="contact"
                    value={currentRecord.contact}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, contact: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">联系电话</Label>
                  <Input
                    id="phone"
                    value={currentRecord.phone}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, phone: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">电子邮箱</Label>
                  <Input
                    id="email"
                    value={currentRecord.email}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, email: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">地址</Label>
                <Input
                  id="address"
                  value={currentRecord.address}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, address: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minStock">最低库存</Label>
                  <Input
                    id="minStock"
                    type="number"
                    value={currentRecord.minStock}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, minStock: Number(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxStock">最高库存</Label>
                  <Input
                    id="maxStock"
                    type="number"
                    value={currentRecord.maxStock}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, maxStock: Number(e.target.value) })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lastSupplyDate">上次供应日期</Label>
                  <Input
                    id="lastSupplyDate"
                    type="date"
                    value={currentRecord.lastSupplyDate}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, lastSupplyDate: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nextSupplyDate">下次供应日期</Label>
                  <Input
                    id="nextSupplyDate"
                    type="date"
                    value={currentRecord.nextSupplyDate}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, nextSupplyDate: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">物资描述</Label>
                <Textarea
                  id="description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索物资..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[300px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="normal">正常供应</TabsTrigger>
            <TabsTrigger value="low">库存不足</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <Package className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{record.name}</CardTitle>
              </div>
              {getStatusBadge(record.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    {record.category}
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                    {record.price}元/{record.unit}
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                  供应商: {record.supplier}
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  联系人: {record.contact} ({record.phone})
                </div>
                <div className="flex items-center text-sm">
                  <Store className="h-4 w-4 mr-2 text-gray-500" />
                  库存: {record.currentStock}/{record.maxStock} {record.unit}
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  上次供应: {record.lastSupplyDate}
                </div>
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 mr-2 text-gray-500" />
                  下次供应: {record.nextSupplyDate}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">物资描述:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.description}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleUpdateStock(record, 100)}>
                    <Truck className="h-4 w-4 mr-2" />
                    入库100{record.unit}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleUpdateStock(record, -50)}>
                    <Store className="h-4 w-4 mr-2" />
                    出库50{record.unit}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>物资列表</CardTitle>
          <CardDescription>管理所有物资供应记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>物资名称</TableHead>
                <TableHead>类别</TableHead>
                <TableHead>规格型号</TableHead>
                <TableHead>单价</TableHead>
                <TableHead>供应商</TableHead>
                <TableHead>当前库存</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.name}</TableCell>
                  <TableCell>{record.category}</TableCell>
                  <TableCell>{record.specification}</TableCell>
                  <TableCell>{record.price}元/{record.unit}</TableCell>
                  <TableCell>{record.supplier}</TableCell>
                  <TableCell>{record.currentStock}/{record.maxStock} {record.unit}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => handleUpdateStock(record, 100)}>
                      <Truck className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleUpdateStock(record, -50)}>
                      <Store className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>编辑物资</DialogTitle>
            <DialogDescription>
              修改物资的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">物资名称</Label>
                <Input
                  id="edit-name"
                  value={currentRecord.name}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">物资类别</Label>
                <Select
                  value={currentRecord.category}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                >
                  <SelectTrigger id="edit-category">
                    <SelectValue placeholder="选择物资类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="安全防护">安全防护</SelectItem>
                    <SelectItem value="照明设备">照明设备</SelectItem>
                    <SelectItem value="工具设备">工具设备</SelectItem>
                    <SelectItem value="办公用品">办公用品</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-specification">规格型号</Label>
                <Input
                  id="edit-specification"
                  value={currentRecord.specification}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, specification: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-unit">单位</Label>
                <Input
                  id="edit-unit"
                  value={currentRecord.unit}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, unit: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-price">单价</Label>
              <Input
                id="edit-price"
                type="number"
                value={currentRecord.price}
                onChange={(e) => setCurrentRecord({ ...currentRecord, price: Number(e.target.value) })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-supplier">供应商</Label>
                <Input
                  id="edit-supplier"
                  value={currentRecord.supplier}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, supplier: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-contact">联系人</Label>
                <Input
                  id="edit-contact"
                  value={currentRecord.contact}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, contact: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-phone">联系电话</Label>
                <Input
                  id="edit-phone"
                  value={currentRecord.phone}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, phone: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email">电子邮箱</Label>
                <Input
                  id="edit-email"
                  value={currentRecord.email}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, email: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-address">地址</Label>
              <Input
                id="edit-address"
                value={currentRecord.address}
                onChange={(e) => setCurrentRecord({ ...currentRecord, address: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-minStock">最低库存</Label>
                <Input
                  id="edit-minStock"
                  type="number"
                  value={currentRecord.minStock}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, minStock: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-maxStock">最高库存</Label>
                <Input
                  id="edit-maxStock"
                  type="number"
                  value={currentRecord.maxStock}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, maxStock: Number(e.target.value) })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-lastSupplyDate">上次供应日期</Label>
                <Input
                  id="edit-lastSupplyDate"
                  type="date"
                  value={currentRecord.lastSupplyDate}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, lastSupplyDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-nextSupplyDate">下次供应日期</Label>
                <Input
                  id="edit-nextSupplyDate"
                  type="date"
                  value={currentRecord.nextSupplyDate}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, nextSupplyDate: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">物资描述</Label>
              <Textarea
                id="edit-description"
                value={currentRecord.description}
                onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.name}" 的物资记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
