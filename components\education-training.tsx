"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  RefreshCw,
  Download,
  BookOpen,
  Users,
  GraduationCap,
  Award,
  Search,
  FileText,
  User,
  MapPin,
  Calendar,
  Building2,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface TrainingCourse {
  id: string
  courseName: string
  courseType: string
  instructor: string
  startDate: string
  endDate: string
  location: string
  capacity: number
  enrolled: number
  status: string
}

interface TrainingRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  courseName: string
  startDate: string
  endDate: string
  completionStatus: string
  score: number
  certificateIssued: boolean
}

export function EducationTraining() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [isAddCourseOpen, setIsAddCourseOpen] = useState(false)
  const [isEditCourseOpen, setIsEditCourseOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedCourse, setSelectedCourse] = useState<TrainingCourse | null>(null)
  const [selectedRecord, setSelectedRecord] = useState<TrainingRecord | null>(null)
  const [isAddRecordOpen, setIsAddRecordOpen] = useState(false)
  const [isEditRecordOpen, setIsEditRecordOpen] = useState(false)
  const [isViewRecordDetailOpen, setIsViewRecordDetailOpen] = useState(false)
  const [isDeleteRecordDialogOpen, setIsDeleteRecordDialogOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [courses, setCourses] = useState<TrainingCourse[]>([
    {
      id: "TC001",
      courseName: "矿山安全生产培训",
      courseType: "安全培训",
      instructor: "李安全",
      startDate: "2025-03-15",
      endDate: "2025-03-20",
      location: "培训中心A室",
      capacity: 50,
      enrolled: 45,
      status: "进行中",
    },
    {
      id: "TC002",
      courseName: "新员工入职培训",
      courseType: "入职培训",
      instructor: "王人事",
      startDate: "2025-04-01",
      endDate: "2025-04-05",
      location: "培训中心B室",
      capacity: 30,
      enrolled: 25,
      status: "计划中",
    },
    {
      id: "TC003",
      courseName: "矿山设备操作技能提升",
      courseType: "技能培训",
      instructor: "张技术",
      startDate: "2025-02-10",
      endDate: "2025-02-15",
      location: "设备操作实训室",
      capacity: 20,
      enrolled: 20,
      status: "已完成",
    },
    {
      id: "TC004",
      courseName: "矿山救援技能培训",
      courseType: "安全培训",
      instructor: "刘救援",
      startDate: "2025-03-05",
      endDate: "2025-03-10",
      location: "应急救援中心",
      capacity: 25,
      enrolled: 15,
      status: "计划中",
    },
    {
      id: "TC005",
      courseName: "管理人员领导力培训",
      courseType: "管理培训",
      instructor: "赵管理",
      startDate: "2025-01-20",
      endDate: "2025-01-25",
      location: "会议中心",
      capacity: 15,
      enrolled: 15,
      status: "已完成",
    },
  ])

  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>([
    {
      id: "TR001",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "采矿部",
      courseName: "矿山安全生产培训",
      startDate: "2025-03-15",
      endDate: "2025-03-20",
      completionStatus: "进行中",
      score: 0,
      certificateIssued: false,
    },
    {
      id: "TR002",
      employeeName: "李四",
      employeeId: "EMP045",
      department: "安全部",
      courseName: "矿山设备操作技能提升",
      startDate: "2025-02-10",
      endDate: "2025-02-15",
      completionStatus: "已完成",
      score: 92,
      certificateIssued: true,
    },
    {
      id: "TR003",
      employeeName: "王五",
      employeeId: "EMP078",
      department: "机电部",
      courseName: "矿山设备操作技能提升",
      startDate: "2025-02-10",
      endDate: "2025-02-15",
      completionStatus: "已完成",
      score: 85,
      certificateIssued: true,
    },
    {
      id: "TR004",
      employeeName: "赵六",
      employeeId: "EMP102",
      department: "运输部",
      courseName: "管理人员领导力培训",
      startDate: "2025-01-20",
      endDate: "2025-01-25",
      completionStatus: "已完成",
      score: 88,
      certificateIssued: true,
    },
    {
      id: "TR005",
      employeeName: "钱七",
      employeeId: "EMP156",
      department: "通风部",
      courseName: "新员工入职培训",
      startDate: "2025-04-01",
      endDate: "2025-04-05",
      completionStatus: "计划中",
      score: 0,
      certificateIssued: false,
    },
  ])

  // 处理查看课程详情
  const handleViewCourseDetail = (course: TrainingCourse) => {
    setSelectedCourse(course)
    setIsViewDetailOpen(true)
  }

  // 处理编辑课程
  const handleEditCourse = (course: TrainingCourse) => {
    setSelectedCourse(course)
    setIsEditCourseOpen(true)
  }

  // 处理删除课程
  const handleDeleteCourse = (course: TrainingCourse) => {
    setSelectedCourse(course)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除课程
  const confirmDeleteCourse = () => {
    if (selectedCourse) {
      setCourses(prev => prev.filter(c => c.id !== selectedCourse.id))
      toast({
        title: "删除成功",
        description: "已成功删除培训课程",
      })
      setIsDeleteDialogOpen(false)
      setSelectedCourse(null)
    }
  }

  // 处理添加课程
  const handleAddCourse = (formData: Partial<TrainingCourse>) => {
    const newCourse: TrainingCourse = {
      id: `TC${courses.length + 1}`.padStart(5, '0'),
      courseName: formData.courseName || "",
      courseType: formData.courseType || "",
      instructor: formData.instructor || "",
      startDate: formData.startDate || "",
      endDate: formData.endDate || "",
      location: formData.location || "",
      capacity: formData.capacity || 0,
      enrolled: 0,
      status: "未开始"
    }

    setCourses(prev => [...prev, newCourse])
    toast({
      title: "添加成功",
      description: "已成功添加新的培训课程",
    })
    setIsAddCourseOpen(false)
  }

  // 处理更新课程
  const handleUpdateCourse = (formData: Partial<TrainingCourse>) => {
    if (!selectedCourse) return

    setCourses(prev => prev.map(course =>
      course.id === selectedCourse.id
        ? { ...course, ...formData }
        : course
    ))

    toast({
      title: "更新成功",
      description: "已成功更新培训课程信息",
    })
    setIsEditCourseOpen(false)
    setSelectedCourse(null)
  }

  // 处理查看培训记录详情
  const handleViewRecordDetail = (record: TrainingRecord) => {
    setSelectedRecord(record)
    setIsViewRecordDetailOpen(true)
  }

  // 处理编辑培训记录
  const handleEditRecord = (record: TrainingRecord) => {
    setSelectedRecord(record)
    setIsEditRecordOpen(true)
  }

  // 处理添加培训记录
  const handleAddRecord = (formData: Partial<TrainingRecord>) => {
    const newRecord: TrainingRecord = {
      id: `TR${trainingRecords.length + 1}`.padStart(5, '0'),
      employeeName: formData.employeeName || "",
      employeeId: formData.employeeId || "",
      department: formData.department || "",
      courseName: formData.courseName || "",
      startDate: formData.startDate || "",
      endDate: formData.endDate || "",
      completionStatus: "未开始",
      score: 0,
      certificateIssued: false
    }

    setTrainingRecords(prev => [...prev, newRecord])
    toast({
      title: "添加成功",
      description: "已成功添加新的培训记录",
    })
    setIsAddRecordOpen(false)
  }

  // 处理更新培训记录
  const handleUpdateRecord = (formData: Partial<TrainingRecord>) => {
    if (!selectedRecord) return

    setTrainingRecords(prev => prev.map(record =>
      record.id === selectedRecord.id
        ? { ...record, ...formData }
        : record
    ))

    toast({
      title: "更新成功",
      description: "已成功更新培训记录",
    })
    setIsEditRecordOpen(false)
    setSelectedRecord(null)
  }

  // 处理删除培训记录
  const handleDeleteRecord = (record: TrainingRecord) => {
    setSelectedRecord(record)
    setIsDeleteRecordDialogOpen(true)
  }

  // 确认删除培训记录
  const confirmDeleteRecord = () => {
    if (selectedRecord) {
      setTrainingRecords(prev => prev.filter(r => r.id !== selectedRecord.id))
      toast({
        title: "删除成功",
        description: "已成功删除培训记录",
      })
      setIsDeleteRecordDialogOpen(false)
      setSelectedRecord(null)
    }
  }

  // 处理刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "刷新成功",
        description: "数据已更新",
      })
    }, 1000)
  }

  // 处理导出数据
  const handleExport = () => {
    try {
      // 创建CSV内容
      const headers = ["记录编号", "员工姓名", "员工编号", "部门", "课程名称", "开始日期", "结束日期", "完成状态", "成绩", "证书状态"]
      const rows = trainingRecords.map(record => [
        record.id,
        record.employeeName,
        record.employeeId,
        record.department,
        record.courseName,
        record.startDate,
        record.endDate,
        record.completionStatus,
        record.score,
        record.certificateIssued ? "已发放" : "未发放"
      ])

      const csvContent = [headers, ...rows].map(row => row.join(",")).join("\n")
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")
      const url = URL.createObjectURL(blob)

      link.setAttribute("href", url)
      link.setAttribute("download", `培训记录_${new Date().toLocaleDateString()}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      })
    } catch (error) {
      console.error("导出失败:", error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 筛选课程
  const filteredCourses = courses.filter(course => {
    const matchesSearch = !searchTerm ||
      course.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.courseType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = typeFilter === "all" || course.courseType === typeFilter
    const matchesStatus = statusFilter === "all" || course.status === statusFilter

    return matchesSearch && matchesType && matchesStatus
  })

  // 筛选培训记录
  const filteredRecords = trainingRecords.filter(record => {
    const matchesSearch = !searchTerm ||
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.courseName.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesDepartment = departmentFilter === "all" || record.department === departmentFilter
    const matchesStatus = statusFilter === "all" || record.completionStatus === statusFilter

    return matchesSearch && matchesDepartment && matchesStatus
  })

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">教育与培训管理</h1>
        <div className="flex items-center space-x-2">
          <Button onClick={handleRefresh} variant="outline" size="icon" disabled={loading}>
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          </Button>
          <Button onClick={handleExport} variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <BookOpen className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">培训课程总数</p>
                  <div className="text-2xl font-bold">
                    {courses.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    进行中 {courses.filter(c => c.status === "进行中").length} 个
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <Users className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">参训人数</p>
                  <div className="text-2xl font-bold">
                    {trainingRecords.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    完成率 {((trainingRecords.filter(r => r.completionStatus === "已完成").length / trainingRecords.length) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-yellow-100 p-3">
                  <GraduationCap className="h-6 w-6 text-yellow-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">平均成绩</p>
                  <div className="text-2xl font-bold">
                    {(trainingRecords.reduce((acc, r) => acc + (r.completionStatus === "已完成" ? r.score : 0), 0) / trainingRecords.filter(r => r.completionStatus === "已完成").length).toFixed(1)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    优秀率 {((trainingRecords.filter(r => r.score >= 90).length / trainingRecords.filter(r => r.completionStatus === "已完成").length) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-purple-100 p-3">
                  <Award className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">证书发放</p>
                  <div className="text-2xl font-bold">
                    {trainingRecords.filter(r => r.certificateIssued).length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    发放率 {((trainingRecords.filter(r => r.certificateIssued).length / trainingRecords.filter(r => r.completionStatus === "已完成").length) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-wrap gap-4">
        <div className="flex items-center space-x-2">
          <Search className="w-4 h-4 text-gray-500" />
          <Input
            placeholder="搜索课程名称、讲师..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </div>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="课程类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="安全培训">安全培训</SelectItem>
            <SelectItem value="技能培训">技能培训</SelectItem>
            <SelectItem value="管理培训">管理培训</SelectItem>
            <SelectItem value="入职培训">入职培训</SelectItem>
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="课程状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="未开始">未开始</SelectItem>
            <SelectItem value="进行中">进行中</SelectItem>
            <SelectItem value="已完成">已完成</SelectItem>
          </SelectContent>
        </Select>

        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="所属部门" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部部门</SelectItem>
            <SelectItem value="采矿部">采矿部</SelectItem>
            <SelectItem value="安全部">安全部</SelectItem>
            <SelectItem value="机电部">机电部</SelectItem>
            <SelectItem value="运输部">运输部</SelectItem>
            <SelectItem value="通风部">通风部</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="courses">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="courses">培训课程</TabsTrigger>
          <TabsTrigger value="records">培训记录</TabsTrigger>
        </TabsList>

        <TabsContent value="courses" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>培训课程管理</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>新增课程</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增培训课程</DialogTitle>
                      <DialogDescription>请填写培训课程信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="courseName">课程名称</Label>
                        <Input id="courseName" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="courseType">课程类型</Label>
                          <Input id="courseType" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="instructor">讲师</Label>
                          <Input id="instructor" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="startDate">开始日期</Label>
                          <Input id="startDate" type="date" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endDate">结束日期</Label>
                          <Input id="endDate" type="date" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="location">培训地点</Label>
                          <Input id="location" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="capacity">培训容量</Label>
                          <Input id="capacity" type="number" />
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理培训课程，包括安全培训、技能培训、管理培训等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程编号</TableHead>
                    <TableHead>课程名称</TableHead>
                    <TableHead>课程类型</TableHead>
                    <TableHead>讲师</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>结束日期</TableHead>
                    <TableHead>培训地点</TableHead>
                    <TableHead>报名情况</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCourses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>{course.id}</TableCell>
                      <TableCell>{course.courseName}</TableCell>
                      <TableCell>{course.courseType}</TableCell>
                      <TableCell>{course.instructor}</TableCell>
                      <TableCell>{course.startDate}</TableCell>
                      <TableCell>{course.endDate}</TableCell>
                      <TableCell>{course.location}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-xs">
                            {course.enrolled}/{course.capacity}
                          </div>
                          <Progress value={(course.enrolled / course.capacity) * 100} />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            course.status === "已完成" ? "default" :
                            course.status === "进行中" ? "secondary" :
                            "outline"
                          }
                        >
                          {course.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" onClick={() => handleViewCourseDetail(course)}>
                            查看
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEditCourse(course)}>
                            编辑
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteCourse(course)}>
                            删除
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="records" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>培训记录管理</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>新增记录</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>新增培训记录</DialogTitle>
                      <DialogDescription>请填写员工培训记录信息，所有字段均为必填</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="employeeName">员工姓名</Label>
                          <Input id="employeeName" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employeeId">员工编号</Label>
                          <Input id="employeeId" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="department">所属部门</Label>
                          <Input id="department" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="courseName">课程名称</Label>
                          <Input id="courseName" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="startDate">开始日期</Label>
                          <Input id="startDate" type="date" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endDate">结束日期</Label>
                          <Input id="endDate" type="date" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="completionStatus">完成状态</Label>
                          <Input id="completionStatus" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="score">成绩</Label>
                          <Input id="score" type="number" />
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">提交</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理员工培训记录，包括培训课程、完成情况、成绩等</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>记录编号</TableHead>
                    <TableHead>员工姓名</TableHead>
                    <TableHead>员工编号</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>课程名称</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>结束日期</TableHead>
                    <TableHead>完成状态</TableHead>
                    <TableHead>成绩</TableHead>
                    <TableHead>证书</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>{record.id}</TableCell>
                      <TableCell>{record.employeeName}</TableCell>
                      <TableCell>{record.employeeId}</TableCell>
                      <TableCell>{record.department}</TableCell>
                      <TableCell>{record.courseName}</TableCell>
                      <TableCell>{record.startDate}</TableCell>
                      <TableCell>{record.endDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            record.completionStatus === "已完成" ? "default" :
                            record.completionStatus === "进行中" ? "secondary" :
                            "outline"
                          }
                        >
                          {record.completionStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.completionStatus === "已完成" ? record.score : "-"}</TableCell>
                      <TableCell>{record.certificateIssued ? "已发放" : "未发放"}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" onClick={() => handleViewRecordDetail(record)}>
                            查看
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEditRecord(record)}>
                            编辑
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteRecord(record)}>
                            删除
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>培训课程详情</DialogTitle>
          </DialogHeader>
          {selectedCourse && (
            <div className="space-y-6">
              <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                <div className="rounded-full bg-blue-100 p-3">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedCourse.courseName}</h3>
                  <p className="text-sm text-muted-foreground">课程编号：{selectedCourse.id}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>课程类型</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCourse.courseType}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>课程状态</Label>
                  <div className="mt-1">
                    <Badge variant={selectedCourse.status === "已完成" ? "default" : selectedCourse.status === "进行中" ? "secondary" : "outline"}>
                      {selectedCourse.status}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>讲师</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCourse.instructor}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>培训地点</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCourse.location}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>开始日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCourse.startDate}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>结束日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedCourse.endDate}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>报名情况</Label>
                <div className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-muted-foreground">已报名/总容量</span>
                    <span className="text-sm font-medium">{selectedCourse.enrolled}/{selectedCourse.capacity}</span>
                  </div>
                  <Progress value={(selectedCourse.enrolled / selectedCourse.capacity) * 100} />
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsViewDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewDetailOpen(false)
              if (selectedCourse) {
                handleEditCourse(selectedCourse)
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditCourseOpen} onOpenChange={setIsEditCourseOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑培训课程</DialogTitle>
            <DialogDescription>修改培训课程信息</DialogDescription>
          </DialogHeader>
          {selectedCourse && (
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-courseName">课程名称</Label>
                <Input
                  id="edit-courseName"
                  defaultValue={selectedCourse.courseName}
                  onChange={(e) => {
                    if (selectedCourse) {
                      setSelectedCourse({
                        ...selectedCourse,
                        courseName: e.target.value
                      })
                    }
                  }}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-courseType">课程类型</Label>
                  <Select
                    defaultValue={selectedCourse.courseType}
                    onValueChange={(value) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          courseType: value
                        })
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择课程类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全培训">安全培训</SelectItem>
                      <SelectItem value="技能培训">技能培训</SelectItem>
                      <SelectItem value="管理培训">管理培训</SelectItem>
                      <SelectItem value="入职培训">入职培训</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-instructor">讲师</Label>
                  <Input
                    id="edit-instructor"
                    defaultValue={selectedCourse.instructor}
                    onChange={(e) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          instructor: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-startDate">开始日期</Label>
                  <Input
                    id="edit-startDate"
                    type="date"
                    defaultValue={selectedCourse.startDate}
                    onChange={(e) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          startDate: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-endDate">结束日期</Label>
                  <Input
                    id="edit-endDate"
                    type="date"
                    defaultValue={selectedCourse.endDate}
                    onChange={(e) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          endDate: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-location">培训地点</Label>
                  <Input
                    id="edit-location"
                    defaultValue={selectedCourse.location}
                    onChange={(e) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          location: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-capacity">培训容量</Label>
                  <Input
                    id="edit-capacity"
                    type="number"
                    min={selectedCourse.enrolled}
                    defaultValue={selectedCourse.capacity}
                    onChange={(e) => {
                      if (selectedCourse) {
                        setSelectedCourse({
                          ...selectedCourse,
                          capacity: parseInt(e.target.value)
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">课程状态</Label>
                <Select
                  defaultValue={selectedCourse.status}
                  onValueChange={(value) => {
                    if (selectedCourse) {
                      setSelectedCourse({
                        ...selectedCourse,
                        status: value
                      })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择课程状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="未开始">未开始</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditCourseOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (selectedCourse) {
                handleUpdateCourse(selectedCourse)
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除该培训课程吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCourse}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isViewRecordDetailOpen} onOpenChange={setIsViewRecordDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>培训记录详情</DialogTitle>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-6">
              <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                <div className="rounded-full bg-blue-100 p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedRecord.employeeName}</h3>
                  <p className="text-sm text-muted-foreground">员工编号：{selectedRecord.employeeId}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>所属部门</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.department}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>课程名称</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.courseName}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>开始日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.startDate}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>结束日期</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.endDate}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>完成状态</Label>
                  <div className="mt-1">
                    <Badge variant={selectedRecord.completionStatus === "已完成" ? "default" : selectedRecord.completionStatus === "进行中" ? "secondary" : "outline"}>
                      {selectedRecord.completionStatus}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>成绩</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <GraduationCap className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.completionStatus === "已完成" ? selectedRecord.score : "-"}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>证书状态</Label>
                <div className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedRecord.certificateIssued ? "已发放" : "未发放"}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsViewRecordDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewRecordDetailOpen(false)
              if (selectedRecord) {
                handleEditRecord(selectedRecord)
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditRecordOpen} onOpenChange={setIsEditRecordOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑培训记录</DialogTitle>
            <DialogDescription>修改培训记录信息</DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-employeeName">员工姓名</Label>
                  <Input
                    id="edit-employeeName"
                    defaultValue={selectedRecord.employeeName}
                    onChange={(e) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          employeeName: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-employeeId">员工编号</Label>
                  <Input
                    id="edit-employeeId"
                    defaultValue={selectedRecord.employeeId}
                    onChange={(e) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          employeeId: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-department">所属部门</Label>
                  <Select
                    defaultValue={selectedRecord.department}
                    onValueChange={(value) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          department: value
                        })
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="采矿部">采矿部</SelectItem>
                      <SelectItem value="安全部">安全部</SelectItem>
                      <SelectItem value="机电部">机电部</SelectItem>
                      <SelectItem value="运输部">运输部</SelectItem>
                      <SelectItem value="通风部">通风部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-courseName">课程名称</Label>
                  <Select
                    defaultValue={selectedRecord.courseName}
                    onValueChange={(value) => {
                      if (selectedRecord) {
                        const course = courses.find(c => c.courseName === value)
                        if (course) {
                          setSelectedRecord({
                            ...selectedRecord,
                            courseName: value,
                            startDate: course.startDate,
                            endDate: course.endDate
                          })
                        }
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择课程" />
                    </SelectTrigger>
                    <SelectContent>
                      {courses.map(course => (
                        <SelectItem key={course.id} value={course.courseName}>
                          {course.courseName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-startDate">开始日期</Label>
                  <Input
                    id="edit-startDate"
                    type="date"
                    defaultValue={selectedRecord.startDate}
                    onChange={(e) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          startDate: e.target.value
                        })
                      }
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-endDate">结束日期</Label>
                  <Input
                    id="edit-endDate"
                    type="date"
                    defaultValue={selectedRecord.endDate}
                    onChange={(e) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          endDate: e.target.value
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-completionStatus">完成状态</Label>
                  <Select
                    defaultValue={selectedRecord.completionStatus}
                    onValueChange={(value) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          completionStatus: value
                        })
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="未开始">未开始</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-score">成绩</Label>
                  <Input
                    id="edit-score"
                    type="number"
                    min="0"
                    max="100"
                    defaultValue={selectedRecord.score}
                    disabled={selectedRecord.completionStatus !== "已完成"}
                    onChange={(e) => {
                      if (selectedRecord) {
                        setSelectedRecord({
                          ...selectedRecord,
                          score: parseInt(e.target.value)
                        })
                      }
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-certificateIssued">证书状态</Label>
                <Select
                  defaultValue={selectedRecord.certificateIssued ? "true" : "false"}
                  onValueChange={(value) => {
                    if (selectedRecord) {
                      setSelectedRecord({
                        ...selectedRecord,
                        certificateIssued: value === "true"
                      })
                    }
                  }}
                  disabled={selectedRecord.completionStatus !== "已完成"}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">已发放</SelectItem>
                    <SelectItem value="false">未发放</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditRecordOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (selectedRecord) {
                handleUpdateRecord(selectedRecord)
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteRecordDialogOpen} onOpenChange={setIsDeleteRecordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除该培训记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteRecordDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteRecord}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

