"use client"

import { useState, useMemo } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useTheme } from "@/contexts/theme-context"
import {
  AlertCircle,
  TrendingUp,
  Lightbulb,
  Search,
  Filter,
  ChevronLeft,
  AlertTriangle,
  Info,
  ArrowUpRight,
  ArrowDownRight,
  CheckCircle,
  Clock,
  Calendar,
  FileText,
  BarChart,
  Zap,
  Package,
  Shield,
  Users,
  DollarSign,
  Database,
  Wrench,
  Truck,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import styles from "@/components/insights.module.css"

// 定义洞察数据接口
interface Insight {
  id: number;
  title: string;
  description: string;
  type: 'anomaly' | 'trend' | 'recommendation' | 'alert';
  timestamp: Date;
  module: string;
  importance: 'high' | 'medium' | 'low';
  icon: React.ReactNode;
  color: string;
}

export function InsightsView() {
  const router = useRouter()
  const { themeMode } = useTheme()
  
  // 设置日期在2025年1月至4月之间
  const getRandomDate = () => {
    const start = new Date('2025-01-01')
    const end = new Date('2025-04-15')
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  }
  
  // 智能洞察数据
  const [insights, setInsights] = useState<Insight[]>([
    {
      id: 1,
      title: '能源使用异常检测',
      description: '检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况',
      type: 'anomaly',
      timestamp: getRandomDate(),
      module: '能源管理',
      importance: 'high',
      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 2,
      title: '财务趋势预测',
      description: '根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标',
      type: 'trend',
      timestamp: getRandomDate(),
      module: '财务管理',
      importance: 'medium',
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      color: '#52c41a',
    },
    {
      id: 3,
      title: '设备维护建议',
      description: '3号生产线设备预计在14天内需要进行预防性维护，建议提前安排',
      type: 'recommendation',
      timestamp: getRandomDate(),
      module: '设备管理',
      importance: 'medium',
      icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
      color: '#faad14',
    },
    {
      id: 4,
      title: '项目风险预警',
      description: '主厂房建设项目进度已落后计划7.2%，建议增加资源投入',
      type: 'alert',
      timestamp: getRandomDate(),
      module: '项目管理',
      importance: 'high',
      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 5,
      title: '物资库存优化',
      description: '分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本',
      type: 'recommendation',
      timestamp: getRandomDate(),
      module: '物资管理',
      importance: 'low',
      icon: <Lightbulb className="h-5 w-5 text-blue-500" />,
      color: '#1890ff',
    },
    {
      id: 6,
      title: '安全隐患预警',
      description: 'A区3号巷道支护强度不足，存在安全隐患风险',
      type: 'alert',
      timestamp: getRandomDate(),
      module: '安全管理',
      importance: 'high',
      icon: <AlertTriangle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 7,
      title: '人员配置优化',
      description: 'B区人员配置效率低下，建议调整班次安排提高效率',
      type: 'recommendation',
      timestamp: getRandomDate(),
      module: '人事管理',
      importance: 'medium',
      icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
      color: '#faad14',
    },
    {
      id: 8,
      title: '设备效率分析',
      description: '2号生产线效率较上月提升15%，可推广其操作方法',
      type: 'trend',
      timestamp: getRandomDate(),
      module: '设备管理',
      importance: 'medium',
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      color: '#52c41a',
    },
    {
      id: 9,
      title: '成本节约机会',
      description: '通过优化采购流程，预计可节省年度采购成本8.5%',
      type: 'recommendation',
      timestamp: getRandomDate(),
      module: '财务管理',
      importance: 'medium',
      icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
      color: '#faad14',
    },
    {
      id: 10,
      title: '能源使用效率提升',
      description: 'C区通过设备调整，能源使用效率提升12%',
      type: 'trend',
      timestamp: getRandomDate(),
      module: '能源管理',
      importance: 'low',
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      color: '#52c41a',
    },
    {
      id: 11,
      title: '库存周转率异常',
      description: '主要原材料库存周转率下降30%，建议检查供应链',
      type: 'anomaly',
      timestamp: getRandomDate(),
      module: '物资管理',
      importance: 'high',
      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 12,
      title: '安全培训效果分析',
      description: '新安全培训计划实施后，安全事故率下降25%',
      type: 'trend',
      timestamp: getRandomDate(),
      module: '安全管理',
      importance: 'medium',
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      color: '#52c41a',
    },
  ])
  
  // 筛选状态
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [importanceFilter, setImportanceFilter] = useState("all")
  const [moduleFilter, setModuleFilter] = useState("all")
  const [viewMode, setViewMode] = useState("card") // card 或 table
  const [activeTab, setActiveTab] = useState("all") // today, week, all
  
  // 获取所有模块列表（用于筛选）
  const allModules = useMemo(() => {
    const modules = new Set<string>()
    insights.forEach(insight => modules.add(insight.module))
    return Array.from(modules)
  }, [insights])
  
  // 根据筛选条件过滤洞察
  const filteredInsights = useMemo(() => {
    // 首先按照时间标签筛选
    let filtered = [...insights]
    
    if (activeTab === "today") {
      const today = new Date()
      filtered = filtered.filter(insight => 
        insight.timestamp.getDate() === today.getDate() &&
        insight.timestamp.getMonth() === today.getMonth() &&
        insight.timestamp.getFullYear() === today.getFullYear()
      )
    } else if (activeTab === "week") {
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      filtered = filtered.filter(insight => insight.timestamp >= weekAgo)
    }
    
    // 然后应用其他筛选条件
    return filtered.filter(insight => {
      // 搜索词筛选
      const matchesSearch = searchTerm === "" ||
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.description.toLowerCase().includes(searchTerm.toLowerCase())
      
      // 类型筛选
      const matchesType = typeFilter === "all" || insight.type === typeFilter
      
      // 重要程度筛选
      const matchesImportance = importanceFilter === "all" || insight.importance === importanceFilter
      
      // 模块筛选
      const matchesModule = moduleFilter === "all" || insight.module === moduleFilter
      
      return matchesSearch && matchesType && matchesImportance && matchesModule
    })
  }, [insights, searchTerm, typeFilter, importanceFilter, moduleFilter, activeTab])
  
  // 获取类型图标和颜色
  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'anomaly':
        return { 
          icon: <AlertCircle className="h-4 w-4" />, 
          label: '异常', 
          color: 'text-red-500',
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          textColor: 'text-red-700 dark:text-red-300'
        }
      case 'trend':
        return { 
          icon: <TrendingUp className="h-4 w-4" />, 
          label: '趋势', 
          color: 'text-green-500',
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          textColor: 'text-green-700 dark:text-green-300'
        }
      case 'recommendation':
        return { 
          icon: <Lightbulb className="h-4 w-4" />, 
          label: '建议', 
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          textColor: 'text-yellow-700 dark:text-yellow-300'
        }
      case 'alert':
        return { 
          icon: <AlertTriangle className="h-4 w-4" />, 
          label: '警告', 
          color: 'text-red-500',
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          textColor: 'text-red-700 dark:text-red-300'
        }
      default:
        return { 
          icon: <Info className="h-4 w-4" />, 
          label: '信息', 
          color: 'text-blue-500',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30',
          textColor: 'text-blue-700 dark:text-blue-300'
        }
    }
  }
  
  // 获取模块图标
  const getModuleIcon = (module: string) => {
    switch (module) {
      case '能源管理':
        return <Zap className="h-4 w-4" />
      case '财务管理':
        return <DollarSign className="h-4 w-4" />
      case '设备管理':
        return <Wrench className="h-4 w-4" />
      case '项目管理':
        return <BarChart className="h-4 w-4" />
      case '物资管理':
        return <Package className="h-4 w-4" />
      case '安全管理':
        return <Shield className="h-4 w-4" />
      case '人事管理':
        return <Users className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }
  
  // 获取重要程度标签
  const getImportanceBadge = (importance: string) => {
    switch (importance) {
      case 'high':
        return <Badge variant="destructive">高</Badge>
      case 'medium':
        return <Badge>中</Badge>
      case 'low':
        return <Badge variant="outline">低</Badge>
      default:
        return null
    }
  }
  
  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredInsights.map(insight => (
          <Card 
            key={insight.id} 
            className={`overflow-hidden transition-all duration-300 hover:shadow-md ${
              themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c]" : "bg-white"
            }`}
            style={{ borderLeft: `4px solid ${insight.color}` }}
            onClick={() => router.push(`/${insight.module.toLowerCase().replace(/\s+/g, '-')}`)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <div className={`p-2 rounded-md ${
                    themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"
                  }`}>
                    {insight.icon}
                  </div>
                  <div>
                    <CardTitle className="text-base">{insight.title}</CardTitle>
                    <CardDescription className="text-xs flex items-center gap-1">
                      {insight.timestamp.toLocaleDateString('zh-CN')} • {insight.module}
                    </CardDescription>
                  </div>
                </div>
                <div>
                  {insight.importance === 'high' && <span className={styles.highImportance}>高</span>}
                  {insight.importance === 'medium' && <span className={styles.mediumImportance}>中</span>}
                  {insight.importance === 'low' && <span className={styles.lowImportance}>低</span>}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className={`text-sm ${themeMode === "dark" ? "text-gray-300" : "text-gray-600"}`}>
                {insight.description}
              </p>
            </CardContent>
            <CardFooter className="pt-0 flex justify-between">
              <div className={`text-xs px-2 py-1 rounded-full ${getTypeInfo(insight.type).bgColor} ${getTypeInfo(insight.type).textColor}`}>
                <div className="flex items-center gap-1">
                  {getTypeInfo(insight.type).icon}
                  <span>{getTypeInfo(insight.type).label}</span>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-xs"
                onClick={(e) => {
                  e.stopPropagation()
                  router.push(`/${insight.module.toLowerCase().replace(/\s+/g, '-')}`)
                }}
              >
                查看详情
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }
  
  // 渲染表格视图
  const renderTableView = () => {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>洞察</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>模块</TableHead>
              <TableHead>时间</TableHead>
              <TableHead>重要程度</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredInsights.map(insight => (
              <TableRow key={insight.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className={`p-1.5 rounded-md ${
                      themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"
                    }`}>
                      {insight.icon}
                    </div>
                    <div>
                      <div>{insight.title}</div>
                      <div className={`text-xs ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                        {insight.description.length > 50 
                          ? `${insight.description.substring(0, 50)}...` 
                          : insight.description}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getTypeInfo(insight.type).bgColor} ${getTypeInfo(insight.type).textColor}`}>
                    {getTypeInfo(insight.type).icon}
                    <span className="ml-1">{getTypeInfo(insight.type).label}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    {getModuleIcon(insight.module)}
                    <span>{insight.module}</span>
                  </div>
                </TableCell>
                <TableCell>{insight.timestamp.toLocaleDateString('zh-CN')}</TableCell>
                <TableCell>{getImportanceBadge(insight.importance)}</TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => router.push(`/${insight.module.toLowerCase().replace(/\s+/g, '-')}`)}
                  >
                    查看详情
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* 头部区域 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={() => router.push("/")}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div>
            <h2 className="text-2xl font-bold">智能洞察</h2>
            <p className={`text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
              系统发现的异常、趋势和优化建议
            </p>
          </div>
        </div>
      </div>
      
      {/* 筛选和视图切换区域 */}
      <Card className={themeMode === "dark" ? "bg-[#2c2c2e] border-[#3a3a3c]" : ""}>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="today">今日</TabsTrigger>
                <TabsTrigger value="week">本周</TabsTrigger>
                <TabsTrigger value="all">全部</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索洞察..."
                  className="w-[200px] pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="类型筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="anomaly">异常</SelectItem>
                  <SelectItem value="trend">趋势</SelectItem>
                  <SelectItem value="recommendation">建议</SelectItem>
                  <SelectItem value="alert">警告</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={importanceFilter} onValueChange={setImportanceFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="重要程度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部级别</SelectItem>
                  <SelectItem value="high">高</SelectItem>
                  <SelectItem value="medium">中</SelectItem>
                  <SelectItem value="low">低</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={moduleFilter} onValueChange={setModuleFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="模块筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部模块</SelectItem>
                  {allModules.map(module => (
                    <SelectItem key={module} value={module}>{module}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setViewMode("card")}>
                    卡片视图
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setViewMode("table")}>
                    表格视图
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="mb-4">
            <p className={`text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
              共找到 {filteredInsights.length} 条智能洞察
              {filteredInsights.length === 0 && searchTerm && (
                <span className="ml-2">没有找到与"{searchTerm}"相关的洞察</span>
              )}
            </p>
          </div>
          
          {viewMode === "card" ? renderCardView() : renderTableView()}
        </CardContent>
      </Card>
    </div>
  )
}
