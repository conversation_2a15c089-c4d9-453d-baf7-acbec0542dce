"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  AlertTriangle,
  ClipboardCheck,
  ThumbsUp,
  ThumbsDown,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  FileText,
  MoreHorizontal,
  RefreshCcw,
  FileCheck,
  Clock,
  PieChart,
  TrendingUp,
  TrendingDown,
  BarChart,
  ListFilter,
  XCircle,
  Share2,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  Dialog<PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"
import { Progress } from "@/components/ui/progress"

// 检查记录接口
interface QualityRecord {
  id: string;
  project: string;
  department: string;
  inspectionType: string;
  inspector: string;
  date: string;
  location: string;
  result: "合格" | "不合格" | "部分合格";
  issues: number;
  score: number;  // 质量评分，满分100
  recommendations: string;
  status: "已完成" | "整改中" | "待审核" | "已取消";
  deadline?: string;  // 整改截止日期
  followUp?: string;  // 跟进记录
  attachments?: string[];  // 附件
  createdAt?: string;
  updatedAt?: string;
  disabled?: boolean;
  qualityStandard?: string; // 采用的质量标准
  problemCategory?: string; // 问题分类
  severity?: "高" | "中" | "低"; // 问题严重程度
  remediation?: QualityRemediation[]; // 整改记录
}

// 整改记录接口
interface QualityRemediation {
  id: string;
  qualityRecordId: string;
  date: string;
  measures: string;
  handler: string;
  result: string;
  progress: number;
  remarks?: string;
  verifier?: string;
  verifyDate?: string;
  attachments?: string[];
}

// 统计数据接口
interface QualityStatistics {
  total: number;  // 总记录数
  passed: number; // 合格数
  failed: number; // 不合格数
  partial: number; // 部分合格数
  fixing: number;  // 整改中数
  pending: number; // 待审核数
  avgScore: number; // 平均质量评分
  typeDistribution: Record<string, number>;  // 检查类型分布
  resultDistribution: Record<string, number>; // 结果分布
  departmentDistribution: Record<string, number>; // 部门分布
  problemCategories: Record<string, number>; // 问题类别分布
  trendByMonth: Array<{  // 按月趋势
    month: string;
    score: number;
    issues: number;
  }>;
  topProblems: Array<{  // 常见问题统计
    category: string;
    count: number;
  }>;
}

// 导出设置接口
interface ExportSettings {
  format: "xlsx" | "csv" | "pdf";
  range: "all" | "selected" | "filtered";
  dateRange: { start: string; end: string };
  includeAttachments: boolean;
}

export function ProjectQuality() {
  const { toast } = useToast()
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isBatchDelete, setIsBatchDelete] = useState(false)
  const [isRemediationDialogOpen, setIsRemediationDialogOpen] = useState(false)
  
  const [selectedRecord, setSelectedRecord] = useState<QualityRecord | null>(null)
  const [selectedRecordIds, setSelectedRecordIds] = useState<string[]>([])
  const [formData, setFormData] = useState<Partial<QualityRecord>>({})
  const [remediationFormData, setRemediationFormData] = useState<Partial<QualityRemediation>>({})
  const [searchText, setSearchText] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterResult, setFilterResult] = useState("all")
  const [filterDepartment, setFilterDepartment] = useState("all")
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)

  // 示例数据
  const [qualityRecords, setQualityRecords] = useState<QualityRecord[]>([
    {
      id: "1",
      project: "矿区A3开发项目",
      department: "采矿工程部",
      inspectionType: "设备质量检查",
      inspector: "张三",
      date: "2023-12-10",
      location: "A3矿区设备间",
      result: "合格",
      score: 92,
      issues: 0,
      recommendations: "继续保持",
      status: "已完成",
      qualityStandard: "GB/T 19001-2016",
      problemCategory: "无",
      severity: "低",
      createdAt: "2023-12-10",
      updatedAt: "2023-12-10",
      remediation: []
    },
    {
      id: "2",
      project: "设备更新计划",
      department: "设备管理部",
      inspectionType: "材料质量检查",
      inspector: "李四",
      date: "2023-12-08",
      location: "材料库房",
      result: "不合格",
      score: 65,
      issues: 3,
      recommendations: "更换供应商",
      status: "整改中",
      deadline: "2023-12-20",
      qualityStandard: "JB/T 9001-2000",
      problemCategory: "材料规格不符",
      severity: "高",
      createdAt: "2023-12-08",
      updatedAt: "2023-12-09",
      remediation: [
        {
          id: "r1",
          qualityRecordId: "2",
          date: "2023-12-09",
          measures: "联系新供应商提供样品",
          handler: "王工",
          result: "待验收",
          progress: 30,
          remarks: "新供应商已提供样品，正在测试中"
        }
      ]
    },
    {
      id: "3",
      project: "安全系统升级",
      department: "安全管理部",
      inspectionType: "系统功能测试",
      inspector: "王五",
      date: "2023-12-05",
      location: "控制中心",
      result: "部分合格",
      score: 78,
      issues: 2,
      recommendations: "修复已发现问题",
      status: "整改中",
      deadline: "2023-12-15",
      qualityStandard: "GB/T 33131-2016",
      problemCategory: "功能缺陷",
      severity: "中",
      createdAt: "2023-12-05",
      updatedAt: "2023-12-07",
      remediation: [
        {
          id: "r2",
          qualityRecordId: "3",
          date: "2023-12-07",
          measures: "修复报警延迟问题",
          handler: "技术小组",
          result: "已解决",
          progress: 60,
          remarks: "已修复主要问题，次要问题正在处理"
        }
      ]
    },
    {
      id: "4",
      project: "新矿区勘探",
      department: "勘探部",
      inspectionType: "勘探数据质量",
      inspector: "赵六",
      date: "2023-12-12",
      location: "B2矿区",
      result: "合格",
      score: 95,
      issues: 0,
      recommendations: "继续下一阶段",
      status: "已完成",
      qualityStandard: "DZ/T 0078-2015",
      problemCategory: "无",
      severity: "低",
      createdAt: "2023-12-12",
      updatedAt: "2023-12-12",
      remediation: []
    },
    {
      id: "5",
      project: "环保设施改造",
      department: "环保部",
      inspectionType: "环保设备检测",
      inspector: "钱七",
      date: "2023-12-07",
      location: "污水处理站",
      result: "部分合格",
      score: 75,
      issues: 1,
      recommendations: "调整设备参数",
      status: "整改中",
      deadline: "2023-12-17",
      qualityStandard: "GB/T 24001-2016",
      problemCategory: "参数不达标",
      severity: "中",
      createdAt: "2023-12-07",
      updatedAt: "2023-12-10",
      remediation: [
        {
          id: "r3",
          qualityRecordId: "5",
          date: "2023-12-10",
          measures: "重新调试过滤系统",
          handler: "环保技术员",
          result: "进行中",
          progress: 40,
          remarks: "需要更换部分零件"
        }
      ]
    },
    {
      id: "6",
      project: "井下支护工程",
      department: "土建工程部",
      inspectionType: "结构质量检查",
      inspector: "孙八",
      date: "2023-12-11",
      location: "主井巷道",
      result: "合格",
      score: 88,
      issues: 1,
      recommendations: "增加巡检频率",
      status: "已完成",
      qualityStandard: "GB 50086-2001",
      problemCategory: "轻微偏差",
      severity: "低",
      createdAt: "2023-12-11",
      updatedAt: "2023-12-11",
      remediation: []
    },
    {
      id: "7",
      project: "变电站升级",
      department: "电力工程部",
      inspectionType: "电气安装质量",
      inspector: "周九",
      date: "2023-12-06",
      location: "主变电站",
      result: "合格",
      score: 90,
      issues: 0,
      recommendations: "按期验收",
      status: "待审核",
      qualityStandard: "GB 50303-2015",
      problemCategory: "无",
      severity: "低",
      createdAt: "2023-12-06",
      updatedAt: "2023-12-06",
      remediation: []
    },
    {
      id: "8",
      project: "矿山道路修缮",
      department: "基础设施部",
      inspectionType: "路面质量检查",
      inspector: "吴十",
      date: "2023-12-09",
      location: "矿区主干道",
      result: "不合格",
      score: 60,
      issues: 2,
      recommendations: "重新铺设",
      status: "待审核",
      qualityStandard: "JTG F80/1-2017",
      problemCategory: "材料不达标",
      severity: "高",
      createdAt: "2023-12-09",
      updatedAt: "2023-12-09",
      remediation: []
    }
  ])

  // 计算统计数据
  const statistics: QualityStatistics = {
    total: qualityRecords.length,
    passed: qualityRecords.filter(r => r.result === "合格").length,
    failed: qualityRecords.filter(r => r.result === "不合格").length,
    partial: qualityRecords.filter(r => r.result === "部分合格").length,
    fixing: qualityRecords.filter(r => r.status === "整改中").length,
    pending: qualityRecords.filter(r => r.status === "待审核").length,
    avgScore: Math.round(qualityRecords.reduce((sum, r) => sum + r.score, 0) / qualityRecords.length),
    
    // 统计数据分布
    typeDistribution: qualityRecords.reduce((acc, record) => {
      acc[record.inspectionType] = (acc[record.inspectionType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    
    resultDistribution: {
      "合格": qualityRecords.filter(r => r.result === "合格").length,
      "部分合格": qualityRecords.filter(r => r.result === "部分合格").length,
      "不合格": qualityRecords.filter(r => r.result === "不合格").length
    },
    
    departmentDistribution: qualityRecords.reduce((acc, record) => {
      acc[record.department] = (acc[record.department] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    
    problemCategories: qualityRecords.reduce((acc, record) => {
      if (record.problemCategory && record.problemCategory !== "无") {
        acc[record.problemCategory] = (acc[record.problemCategory] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>),
    
    // 按月趋势（模拟数据）
    trendByMonth: [
      { month: "2023-07", score: 82, issues: 5 },
      { month: "2023-08", score: 85, issues: 4 },
      { month: "2023-09", score: 80, issues: 6 },
      { month: "2023-10", score: 84, issues: 3 },
      { month: "2023-11", score: 87, issues: 2 },
      { month: "2023-12", score: 83, issues: 4 }
    ],
    
    // 常见问题统计
    topProblems: [
      { category: "材料不达标", count: 3 },
      { category: "功能缺陷", count: 2 },
      { category: "参数不达标", count: 2 },
      { category: "安装不规范", count: 1 },
      { category: "文档不完整", count: 1 }
    ]
  }

  // 获取结果对应的徽章样式
  const getResultBadge = (result: string) => {
    switch (result) {
      case "合格":
        return <Badge className="bg-green-500">合格</Badge>
      case "不合格":
        return <Badge variant="destructive">不合格</Badge>
      case "部分合格":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            部分合格
          </Badge>
        )
      default:
        return <Badge>{result}</Badge>
    }
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "整改中":
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            整改中
          </Badge>
        )
      case "待审核":
        return <Badge variant="secondary">待审核</Badge>
      case "已取消":
        return <Badge variant="outline" className="text-gray-500 border-gray-500">已取消</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取严重程度对应的徽章样式
  const getSeverityBadge = (severity: string | undefined) => {
    if (!severity) return null;
    
    switch (severity) {
      case "高":
        return <Badge variant="destructive">高</Badge>
      case "中":
        return <Badge variant="outline" className="text-orange-500 border-orange-500">中</Badge>
      case "低":
        return <Badge variant="outline" className="text-green-500 border-green-500">低</Badge>
      default:
        return <Badge>{severity}</Badge>
    }
  }

  // 消息提示函数
  const showMessage = (type: 'success' | 'error', content: string) => {
    toast({
      variant: type === 'success' ? 'default' : 'destructive',
      title: content,
    })
  }

  // 处理新增检查记录
  const handleAddRecord = () => {
    if (!formData.project || !formData.inspectionType || !formData.inspector || !formData.date || !formData.result) {
      showMessage('error', '请填写必填项');
      return;
    }

    const newRecord: QualityRecord = {
      id: Date.now().toString(),
      project: formData.project,
      department: formData.department || '',
      inspectionType: formData.inspectionType,
      inspector: formData.inspector,
      date: formData.date,
      location: formData.location || '',
      result: formData.result as "合格" | "不合格" | "部分合格",
      issues: formData.issues || 0,
      score: formData.score || 0,
      recommendations: formData.recommendations || '',
      status: formData.status as "已完成" | "整改中" | "待审核" | "已取消",
      deadline: formData.deadline,
      qualityStandard: formData.qualityStandard,
      problemCategory: formData.problemCategory,
      severity: formData.severity,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      remediation: []
    };

    setQualityRecords([newRecord, ...qualityRecords]);
    setIsAddDialogOpen(false);
    setFormData({});
    showMessage('success', '记录添加成功');
  }

  // 处理查看详情
  const handleViewDetails = (record: QualityRecord) => {
    setSelectedRecord(record);
    setIsViewModalOpen(true);
  }

  // 处理编辑记录
  const handleEditRecord = (record: QualityRecord) => {
    setSelectedRecord(record);
    setFormData(record);
    setIsEditModalOpen(true);
  }

  // 保存编辑
  const handleSaveEdit = () => {
    if (!selectedRecord || !formData.project || !formData.inspectionType) {
      showMessage('error', '请填写必填项');
      return;
    }

    const updatedRecords = qualityRecords.map(record => 
      record.id === selectedRecord.id 
        ? { 
            ...record, 
            ...formData, 
            updatedAt: new Date().toISOString().split('T')[0]
          } 
        : record
    );

    setQualityRecords(updatedRecords);
    setIsEditModalOpen(false);
    setSelectedRecord(null);
    setFormData({});
    showMessage('success', '记录已更新');
  }

  // 处理删除记录
  const handleDelete = (id: string) => {
    setSelectedRecordIds([id]);
    setIsBatchDelete(false);
    setIsDeleteDialogOpen(true);
  }

  // 确认删除
  const confirmDelete = () => {
    if (isBatchDelete) {
      setQualityRecords(qualityRecords.filter(record => !selectedRecordIds.includes(record.id)));
      setSelectedRecordIds([]);
      showMessage('success', '批量删除成功');
    } else {
      setQualityRecords(qualityRecords.filter(record => record.id !== selectedRecordIds[0]));
      showMessage('success', '记录已删除');
    }
    setIsDeleteDialogOpen(false);
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRecordIds.length === 0) {
      showMessage('error', '请选择要删除的记录');
      return;
    }
    setIsBatchDelete(true);
    setIsDeleteDialogOpen(true);
  }

  // 处理行选择
  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedRecordIds([...selectedRecordIds, id]);
    } else {
      setSelectedRecordIds(selectedRecordIds.filter(recordId => recordId !== id));
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRecordIds(qualityRecords.map(record => record.id));
    } else {
      setSelectedRecordIds([]);
    }
  }

  // 处理刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      showMessage('success', '数据已刷新');
    }, 800);
  }

  // 处理导出报告
  const handleExport = () => {
    setIsExportDialogOpen(true);
  }

  // 处理确认导出
  const handleConfirmExport = () => {
    setIsExportDialogOpen(false);
    showMessage('success', '报告导出成功');
  }

  // 处理进度整改
  const handleRemediation = (record: QualityRecord) => {
    setSelectedRecord(record);
    setRemediationFormData({
      qualityRecordId: record.id,
      date: new Date().toISOString().split('T')[0],
      progress: 0
    });
    setIsRemediationDialogOpen(true);
  }

  // 保存整改记录
  const handleSaveRemediation = () => {
    if (!selectedRecord || !remediationFormData.measures || !remediationFormData.handler) {
      showMessage('error', '请填写必填项');
      return;
    }

    const newRemediation: QualityRemediation = {
      id: 'r' + Date.now().toString(),
      qualityRecordId: selectedRecord.id,
      date: remediationFormData.date || new Date().toISOString().split('T')[0],
      measures: remediationFormData.measures,
      handler: remediationFormData.handler,
      result: remediationFormData.result || '进行中',
      progress: remediationFormData.progress || 0,
      remarks: remediationFormData.remarks
    };

    const updatedRecords = qualityRecords.map(record => {
      if (record.id === selectedRecord.id) {
        const remediation = [...(record.remediation || []), newRemediation];
        const progress = Math.max(...remediation.map(r => r.progress), 0);
        
        // 根据进度自动更新状态
        let status = record.status;
        if (progress >= 100) {
          status = '已完成';
        } else if (progress > 0) {
          status = '整改中';
        }
        
        return {
          ...record,
          remediation,
          status: status as "已完成" | "整改中" | "待审核" | "已取消",
          updatedAt: new Date().toISOString().split('T')[0]
        };
      }
      return record;
    });

    setQualityRecords(updatedRecords);
    setIsRemediationDialogOpen(false);
    setSelectedRecord(null);
    setRemediationFormData({});
    showMessage('success', '整改记录已添加');
  }

  // 过滤记录
  const filteredRecords = qualityRecords.filter(record => {
    const matchesSearch = searchText.trim() === '' || 
      record.project.toLowerCase().includes(searchText.toLowerCase()) ||
      record.inspector.toLowerCase().includes(searchText.toLowerCase()) ||
      record.location.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = filterType === 'all' || record.inspectionType === filterType;
    const matchesResult = filterResult === 'all' || record.result === filterResult;
    const matchesDepartment = filterDepartment === 'all' || record.department === filterDepartment;
    
    return matchesSearch && matchesType && matchesResult && matchesDepartment;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">工程质量管理</h2>
          <p className="text-muted-foreground">记录和跟踪工程质量检查结果，确保符合标准要求</p>
        </div>
        <div className="flex items-center gap-2">
          {/* 删除检查计划按钮 */}
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button size="sm" onClick={() => {setFormData({}); setIsAddDialogOpen(true);}}>
                <Plus className="h-4 w-4 mr-2" />
                新增检查
              </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <ClipboardCheck className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">总检查次数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <ThumbsUp className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.passed}</h3>
            <p className="text-sm text-muted-foreground">合格项目</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.partial}</h3>
            <p className="text-sm text-muted-foreground">部分合格</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <ThumbsDown className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.failed}</h3>
            <p className="text-sm text-muted-foreground">不合格项目</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.fixing}</h3>
            <p className="text-sm text-muted-foreground">整改中项目</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-purple-100 p-3 mb-4">
              <Share2 className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.avgScore}</h3>
            <p className="text-sm text-muted-foreground">平均评分</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>质量检查记录</CardTitle>
              <CardDescription>查看和管理所有工程质量检查记录</CardDescription>
            </div>
            <Tabs defaultValue="all" onValueChange={(value) => setFilterResult(value === 'all' ? 'all' : value)}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="合格">合格</TabsTrigger>
                <TabsTrigger value="部分合格">部分合格</TabsTrigger>
                <TabsTrigger value="不合格">不合格</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 搜索和筛选 */}
            <div className="flex flex-wrap justify-between items-center gap-4">
              <div className="flex flex-wrap items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    type="search" 
                    placeholder="搜索记录..." 
                    className="pl-8 w-[250px]" 
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
              </div>
                <Select 
                  value={filterType} 
                  onValueChange={setFilterType}
                >
                  <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="检查类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="设备质量检查">设备质量检查</SelectItem>
                    <SelectItem value="材料质量检查">材料质量检查</SelectItem>
                    <SelectItem value="系统功能测试">系统功能测试</SelectItem>
                    <SelectItem value="勘探数据质量">勘探数据质量</SelectItem>
                    <SelectItem value="环保设备检测">环保设备检测</SelectItem>
                    <SelectItem value="结构质量检查">结构质量检查</SelectItem>
                    <SelectItem value="电气安装质量">电气安装质量</SelectItem>
                    <SelectItem value="路面质量检查">路面质量检查</SelectItem>
                  </SelectContent>
                </Select>
                <Select 
                  value={filterDepartment} 
                  onValueChange={setFilterDepartment}
                >
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="部门筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    {Object.keys(statistics.departmentDistribution).map(dept => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
              
              {/* 批量操作 */}
              {selectedRecordIds.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">已选择 {selectedRecordIds.length} 项</span>
                  <Button variant="destructive" size="sm" onClick={handleBatchDelete}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                  </Button>
                </div>
              )}
          </div>

            {/* 数据表格 */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                    <TableHead className="w-[30px]">
                      <Checkbox 
                        checked={selectedRecordIds.length === filteredRecords.length && filteredRecords.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                  <TableHead>工程项目</TableHead>
                    <TableHead>部门</TableHead>
                  <TableHead>检查类型</TableHead>
                  <TableHead>检查人员</TableHead>
                  <TableHead>检查日期</TableHead>
                  <TableHead>检查结果</TableHead>
                    <TableHead>质量评分</TableHead>
                  <TableHead>问题数量</TableHead>
                    <TableHead>严重程度</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={12} className="text-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <RefreshCcw className="h-8 w-8 animate-spin text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">正在加载数据...</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredRecords.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={12} className="text-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <FileText className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">未找到匹配的记录</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                        <TableCell>
                          <Checkbox 
                            checked={selectedRecordIds.includes(record.id)}
                            onCheckedChange={(checked) => handleRowSelect(record.id, !!checked)}
                          />
                        </TableCell>
                    <TableCell className="font-medium">{record.project}</TableCell>
                        <TableCell>{record.department}</TableCell>
                    <TableCell>{record.inspectionType}</TableCell>
                    <TableCell>{record.inspector}</TableCell>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>{getResultBadge(record.result)}</TableCell>
                        <TableCell>
                          <span className={
                            record.score >= 90 ? "text-green-600" : 
                            record.score >= 75 ? "text-yellow-600" : 
                            "text-red-600"
                          }>
                            {record.score}
                          </span>
                        </TableCell>
                    <TableCell>{record.issues}</TableCell>
                        <TableCell>{record.severity ? getSeverityBadge(record.severity) : "-"}</TableCell>
                    <TableCell>{getStatusBadge(record.status)}</TableCell>
                    <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Button variant="ghost" size="icon" onClick={() => handleViewDetails(record)}>
                              <Eye className="h-4 w-4" />
                      </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleEditRecord(record)}>
                              <Edit className="h-4 w-4" />
                      </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleRemediation(record)}>
                                  <FileCheck className="h-4 w-4 mr-2" />
                                  整改记录
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDelete(record.id)}>
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                    </TableCell>
                  </TableRow>
                    ))
                  )}
              </TableBody>
            </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredRecords.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled={currentPage === 1}>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              {currentPage}
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              disabled={filteredRecords.length <= pageSize}
            >
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="h-[500px] flex flex-col">
          <CardHeader>
            <CardTitle>质量问题分布</CardTitle>
            <CardDescription>按类别统计的质量问题分布情况</CardDescription>
          </CardHeader>
          <CardContent className="flex-1 overflow-y-auto pr-2">
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">检查类型分布</h4>
                <div className="space-y-2">
                  {Object.entries(statistics.typeDistribution).map(([type, count]) => (
                    <div key={type} className="flex flex-col gap-1.5">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{type}</span>
                        <span className="text-sm text-muted-foreground">{count} 次检查</span>
                      </div>
                      <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-primary rounded-full" 
                          style={{ width: `${(count / statistics.total) * 100}%` }} 
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-4">检查结果分布</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex flex-col items-center gap-2 bg-green-50 p-4 rounded-lg">
                    <ThumbsUp className="h-8 w-8 text-green-500" />
                    <span className="text-lg font-bold">{statistics.passed}</span>
                    <span className="text-xs text-muted-foreground">合格检查</span>
                    <div className="w-full h-1.5 bg-green-100 rounded-full">
                      <div 
                        className="h-full bg-green-500 rounded-full" 
                        style={{ width: `${(statistics.passed / statistics.total) * 100}%` }} 
                      />
                    </div>
                  </div>
                  <div className="flex flex-col items-center gap-2 bg-yellow-50 p-4 rounded-lg">
                    <AlertTriangle className="h-8 w-8 text-yellow-500" />
                    <span className="text-lg font-bold">{statistics.partial}</span>
                    <span className="text-xs text-muted-foreground">部分合格</span>
                    <div className="w-full h-1.5 bg-yellow-100 rounded-full">
                      <div 
                        className="h-full bg-yellow-500 rounded-full" 
                        style={{ width: `${(statistics.partial / statistics.total) * 100}%` }} 
                      />
                    </div>
                  </div>
                  <div className="flex flex-col items-center gap-2 bg-red-50 p-4 rounded-lg">
                    <ThumbsDown className="h-8 w-8 text-red-500" />
                    <span className="text-lg font-bold">{statistics.failed}</span>
                    <span className="text-xs text-muted-foreground">不合格</span>
                    <div className="w-full h-1.5 bg-red-100 rounded-full">
                      <div 
                        className="h-full bg-red-500 rounded-full" 
                        style={{ width: `${(statistics.failed / statistics.total) * 100}%` }} 
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-4">常见问题分类</h4>
                <div className="space-y-2">
                  {statistics.topProblems.map((problem) => (
                    <div key={problem.category} className="flex flex-col gap-1.5">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{problem.category}</span>
                        <span className="text-sm text-muted-foreground">{problem.count} 次</span>
                      </div>
                      <div className="w-full h-2 bg-secondary rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 rounded-full" 
                          style={{ width: `${(problem.count / statistics.topProblems.reduce((sum, p) => sum + p.count, 0)) * 100}%` }} 
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="h-[500px] flex flex-col">
          <CardHeader>
            <CardTitle>质量趋势分析</CardTitle>
            <CardDescription>近期质量分数和问题数量的变化趋势</CardDescription>
          </CardHeader>
          <CardContent className="flex-1 overflow-y-auto pr-2">
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">平均质量评分趋势</h4>
                <div className="h-[150px] overflow-x-auto">
                  <div className="min-w-[600px] h-full">
                    <div className="flex items-end h-[120px] gap-2">
                      {statistics.trendByMonth.map((item) => (
                        <div key={item.month} className="flex-1 flex flex-col items-center">
                          <div 
                            className="w-full bg-blue-500 rounded-t-sm" 
                            style={{ height: `${(item.score / 100) * 120}px` }}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="grid grid-cols-6 mt-2">
                      {statistics.trendByMonth.map((item) => (
                        <div key={item.month} className="text-center">
                          <span className="text-xs font-medium">{item.score}</span>
                          <p className="text-xs text-muted-foreground mt-1">{item.month.split('-')[1]}月</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-4">问题数量趋势</h4>
                <div className="h-[150px] overflow-x-auto">
                  <div className="min-w-[600px] h-full">
                    <div className="flex items-end h-[120px] gap-2">
                      {statistics.trendByMonth.map((item) => (
                        <div key={item.month} className="flex-1 flex flex-col items-center">
                          <div 
                            className="w-full bg-orange-500 rounded-t-sm" 
                            style={{ height: `${(item.issues / Math.max(...statistics.trendByMonth.map(i => i.issues))) * 120}px` }}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="grid grid-cols-6 mt-2">
                      {statistics.trendByMonth.map((item) => (
                        <div key={item.month} className="text-center">
                          <span className="text-xs font-medium">{item.issues}</span>
                          <p className="text-xs text-muted-foreground mt-1">{item.month.split('-')[1]}月</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-4">部门质量得分排名</h4>
                <div className="space-y-3 overflow-y-auto pr-2">
                  {Object.entries(statistics.departmentDistribution)
                    .map(([dept]) => ({
                      name: dept,
                      score: Math.round(Math.random() * 30 + 70) // 模拟数据
                    }))
                    .sort((a, b) => b.score - a.score)
                    .map((dept, idx) => (
                      <div key={dept.name} className="flex items-center gap-3">
                        <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium text-primary">
                          {idx + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">{dept.name}</span>
                            <span className="text-sm font-medium">{dept.score}</span>
                          </div>
                          <div className="w-full h-2 bg-secondary rounded-full mt-1">
                            <div 
                              className="h-full bg-primary rounded-full" 
                              style={{ width: `${dept.score}%` }} 
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 新增检查记录对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>新增质量检查记录</DialogTitle>
            <DialogDescription>添加新的工程质量检查记录和结果</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-project">工程项目 <span className="text-red-500">*</span></Label>
                <Input 
                  id="add-project" 
                  placeholder="输入工程项目名称" 
                  value={formData.project || ''}
                  onChange={(e) => setFormData({...formData, project: e.target.value})}
                />
    </div>
              <div className="space-y-2">
                <Label htmlFor="add-department">所属部门 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.department}
                  onValueChange={(value) => setFormData({...formData, department: value})}
                >
                  <SelectTrigger id="add-department">
                    <SelectValue placeholder="选择所属部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程部">采矿工程部</SelectItem>
                    <SelectItem value="设备管理部">设备管理部</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="勘探部">勘探部</SelectItem>
                    <SelectItem value="环保部">环保部</SelectItem>
                    <SelectItem value="土建工程部">土建工程部</SelectItem>
                    <SelectItem value="电力工程部">电力工程部</SelectItem>
                    <SelectItem value="基础设施部">基础设施部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-inspection-type">检查类型 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.inspectionType}
                  onValueChange={(value) => setFormData({...formData, inspectionType: value})}
                >
                  <SelectTrigger id="add-inspection-type">
                    <SelectValue placeholder="选择检查类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="设备质量检查">设备质量检查</SelectItem>
                    <SelectItem value="材料质量检查">材料质量检查</SelectItem>
                    <SelectItem value="系统功能测试">系统功能测试</SelectItem>
                    <SelectItem value="勘探数据质量">勘探数据质量</SelectItem>
                    <SelectItem value="环保设备检测">环保设备检测</SelectItem>
                    <SelectItem value="结构质量检查">结构质量检查</SelectItem>
                    <SelectItem value="电气安装质量">电气安装质量</SelectItem>
                    <SelectItem value="路面质量检查">路面质量检查</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-location">检查位置</Label>
                <Input 
                  id="add-location" 
                  placeholder="输入检查位置" 
                  value={formData.location || ''}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-inspector">检查人员 <span className="text-red-500">*</span></Label>
                <Input 
                  id="add-inspector" 
                  placeholder="输入检查人员姓名" 
                  value={formData.inspector || ''}
                  onChange={(e) => setFormData({...formData, inspector: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-date">检查日期 <span className="text-red-500">*</span></Label>
                <Input 
                  id="add-date" 
                  type="date" 
                  value={formData.date || ''}
                  onChange={(e) => setFormData({...formData, date: e.target.value})}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-result">检查结果 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.result}
                  onValueChange={(value) => setFormData({...formData, result: value as "合格" | "不合格" | "部分合格"})}
                >
                  <SelectTrigger id="add-result">
                    <SelectValue placeholder="选择检查结果" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="合格">合格</SelectItem>
                    <SelectItem value="不合格">不合格</SelectItem>
                    <SelectItem value="部分合格">部分合格</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-severity">问题严重程度</Label>
                <Select 
                  value={formData.severity}
                  onValueChange={(value) => setFormData({...formData, severity: value as "高" | "中" | "低"})}
                >
                  <SelectTrigger id="add-severity">
                    <SelectValue placeholder="选择严重程度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-issues">问题数量</Label>
                <Input 
                  id="add-issues" 
                  type="number" 
                  min="0" 
                  placeholder="输入发现的问题数量" 
                  value={formData.issues || ''}
                  onChange={(e) => setFormData({...formData, issues: parseInt(e.target.value)})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-score">质量评分 (0-100)</Label>
                <Input 
                  id="add-score" 
                  type="number" 
                  min="0" 
                  max="100" 
                  placeholder="输入质量评分" 
                  value={formData.score || ''}
                  onChange={(e) => setFormData({...formData, score: parseInt(e.target.value)})}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-recommendations">建议措施</Label>
              <Textarea 
                id="add-recommendations" 
                placeholder="输入建议措施" 
                rows={3} 
                value={formData.recommendations || ''}
                onChange={(e) => setFormData({...formData, recommendations: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-status">状态 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value as "已完成" | "整改中" | "待审核" | "已取消"})}
                >
                  <SelectTrigger id="add-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="整改中">整改中</SelectItem>
                    <SelectItem value="待审核">待审核</SelectItem>
                    <SelectItem value="已取消">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="add-standard">采用质量标准</Label>
                <Input 
                  id="add-standard" 
                  placeholder="输入质量标准编号" 
                  value={formData.qualityStandard || ''}
                  onChange={(e) => setFormData({...formData, qualityStandard: e.target.value})}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddRecord}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑质量检查记录对话框 */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑质量检查记录</DialogTitle>
            <DialogDescription>修改已有的质量检查记录</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-project">工程项目 <span className="text-red-500">*</span></Label>
                <Input 
                  id="edit-project" 
                  placeholder="输入工程项目名称" 
                  value={formData.project || ''}
                  onChange={(e) => setFormData({...formData, project: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-department">所属部门 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.department}
                  onValueChange={(value) => setFormData({...formData, department: value})}
                >
                  <SelectTrigger id="edit-department">
                    <SelectValue placeholder="选择所属部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程部">采矿工程部</SelectItem>
                    <SelectItem value="设备管理部">设备管理部</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="勘探部">勘探部</SelectItem>
                    <SelectItem value="环保部">环保部</SelectItem>
                    <SelectItem value="土建工程部">土建工程部</SelectItem>
                    <SelectItem value="电力工程部">电力工程部</SelectItem>
                    <SelectItem value="基础设施部">基础设施部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* 其余编辑字段与新增类似，但id有所不同 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-inspection-type">检查类型 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.inspectionType}
                  onValueChange={(value) => setFormData({...formData, inspectionType: value})}
                >
                  <SelectTrigger id="edit-inspection-type">
                    <SelectValue placeholder="选择检查类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="设备质量检查">设备质量检查</SelectItem>
                    <SelectItem value="材料质量检查">材料质量检查</SelectItem>
                    <SelectItem value="系统功能测试">系统功能测试</SelectItem>
                    <SelectItem value="勘探数据质量">勘探数据质量</SelectItem>
                    <SelectItem value="环保设备检测">环保设备检测</SelectItem>
                    <SelectItem value="结构质量检查">结构质量检查</SelectItem>
                    <SelectItem value="电气安装质量">电气安装质量</SelectItem>
                    <SelectItem value="路面质量检查">路面质量检查</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-result">检查结果 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.result}
                  onValueChange={(value) => setFormData({...formData, result: value as "合格" | "不合格" | "部分合格"})}
                >
                  <SelectTrigger id="edit-result">
                    <SelectValue placeholder="选择检查结果" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="合格">合格</SelectItem>
                    <SelectItem value="不合格">不合格</SelectItem>
                    <SelectItem value="部分合格">部分合格</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态 <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value as "已完成" | "整改中" | "待审核" | "已取消"})}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="整改中">整改中</SelectItem>
                    <SelectItem value="待审核">待审核</SelectItem>
                    <SelectItem value="已取消">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-severity">问题严重程度</Label>
                <Select 
                  value={formData.severity}
                  onValueChange={(value) => setFormData({...formData, severity: value as "高" | "中" | "低"})}
                >
                  <SelectTrigger id="edit-severity">
                    <SelectValue placeholder="选择严重程度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-recommendations">建议措施</Label>
              <Textarea 
                id="edit-recommendations" 
                placeholder="输入建议措施" 
                rows={3} 
                value={formData.recommendations || ''}
                onChange={(e) => setFormData({...formData, recommendations: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{isBatchDelete ? '批量删除' : '删除记录'}</AlertDialogTitle>
            <AlertDialogDescription>
              {isBatchDelete 
                ? `确定要删除选中的 ${selectedRecordIds.length} 条记录吗？此操作不可撤销。`
                : '确定要删除该记录吗？此操作不可撤销。'
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>确定删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 质量检查详情抽屉 */}
      <Sheet open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <SheetContent className="w-[600px] sm:w-[540px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>质量检查详情</SheetTitle>
            <SheetDescription>
              {selectedRecord ? `${selectedRecord.project} - ${selectedRecord.inspectionType}` : ""}
            </SheetDescription>
          </SheetHeader>
          {selectedRecord && (
            <div className="mt-6 space-y-6">
              {/* 基本信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">基本信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>工程项目</Label>
                    <p className="mt-1 font-medium">{selectedRecord.project}</p>
                  </div>
                  <div>
                    <Label>所属部门</Label>
                    <p className="mt-1">{selectedRecord.department}</p>
                  </div>
                  <div>
                    <Label>检查类型</Label>
                    <p className="mt-1">{selectedRecord.inspectionType}</p>
                  </div>
                  <div>
                    <Label>检查位置</Label>
                    <p className="mt-1">{selectedRecord.location}</p>
                  </div>
                  <div>
                    <Label>检查人员</Label>
                    <p className="mt-1">{selectedRecord.inspector}</p>
                  </div>
                  <div>
                    <Label>检查日期</Label>
                    <p className="mt-1">{selectedRecord.date}</p>
                  </div>
                </div>
              </div>

              {/* 检查结果 */}
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">检查结果</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>检查结果</Label>
                    <p className="mt-1">{getResultBadge(selectedRecord.result)}</p>
                  </div>
                  <div>
                    <Label>质量评分</Label>
                    <p className="mt-1">
                      <span className={
                        selectedRecord.score >= 90 ? "text-green-600 font-medium" : 
                        selectedRecord.score >= 75 ? "text-yellow-600 font-medium" : 
                        "text-red-600 font-medium"
                      }>
                        {selectedRecord.score}
                      </span>
                    </p>
                  </div>
                  <div>
                    <Label>问题数量</Label>
                    <p className="mt-1">{selectedRecord.issues}</p>
                  </div>
                  <div>
                    <Label>严重程度</Label>
                    <p className="mt-1">{getSeverityBadge(selectedRecord.severity)}</p>
                  </div>
                  <div>
                    <Label>当前状态</Label>
                    <p className="mt-1">{getStatusBadge(selectedRecord.status)}</p>
                  </div>
                  <div>
                    <Label>质量标准</Label>
                    <p className="mt-1">{selectedRecord.qualityStandard || "-"}</p>
                  </div>
                </div>
              </div>

              {/* 问题及措施 */}
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">问题及建议措施</h3>
                <div className="space-y-4">
                  {selectedRecord.problemCategory && selectedRecord.problemCategory !== "无" && (
                    <div>
                      <Label>问题分类</Label>
                      <p className="mt-1">{selectedRecord.problemCategory}</p>
                    </div>
                  )}
                  <div>
                    <Label>建议措施</Label>
                    <p className="mt-1">{selectedRecord.recommendations || "无"}</p>
                  </div>
                  {selectedRecord.deadline && (
                    <div>
                      <Label>整改截止日期</Label>
                      <p className="mt-1">{selectedRecord.deadline}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* 整改记录 */}
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium mb-4">整改记录</h3>
                <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                  {selectedRecord.remediation && selectedRecord.remediation.length > 0 ? (
                    selectedRecord.remediation.map((item) => (
                      <Card key={item.id}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <p className="font-medium">{item.date}</p>
                              <p className="text-sm text-muted-foreground">处理人: {item.handler}</p>
                            </div>
                            <Badge variant={
                              item.progress >= 100 ? "default" :
                              item.progress >= 50 ? "outline" : "secondary"
                            }>
                              {item.progress}% 完成
                            </Badge>
                          </div>
                          <div className="mb-2">
                            <Label className="text-xs">整改措施</Label>
                            <p className="text-sm">{item.measures}</p>
                          </div>
                          {item.remarks && (
                            <div>
                              <Label className="text-xs">备注</Label>
                              <p className="text-sm">{item.remarks}</p>
                            </div>
                          )}
                          <Progress className="mt-2 h-2" value={item.progress} />
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-8 w-8 mx-auto mb-2" />
                      <p>暂无整改记录</p>
                    </div>
                  )}
                </div>
                {selectedRecord.result !== "合格" && (
                  <Button 
                    className="w-full mt-4" 
                    variant="outline"
                    onClick={() => {
                      setIsViewModalOpen(false);
                      setTimeout(() => handleRemediation(selectedRecord), 100);
                    }}
                  >
                    <FileCheck className="h-4 w-4 mr-2" />
                    添加整改记录
                  </Button>
                )}
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 整改记录对话框 */}
      <Dialog open={isRemediationDialogOpen} onOpenChange={setIsRemediationDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>添加整改记录</DialogTitle>
            <DialogDescription>
              {selectedRecord ? `为 ${selectedRecord.project} 添加整改记录` : ""}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="remediation-date">整改日期 <span className="text-red-500">*</span></Label>
              <Input 
                id="remediation-date"
                type="date"
                value={remediationFormData.date || ''}
                onChange={(e) => setRemediationFormData({...remediationFormData, date: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="remediation-measures">整改措施 <span className="text-red-500">*</span></Label>
              <Textarea 
                id="remediation-measures"
                placeholder="请输入整改措施详情"
                rows={3}
                value={remediationFormData.measures || ''}
                onChange={(e) => setRemediationFormData({...remediationFormData, measures: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="remediation-handler">处理人 <span className="text-red-500">*</span></Label>
              <Input 
                id="remediation-handler"
                placeholder="请输入处理人姓名"
                value={remediationFormData.handler || ''}
                onChange={(e) => setRemediationFormData({...remediationFormData, handler: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="remediation-progress">整改进度</Label>
                <span className="text-sm text-muted-foreground">{remediationFormData.progress || 0}%</span>
              </div>
              <Input 
                id="remediation-progress"
                type="range"
                min={0}
                max={100}
                step={5}
                value={remediationFormData.progress || 0}
                onChange={(e) => setRemediationFormData({...remediationFormData, progress: parseInt(e.target.value)})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="remediation-remarks">备注</Label>
              <Textarea 
                id="remediation-remarks"
                placeholder="请输入备注信息"
                rows={2}
                value={remediationFormData.remarks || ''}
                onChange={(e) => setRemediationFormData({...remediationFormData, remarks: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRemediationDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveRemediation}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计分析抽屉 */}
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-full d:max-w-[1080px] sm:max-w-md overflow-y-auto p-3">
          <SheetHeader>
            <SheetTitle className="text-2xl">质量统计分析</SheetTitle>
            <SheetDescription>查看工程质量检查的统计分析</SheetDescription>
          </SheetHeader>
          <div className="mt-6 space-y-8">
            {/* 总体概况 */}
            <div>
              <h3 className="text-lg font-medium mb-4">总体概况</h3>
              <div className="grid grid-cols-3 gap-4">
                <Card className="bg-green-50 hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="rounded-full bg-green-100 p-3">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">合格率</p>
                        <p className="text-xl font-bold">
                          {((statistics.passed / statistics.total) * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-blue-50 hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="rounded-full bg-blue-100 p-3">
                        <BarChart className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">平均评分</p>
                        <p className="text-xl font-bold">{statistics.avgScore}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-red-50 hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="rounded-full bg-red-100 p-3">
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">问题总数</p>
                        <p className="text-xl font-bold">
                          {qualityRecords.reduce((sum, record) => sum + record.issues, 0)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            {/* 部门质量表现 */}
            <div>
              <h3 className="text-lg font-medium mb-4">部门质量表现</h3>
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6 max-h-[350px] overflow-y-auto">
                  {Object.entries(statistics.departmentDistribution)
                    .map(([dept, count]) => ({
                      name: dept,
                      count,
                      passRate: Math.round(
                        (qualityRecords.filter(r => r.department === dept && r.result === "合格").length / 
                        count) * 100
                      )
                    }))
                    .sort((a, b) => b.passRate - a.passRate)
                    .map((dept) => (
                      <div key={dept.name} className="flex flex-col mb-5">
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-sm font-medium">{dept.name}</p>
                          <div className="flex items-center space-x-3">
                            <span className="text-xs text-muted-foreground">{dept.count}次检查</span>
                            <span className="text-xs font-medium px-1.5 py-0.5 bg-muted rounded-full">{dept.passRate}%</span>
                          </div>
                        </div>
                        <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                          <div 
                            className={`h-full rounded-full ${
                              dept.passRate >= 90 ? "bg-green-500" :
                              dept.passRate >= 70 ? "bg-yellow-500" :
                              "bg-red-500"
                            }`}
                            style={{ width: `${dept.passRate}%` }} 
                          />
                        </div>
                      </div>
                    ))
                  }
                </CardContent>
              </Card>
            </div>

            {/* 质量趋势 */}
            <div>
              <h3 className="text-lg font-medium mb-4">质量趋势变化</h3>
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="h-[300px] overflow-x-auto">
                    <div className="min-w-[600px]">
                      <div className="grid grid-cols-2 gap-10 h-full">
                        <div>
                          <p className="text-sm font-medium mb-4 text-center">质量评分趋势</p>
                          <div className="h-[220px] flex items-end">
                            {statistics.trendByMonth.map((item, index) => (
                              <div 
                                key={item.month} 
                                className="flex-1 mx-1.5 flex flex-col items-center"
                              >
                                <div className="text-xs font-medium mb-1">{item.score}</div>
                                <div 
                                  className={`w-full ${
                                    item.score >= 90 ? "bg-green-500" :
                                    item.score >= 75 ? "bg-yellow-500" :
                                    "bg-red-500"
                                  } rounded-t-md`}
                                  style={{ height: `${(item.score / 100) * 170}px` }}
                                />
                                <p className="text-xs mt-2">{item.month.split('-')[1]}月</p>
                              </div>
                            ))}
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium mb-4 text-center">问题数量趋势</p>
                          <div className="h-[220px] flex items-end">
                            {statistics.trendByMonth.map((item, index) => (
                              <div 
                                key={item.month} 
                                className="flex-1 mx-1.5 flex flex-col items-center"
                              >
                                <div className="text-xs font-medium mb-1">{item.issues}</div>
                                <div 
                                  className="w-full bg-blue-500 rounded-t-md"
                                  style={{ 
                                    height: `${(item.issues / Math.max(...statistics.trendByMonth.map(i => i.issues))) * 170}px` 
                                  }}
                                />
                                <p className="text-xs mt-2">{item.month.split('-')[1]}月</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 问题类别分布 */}
            <div>
              <h3 className="text-lg font-medium mb-4">问题类别分布</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle>常见问题统计</CardTitle>
                  </CardHeader>
                  <CardContent className="px-6 pb-6 pt-0">
                    <div className="space-y-4">
                      {statistics.topProblems.map((problem) => (
                        <div key={problem.category} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 rounded-full bg-primary"></div>
                              <span className="text-sm font-medium">{problem.category}</span>
                            </div>
                            <span className="text-sm text-muted-foreground">{problem.count} 次</span>
                          </div>
                          <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary rounded-full" 
                              style={{ width: `${(problem.count / statistics.topProblems.reduce((sum, p) => sum + p.count, 0)) * 100}%` }} 
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
          
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle>检查类型分布</CardTitle>
                  </CardHeader>
                  <CardContent className="px-6 pb-6 pt-0">
                    <div className="space-y-4">
                      {Object.entries(statistics.typeDistribution)
                        .sort((a, b) => b[1] - a[1])
                        .map(([type, count]) => {
                          const percentage = Math.round((count / statistics.total) * 100);
                          const colors = ["bg-blue-500", "bg-green-500", "bg-purple-500", "bg-amber-500", "bg-red-500"];
                          const color = colors[Math.floor(Math.random() * colors.length)];
                          
                          return (
                            <div key={type} className="space-y-2">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <div className={`w-3 h-3 rounded-full ${color}`}></div>
                                  <span className="text-sm font-medium">{type}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm text-muted-foreground">{count}</span>
                                  <span className="text-xs bg-muted px-1.5 rounded-full">{percentage}%</span>
                                </div>
                              </div>
                              <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                                <div 
                                  className={`h-full rounded-full ${color}`} 
                                  style={{ width: `${percentage}%` }}
                                />
                              </div>
                            </div>
                          );
                        })
                      }
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* 导出报告对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogTitle>导出质量检查报告</DialogTitle>
            <DialogDescription>选择导出格式和范围</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <div className="flex gap-4">
                <div className="flex items-center gap-2">
                  <input 
                    type="radio" 
                    id="format-xlsx" 
                    name="format" 
                    value="xlsx" 
                    defaultChecked 
                  />
                  <Label htmlFor="format-xlsx" className="cursor-pointer">Excel (.xlsx)</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input 
                    type="radio" 
                    id="format-pdf" 
                    name="format" 
                    value="pdf" 
                  />
                  <Label htmlFor="format-pdf" className="cursor-pointer">PDF (.pdf)</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input 
                    type="radio" 
                    id="format-csv" 
                    name="format" 
                    value="csv" 
                  />
                  <Label htmlFor="format-csv" className="cursor-pointer">CSV (.csv)</Label>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label>导出范围</Label>
              <div className="flex gap-4">
                <div className="flex items-center gap-2">
                  <input 
                    type="radio" 
                    id="range-all" 
                    name="range" 
                    value="all" 
                    defaultChecked 
                  />
                  <Label htmlFor="range-all" className="cursor-pointer">全部记录</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input 
                    type="radio" 
                    id="range-filtered" 
                    name="range" 
                    value="filtered" 
                  />
                  <Label htmlFor="range-filtered" className="cursor-pointer">当前筛选结果</Label>
                </div>
                {selectedRecordIds.length > 0 && (
                  <div className="flex items-center gap-2">
                    <input 
                      type="radio" 
                      id="range-selected" 
                      name="range" 
                      value="selected" 
                    />
                    <Label htmlFor="range-selected" className="cursor-pointer">已选择 ({selectedRecordIds.length})</Label>
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label>其他设置</Label>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Checkbox id="include-headers" defaultChecked />
                  <Label htmlFor="include-headers" className="cursor-pointer">包含表头</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Checkbox id="include-stats" defaultChecked />
                  <Label htmlFor="include-stats" className="cursor-pointer">包含统计数据</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleConfirmExport}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

