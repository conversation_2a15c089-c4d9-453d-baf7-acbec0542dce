"use strict";
exports.__esModule = true;
var react_1 = require("react");
var ogl_1 = require("ogl");
function debounce(func, wait) {
    var timeout;
    return function () {
        var _this = this;
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        clearTimeout(timeout);
        timeout = setTimeout(function () { return func.apply(_this, args); }, wait);
    };
}
function lerp(p1, p2, t) {
    return p1 + (p2 - p1) * t;
}
function autoBind(instance) {
    var proto = Object.getPrototypeOf(instance);
    Object.getOwnPropertyNames(proto).forEach(function (key) {
        if (key !== 'constructor' && typeof instance[key] === 'function') {
            instance[key] = instance[key].bind(instance);
        }
    });
}
function createTextTexture(gl, text, font, color) {
    if (font === void 0) { font = "bold 30px monospace"; }
    if (color === void 0) { color = "black"; }
    var canvas = document.createElement("canvas");
    var context = canvas.getContext("2d");
    context.font = font;
    var metrics = context.measureText(text);
    var textWidth = Math.ceil(metrics.width);
    var textHeight = Math.ceil(parseInt(font, 10) * 1.2);
    canvas.width = textWidth + 20;
    canvas.height = textHeight + 20;
    context.font = font;
    context.fillStyle = color;
    context.textBaseline = "middle";
    context.textAlign = "center";
    context.clearRect(0, 0, canvas.width, canvas.height);
    context.fillText(text, canvas.width / 2, canvas.height / 2);
    var texture = new ogl_1.Texture(gl, { generateMipmaps: false });
    texture.image = canvas;
    return { texture: texture, width: canvas.width, height: canvas.height };
}
var Title = /** @class */ (function () {
    function Title(_a) {
        var gl = _a.gl, plane = _a.plane, renderer = _a.renderer, text = _a.text, _b = _a.textColor, textColor = _b === void 0 ? "#545050" : _b, _c = _a.font, font = _c === void 0 ? "30px sans-serif" : _c;
        autoBind(this);
        this.gl = gl;
        this.plane = plane;
        this.renderer = renderer;
        this.text = text;
        this.textColor = textColor;
        this.font = font;
        this.createMesh();
    }
    Title.prototype.createMesh = function () {
        var _a = createTextTexture(this.gl, this.text, this.font, this.textColor), texture = _a.texture, width = _a.width, height = _a.height;
        var geometry = new ogl_1.Plane(this.gl);
        var program = new ogl_1.Program(this.gl, {
            vertex: "\n        attribute vec3 position;\n        attribute vec2 uv;\n        uniform mat4 modelViewMatrix;\n        uniform mat4 projectionMatrix;\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n        }\n      ",
            fragment: "\n        precision highp float;\n        uniform sampler2D tMap;\n        varying vec2 vUv;\n        void main() {\n          vec4 color = texture2D(tMap, vUv);\n          if (color.a < 0.1) discard;\n          gl_FragColor = color;\n        }\n      ",
            uniforms: { tMap: { value: texture } },
            transparent: true
        });
        this.mesh = new ogl_1.Mesh(this.gl, { geometry: geometry, program: program });
        var aspect = width / height;
        var textHeight = this.plane.scale.y * 0.15;
        var textWidth = textHeight * aspect;
        this.mesh.scale.set(textWidth, textHeight, 1);
        this.mesh.position.y = -this.plane.scale.y * 0.5 - textHeight * 0.5 - 0.05;
        this.mesh.setParent(this.plane);
    };
    return Title;
}());
var Media = /** @class */ (function () {
    function Media(_a) {
        var geometry = _a.geometry, gl = _a.gl, image = _a.image, index = _a.index, length = _a.length, renderer = _a.renderer, scene = _a.scene, screen = _a.screen, text = _a.text, viewport = _a.viewport, bend = _a.bend, textColor = _a.textColor, _b = _a.borderRadius, borderRadius = _b === void 0 ? 0 : _b, font = _a.font;
        this.extra = 0;
        this.geometry = geometry;
        this.gl = gl;
        this.image = image;
        this.index = index;
        this.length = length;
        this.renderer = renderer;
        this.scene = scene;
        this.screen = screen;
        this.text = text;
        this.viewport = viewport;
        this.bend = bend;
        this.textColor = textColor;
        this.borderRadius = borderRadius;
        this.font = font;
        this.createShader();
        this.createMesh();
        this.createTitle();
        this.onResize();
    }
    Media.prototype.createShader = function () {
        var _this = this;
        var texture = new ogl_1.Texture(this.gl, { generateMipmaps: false });
        this.program = new ogl_1.Program(this.gl, {
            depthTest: false,
            depthWrite: false,
            vertex: "\n        precision highp float;\n        attribute vec3 position;\n        attribute vec2 uv;\n        uniform mat4 modelViewMatrix;\n        uniform mat4 projectionMatrix;\n        uniform float uTime;\n        uniform float uSpeed;\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          vec3 p = position;\n          p.z = (sin(p.x * 4.0 + uTime) * 1.5 + cos(p.y * 2.0 + uTime) * 1.5) * (0.1 + uSpeed * 0.5);\n          gl_Position = projectionMatrix * modelViewMatrix * vec4(p, 1.0);\n        }\n      ",
            fragment: "\n        precision highp float;\n        uniform vec2 uImageSizes;\n        uniform vec2 uPlaneSizes;\n        uniform sampler2D tMap;\n        uniform float uBorderRadius;\n        varying vec2 vUv;\n        \n        float roundedBoxSDF(vec2 p, vec2 b, float r) {\n          vec2 d = abs(p) - b;\n          return length(max(d, vec2(0.0))) + min(max(d.x, d.y), 0.0) - r;\n        }\n        \n        void main() {\n          vec2 ratio = vec2(\n            min((uPlaneSizes.x / uPlaneSizes.y) / (uImageSizes.x / uImageSizes.y), 1.0),\n            min((uPlaneSizes.y / uPlaneSizes.x) / (uImageSizes.y / uImageSizes.x), 1.0)\n          );\n          vec2 uv = vec2(\n            vUv.x * ratio.x + (1.0 - ratio.x) * 0.5,\n            vUv.y * ratio.y + (1.0 - ratio.y) * 0.5\n          );\n          vec4 color = texture2D(tMap, uv);\n          \n          float d = roundedBoxSDF(vUv - 0.5, vec2(0.5 - uBorderRadius), uBorderRadius);\n          if(d > 0.0) {\n            discard;\n          }\n          \n          gl_FragColor = vec4(color.rgb, 1.0);\n        }\n      ",
            uniforms: {
                tMap: { value: texture },
                uPlaneSizes: { value: [0, 0] },
                uImageSizes: { value: [0, 0] },
                uSpeed: { value: 0 },
                uTime: { value: 100 * Math.random() },
                uBorderRadius: { value: this.borderRadius }
            },
            transparent: true
        });
        var img = new Image();
        img.crossOrigin = "anonymous";
        img.src = this.image;
        img.onload = function () {
            texture.image = img;
            _this.program.uniforms.uImageSizes.value = [img.naturalWidth, img.naturalHeight];
        };
    };
    Media.prototype.createMesh = function () {
        this.plane = new ogl_1.Mesh(this.gl, {
            geometry: this.geometry,
            program: this.program
        });
        this.plane.setParent(this.scene);
    };
    Media.prototype.createTitle = function () {
        this.title = new Title({
            gl: this.gl,
            plane: this.plane,
            renderer: this.renderer,
            text: this.text,
            textColor: this.textColor,
            fontFamily: this.font
        });
    };
    Media.prototype.update = function (scroll, direction) {
        this.plane.position.x = this.x - scroll.current - this.extra;
        var x = this.plane.position.x;
        var H = this.viewport.width / 2;
        if (this.bend === 0) {
            this.plane.position.y = 0;
            this.plane.rotation.z = 0;
        }
        else {
            var B_abs = Math.abs(this.bend);
            var R = (H * H + B_abs * B_abs) / (2 * B_abs);
            var effectiveX = Math.min(Math.abs(x), H);
            var arc = R - Math.sqrt(R * R - effectiveX * effectiveX);
            if (this.bend > 0) {
                this.plane.position.y = -arc;
                this.plane.rotation.z = -Math.sign(x) * Math.asin(effectiveX / R);
            }
            else {
                this.plane.position.y = arc;
                this.plane.rotation.z = Math.sign(x) * Math.asin(effectiveX / R);
            }
        }
        this.speed = scroll.current - scroll.last;
        this.program.uniforms.uTime.value += 0.04;
        this.program.uniforms.uSpeed.value = this.speed;
        var planeOffset = this.plane.scale.x / 2;
        var viewportOffset = this.viewport.width / 2;
        this.isBefore = this.plane.position.x + planeOffset < -viewportOffset;
        this.isAfter = this.plane.position.x - planeOffset > viewportOffset;
        if (direction === 'right' && this.isBefore) {
            this.extra -= this.widthTotal;
            this.isBefore = this.isAfter = false;
        }
        if (direction === 'left' && this.isAfter) {
            this.extra += this.widthTotal;
            this.isBefore = this.isAfter = false;
        }
    };
    Media.prototype.onResize = function (_a) {
        var _b = _a === void 0 ? {} : _a, screen = _b.screen, viewport = _b.viewport;
        if (screen)
            this.screen = screen;
        if (viewport) {
            this.viewport = viewport;
            if (this.plane.program.uniforms.uViewportSizes) {
                this.plane.program.uniforms.uViewportSizes.value = [this.viewport.width, this.viewport.height];
            }
        }
        this.scale = this.screen.height / 1500;
        this.plane.scale.y = (this.viewport.height * (900 * this.scale)) / this.screen.height;
        this.plane.scale.x = (this.viewport.width * (700 * this.scale)) / this.screen.width;
        this.plane.program.uniforms.uPlaneSizes.value = [this.plane.scale.x, this.plane.scale.y];
        this.padding = 2;
        this.width = this.plane.scale.x + this.padding;
        this.widthTotal = this.width * this.length;
        this.x = this.width * this.index;
    };
    return Media;
}());
var App = /** @class */ (function () {
    function App(container, _a) {
        var _b = _a === void 0 ? {} : _a, items = _b.items, bend = _b.bend, _c = _b.textColor, textColor = _c === void 0 ? "#ffffff" : _c, _d = _b.borderRadius, borderRadius = _d === void 0 ? 0 : _d, _e = _b.font, font = _e === void 0 ? "bold 30px DM Sans" : _e;
        document.documentElement.classList.remove('no-js');
        this.container = container;
        this.scroll = { ease: 0.05, current: 0, target: 0, last: 0 };
        this.onCheckDebounce = debounce(this.onCheck, 200);
        this.createRenderer();
        this.createCamera();
        this.createScene();
        this.onResize();
        this.createGeometry();
        this.createMedias(items, bend, textColor, borderRadius, font);
        this.update();
        this.addEventListeners();
    }
    App.prototype.createRenderer = function () {
        this.renderer = new ogl_1.Renderer({ alpha: true });
        this.gl = this.renderer.gl;
        this.gl.clearColor(0, 0, 0, 0);
        this.container.appendChild(this.gl.canvas);
    };
    App.prototype.createCamera = function () {
        this.camera = new ogl_1.Camera(this.gl);
        this.camera.fov = 45;
        this.camera.position.z = 20;
    };
    App.prototype.createScene = function () {
        this.scene = new ogl_1.Transform();
    };
    App.prototype.createGeometry = function () {
        this.planeGeometry = new ogl_1.Plane(this.gl, {
            heightSegments: 50,
            widthSegments: 100
        });
    };
    App.prototype.createMedias = function (items, bend, textColor, borderRadius, font) {
        var _this = this;
        if (bend === void 0) { bend = 1; }
        var defaultItems = [
            { image: '/zhanshi/1.jpg', text: '施工现场1' },
            { image: '/zhanshi/2.jpg', text: '施工现场2' },
            { image: '/zhanshi/3.jpg', text: '施工现场3' },
            { image: '/zhanshi/4.jpg', text: '施工现场4' },
            { image: '/zhanshi/5.jpg', text: '施工现场5' },
            { image: '/zhanshi/6.jpg', text: '施工现场6' },
            { image: '/zhanshi/7.jpg', text: '施工现场7' },
            { image: '/zhanshi/8.jpg', text: '施工现场8' },
        ];
        var galleryItems = items && items.length ? items : defaultItems;
        this.mediasImages = galleryItems.concat(galleryItems);
        this.medias = this.mediasImages.map(function (data, index) {
            return new Media({
                geometry: _this.planeGeometry,
                gl: _this.gl,
                image: data.image,
                index: index,
                length: _this.mediasImages.length,
                renderer: _this.renderer,
                scene: _this.scene,
                screen: _this.screen,
                text: data.text,
                viewport: _this.viewport,
                bend: bend,
                textColor: textColor,
                borderRadius: borderRadius,
                font: font
            });
        });
    };
    App.prototype.onTouchDown = function (e) {
        this.isDown = true;
        this.scroll.position = this.scroll.current;
        this.start = e.touches ? e.touches[0].clientX : e.clientX;
    };
    App.prototype.onTouchMove = function (e) {
        if (!this.isDown)
            return;
        var x = e.touches ? e.touches[0].clientX : e.clientX;
        var distance = (this.start - x) * 0.05;
        this.scroll.target = this.scroll.position + distance;
    };
    App.prototype.onTouchUp = function () {
        this.isDown = false;
        this.onCheck();
    };
    App.prototype.onWheel = function () {
        this.scroll.target += 2;
        this.onCheckDebounce();
    };
    App.prototype.onCheck = function () {
        if (!this.medias || !this.medias[0])
            return;
        var width = this.medias[0].width;
        var itemIndex = Math.round(Math.abs(this.scroll.target) / width);
        var item = width * itemIndex;
        this.scroll.target = this.scroll.target < 0 ? -item : item;
    };
    App.prototype.onResize = function () {
        var _this = this;
        this.screen = {
            width: this.container.clientWidth,
            height: this.container.clientHeight
        };
        this.renderer.setSize(this.screen.width, this.screen.height);
        this.camera.perspective({
            aspect: this.screen.width / this.screen.height
        });
        var fov = (this.camera.fov * Math.PI) / 180;
        var height = 2 * Math.tan(fov / 2) * this.camera.position.z;
        var width = height * this.camera.aspect;
        this.viewport = { width: width, height: height };
        if (this.medias) {
            this.medias.forEach(function (media) {
                return media.onResize({ screen: _this.screen, viewport: _this.viewport });
            });
        }
    };
    App.prototype.update = function () {
        var _this = this;
        this.scroll.current = lerp(this.scroll.current, this.scroll.target, this.scroll.ease);
        var direction = this.scroll.current > this.scroll.last ? 'right' : 'left';
        if (this.medias) {
            this.medias.forEach(function (media) { return media.update(_this.scroll, direction); });
        }
        this.renderer.render({ scene: this.scene, camera: this.camera });
        this.scroll.last = this.scroll.current;
        this.raf = window.requestAnimationFrame(this.update.bind(this));
    };
    App.prototype.addEventListeners = function () {
        this.boundOnResize = this.onResize.bind(this);
        this.boundOnWheel = this.onWheel.bind(this);
        this.boundOnTouchDown = this.onTouchDown.bind(this);
        this.boundOnTouchMove = this.onTouchMove.bind(this);
        this.boundOnTouchUp = this.onTouchUp.bind(this);
        window.addEventListener('resize', this.boundOnResize);
        window.addEventListener('mousewheel', this.boundOnWheel);
        window.addEventListener('wheel', this.boundOnWheel);
        window.addEventListener('mousedown', this.boundOnTouchDown);
        window.addEventListener('mousemove', this.boundOnTouchMove);
        window.addEventListener('mouseup', this.boundOnTouchUp);
        window.addEventListener('touchstart', this.boundOnTouchDown);
        window.addEventListener('touchmove', this.boundOnTouchMove);
        window.addEventListener('touchend', this.boundOnTouchUp);
    };
    App.prototype.destroy = function () {
        window.cancelAnimationFrame(this.raf);
        window.removeEventListener('resize', this.boundOnResize);
        window.removeEventListener('mousewheel', this.boundOnWheel);
        window.removeEventListener('wheel', this.boundOnWheel);
        window.removeEventListener('mousedown', this.boundOnTouchDown);
        window.removeEventListener('mousemove', this.boundOnTouchMove);
        window.removeEventListener('mouseup', this.boundOnTouchUp);
        window.removeEventListener('touchstart', this.boundOnTouchDown);
        window.removeEventListener('touchmove', this.boundOnTouchMove);
        window.removeEventListener('touchend', this.boundOnTouchUp);
        if (this.renderer && this.renderer.gl && this.renderer.gl.canvas.parentNode) {
            this.renderer.gl.canvas.parentNode.removeChild(this.renderer.gl.canvas);
        }
    };
    return App;
}());
function CircularGallery(_a) {
    var items = _a.items, _b = _a.bend, bend = _b === void 0 ? 3 : _b, _c = _a.textColor, textColor = _c === void 0 ? "#ffffff" : _c, _d = _a.borderRadius, borderRadius = _d === void 0 ? 0.05 : _d, _e = _a.font, font = _e === void 0 ? "bold 30px DM Sans" : _e;
    var containerRef = react_1.useRef(null);
    react_1.useEffect(function () {
        if (!containerRef.current)
            return;
        var app = new App(containerRef.current, { items: items, bend: bend, textColor: textColor, borderRadius: borderRadius, font: font });
        return function () {
            app.destroy();
        };
    }, [items, bend, textColor, borderRadius, font]);
    return (React.createElement("div", { className: 'circular-gallery', ref: containerRef }));
}
exports["default"] = CircularGallery;
