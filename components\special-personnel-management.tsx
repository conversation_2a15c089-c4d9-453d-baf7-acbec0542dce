"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  User,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileCheck,
  BadgeCheck,
  Upload,
  RefreshCcw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"

interface SpecialPersonnel {
  id: string
  name: string
  gender: string
  type: string
  certificateNo: string
  issueDate: string
  expiryDate: string
  status: string
  department: string
  trainingDate: string
  phone?: string
  idNumber?: string
  issueAuthority?: string
  remarks?: string
  disabled?: boolean
}

export function SpecialPersonnelManagement() {
  const [isAddPersonnelOpen, setIsAddPersonnelOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [selectedPerson, setSelectedPerson] = useState<SpecialPersonnel | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [loading, setLoading] = useState(false)
  const [personnel, setPersonnel] = useState<SpecialPersonnel[]>([
    {
      id: "1",
      name: "张三",
      gender: "男",
      type: "爆破工",
      certificateNo: "BZ2025001",
      issueDate: "2025-01-15",
      expiryDate: "2025-04-14",
      status: "有效",
      department: "爆破作业部",
      trainingDate: "2025-01-10",
      phone: "13800138000",
      idNumber: "110101199001011234",
      issueAuthority: "省安全生产监督管理局",
      remarks: "优秀爆破工，多次参与重大项目",
    },
    {
      id: "2",
      name: "李四",
      gender: "男",
      type: "电工",
      certificateNo: "DG2025045",
      issueDate: "2025-01-20",
      expiryDate: "2025-04-10",
      status: "有效",
      department: "设备维护部",
      trainingDate: "2025-02-15",
      phone: "13900139000",
      idNumber: "110101199002021235",
      issueAuthority: "市安全生产监督管理局",
    },
    {
      id: "3",
      name: "王五",
      gender: "男",
      type: "焊工",
      certificateNo: "HG2025078",
      issueDate: "2025-02-10",
      expiryDate: "2025-04-09",
      status: "有效",
      department: "设备维护部",
      trainingDate: "2025-03-05",
      phone: "13700137000",
      issueAuthority: "市特种设备安全监督检验所",
    },
    {
      id: "4",
      name: "赵六",
      gender: "男",
      type: "起重工",
      certificateNo: "QZ2025112",
      issueDate: "2025-01-05",
      expiryDate: "2025-04-04",
      status: "即将到期",
      department: "工程管理部",
      trainingDate: "2025-03-15",
      phone: "13600136000",
      issueAuthority: "省特种设备安全监督检验研究院",
    },
    {
      id: "5",
      name: "钱七",
      gender: "女",
      type: "安全员",
      certificateNo: "AQ2025033",
      issueDate: "2025-01-18",
      expiryDate: "2025-04-15",
      status: "已过期",
      department: "安全管理部",
      trainingDate: "2025-02-25",
      phone: "13500135000",
      issueAuthority: "省安全生产监督管理局",
    },
  ])

  // 统计数据
  const statistics = {
    total: personnel.length,
    valid: personnel.filter(p => p.status === "有效" && !p.disabled).length,
    expiring: personnel.filter(p => p.status === "即将到期").length,
    expired: personnel.filter(p => p.status === "已过期").length,
    needTraining: personnel.filter(p => {
      const lastTraining = new Date(p.trainingDate)
      const today = new Date()
      const monthsDiff = (today.getFullYear() - lastTraining.getFullYear()) * 12 +
                        today.getMonth() - lastTraining.getMonth()
      return monthsDiff >= 12
    }).length
  }

  // 处理搜索和筛选
  const filteredPersonnel = personnel.filter(person => {
    const matchesSearch = person.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         person.certificateNo.toLowerCase().includes(searchText.toLowerCase()) ||
                         person.department.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || person.type === selectedType
    const matchesDepartment = selectedDepartment === "all" || person.department === selectedDepartment
    const matchesStatus = selectedStatus === "all" || person.status === selectedStatus
    return matchesSearch && matchesType && matchesDepartment && matchesStatus
  })

  // 处理添加人员
  const handleAddPersonnel = (formData: any) => {
    const newPerson: SpecialPersonnel = {
      id: Date.now().toString(),
      ...formData,
    }
    setPersonnel([...personnel, newPerson])
    setIsAddPersonnelOpen(false)
    message.success("人员添加成功")
  }

  // 处理删除人员
  const handleDeletePersonnel = (id: string) => {
    const updatedPersonnel = personnel.filter(p => p.id !== id)
    setPersonnel(updatedPersonnel)
    message.success("人员删除成功")
  }

  // 处理禁用人员
  const handleDisablePersonnel = (id: string) => {
    const updatedPersonnel = personnel.map(p =>
      p.id === id ? { ...p, disabled: !p.disabled } : p
    )
    setPersonnel(updatedPersonnel)
    message.success(`人员${personnel.find(p => p.id === id)?.disabled ? "启用" : "禁用"}成功`)
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      const exportData = personnel.map(person => ({
        '姓名': person.name,
        '性别': person.gender,
        '人员类型': person.type,
        '证书编号': person.certificateNo,
        '发证日期': person.issueDate,
        '到期日期': person.expiryDate,
        '状态': person.status,
        '所属部门': person.department,
        '最近培训': person.trainingDate,
        '联系电话': person.phone,
        '身份证号': person.idNumber,
        '发证机构': person.issueAuthority,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 10 }, // 姓名
        { wch: 6 },  // 性别
        { wch: 12 }, // 人员类型
        { wch: 15 }, // 证书编号
        { wch: 12 }, // 发证日期
        { wch: 12 }, // 到期日期
        { wch: 10 }, // 状态
        { wch: 15 }, // 所属部门
        { wch: 12 }, // 最近培训
        { wch: 15 }, // 联系电话
        { wch: 20 }, // 身份证号
        { wch: 25 }, // 发证机构
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '特种人员列表')
      XLSX.writeFile(wb, `特种人员列表_${new Date().toISOString().split('T')[0]}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 导入Excel
  const handleImportExcel = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 转换导入的数据格式
        const importedPersonnel: SpecialPersonnel[] = jsonData.map((item: any) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: item['姓名'],
          gender: item['性别'],
          type: item['人员类型'],
          certificateNo: item['证书编号'],
          issueDate: item['发证日期'],
          expiryDate: item['到期日期'],
          status: item['状态'],
          department: item['所属部门'],
          trainingDate: item['最近培训'],
          phone: item['联系电话'],
          idNumber: item['身份证号'],
          issueAuthority: item['发证机构'],
        }))

        setPersonnel([...personnel, ...importedPersonnel])
        message.success('导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">特种人员管理</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">特种人员总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.valid}</h3>
            <p className="text-sm text-muted-foreground">证书有效</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.expiring}</h3>
            <p className="text-sm text-muted-foreground">即将到期</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.expired}</h3>
            <p className="text-sm text-muted-foreground">已过期</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>特种人员列表</CardTitle>
              <CardDescription>管理特种作业人员及其证书信息</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索人员..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="人员类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="爆破工">爆破工</SelectItem>
                    <SelectItem value="电工">电工</SelectItem>
                    <SelectItem value="焊工">焊工</SelectItem>
                    <SelectItem value="起重工">起重工</SelectItem>
                    <SelectItem value="安全员">安全员</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    <SelectItem value="爆破作业部">爆破作业部</SelectItem>
                    <SelectItem value="设备维护部">设备维护部</SelectItem>
                    <SelectItem value="工程管理部">工程管理部</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="有效">有效</SelectItem>
                    <SelectItem value="即将到期">即将到期</SelectItem>
                    <SelectItem value="已过期">已过期</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={handleRefresh}>
                  <RefreshCcw className="h-4 w-4" />
                </Button>
                <Button variant="outline" onClick={handleExportExcel}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Input
                  type="file"
                  accept=".xlsx,.xls"
                  className="hidden"
                  id="import-excel"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleImportExcel(file)
                  }}
                />
                <Button variant="outline" onClick={() => document.getElementById('import-excel')?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  导入
                </Button>
                <Button onClick={() => setIsAddPersonnelOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加人员
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>姓名</TableHead>
                    <TableHead>人员类型</TableHead>
                    <TableHead>证书编号</TableHead>
                    <TableHead>发证日期</TableHead>
                    <TableHead>到期日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>所属部门</TableHead>
                    <TableHead>最近培训</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPersonnel.map((person) => (
                    <TableRow key={person.id} className={person.disabled ? "opacity-50" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{person.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{person.type}</TableCell>
                      <TableCell>{person.certificateNo}</TableCell>
                      <TableCell>{person.issueDate}</TableCell>
                      <TableCell>{person.expiryDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            person.status === "有效"
                              ? "default"
                              : person.status === "即将到期"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {person.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{person.department}</TableCell>
                      <TableCell>{person.trainingDate}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedPerson(person)
                              setIsViewModalOpen(true)
                            }}
                          >
                            <FileCheck className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedPerson(person)
                                setIsViewModalOpen(true)
                              }}>
                                <FileText className="h-4 w-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDisablePersonnel(person.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                {person.disabled ? "启用" : "禁用"}
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Calendar className="h-4 w-4 mr-2" />
                                更新培训记录
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <BadgeCheck className="h-4 w-4 mr-2" />
                                更新证书
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeletePersonnel(person.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredPersonnel.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>人员详情</DialogTitle>
          </DialogHeader>
          {selectedPerson && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>姓名</Label>
                <p className="text-sm">{selectedPerson.name}</p>
              </div>
              <div className="space-y-2">
                <Label>性别</Label>
                <p className="text-sm">{selectedPerson.gender}</p>
              </div>
              <div className="space-y-2">
                <Label>人员类型</Label>
                <p className="text-sm">{selectedPerson.type}</p>
              </div>
              <div className="space-y-2">
                <Label>所属部门</Label>
                <p className="text-sm">{selectedPerson.department}</p>
              </div>
              <div className="space-y-2">
                <Label>证书编号</Label>
                <p className="text-sm">{selectedPerson.certificateNo}</p>
              </div>
              <div className="space-y-2">
                <Label>发证机构</Label>
                <p className="text-sm">{selectedPerson.issueAuthority}</p>
              </div>
              <div className="space-y-2">
                <Label>发证日期</Label>
                <p className="text-sm">{selectedPerson.issueDate}</p>
              </div>
              <div className="space-y-2">
                <Label>到期日期</Label>
                <p className="text-sm">{selectedPerson.expiryDate}</p>
              </div>
              <div className="space-y-2">
                <Label>最近培训日期</Label>
                <p className="text-sm">{selectedPerson.trainingDate}</p>
              </div>
              <div className="space-y-2">
                <Label>证书状态</Label>
                <p className="text-sm">{selectedPerson.status}</p>
              </div>
              <div className="space-y-2">
                <Label>联系电话</Label>
                <p className="text-sm">{selectedPerson.phone}</p>
              </div>
              <div className="space-y-2">
                <Label>身份证号</Label>
                <p className="text-sm">{selectedPerson.idNumber}</p>
              </div>
              {selectedPerson.remarks && (
                <div className="col-span-2 space-y-2">
                  <Label>备注</Label>
                  <p className="text-sm">{selectedPerson.remarks}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

