import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Personnel } from "@/hooks/use-personnel-data"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
)

interface PersonnelStatisticsProps {
  personnel: Personnel[]
}

export function PersonnelStatistics({ personnel }: PersonnelStatisticsProps) {
  // 计算部门人员分布
  const departmentDistribution = personnel.reduce((acc, person) => {
    acc[person.department] = (acc[person.department] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 计算学历分布
  const educationDistribution = personnel.reduce((acc, person) => {
    const education = person.education || '其他'
    acc[education] = (acc[education] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 计算入职时间分布（按月统计）
  const hireDateDistribution = personnel.reduce((acc, person) => {
    const month = person.hireDate.substring(0, 7) // 获取年月
    acc[month] = (acc[month] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 计算人员状态分布
  const statusDistribution = personnel.reduce((acc, person) => {
    acc[person.status] = (acc[person.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 生成图表数据
  const departmentChartData = {
    labels: Object.keys(departmentDistribution),
    datasets: [
      {
        data: Object.values(departmentDistribution),
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
        ],
        borderWidth: 1,
      },
    ],
  }

  const educationChartData = {
    labels: Object.keys(educationDistribution),
    datasets: [
      {
        data: Object.values(educationDistribution),
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
        ],
        borderWidth: 1,
      },
    ],
  }

  const hireDateChartData = {
    labels: Object.keys(hireDateDistribution).sort(),
    datasets: [
      {
        label: '入职人数',
        data: Object.keys(hireDateDistribution)
          .sort()
          .map(key => hireDateDistribution[key]),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  }

  const statusChartData = {
    labels: Object.keys(statusDistribution),
    datasets: [
      {
        label: '人数',
        data: Object.values(statusDistribution),
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',
          'rgba(255, 99, 132, 0.8)',
        ],
      },
    ],
  }

  // 图表配置
  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
    },
  }

  const lineOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  }

  const barOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  }

  return (
    <div className="grid grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">部门人员分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <Pie data={departmentChartData} options={pieOptions} />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">学历分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <Doughnut data={educationChartData} options={pieOptions} />
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-2">
        <CardHeader>
          <CardTitle className="text-lg">入职时间分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Line data={hireDateChartData} options={lineOptions} />
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-2">
        <CardHeader>
          <CardTitle className="text-lg">人员状态分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <Bar data={statusChartData} options={barOptions} />
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 