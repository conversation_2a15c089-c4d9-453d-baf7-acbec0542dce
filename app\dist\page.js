"use client";
"use strict";
exports.__esModule = true;
var main_layout_1 = require("@/components/main-layout");
var dashboard_1 = require("@/components/dashboard");
var auth_context_1 = require("@/contexts/auth-context");
var react_1 = require("react");
var navigation_1 = require("next/navigation");
function Home() {
    var isAuthenticated = auth_context_1.useAuth().isAuthenticated;
    var router = navigation_1.useRouter();
    react_1.useEffect(function () {
        // 确保未登录用户被重定向到登录页面
        if (!isAuthenticated) {
            router.replace("/login");
        }
    }, [isAuthenticated, router]);
    return (React.createElement(main_layout_1.MainLayout, null,
        React.createElement(dashboard_1.Dashboard, null)));
}
exports["default"] = Home;
