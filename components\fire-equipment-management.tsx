"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  Calendar,
  Flame,
  CheckCircle,
  AlertTriangle,
  Clock,
  QrCode,
  History,
  Printer,
  RefreshCcw,
  FileCheck,
  BarChart2,
  Eye,
  Settings,
  BellRing,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { message } from "@/components/ui/use-message"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"

interface FireEquipment {
  id: string
  name: string
  type: string
  model: string
  location: string
  installDate: string
  expiryDate: string
  lastInspectionDate: string
  nextInspectionDate: string
  status: string
  responsible: string
  manufacturer: string
  maintenanceHistory: MaintenanceRecord[]
  inspectionCycle: number
  remarks: string
  qrCode: string
  disabled: boolean
}

interface MaintenanceRecord {
  id: string
  date: string
  type: string
  description: string
  operator: string
  result: string
}

interface Statistics {
  total: number
  normal: number
  pending: number
  needReplace: number
  maintenance: number
  expiringSoon: number
}

export function FireEquipmentManagement() {
  const { toast } = useToast()
  const [isAddEquipmentOpen, setIsAddEquipmentOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<FireEquipment | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [maintenanceHistory, setMaintenanceHistory] = useState<MaintenanceRecord[]>([])
  const [formData, setFormData] = useState<Partial<FireEquipment>>({})
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<string>("")
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [selectedScheduleEquipment, setSelectedScheduleEquipment] = useState<FireEquipment | null>(null)
  const [scheduleFormData, setScheduleFormData] = useState({
    scheduledDate: "",
    inspector: "",
    notes: "",
  })

  const [fireEquipments, setFireEquipments] = useState<FireEquipment[]>([
    {
      id: "1",
      name: "手提式干粉灭火器",
      type: "灭火器",
      model: "MF/ABC4",
      location: "办公楼一层",
      installDate: "2025-01-15",
      expiryDate: "2025-04-15",
      lastInspectionDate: "2025-03-20",
      nextInspectionDate: "2025-04-10",
      status: "正常",
      responsible: "张三",
      manufacturer: "ABC消防器材有限公司",
      maintenanceHistory: [],
      inspectionCycle: 30,
      remarks: "",
      qrCode: "",
      disabled: false
    },
    {
      id: "2",
      name: "消防栓",
      type: "消防栓",
      model: "SN-65",
      location: "矿区A3入口",
      installDate: "2025-01-20",
      expiryDate: "2025-04-10",
      lastInspectionDate: "2025-02-15",
      nextInspectionDate: "2025-03-15",
      status: "正常",
      responsible: "李四",
      manufacturer: "",
      maintenanceHistory: [],
      inspectionCycle: 0,
      remarks: "",
      qrCode: "",
      disabled: false
    },
    {
      id: "3",
      name: "火灾自动报警系统",
      type: "报警系统",
      model: "JB-QB-GST5000",
      location: "控制室",
      installDate: "2025-02-10",
      expiryDate: "2025-04-10",
      lastInspectionDate: "2025-03-05",
      nextInspectionDate: "2025-04-05",
      status: "正常",
      responsible: "王五",
      manufacturer: "",
      maintenanceHistory: [],
      inspectionCycle: 0,
      remarks: "",
      qrCode: "",
      disabled: false
    },
    {
      id: "4",
      name: "手提式二氧化碳灭火器",
      type: "灭火器",
      model: "MT/5",
      location: "变电所",
      installDate: "2025-01-25",
      expiryDate: "2025-04-05",
      lastInspectionDate: "2025-02-25",
      nextInspectionDate: "2025-03-25",
      status: "待检查",
      responsible: "赵六",
      manufacturer: "",
      maintenanceHistory: [],
      inspectionCycle: 0,
      remarks: "",
      qrCode: "",
      disabled: false
    },
    {
      id: "5",
      name: "消防水带",
      type: "水带",
      model: "PH65-20",
      location: "矿区B2入口",
      installDate: "2025-01-15",
      expiryDate: "2025-04-15",
      lastInspectionDate: "2025-02-15",
      nextInspectionDate: "2025-03-15",
      status: "需更换",
      responsible: "钱七",
      manufacturer: "",
      maintenanceHistory: [],
      inspectionCycle: 0,
      remarks: "",
      qrCode: "",
      disabled: false
    },
  ])

  // 设备状态统计
  const statistics: Statistics = {
    total: fireEquipments.length,
    normal: fireEquipments.filter(e => e.status === "正常").length,
    pending: fireEquipments.filter(e => e.status === "待检查").length,
    needReplace: fireEquipments.filter(e => e.status === "需更换").length,
    maintenance: fireEquipments.filter(e => e.status === "维修中").length,
    expiringSoon: fireEquipments.filter(e => {
      const expiryDate = new Date(e.expiryDate);
      const today = new Date();
      const diffDays = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      return diffDays <= 30;
    }).length
  }

  // 替换 message 为 toast
  const showMessage = (type: "success" | "error", content: string) => {
    toast({
      variant: type === "success" ? "default" : "destructive",
      title: content,
    })
  }

  // 更新消息提示
  const handleExportExcel = () => {
    showMessage("success", "导出成功")
  }

  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      showMessage("success", "数据已刷新")
    }, 1000)
  }

  // 处理查看详情
  const handleView = (equipment: FireEquipment) => {
    setSelectedEquipment(equipment)
    setIsViewModalOpen(true)
  }

  // 处理编辑
  const handleEdit = (equipment: FireEquipment) => {
    setSelectedEquipment(equipment)
    setFormData(equipment)
    setIsEditModalOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = () => {
    if (!selectedEquipment || !formData) return

    const updatedEquipments = fireEquipments.map(equipment =>
      equipment.id === selectedEquipment.id ? { ...equipment, ...formData } : equipment
    )
    setFireEquipments(updatedEquipments)
    setIsEditModalOpen(false)
    setSelectedEquipment(null)
    setFormData({})
    showMessage("success", "修改成功")
  }

  // 更新确认对话框
  const handleDelete = (id: string) => {
    setDeleteId(id)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    const newEquipments = fireEquipments.filter(equipment => equipment.id !== deleteId)
    setFireEquipments(newEquipments)
    setIsDeleteDialogOpen(false)
    showMessage("success", "记录已删除")
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setIsDeleteDialogOpen(true)
  }

  const confirmBatchDelete = () => {
    setFireEquipments(fireEquipments.filter(equipment => !selectedRowKeys.includes(equipment.id)))
    setSelectedRowKeys([])
    showMessage("success", "批量删除成功")
    setIsDeleteDialogOpen(false)
  }

  // 处理禁用/启用
  const handleToggleDisable = (id: string) => {
    setFireEquipments(fireEquipments.map(equipment =>
      equipment.id === id ? { ...equipment, disabled: !equipment.disabled } : equipment
    ))
    showMessage("success", `${fireEquipments.find(e => e.id === id)?.disabled ? '启用' : '禁用'}成功`)
  }

  // 处理生成二维码
  const handleGenerateQR = (equipment: FireEquipment) => {
    // TODO: 实现二维码生成功能
    showMessage("success", "二维码已生成")
  }

  // 处理检查记录
  const handleViewHistory = (equipment: FireEquipment) => {
    setSelectedEquipment(equipment)
    setMaintenanceHistory(equipment.maintenanceHistory || [])
    // TODO: 显示检查记录抽屉
  }

  // 处理安排检查
  const handleScheduleInspection = (equipment: FireEquipment) => {
    setSelectedScheduleEquipment(equipment)
    setScheduleFormData({
      scheduledDate: "",
      inspector: "",
      notes: "",
    })
    setIsScheduleDialogOpen(true)
  }

  // 确认安排检查
  const confirmScheduleInspection = () => {
    if (!selectedScheduleEquipment || !scheduleFormData.scheduledDate || !scheduleFormData.inspector) {
      showMessage("error", "请填写完整的检查信息")
      return
    }

    // 更新设备的检查记录
    const updatedEquipments = fireEquipments.map(equipment => {
      if (equipment.id === selectedScheduleEquipment.id) {
        return {
          ...equipment,
          maintenanceHistory: [
            ...equipment.maintenanceHistory,
            {
              id: Date.now().toString(),
              date: scheduleFormData.scheduledDate,
              type: "定期检查",
              description: scheduleFormData.notes || "常规检查",
              operator: scheduleFormData.inspector,
              result: "待检查"
            },
          ],
          nextInspectionDate: scheduleFormData.scheduledDate,
          status: "待检查"
        }
      }
      return equipment
    })

    setFireEquipments(updatedEquipments)
    setIsScheduleDialogOpen(false)
    showMessage("success", "检查已安排")
  }

  // 渲染统计抽屉
  const renderStatsDrawer = () => (
    <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <BarChart2 className="h-5 w-5" />
              设备统计分析
            </div>
          </SheetTitle>
          <SheetDescription>查看设备状态和检查情况的统计数据</SheetDescription>
        </SheetHeader>
        <div className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>设备状态分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">正常运行</span>
                    <span className="text-sm text-muted-foreground">{statistics.normal}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-green-500"
                      style={{ width: `${(statistics.normal / statistics.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">待检查</span>
                    <span className="text-sm text-muted-foreground">{statistics.pending}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-yellow-500"
                      style={{ width: `${(statistics.pending / statistics.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">需更换</span>
                    <span className="text-sm text-muted-foreground">{statistics.needReplace}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-red-500"
                      style={{ width: `${(statistics.needReplace / statistics.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">维修中</span>
                    <span className="text-sm text-muted-foreground">{statistics.maintenance}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-purple-500"
                      style={{ width: `${(statistics.maintenance / statistics.total) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>设备检查情况</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">即将到期设备</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="bg-orange-50">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">7天内到期</p>
                            <h3 className="text-2xl font-bold mt-1">{statistics.expiringSoon}</h3>
                          </div>
                          <div className="rounded-full bg-orange-100 p-2">
                            <BellRing className="h-4 w-4 text-orange-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="bg-yellow-50">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">30天内到期</p>
                            <h3 className="text-2xl font-bold mt-1">
                              {fireEquipments.filter(e => {
                                const expiryDate = new Date(e.expiryDate);
                                const today = new Date();
                                const diffDays = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                                return diffDays <= 30;
                              }).length}
                            </h3>
                          </div>
                          <div className="rounded-full bg-yellow-100 p-2">
                            <Clock className="h-4 w-4 text-yellow-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">设备类型分布</h4>
                  <div className="grid grid-cols-3 gap-4">
                    {["灭火器", "消防栓", "报警系统", "水带", "其他设备"].map(type => {
                      const count = fireEquipments.filter(e => e.type === type).length;
                      return (
                        <div key={type} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{type}</span>
                            <span className="text-sm text-muted-foreground">{count}</span>
                          </div>
                          <div className="h-2 rounded-full bg-gray-100">
                            <div
                              className="h-full rounded-full bg-blue-500"
                              style={{ width: `${(count / statistics.total) * 100}%` }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  )

  // 渲染查看详情抽屉
  const renderViewDrawer = () => (
    <Sheet open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              设备详情
            </div>
          </SheetTitle>
          <SheetDescription>查看设备的详细信息</SheetDescription>
        </SheetHeader>
        {selectedEquipment && (
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>设备名称</Label>
                <p className="mt-1">{selectedEquipment.name}</p>
              </div>
              <div>
                <Label>设备类型</Label>
                <p className="mt-1">{selectedEquipment.type}</p>
              </div>
              <div>
                <Label>型号规格</Label>
                <p className="mt-1">{selectedEquipment.model}</p>
              </div>
              <div>
                <Label>安装位置</Label>
                <p className="mt-1">{selectedEquipment.location}</p>
              </div>
              <div>
                <Label>安装日期</Label>
                <p className="mt-1">{selectedEquipment.installDate}</p>
              </div>
              <div>
                <Label>到期日期</Label>
                <p className="mt-1">{selectedEquipment.expiryDate}</p>
              </div>
              <div>
                <Label>上次检查</Label>
                <p className="mt-1">{selectedEquipment.lastInspectionDate}</p>
              </div>
              <div>
                <Label>下次检查</Label>
                <p className="mt-1">{selectedEquipment.nextInspectionDate}</p>
              </div>
              <div>
                <Label>状态</Label>
                <p className="mt-1">
                  <Badge
                    variant={
                      selectedEquipment.status === "正常"
                        ? "default"
                        : selectedEquipment.status === "待检查"
                          ? "secondary"
                          : selectedEquipment.status === "需更换"
                            ? "destructive"
                            : "outline"
                    }
                  >
                    {selectedEquipment.status}
                  </Badge>
                </p>
              </div>
              <div>
                <Label>责任人</Label>
                <p className="mt-1">{selectedEquipment.responsible}</p>
              </div>
            </div>

            <div>
              <Label>检查记录</Label>
              <div className="mt-2 space-y-2">
                {selectedEquipment.maintenanceHistory.length > 0 ? (
                  selectedEquipment.maintenanceHistory.map((record) => (
                    <Card key={record.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">{record.type}</p>
                            <p className="text-sm text-muted-foreground">{record.description}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm">{record.date}</p>
                            <p className="text-sm text-muted-foreground">{record.operator}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">暂无检查记录</p>
                )}
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )

  // 渲染编辑抽屉
  const renderEditDrawer = () => (
    <Sheet open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              编辑设备
            </div>
          </SheetTitle>
          <SheetDescription>修改设备的信息</SheetDescription>
        </SheetHeader>
        <div className="mt-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">设备名称 <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">设备类型 <span className="text-red-500">*</span></Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="选择设备类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="灭火器">灭火器</SelectItem>
                  <SelectItem value="消防栓">消防栓</SelectItem>
                  <SelectItem value="报警系统">报警系统</SelectItem>
                  <SelectItem value="水带">水带</SelectItem>
                  <SelectItem value="其他设备">其他设备</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="model">型号规格 <span className="text-red-500">*</span></Label>
              <Input
                id="model"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">安装位置 <span className="text-red-500">*</span></Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="installDate">安装日期 <span className="text-red-500">*</span></Label>
              <Input
                id="installDate"
                type="date"
                value={formData.installDate}
                onChange={(e) => setFormData({ ...formData, installDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expiryDate">到期日期 <span className="text-red-500">*</span></Label>
              <Input
                id="expiryDate"
                type="date"
                value={formData.expiryDate}
                onChange={(e) => setFormData({ ...formData, expiryDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastInspectionDate">上次检查日期</Label>
              <Input
                id="lastInspectionDate"
                type="date"
                value={formData.lastInspectionDate}
                onChange={(e) => setFormData({ ...formData, lastInspectionDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nextInspectionDate">下次检查日期</Label>
              <Input
                id="nextInspectionDate"
                type="date"
                value={formData.nextInspectionDate}
                onChange={(e) => setFormData({ ...formData, nextInspectionDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">状态 <span className="text-red-500">*</span></Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="选择设备状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="正常">正常</SelectItem>
                  <SelectItem value="待检查">待检查</SelectItem>
                  <SelectItem value="需更换">需更换</SelectItem>
                  <SelectItem value="维修中">维修中</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="responsible">责任人 <span className="text-red-500">*</span></Label>
              <Input
                id="responsible"
                value={formData.responsible}
                onChange={(e) => setFormData({ ...formData, responsible: e.target.value })}
              />
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <Label htmlFor="remarks">备注</Label>
            <Textarea
              id="remarks"
              value={formData.remarks}
              onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
              rows={3}
            />
          </div>
        </div>
        <SheetFooter className="mt-6">
          <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
            取消
          </Button>
          <Button onClick={handleSaveEdit}>保存</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">消防设备管理</h2>
          <p className="text-muted-foreground">管理和维护消防设备信息，确保安全运行</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportExcel}>
            <Download className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">总设备数</h3>
              <div className="rounded-full bg-blue-100 p-2">
                <Settings className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.total}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">正常运行</h3>
              <div className="rounded-full bg-green-100 p-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.normal}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">待检查</h3>
              <div className="rounded-full bg-yellow-100 p-2">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.pending}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">需更换</h3>
              <div className="rounded-full bg-red-100 p-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.needReplace}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">维修中</h3>
              <div className="rounded-full bg-purple-100 p-2">
                <Settings className="h-4 w-4 text-purple-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.maintenance}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">即将到期</h3>
              <div className="rounded-full bg-orange-100 p-2">
                <BellRing className="h-4 w-4 text-orange-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.expiringSoon}</h1>
              <span className="text-xs text-muted-foreground">台</span>
            </div>
            </CardContent>
          </Card>
      </div>

      {/* 主要内容区域 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>设备列表</CardTitle>
              <CardDescription>管理和维护消防设备信息</CardDescription>
            </div>
            <Button onClick={() => setIsAddEquipmentOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加设备
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* 搜索和筛选 */}
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索设备..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select
                  value={selectedTypes.length > 0 ? selectedTypes.join(',') : 'all'}
                  onValueChange={(value) => setSelectedTypes(value === 'all' ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="设备类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="灭火器">灭火器</SelectItem>
                    <SelectItem value="消防栓">消防栓</SelectItem>
                    <SelectItem value="报警系统">报警系统</SelectItem>
                    <SelectItem value="水带">水带</SelectItem>
                    <SelectItem value="其他设备">其他设备</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={selectedStatus.length > 0 ? selectedStatus.join(',') : 'all'}
                  onValueChange={(value) => setSelectedStatus(value === 'all' ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="设备状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="正常">正常</SelectItem>
                    <SelectItem value="待检查">待检查</SelectItem>
                    <SelectItem value="需更换">需更换</SelectItem>
                    <SelectItem value="维修中">维修中</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 批量操作 */}
            {selectedRowKeys.length > 0 && (
              <div className="bg-muted/50 p-2 rounded-lg">
              <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    已选择 {selectedRowKeys.length} 项
                  </span>
                  <Button variant="destructive" size="sm" onClick={handleBatchDelete}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                    </Button>
                        </div>
              </div>
            )}

            {/* 设备表格 */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>型号</TableHead>
                    <TableHead>安装位置</TableHead>
                    <TableHead>安装日期</TableHead>
                    <TableHead>到期日期</TableHead>
                    <TableHead>上次检查</TableHead>
                    <TableHead>下次检查</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fireEquipments.map((equipment) => (
                    <TableRow key={equipment.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <Flame className="h-4 w-4 mr-2 text-red-500" />
                          {equipment.name}
                        </div>
                      </TableCell>
                      <TableCell>{equipment.type}</TableCell>
                      <TableCell>{equipment.model}</TableCell>
                      <TableCell>{equipment.location}</TableCell>
                      <TableCell>{equipment.installDate}</TableCell>
                      <TableCell>{equipment.expiryDate}</TableCell>
                      <TableCell>{equipment.lastInspectionDate}</TableCell>
                      <TableCell>{equipment.nextInspectionDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            equipment.status === "正常"
                              ? "default"
                              : equipment.status === "待检查"
                                ? "secondary"
                                : equipment.status === "需更换"
                                  ? "destructive"
                                  : "outline"
                          }
                        >
                          {equipment.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleView(equipment)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleEdit(equipment)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleGenerateQR(equipment)}>
                            <QrCode className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewHistory(equipment)}>
                                <History className="h-4 w-4 mr-2" />
                                检查记录
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDelete(equipment.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleDisable(equipment.id)}>
                                {equipment.disabled ? (
                                  <>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    启用
                                  </>
                                ) : (
                                  <>
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    禁用
                                  </>
                                )}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 到期提醒卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>设备检查提醒</CardTitle>
          <CardDescription>即将到期需要检查的消防设备</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {fireEquipments
              .filter(equipment => {
                const nextDate = new Date(equipment.nextInspectionDate);
                const today = new Date();
                const diffDays = Math.ceil((nextDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                return diffDays <= 7;
              })
              .map(equipment => (
                <div key={equipment.id} className="border rounded-md p-4 bg-amber-50">
              <div className="flex items-center mb-2">
                <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                    <h3 className="font-medium">{equipment.name} ({equipment.model})</h3>
                <Badge variant="outline" className="ml-2">
                      检查日期: {equipment.nextInspectionDate}
                </Badge>
              </div>
                  <p className="text-sm text-muted-foreground mb-2">位置: {equipment.location}</p>
              <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground">责任人: {equipment.responsible}</span>
                    <Button variant="outline" size="sm" onClick={() => handleScheduleInspection(equipment)}>
                  <Calendar className="h-4 w-4 mr-2" />
                  安排检查
                </Button>
              </div>
            </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>批量删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除选中的 {selectedRowKeys.length} 条记录吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBatchDelete}>确定</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 添加统计抽屉 */}
      {renderStatsDrawer()}

      {/* 添加查看和编辑抽屉 */}
      {renderViewDrawer()}
      {renderEditDrawer()}

      {/* 安排检查对话框 */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>安排设备检查</DialogTitle>
            <DialogDescription>
              {selectedScheduleEquipment && `为 ${selectedScheduleEquipment.name} 安排检查计划`}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="scheduledDate">计划检查日期 <span className="text-red-500">*</span></Label>
              <Input
                id="scheduledDate"
                type="date"
                value={scheduleFormData.scheduledDate}
                onChange={(e) => setScheduleFormData({ ...scheduleFormData, scheduledDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="inspector">检查人员 <span className="text-red-500">*</span></Label>
              <Input
                id="inspector"
                value={scheduleFormData.inspector}
                onChange={(e) => setScheduleFormData({ ...scheduleFormData, inspector: e.target.value })}
                placeholder="请输入检查人员姓名"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">备注说明</Label>
              <Textarea
                id="notes"
                value={scheduleFormData.notes}
                onChange={(e) => setScheduleFormData({ ...scheduleFormData, notes: e.target.value })}
                placeholder="请输入检查注意事项或其他说明"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={confirmScheduleInspection}>
              确认安排
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

