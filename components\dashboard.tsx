"use client"

import { useState, use<PERSON>ffect, use<PERSON>em<PERSON>, useRef } from "react"
import { useRouter } from "next/navigation"
import {
  Printer,
  Package,
  FileText,
  FolderOpen,
  Bell,
  CheckCircle,
  Clock,
  BarChart2,
  Shield,
  Users,
  HardHat,
  DollarSign,
  BarChart3,
  Calendar,
  MessageSquare,
  AlertTriangle,
  Truck,
  Boxes,
  Wrench,
  ChevronRight,
  Settings,
  Database,
  Zap,
  Lock,
  FileText as FileTextIcon,
  CheckSquare,
  BarChart,
  TrendingUp,
  TrendingDown,
  Star,
  ArrowUpRight,
  ArrowDownRight,
  Info,
  Bell as BellIcon,
  Calendar as CalendarIcon,
  Clock as ClockIcon,
  Bookmark,
  Activity,
  Clipboard,
  AlertCircle,
  Layers,
  Search,
  Filter,
  Lightbulb,
  ChartBar as ChartBarIcon,
  LineChart,
  BrainCircuit,
  ArrowRight as ArrowRightIcon,
  Badge
} from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useTheme } from "@/contexts/theme-context"
import { Input } from "@/components/ui/input"
import { useSafetyCheckData } from "@/hooks/use-safety-check-data"
// 导入相关样式
import styles from "@/components/insights.module.css"
import RollingGallery from "@/components/RollingGallery"
import Silk from "@/components/Silk"

export function Dashboard() {
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const { themeMode } = useTheme()
  const { safetyChecks, getStatistics: getSafetyStatistics } = useSafetyCheckData()
  const [activeTab, setActiveTab] = useState("all")

  // 获取真实的安全检查数据
  const safetyStats = useMemo(() => getSafetyStatistics(), [safetyChecks])

  // 添加数字跳动动画的引用
  const animatedCountersInitialized = useRef(false)

  // 添加智能洞察数据
  const [insights, setInsights] = useState([
    {
      id: 1,
      title: '能源使用异常检测',
      description: '检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况',
      type: 'anomaly',
      timestamp: new Date(),
      module: '能源管理',
      importance: 'high',
      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 2,
      title: '财务趋势预测',
      description: '根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标',
      type: 'trend',
      timestamp: new Date(),
      module: '财务管理',
      importance: 'medium',
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      color: '#52c41a',
    },
    {
      id: 3,
      title: '设备维护建议',
      description: '3号生产线设备预计在14天内需要进行预防性维护，建议提前安排',
      type: 'recommendation',
      timestamp: new Date(),
      module: '设备管理',
      importance: 'medium',
      icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
      color: '#faad14',
    },
    {
      id: 4,
      title: '项目风险预警',
      description: '主厂房建设项目进度已落后计划7.2%，建议增加资源投入',
      type: 'alert',
      timestamp: new Date(),
      module: '项目管理',
      importance: 'high',
      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
      color: '#ff4d4f',
    },
    {
      id: 5,
      title: '物资库存优化',
      description: '分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本',
      type: 'recommendation',
      timestamp: new Date(),
      module: '物资管理',
      importance: 'low',
      icon: <Lightbulb className="h-5 w-5 text-blue-500" />,
      color: '#1890ff',
    },
  ]);

  // 添加智能分析功能状态
  const [analysisActiveTab, setAnalysisActiveTab] = useState('today');
  const [showAnalyticsSidebar, setShowAnalyticsSidebar] = useState(false);

  // 渲染今日洞察组件
  const renderInsights = () => {
    return (
      <div className={styles.insightsContainer}>
        <div className={styles.insightsHeader}>
          <h3>今日智能洞察</h3>
          <div className={styles.insightsTabs}>
            <span
              className={`${styles.insightTab} ${analysisActiveTab === 'today' ? styles.activeTab : ''}`}
              onClick={() => setAnalysisActiveTab('today')}
            >
              今日
            </span>
            <span
              className={`${styles.insightTab} ${analysisActiveTab === 'week' ? styles.activeTab : ''}`}
              onClick={() => setAnalysisActiveTab('week')}
            >
              本周
            </span>
            <span
              className={`${styles.insightTab} ${analysisActiveTab === 'all' ? styles.activeTab : ''}`}
              onClick={() => setAnalysisActiveTab('all')}
            >
              全部
            </span>
          </div>
        </div>
        <div className={styles.insightsList}>
          {insights.map((insight) => (
            <div
              key={insight.id}
              className={styles.insightCard}
              style={{ borderLeft: `4px solid ${insight.color}` }}
              onClick={() => router.push(`/${insight.module.toLowerCase().replace(/\s+/g, '-')}`)}
            >
              <div className={styles.insightIcon}>
                {insight.icon}
              </div>
              <div className={styles.insightContent}>
                <h4>{insight.title}</h4>
                <p>{insight.description}</p>
                <div className={styles.insightMeta}>
                  <span>{insight.module}</span>
                  <span>
                    {insight.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </span>
                </div>
              </div>
              <div className={styles.insightBadge}>
                {insight.importance === 'high' && <span className={styles.highImportance}>高</span>}
                {insight.importance === 'medium' && <span className={styles.mediumImportance}>中</span>}
                {insight.importance === 'low' && <span className={styles.lowImportance}>低</span>}
              </div>
            </div>
          ))}
        </div>
        <div className={styles.viewAllInsights} onClick={() => router.push('/insights')}>
          查看全部
          <span className={styles.viewAllIcon}>→</span>
        </div>
      </div>
    );
  };

  // 实现数字跳动动画
  useEffect(() => {
    if (animatedCountersInitialized.current) return

    const animateCounters = () => {
      const counters = document.querySelectorAll('.animate-counter')

      counters.forEach(counter => {
        const elem = counter as HTMLElement
        const target = parseInt(elem.dataset.target || '0')
        const duration = 1500 // 动画持续时间（毫秒）
        const steps = 60 // 动画步数
        const stepTime = duration / steps
        const increment = target / steps
        let current = 0
        let step = 0

        const updateCounter = () => {
          step++
          current += increment
          elem.innerText = `${Math.round(Math.min(current, target))}%`

          if (step < steps) {
            setTimeout(updateCounter, stepTime)
          } else {
            elem.innerText = `${target}%`
          }
        }

        updateCounter()
      })
    }

    // 页面加载完成后执行动画
    const timer = setTimeout(() => {
      animateCounters()
      animatedCountersInitialized.current = true
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  const [stats, setStats] = useState({
    safetyChecks: 0,
    safetyChecksChange: 0,
    safetyIssues: 0,
    personnel: 247,
    personnelChange: 3,
    projects: 8,
    tasks: 24,
    tasksChange: -8,
  })

  // 修改为从项目真实数据获取周报数据
  const [weeklyStats, setWeeklyStats] = useState([
    { day: '周一', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周二', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周三', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周四', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周五', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周六', safetyChecks: 0, tasks: 0, issues: 0 },
    { day: '周日', safetyChecks: 0, tasks: 0, issues: 0 },
  ])
  const [alerts] = useState([
    {
      id: 1,
      type: 'warning',
      title: '安全风险提醒',
      content: 'A3矿区检测到异常情况，请及时处理',
      time: '10分钟前',
      priority: 'high',
      isRead: false,
      route: '/safety-management/safety-hazard-management'
    },
    {
      id: 2,
      type: 'info',
      title: '系统更新通知',
      content: '系统将于今晚22:00进行例行维护',
      time: '1小时前',
      priority: 'medium',
      isRead: false,
      route: '/system-management/system-settings'
    },
    {
      id: 3,
      type: 'success',
      title: '安全检查完成',
      content: 'B2矿区安全检查已完成，无重大安全隐患',
      time: '2小时前',
      priority: 'normal',
      isRead: true,
      route: '/safety-management/safety-check'
    },
  ])
  const [recentModules, setRecentModules] = useState<string[]>([])
  const [frequentModules, setFrequentModules] = useState<string[]>([
    'safety-management',
    'personnel-management',
    'project-management'
  ])
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false)
  const [welcomeUsername, setWelcomeUsername] = useState("")

  // 添加通知详情弹窗相关状态
  const [showNotificationDialog, setShowNotificationDialog] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<any>(null)
  const [notificationFilter, setNotificationFilter] = useState("all")

  // 添加模块搜索状态
  const [moduleSearchTerm, setModuleSearchTerm] = useState("")

  // 检查是否需要显示欢迎弹窗
  useEffect(() => {
    const shouldShowWelcome = sessionStorage.getItem("showWelcomePopup") === "true"
    const storedUsername = sessionStorage.getItem("username")

    if (shouldShowWelcome) {
      // 显示欢迎弹窗
      setWelcomeUsername(storedUsername || user?.username || "用户")
      setShowWelcomeDialog(true)

      // 清除标记，避免刷新页面后再次显示
      sessionStorage.removeItem("showWelcomePopup")
    }
  }, [user])

  // 关闭欢迎弹窗
  const handleCloseWelcomeDialog = () => {
    setShowWelcomeDialog(false)
  }

  // 主要模块快速访问配置
  const mainModules = [
    {
      id: "system-management",
      title: "系统管理",
      description: "用户、角色和系统设置管理",
      icon: <Settings className="h-8 w-8" />,
      path: "/system-management/user-management"
    },
    {
      id: "safety-management",
      title: "安全管理",
      description: "安全检查、隐患管理和应急预案",
      icon: <Shield className="h-8 w-8" />,
      path: "/safety-management/safety-check"
    },
    {
      id: "project-management",
      title: "工程管理",
      description: "工程计划、进度和质量管理",
      icon: <HardHat className="h-8 w-8" />,
      path: "/project-management/project-homepage"
    },
    {
      id: "personnel-management",
      title: "人事管理",
      description: "人员信息、考核和培训管理",
      icon: <Users className="h-8 w-8" />,
      path: "/personnel-management/personnel-info-management"
    },
    {
      id: "financial-management",
      title: "财务管理",
      description: "财务状况和工资管理",
      icon: <DollarSign className="h-8 w-8" />,
      path: "/financial-management/financial-status-maintenance"
    },
    {
      id: "fixed-assets-management",
      title: "固定资产管理",
      description: "资产信息、维护和设备管理",
      icon: <Database className="h-8 w-8" />,
      path: "/fixed-assets-management/fixed-assets-info"
    },
    {
      id: "energy-management",
      title: "能源管理",
      description: "能源类型和使用情况管理",
      icon: <Zap className="h-8 w-8" />,
      path: "/energy-management/energy-type"
    },
    {
      id: "security-management",
      title: "保卫管理",
      description: "保卫巡检和人员定位管理",
      icon: <Lock className="h-8 w-8" />,
      path: "/security-management/security-dept-inspection"
    },
    {
      id: "office-admin-management",
      title: "办公与行政管理",
      description: "办公室、档案和邮件管理",
      icon: <FileTextIcon className="h-8 w-8" />,
      path: "/office-admin-management/office-management"
    },
    {
      id: "material-supply-chain",
      title: "物资与供应链管理",
      description: "供应、采购和仓库管理",
      icon: <Package className="h-8 w-8" />,
      path: "/material-supply-chain/supply-management"
    },
    {
      id: "task-process-management",
      title: "任务与流程管理",
      description: "任务管理和流程审批",
      icon: <CheckSquare className="h-8 w-8" />,
      path: "/task-process-management/task-management"
    },
    {
      id: "visual-dashboard",
      title: "可视化大屏",
      description: "数据可视化和综合展示",
      icon: <BarChart2 className="h-8 w-8" />,
      path: "/visual-dashboard"
    },
    {
      id: "comprehensive-display",
      title: "综合展示与报表",
      description: "数据统计和报表展示",
      icon: <BarChart className="h-8 w-8" />,
      path: "/comprehensive-display"
    }
  ]

  // 在组件挂载时，从本地存储加载最近访问和常用模块
  useEffect(() => {
    const storedRecent = localStorage.getItem('recent-modules')
    if (storedRecent) {
      setRecentModules(JSON.parse(storedRecent))
    }

    const storedFrequent = localStorage.getItem('frequent-modules')
    if (storedFrequent) {
      setFrequentModules(JSON.parse(storedFrequent))
    }
  }, [])

  // 跳转到指定模块
  const navigateToModule = (path: string, moduleId: string) => {
    try {
      // 更新最近访问的模块
      const updatedRecent = [moduleId, ...recentModules.filter(id => id !== moduleId)].slice(0, 6)
      setRecentModules(updatedRecent)
      localStorage.setItem('recent-modules', JSON.stringify(updatedRecent))

      router.push(path)
    } catch (error) {
      console.error("导航错误:", error)
      toast({
        title: "导航错误",
        description: "无法跳转到指定页面，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 切换模块是否为常用模块
  const toggleFrequent = (e: React.MouseEvent, moduleId: string) => {
    e.stopPropagation()

    let updatedFrequent
    if (frequentModules.includes(moduleId)) {
      updatedFrequent = frequentModules.filter(id => id !== moduleId)
    } else {
      updatedFrequent = [...frequentModules, moduleId]
    }

    setFrequentModules(updatedFrequent)
    localStorage.setItem('frequent-modules', JSON.stringify(updatedFrequent))

    toast({
      title: frequentModules.includes(moduleId) ? "已从常用移除" : "已添加到常用",
      description: frequentModules.includes(moduleId)
        ? "该模块已从常用模块中移除"
        : "该模块已添加到常用模块中",
      variant: "default"
    })
  }

  // 根据当前选择的标签过滤模块
  const filteredModules = useMemo(() => {
    let filtered = [...mainModules]

    // 首先应用标签过滤
    if (activeTab === "frequent") {
      filtered = filtered.filter(m => frequentModules.includes(m.id))
    }
    if (activeTab === "recent") {
      filtered = filtered.filter(m => recentModules.includes(m.id))
    }

    // 然后应用搜索过滤
    if (moduleSearchTerm.trim()) {
      const searchLower = moduleSearchTerm.toLowerCase()
      filtered = filtered.filter(module =>
        module.title.toLowerCase().includes(searchLower) ||
        module.description.toLowerCase().includes(searchLower) ||
        module.id.toLowerCase().includes(searchLower)
      )
    }

    return filtered
  }, [activeTab, frequentModules, recentModules, mainModules, moduleSearchTerm])

  // 修复百分比计算
  const calculateSafetyChecksPercentage = () => {
    return 85 // 假设是85%
  }

  const calculateTasksPercentage = () => {
    return 92 // 假设是92%
  }

  const calculateIssuesPercentage = () => {
    return 78 // 假设是78%
  }

  // 处理通知点击
  const handleNotificationClick = (notification: any) => {
    setSelectedNotification(notification)
    setShowNotificationDialog(true)
  }

  // 标记通知为已读
  const handleMarkAsRead = (e: React.MouseEvent, notificationId: number) => {
    e.stopPropagation()
    alerts.forEach(alert => {
      if (alert.id === notificationId) {
        alert.isRead = true
      }
    })
    toast({
      title: "已标记为已读",
      description: "通知已标记为已读状态",
    })
  }

  // 前往处理通知
  const handleProcessNotification = () => {
    if (selectedNotification && selectedNotification.route) {
      router.push(selectedNotification.route)
      setShowNotificationDialog(false)
    }
  }

  // 过滤通知
  const filteredAlerts = useMemo(() => {
    if (notificationFilter === "unread") {
      return alerts.filter(alert => !alert.isRead)
    }
    return alerts
  }, [alerts, notificationFilter])

  // 加载项目数据
  useEffect(() => {
    // 模拟从localStorage获取项目数据
    const loadProjectData = () => {
      try {
        // 从localStorage获取项目数据
        const projectData = localStorage.getItem("project-data")
        if (projectData) {
          const parsedData = JSON.parse(projectData)

          // 更新项目统计数据
          if (parsedData.projects) {
            setStats(prev => ({
              ...prev,
              projects: parsedData.projects.length || 8,
            }))
          }

          // 更新项目进度数据
          if (parsedData.projects) {
            // 这里可以进一步处理项目数据，提取更多信息
          }
        }
      } catch (error) {
        console.error("加载项目数据出错:", error)
      }
    }

    // 模拟从localStorage获取任务数据
    const loadTaskData = () => {
      try {
        // 从localStorage获取任务数据
        const taskData = localStorage.getItem("task-data")
        if (taskData) {
          const parsedData = JSON.parse(taskData)

          // 更新任务统计数据
          if (parsedData.tasks) {
            setStats(prev => ({
              ...prev,
              tasks: parsedData.tasks.length || 24,
              tasksChange: parsedData.tasksChange || -8,
            }))
          }
        }
      } catch (error) {
        console.error("加载任务数据出错:", error)
      }
    }

    // 更新安全检查统计数据
    const updateSafetyStats = () => {
      // 更新安全检查统计数据
      setStats(prev => ({
        ...prev,
        safetyChecks: safetyStats.completed || 0,
        safetyIssues: safetyStats.totalIssues || 0,
      }))

      // 更新周报数据
      setWeeklyStats([
        { day: '周一', safetyChecks: 85, tasks: 90, issues: 75 },
        { day: '周二', safetyChecks: 88, tasks: 85, issues: 80 },
        { day: '周三', safetyChecks: 92, tasks: 92, issues: 85 },
        { day: '周四', safetyChecks: 90, tasks: 95, issues: 90 },
        { day: '周五', safetyChecks: 95, tasks: 88, issues: 95 },
        { day: '周六', safetyChecks: 85, tasks: 80, issues: 85 },
        { day: '周日', safetyChecks: 80, tasks: 75, issues: 80 },
      ])
    }

    // 加载所有数据
    loadProjectData()
    loadTaskData()
    updateSafetyStats()
  }, [safetyStats])

  // 添加施工现场图片数组
  const constructionImages = [
    "/zhanshi/1.jpg",
    "/zhanshi/2.jpg",
    "/zhanshi/3.jpg",
    "/zhanshi/4.jpg",
    "/zhanshi/5.jpg",
    "/zhanshi/6.jpg",
    "/zhanshi/7.jpg",
    "/zhanshi/8.jpg",
    "/zhanshi/9.jpg",
    "/zhanshi/10.jpg",
  ]

  return (
    <div className={`space-y-4 transition-colors duration-200 ${
      themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"
    }`}>
      {/* 欢迎弹窗 */}
      <AlertDialog open={showWelcomeDialog} onOpenChange={setShowWelcomeDialog}>
        <AlertDialogContent className={`${
          themeMode === "dark"
            ? "bg-[#2c2c2e] border-[#3a3a3c] text-white"
            : "bg-white"
        } max-w-[400px] border shadow-xl animate-scaleUp`}>
          <AlertDialogHeader>
            <div className="flex justify-center mb-4">
              <div className={`w-16 h-16 flex items-center justify-center rounded-full ${
                themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"
              }`}>
                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="60%" height="60%" className={`${
                  themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"
                }`}>
                  <path d="M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-112-119-220.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z" fill="currentColor" />
                </svg>
              </div>
            </div>
            <AlertDialogTitle className="text-center text-2xl">
              登录成功！
            </AlertDialogTitle>
            <AlertDialogDescription className={`text-center ${themeMode === "dark" ? "text-gray-300" : "text-gray-600"}`}>
              <p className="text-lg mb-1">欢迎回来，<span className={`font-medium ${themeMode === "dark" ? "text-blue-300" : "text-blue-600"}`}>{welcomeUsername}</span></p>
              <p>祝您在矿业公司综合管理系统中工作愉快</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="mt-4">
            <AlertDialogAction
              className={`w-full ${
                themeMode === "dark"
                  ? "bg-white text-black hover:bg-gray-200"
                  : "bg-black text-white hover:bg-gray-800"
              }`}
              onClick={handleCloseWelcomeDialog}
            >
              开始使用
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 顶部欢迎区域 - 更现代的设计 */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600/90 to-indigo-600/90 p-6 mb-4 shadow-lg animate-float">
        {/* 添加丝绸效果背景 */}
        <Silk
          speed={3}
          scale={1.5}
          color={themeMode === "dark" ? "#4338CA" : "#6366F1"}
          noiseIntensity={1.2}
          rotation={0.2}
          className="opacity-50"
        />

        {/* 添加背景图片 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-cover bg-center z-0" style={{ backgroundImage: 'url(/menu-bg/huanying.png)', opacity: 0.15 }}></div>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/95 to-indigo-600/95 z-[1] opacity-60"></div>
        </div>

        {/* 添加浮动粒子效果 */}
        <div className="absolute inset-0 overflow-hidden z-[2]">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-white/10"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                width: `${Math.random() * 10 + 2}px`,
                height: `${Math.random() * 10 + 2}px`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${Math.random() * 10 + 15}s`,
              }}
            />
          ))}
        </div>

        <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/20 rounded-full filter blur-3xl animate-pulse z-[2]"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-indigo-500/20 rounded-full filter blur-3xl animate-pulse z-[2]" style={{animationDelay: '2s'}}></div>

        <div className="absolute top-0 left-0 w-full h-full opacity-20 z-[2]">
          <svg
            viewBox="0 0 1000 1000"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full h-full"
          >
            <defs>
              <linearGradient id="b" gradientTransform="rotate(45 0.5 0.5)">
                <stop offset="0%" stopColor="#ffffff" stopOpacity="0.3" />
                <stop offset="100%" stopColor="#ffffff" stopOpacity="0" />
              </linearGradient>
            </defs>
            <path
              d="M0,1000 C200,900 400,650 600,800 C750,900 800,800 1000,800 L1000,1000 L0,1000 Z"
              fill="url(#b)"
              className="animate-wave"
            />
            <path
              d="M0,1000 C150,850 350,700 500,800 C650,900 750,850 1000,700 L1000,1000 L0,1000 Z"
              fill="url(#b)"
              opacity="0.5"
              className="animate-wave-slow"
            />
          </svg>
        </div>

        <div className="relative z-[3] flex flex-col md:flex-row justify-between items-start md:items-center">
          <div className="mb-3 md:mb-0 animate-fadeInUp" style={{animationDelay: '0.3s'}}>
            <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-white mb-1 flex items-center">
              欢迎回来，{user?.username || "用户"}
              <span className="inline-block ml-2 animate-wave-hand">👋</span>
            </h2>
            <p className="text-blue-100 opacity-90 animate-fadeInUp" style={{animationDelay: '0.4s'}}>
              今天是 {new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' })}
            </p>
            <p className="text-blue-100 opacity-75 mt-1 animate-fadeInUp" style={{animationDelay: '0.5s'}}>
              这是您的管理概览，一目了然掌握系统动态
            </p>
          </div>

          <div className="flex items-center gap-2 mb-4">
            <Button
              onClick={() => router.push('/visual-dashboard')}
              className="bg-white/10 hover:bg-white/20 text-white border-0 transition-all duration-300 hover:scale-105 backdrop-blur-sm"
            >
              <BarChart2 className="mr-2 h-4 w-4" />
              可视化大屏
            </Button>
            <Button
              onClick={() => router.push('/task-process-management/task-management')}
              className="bg-white hover:bg-blue-50 text-indigo-700 border-0 transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg"
            >
              <Clock className="mr-2 h-4 w-4" />
              今日任务
            </Button>
          </div>
        </div>

        {/* 添加CSS动画样式 */}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
          }

          @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-20px); }
          }

          @keyframes wave-slow {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(15px); }
          }

          @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }

          @keyframes wave-hand {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(15deg); }
            50% { transform: rotate(0deg); }
            75% { transform: rotate(15deg); }
          }

          @keyframes float-particle {
            0%, 100% { transform: translateY(0) translateX(0); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(0) translateX(20px); }
            75% { transform: translateY(20px) translateX(10px); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.3; }
          }

          .animate-float {
            animation: float 6s ease-in-out infinite;
          }

          .animate-wave {
            animation: wave 8s ease-in-out infinite;
          }

          .animate-wave-slow {
            animation: wave-slow 12s ease-in-out infinite;
          }

          .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
          }

          .animate-wave-hand {
            animation: wave-hand 2s ease-in-out infinite;
            transform-origin: 70% 70%;
            display: inline-block;
          }

          .animate-pulse {
            animation: pulse 4s ease-in-out infinite;
          }

          .absolute {
            animation: float-particle 20s ease-in-out infinite;
          }
        `}</style>
      </div>

      {/* 添加施工现场展示区域 */}
      <div className={`rounded-xl overflow-hidden ${
        themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-[#f5f5f7]"
      }`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h3 className={`text-2xl font-bold ${themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"}`}>
                施工现场实况
              </h3>
              <div className={`px-2 py-1 text-xs rounded-full ${
                themeMode === "dark"
                  ? "bg-blue-500/10 text-blue-400"
                  : "bg-blue-100 text-blue-600"
              }`}>
                实时更新
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className={`${
                themeMode === "dark"
                  ? "text-gray-400 hover:text-white hover:bg-[#2c2c2e]"
                  : "text-gray-500 hover:text-gray-900"
              }`}
              onClick={() => router.push('/visual-dashboard')}
            >
              查看更多
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
          
          <RollingGallery
            autoplay={true}
            pauseOnHover={true}
            images={constructionImages}
          />
        </div>
      </div>

      {/* 添加现代化的统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-2">
        {/* 安全检查卡片 */}
        <div className={`card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 ${
              themeMode === "dark"
            ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
            : "bg-white border border-gray-100 card-glow-light"
        }`}>
          <div className="relative px-6 py-5">
            {/* 微妙的粒子装饰元素 */}
            <div className="absolute top-0 right-0 w-24 h-24 opacity-30">
              <div className="absolute top-6 right-10 w-2 h-2 rounded-full bg-blue-400/30 animate-ping-slow"></div>
              <div className="absolute top-14 right-6 w-1 h-1 rounded-full bg-blue-400/20 animate-ping-slow" style={{animationDelay: '1s'}}></div>
              <div className="absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-blue-500/20 animate-ping-slow" style={{animationDelay: '2s'}}></div>
              </div>

            <div className="flex justify-between items-start">
                <div>
                <p className={`text-sm ${themeMode === "dark" ? "text-blue-300" : "text-blue-600"}`}>
                  安全检查完成率
                </p>
                <h3 className={`text-2xl font-bold mt-1 ${themeMode === "dark" ? "text-white" : "text-gray-900"} animate-counter`}
                  data-target={safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(check => check.status !== "已完成").length)) * 100) : 0}>
                    {safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(check => check.status !== "已完成").length)) * 100) : 0}%
                </h3>
                <p className={`text-xs mt-1 ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                    已完成 {safetyStats.completed}/{safetyStats.completed + safetyChecks.filter(check => check.status !== "已完成").length}
                </p>
                  </div>
              <div className={`p-2 rounded-lg icon-container ${
                themeMode === "dark" ? "bg-blue-500/10" : "bg-blue-50"
              }`}>
                <Shield className={`h-6 w-6 ${themeMode === "dark" ? "text-blue-400" : "text-blue-500"}`} />
                </div>
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-xs mb-1">
                <span className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>进度</span>
                <span className={themeMode === "dark" ? "text-blue-300" : "text-blue-600"}>
                  {safetyStats.completed > 0 ? Math.round((safetyStats.completed / (safetyStats.completed + safetyChecks.filter(check => check.status !== "已完成").length)) * 100) : 0}%
                </span>
              </div>
              <div className="relative h-2 rounded-full overflow-hidden">
                <div className={`absolute inset-0 ${themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"}`}></div>
                <div
                  className="absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate"
                      style={{
                    width: `${safetyStats.completed > 0 ? (safetyStats.completed / (safetyStats.completed + safetyChecks.filter(check => check.status !== "已完成").length)) * 100 : 0}%`,
                    background: themeMode === "dark"
                      ? "linear-gradient(90deg, rgba(37, 99, 235, 0.8) 0%, rgba(59, 130, 246, 0.9) 100%)"
                      : "linear-gradient(90deg, rgba(37, 99, 235, 0.7) 0%, rgba(59, 130, 246, 0.8) 100%)"
                  }}
                >
                  <div className="absolute top-0 right-0 bottom-0 w-20 progress-bar-shine"></div>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs">
              <div className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>
                较上周
                <span className={themeMode === "dark" ? "text-blue-300 ml-1" : "text-blue-600 ml-1"}>
                  +5%
                  <TrendingUp className="h-3 w-3 inline ml-0.5" />
                </span>
              </div>
              <Button
                size="sm"
                variant="ghost"
                className={`text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 ${
                  themeMode === "dark"
                    ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                onClick={() => router.push('/safety-management/safety-check')}
              >
                查看详情
                <ChevronRight className="h-3 w-3 ml-0.5" />
              </Button>
            </div>
              </div>
            </div>

        {/* 任务完成卡片 */}
        <div className={`card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 ${
              themeMode === "dark"
            ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
            : "bg-white border border-gray-100 card-glow-light"
        }`}>
          <div className="relative px-6 py-5">
            {/* 微妙的粒子装饰元素 */}
            <div className="absolute top-0 right-0 w-24 h-24 opacity-30">
              <div className="absolute top-6 right-10 w-2 h-2 rounded-full bg-green-400/30 animate-ping-slow"></div>
              <div className="absolute top-14 right-6 w-1 h-1 rounded-full bg-green-400/20 animate-ping-slow" style={{animationDelay: '1.5s'}}></div>
              <div className="absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-green-500/20 animate-ping-slow" style={{animationDelay: '0.5s'}}></div>
              </div>

            <div className="flex justify-between items-start">
                <div>
                <p className={`text-sm ${themeMode === "dark" ? "text-green-300" : "text-green-600"}`}>
                  任务完成率
                </p>
                <h3 className={`text-2xl font-bold mt-1 ${themeMode === "dark" ? "text-white" : "text-gray-900"} animate-counter`}
                  data-target={calculateTasksPercentage()}>
                    {calculateTasksPercentage()}%
                </h3>
                <p className={`text-xs mt-1 ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                    已完成 {Math.round(stats.tasks * calculateTasksPercentage() / 100)}/{stats.tasks}
                </p>
                  </div>
              <div className={`p-2 rounded-lg icon-container ${
                themeMode === "dark" ? "bg-green-500/10" : "bg-green-50"
              }`}>
                <CheckSquare className={`h-6 w-6 ${themeMode === "dark" ? "text-green-400" : "text-green-500"}`} />
                </div>
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-xs mb-1">
                <span className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>进度</span>
                <span className={themeMode === "dark" ? "text-green-300" : "text-green-600"}>
                  {calculateTasksPercentage()}%
                </span>
              </div>
              <div className="relative h-2 rounded-full overflow-hidden">
                <div className={`absolute inset-0 ${themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"}`}></div>
                <div
                  className="absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate"
                      style={{
                    width: `${calculateTasksPercentage()}%`,
                    background: themeMode === "dark"
                      ? "linear-gradient(90deg, rgba(22, 163, 74, 0.8) 0%, rgba(34, 197, 94, 0.9) 100%)"
                      : "linear-gradient(90deg, rgba(22, 163, 74, 0.7) 0%, rgba(34, 197, 94, 0.8) 100%)"
                  }}
                >
                  <div className="absolute top-0 right-0 bottom-0 w-20 progress-bar-shine"></div>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs">
              <div className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>
                较上周
                <span className={stats.tasksChange >= 0 ? (themeMode === "dark" ? "text-green-300 ml-1" : "text-green-600 ml-1") : (themeMode === "dark" ? "text-red-300 ml-1" : "text-red-600 ml-1")}>
                  {stats.tasksChange >= 0 ? `+${stats.tasksChange}%` : `${stats.tasksChange}%`}
                  {stats.tasksChange >= 0 ? <TrendingUp className="h-3 w-3 inline ml-0.5" /> : <TrendingDown className="h-3 w-3 inline ml-0.5" />}
                </span>
              </div>
              <Button
                size="sm"
                variant="ghost"
                className={`text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 ${
                  themeMode === "dark"
                    ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                onClick={() => router.push('/task-process-management/task-management')}
              >
                查看详情
                <ChevronRight className="h-3 w-3 ml-0.5" />
              </Button>
            </div>
              </div>
            </div>

        {/* 安全隐患解决卡片 */}
        <div className={`card-with-hover rounded-xl shadow-sm overflow-hidden transition-all duration-500 hover:z-10 ${
              themeMode === "dark"
            ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] card-glow-dark"
            : "bg-white border border-gray-100 card-glow-light"
        }`}>
          <div className="relative px-6 py-5">
            {/* 微妙的粒子装饰元素 */}
            <div className="absolute top-0 right-0 w-24 h-24 opacity-30">
              <div className="absolute top-6 right-10 w-2 h-2 rounded-full bg-amber-400/30 animate-ping-slow"></div>
              <div className="absolute top-14 right-6 w-1 h-1 rounded-full bg-amber-400/20 animate-ping-slow" style={{animationDelay: '0.7s'}}></div>
              <div className="absolute top-10 right-14 w-1.5 h-1.5 rounded-full bg-amber-500/20 animate-ping-slow" style={{animationDelay: '1.7s'}}></div>
              </div>

            <div className="flex justify-between items-start">
                <div>
                <p className={`text-sm ${themeMode === "dark" ? "text-amber-300" : "text-amber-600"}`}>
                  安全隐患解决率
                </p>
                <h3 className={`text-2xl font-bold mt-1 ${themeMode === "dark" ? "text-white" : "text-gray-900"} animate-counter`}
                  data-target={calculateIssuesPercentage()}>
                    {calculateIssuesPercentage()}%
                </h3>
                <p className={`text-xs mt-1 ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                    已解决 {Math.round(safetyStats.totalIssues * calculateIssuesPercentage() / 100)}/{safetyStats.totalIssues || 1}
                </p>
                  </div>
              <div className={`p-2 rounded-lg icon-container ${
                themeMode === "dark" ? "bg-amber-500/10" : "bg-amber-50"
              }`}>
                <AlertCircle className={`h-6 w-6 ${themeMode === "dark" ? "text-amber-400" : "text-amber-500"}`} />
                </div>
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-xs mb-1">
                <span className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>进度</span>
                <span className={themeMode === "dark" ? "text-amber-300" : "text-amber-600"}>
                  {calculateIssuesPercentage()}%
                </span>
              </div>
              <div className="relative h-2 rounded-full overflow-hidden">
                <div className={`absolute inset-0 ${themeMode === "dark" ? "bg-[#3a3a3c]" : "bg-gray-100"}`}></div>
                <div
                  className="absolute left-0 top-0 bottom-0 rounded-full progress-bar-animate"
                      style={{
                    width: `${calculateIssuesPercentage()}%`,
                    background: themeMode === "dark"
                      ? "linear-gradient(90deg, rgba(217, 119, 6, 0.8) 0%, rgba(245, 158, 11, 0.9) 100%)"
                      : "linear-gradient(90deg, rgba(217, 119, 6, 0.7) 0%, rgba(245, 158, 11, 0.8) 100%)"
                  }}
                >
                  <div className="absolute top-0 right-0 bottom-0 w-20 progress-bar-shine"></div>
                </div>
              </div>
              </div>

            <div className="mt-4 pt-4 border-t border-dashed flex items-center justify-between text-xs">
              <div className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>
                今日新增
                <span className={themeMode === "dark" ? "text-amber-300 ml-1" : "text-amber-600 ml-1"}>
                  3
                  <AlertTriangle className="h-3 w-3 inline ml-0.5" />
                </span>
              </div>
              <Button
                size="sm"
                variant="ghost"
                className={`text-xs px-2 h-7 transition-all duration-300 hover:translate-x-1 ${
                  themeMode === "dark"
                    ? "text-gray-300 hover:text-white hover:bg-[#3a3a3c]"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                onClick={() => router.push('/safety-management/safety-hazard-management')}
              >
                查看详情
                <ChevronRight className="h-3 w-3 ml-0.5" />
              </Button>
            </div>
            </div>
          </div>

        {/* 为卡片添加动画样式，更新进度条相关的动画 */}
        <style jsx global>{`
          @keyframes ping-slow {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(2); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
          }

          @keyframes progress-animation {
            0% { width: 0%; }
            100% { width: var(--target-width); }
          }

          @keyframes float-subtle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
          }

          @keyframes shine {
            0% { transform: translateX(-100%) rotate(20deg); }
            100% { transform: translateX(300%) rotate(20deg); }
          }

          @keyframes pulse-scale {
            0%, 100% { transform: scaleX(1); }
            50% { transform: scaleX(1.02); }
          }

          .animate-ping-slow {
            animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          .progress-bar-animate {
            animation: pulse-scale 2s ease-in-out infinite;
            transform-origin: left center;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
            transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
          }

          .progress-bar-shine {
            background: linear-gradient(
              90deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.4) 50%,
              rgba(255, 255, 255, 0) 100%
            );
            transform: translateX(-100%) rotate(20deg);
            animation: shine 3s ease-in-out infinite;
            animation-delay: 1s;
          }

          .card-with-hover {
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          }

          .card-with-hover:hover {
            transform: translateY(-8px);
          }

          .card-with-hover:hover .progress-bar-animate {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
          }

          .icon-container {
            transition: all 0.3s ease;
          }

          .card-with-hover:hover .icon-container {
            transform: scale(1.1) rotate(5deg);
          }

          .card-glow-dark:hover {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
          }

          .card-glow-light:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
          }
        `}</style>
      </div>

      {/* 提醒信息卡片 */}
      <div className={`rounded-xl p-5 mb-6 shadow-sm ${
            themeMode === "dark"
          ? "bg-gradient-to-br from-amber-500/10 to-red-500/10 border border-amber-500/20"
          : "bg-gradient-to-r from-amber-50 to-amber-100/50 border border-amber-100"
      }`}>
        <div className="flex">
          <div className={`rounded-full p-2 ${
            themeMode === "dark" ? "bg-amber-500/20" : "bg-amber-200/50"
              }`}>
                <AlertTriangle className={`h-5 w-5 ${
              themeMode === "dark" ? "text-amber-400" : "text-amber-600"
                }`} />
              </div>
          <div className="ml-4 flex-1">
                <div className="flex justify-between items-start">
                  <div>
                <h3 className={`font-semibold ${themeMode === "dark" ? "text-white" : "text-gray-900"}`}>
                  安全提醒
                </h3>
                <p className={`mt-1 text-sm ${themeMode === "dark" ? "text-gray-300" : "text-gray-600"}`}>
                  矿区B2安全巡检计划即将到期，距离截止日期还有 <span className={`font-medium ${themeMode === "dark" ? "text-amber-300" : "text-amber-600"}`}>2 天</span>
                    </p>
                  </div>
                  <Button
                    size="sm"
                variant="outline"
                className={`ml-4 ${
                      themeMode === "dark"
                    ? "border-amber-500/30 bg-amber-500/10 hover:bg-amber-500/20 text-amber-400"
                    : "border-amber-200 bg-amber-100/50 hover:bg-amber-100 text-amber-700"
                    }`}
                onClick={() => router.push('/safety-management/safety-check')}
                  >
                立即处理
                  </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 添加智能洞察和分析入口区域 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
        {/* 智能分析入口卡片 - 占据右侧1列 */}
        <div className="h-full lg:col-span-4">
          <div
            className={`${styles.analyticsCard} group relative overflow-hidden hover:shadow-lg transition-all duration-300 ease-in-out p-4`}
            onClick={() => router.push('/analytics-center')}
          >
            {/* 装饰性背景图形 */}
            <div className="absolute -right-6 -top-6 w-24 h-24 bg-blue-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-500"></div>
            <div className="absolute right-10 bottom-10 w-16 h-16 bg-indigo-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-700 delay-100"></div>
            <div className="absolute left-20 bottom-6 w-12 h-12 bg-purple-400/20 rounded-full blur-xl group-hover:scale-150 transition-all duration-700 delay-200"></div>

            <div className={`${styles.cardContent} z-10 relative py-3`}>
              <div
                className={`${styles.cardIconContainer} w-14 h-14 rounded-2xl overflow-hidden relative group-hover:scale-110 transition-all`}
                style={{
                  background: 'linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%)',
                  boxShadow: '0 10px 15px -3px rgba(79, 70, 229, 0.3)'
                }}
              >
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <ChartBarIcon className="h-7 w-7 text-white" />
              </div>
              <div className={`${styles.cardInfo} ml-4 z-10`}>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-1 group-hover:translate-x-1 transition-transform">智能分析中心</h3>
                <p className="text-sm text-gray-500 dark:text-gray-300 mb-3 group-hover:translate-x-1 transition-transform duration-300 delay-75">
                  数据驱动洞察和智能分析平台
                </p>

                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div className="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-2 transform group-hover:translate-y-[-3px] transition-all duration-300">
                    <div className="flex items-center space-x-1.5 mb-1">
                      <LineChart className="h-3.5 w-3.5 text-indigo-500 dark:text-indigo-400" />
                      <span className="text-[11px] font-medium text-indigo-700 dark:text-indigo-300">趋势分析</span>
                    </div>
                    <div className="text-xs text-indigo-800 dark:text-indigo-200 font-semibold">
                      新增 5 项
                    </div>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-2 transform group-hover:translate-y-[-3px] transition-all duration-300 delay-75">
                    <div className="flex items-center space-x-1.5 mb-1">
                      <AlertTriangle className="h-3.5 w-3.5 text-purple-500 dark:text-purple-400" />
                      <span className="text-[11px] font-medium text-purple-700 dark:text-purple-300">异常检测</span>
                    </div>
                    <div className="text-xs text-purple-800 dark:text-purple-200 font-semibold">
                      3 个待处理
                    </div>
                  </div>
                </div>

                <div className="mt-2 flex items-center text-indigo-600 dark:text-indigo-400 group-hover:translate-x-1 transition-transform duration-500">
                  <span className="text-xs font-medium">查看分析中心</span>
                  <ArrowRightIcon className="h-3.5 w-3.5 ml-1.5 transform group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </div>

            {/* 底部装饰线 */}
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></div>
          </div>
        </div>
      </div>

      {/* 活动与通知区域 + 工程进度 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* 左侧：通知和活动区域 */}
        <div className={`lg:col-span-2 border ${
          themeMode === "dark"
            ? "bg-[#2c2c2e] border-[#3a3a3c]"
            : "bg-white border-gray-100"
          } rounded-xl overflow-hidden`}>
          <div className="flex border-b items-center px-4 py-2 justify-between">
            <div className="flex items-center gap-2">
              <BellIcon className={`h-4 w-4 ${themeMode === "dark" ? "text-blue-400" : "text-blue-500"}`} />
              <h3 className={`font-medium ${themeMode === "dark" ? "text-white" : "text-gray-900"}`}>通知与活动</h3>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                className={`text-xs px-2 h-7 rounded ${
                  notificationFilter === "all"
                    ? (themeMode === "dark" ? "bg-[#3a3a3c] text-white" : "bg-gray-100")
                    : (themeMode === "dark" ? "hover:bg-[#3a3a3c] text-white" : "hover:bg-gray-100")
                }`}
                onClick={() => setNotificationFilter("all")}
              >
                全部
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`text-xs px-2 h-7 rounded ${
                  notificationFilter === "unread"
                    ? (themeMode === "dark" ? "bg-[#3a3a3c] text-white" : "bg-gray-100")
                    : (themeMode === "dark" ? "hover:bg-[#3a3a3c] text-white" : "hover:bg-gray-100")
                }`}
                onClick={() => setNotificationFilter("unread")}
              >
                未读
              </Button>
            </div>
          </div>
          <div className={`max-h-[350px] overflow-y-auto ${
            themeMode === "dark" ? "scrollbar-thin scrollbar-thumb-gray-600" : "scrollbar-thin scrollbar-thumb-gray-300"
          }`}>
            {filteredAlerts.concat([
              {
                id: 4,
                type: 'task',
                title: '新的任务分配',
                content: '您被指派为"月度安全检查报告"的负责人',
                time: '昨天',
                priority: 'normal',
                isRead: false,
                route: '/task-process-management/task-management'
              },
              {
                id: 5,
                type: 'approval',
                title: '审批请求',
                content: '设备维护计划需要您的审核',
                time: '2天前',
                priority: 'medium',
                isRead: true,
                route: '/task-process-management/process-approval'
              },
              {
                id: 6,
                type: 'meeting',
                title: '会议提醒',
                content: '安全生产月度会议将于明天上午10:00举行',
                time: '昨天',
                priority: 'normal',
                isRead: false,
                route: '/office-admin-management/office-management'
              }
            ]).map((alert) => (
              <div
                key={alert.id}
                className={`px-4 py-3 border-b ${
                  themeMode === "dark" ? "border-[#3a3a3c] hover:bg-[#3a3a3c]/50" : "border-gray-100 hover:bg-gray-50"
                } transition-colors cursor-pointer ${!alert.isRead ? (themeMode === "dark" ? "bg-blue-900/10" : "bg-blue-50/50") : ""}`}
                onClick={() => handleNotificationClick(alert)}
              >
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-full flex-shrink-0 ${
                    alert.type === 'warning'
                      ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-500"
                      : alert.type === 'info'
                      ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-500"
                      : alert.type === 'success'
                      ? themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-500"
                      : alert.type === 'task'
                      ? themeMode === "dark" ? "bg-purple-500/20 text-purple-400" : "bg-purple-100 text-purple-500"
                      : alert.type === 'approval'
                      ? themeMode === "dark" ? "bg-indigo-500/20 text-indigo-400" : "bg-indigo-100 text-indigo-500"
                      : themeMode === "dark" ? "bg-gray-500/20 text-gray-400" : "bg-gray-100 text-gray-500"
                  }`}>
                    {alert.type === 'warning' && <AlertTriangle className="h-5 w-5" />}
                    {alert.type === 'info' && <Info className="h-5 w-5" />}
                    {alert.type === 'success' && <CheckCircle className="h-5 w-5" />}
                    {alert.type === 'task' && <Clipboard className="h-5 w-5" />}
                    {alert.type === 'approval' && <FileText className="h-5 w-5" />}
                    {alert.type === 'meeting' && <CalendarIcon className="h-5 w-5" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className={`font-medium truncate ${themeMode === "dark" ? "text-white" : "text-gray-900"}`}>
                        {alert.title}
                        {!alert.isRead && (
                          <span className={`ml-2 inline-block w-2 h-2 rounded-full ${
                            themeMode === "dark" ? "bg-blue-400" : "bg-blue-500"
                          }`}></span>
                        )}
                      </h4>
                      <div className={`flex items-center text-xs ${
                        themeMode === "dark" ? "text-gray-500" : "text-gray-500"
                      }`}>
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {alert.time}
                      </div>
                    </div>
                    <p className={`text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-600"}`}>
                      {alert.content}
                    </p>
                    {!alert.isRead && (
                      <div className="flex justify-end mt-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`text-xs h-6 rounded-full ${
                            themeMode === "dark"
                              ? "hover:bg-[#3a3a3c] text-gray-300"
                              : "hover:bg-gray-100 text-gray-600"
                          }`}
                          onClick={(e) => handleMarkAsRead(e, alert.id)}
                        >
                          标记为已读
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="p-3 flex justify-center">
            <Button
              variant="outline"
              size="sm"
              className={`rounded-full shadow-sm text-xs ${
                themeMode === "dark"
                  ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                  : "bg-gray-50 text-[#1d1d1f] border-gray-200"
              }`}
            >
              查看全部通知
              <ChevronRight className="ml-1 h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        {/* 右侧：工程进度 */}
        <Card className={`border ${
          themeMode === "dark"
            ? "bg-[#2c2c2e] border-[#3a3a3c]"
            : "bg-white border-gray-100"
        }`}>
          <CardHeader className="flex flex-row items-center justify-between py-3">
            <CardTitle className={themeMode === "dark" ? "text-white" : ""}>工程进度</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 rounded-full ${
                themeMode === "dark" ? "hover:bg-[#3a3a3c]" : "hover:bg-gray-100"
              }`}
            >
              <Layers className={`h-4 w-4 ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`} />
              <span className="sr-only">查看选项</span>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "矿区A3开发项目", progress: 80, status: "正常" },
                { name: "设备更新计划", progress: 45, status: "延迟" },
                { name: "安全系统升级", progress: 90, status: "超前" },
                { name: "风险评估计划", progress: 65, status: "正常" },
              ].map((project, i) => (
                <div key={i} className={`space-y-2 ${
                  i < 3 ? `pb-4 mb-1 border-b ${themeMode === "dark" ? "border-[#3a3a3c]" : "border-gray-100"}` : ""
                }`}>
                  <div className="flex items-center justify-between">
                    <div className={`text-sm font-medium ${themeMode === "dark" ? "text-white" : ""}`}>{project.name}</div>
                    <div className={`text-xs px-2 py-0.5 rounded-full ${
                      project.status === "正常"
                        ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-600"
                        : project.status === "延迟"
                        ? themeMode === "dark" ? "bg-red-500/20 text-red-400" : "bg-red-100 text-red-600"
                        : themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-600"
                    }`}>
                      {project.status}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress
                      value={project.progress}
                      className={`h-1.5 flex-1 ${themeMode === "dark" ? "bg-[#48484a]" : "bg-gray-100"}`}
                      indicatorClassName={project.status === "正常"
                        ? "bg-blue-500"
                        : project.status === "延迟"
                          ? "bg-red-500"
                          : "bg-green-500"
                      }
                    />
                    <div className={`text-xs font-medium ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                      {project.progress}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              size="sm"
              className={`w-full rounded-full ${
                themeMode === "dark"
                  ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                  : "bg-gray-50 text-[#1d1d1f] border-gray-200"
              }`}
              onClick={() => navigateToModule("/project-management/project-homepage", "project-management")}
            >
              查看全部项目
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* 快速访问模块 - 优化版本 */}
      <div className={`rounded-xl ${
        themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-[#f5f5f7]"
      }`}>
        <div className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
            <div className="flex items-center">
              <h3 className={`text-xl font-bold mr-2 ${themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"}`}>
                快速访问
              </h3>
              <div className={`text-xs px-2 py-1 rounded-full ${
                themeMode === "dark"
                  ? "bg-[#2c2c2e] text-gray-400"
                  : "bg-white/80 text-gray-500 border border-gray-200"
              }`}>
                {mainModules.length} 个模块
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className={`absolute left-3 top-2.5 h-4 w-4 ${
                  themeMode === "dark" ? "text-gray-500" : "text-gray-400"
                }`} />
                <Input
                  placeholder="搜索模块..."
                  className={`w-full rounded-full pl-9 ${
                    themeMode === "dark"
                      ? "bg-[#2c2c2e] border-[#3a3a3c] text-white placeholder:text-gray-500"
                      : "bg-white border-gray-200 placeholder:text-gray-400"
                  }`}
                  value={moduleSearchTerm}
                  onChange={(e) => setModuleSearchTerm(e.target.value)}
                />
              </div>
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-auto"
              >
                <TabsList className={themeMode === "dark"
                  ? "bg-[#2c2c2e]"
                  : "bg-white/90 border border-gray-200 shadow-sm"
                }>
                  <TabsTrigger
                    value="all"
                    className={themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : ""}
                  >全部</TabsTrigger>
                  <TabsTrigger
                    value="frequent"
                    className={themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : ""}
                  >常用</TabsTrigger>
                  <TabsTrigger
                    value="recent"
                    className={themeMode === "dark" ? "data-[state=active]:bg-[#3a3a3c] data-[state=active]:text-white" : ""}
                  >最近</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3 mb-4">
            {mainModules
              .filter((module) => moduleSearchTerm === "" ||
                module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
                module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase()))
              .map((module) => (
                <div
                  key={module.id}
                  className={`relative overflow-hidden ${
                    themeMode === "dark"
                      ? "bg-gradient-to-br from-[#2c2c2e] to-[#1a1a1c] border border-[#3a3a3c] shadow-lg shadow-[#000]/10"
                      : "bg-white border border-gray-100 shadow-md shadow-gray-100/50"
                  } rounded-xl transition-all duration-300 transform hover:translate-y-[-4px] hover:shadow-xl ${
                    themeMode === "dark" ? "hover:shadow-[#000]/20" : "hover:shadow-gray-200/70"
                  } group cursor-pointer`}
                  onClick={() => navigateToModule(module.path, module.id)}
                >
                  {/* 背景装饰元素 */}
                  <div className="absolute -right-8 -bottom-8 w-32 h-32 rounded-full opacity-10 transition-all duration-300 group-hover:scale-125 group-hover:opacity-20"
                    style={{
                      background:
                        module.id === "safety-management" ? "radial-gradient(circle, #3b82f6 0%, #1d4ed8 100%)" :
                        module.id === "project-management" ? "radial-gradient(circle, #f59e0b 0%, #d97706 100%)" :
                        module.id === "personnel-management" ? "radial-gradient(circle, #10b981 0%, #059669 100%)" :
                        module.id === "financial-management" ? "radial-gradient(circle, #6366f1 0%, #4f46e5 100%)" :
                        module.id === "fixed-assets-management" ? "radial-gradient(circle, #8b5cf6 0%, #7c3aed 100%)" :
                        module.id === "energy-management" ? "radial-gradient(circle, #ec4899 0%, #db2777 100%)" :
                        module.id === "security-management" ? "radial-gradient(circle, #f43f5e 0%, #e11d48 100%)" :
                        module.id === "office-admin-management" ? "radial-gradient(circle, #0ea5e9 0%, #0284c7 100%)" :
                        module.id === "material-supply-chain" ? "radial-gradient(circle, #84cc16 0%, #65a30d 100%)" :
                        module.id === "task-process-management" ? "radial-gradient(circle, #14b8a6 0%, #0f766e 100%)" :
                        module.id === "comprehensive-display" ? "radial-gradient(circle, #a855f7 0%, #9333ea 100%)" :
                        "radial-gradient(circle, #475569 0%, #334155 100%)"
                    }}
                  ></div>

                  <div className="p-6 relative z-10">
                    <div className="flex justify-between items-start">
                      <div
                        className={`p-3 rounded-lg mb-4 transition-all duration-300 group-hover:scale-110 ${
                          themeMode === "dark" ? "bg-[#000]/20" : "bg-gray-100"
                        }`}
                        style={{
                          color:
                            module.id === "safety-management" ? "#3b82f6" :
                            module.id === "project-management" ? "#f59e0b" :
                            module.id === "personnel-management" ? "#10b981" :
                            module.id === "financial-management" ? "#6366f1" :
                            module.id === "fixed-assets-management" ? "#8b5cf6" :
                            module.id === "energy-management" ? "#ec4899" :
                            module.id === "security-management" ? "#f43f5e" :
                            module.id === "office-admin-management" ? "#0ea5e9" :
                            module.id === "material-supply-chain" ? "#84cc16" :
                            module.id === "task-process-management" ? "#14b8a6" :
                            module.id === "comprehensive-display" ? "#a855f7" :
                            "#475569"
                        }}
                      >
                        {module.icon}
                      </div>
                      <div className={`${
                        frequentModules.includes(module.id) ? "visible" : "invisible group-hover:visible"
                      } transition-all duration-200`}>
                        <Star
                          className={`h-5 w-5 cursor-pointer transition-transform hover:scale-110 ${
                            frequentModules.includes(module.id)
                              ? `text-yellow-400 fill-yellow-400`
                              : `${themeMode === "dark" ? "text-gray-500" : "text-gray-300"}`
                          }`}
                          onClick={(e) => toggleFrequent(e, module.id)}
                        />
                    </div>
                    </div>
                    <div>
                      <h3 className={`text-lg font-bold mb-1 group-hover:underline decoration-2 underline-offset-4 ${
                        themeMode === "dark" ? "text-white" : "text-gray-900"
                      }`} style={{
                        textDecorationColor:
                          module.id === "safety-management" ? "#3b82f6" :
                          module.id === "project-management" ? "#f59e0b" :
                          module.id === "personnel-management" ? "#10b981" :
                          module.id === "financial-management" ? "#6366f1" :
                          module.id === "fixed-assets-management" ? "#8b5cf6" :
                          module.id === "energy-management" ? "#ec4899" :
                          module.id === "security-management" ? "#f43f5e" :
                          module.id === "office-admin-management" ? "#0ea5e9" :
                          module.id === "material-supply-chain" ? "#84cc16" :
                          module.id === "task-process-management" ? "#14b8a6" :
                          module.id === "comprehensive-display" ? "#a855f7" :
                          "#475569"
                      }}>
                      {module.title}
                      </h3>
                      <p className={`text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                      {module.description}
                      </p>
                    </div>
                  </div>

                  <div className={`absolute bottom-0 left-0 w-full h-1 transition-all duration-300 group-hover:h-1.5 ${
                    themeMode === "dark" ? "opacity-70 group-hover:opacity-100" : "opacity-90 group-hover:opacity-100"
                  }`}
                    style={{
                      background:
                        module.id === "safety-management" ? "#3b82f6" :
                        module.id === "project-management" ? "#f59e0b" :
                        module.id === "personnel-management" ? "#10b981" :
                        module.id === "financial-management" ? "#6366f1" :
                        module.id === "fixed-assets-management" ? "#8b5cf6" :
                        module.id === "energy-management" ? "#ec4899" :
                        module.id === "security-management" ? "#f43f5e" :
                        module.id === "office-admin-management" ? "#0ea5e9" :
                        module.id === "material-supply-chain" ? "#84cc16" :
                        module.id === "task-process-management" ? "#14b8a6" :
                        module.id === "comprehensive-display" ? "#a855f7" :
                        "#475569"
                      }}
                  ></div>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* 底部模块搜索优化 */}
      {moduleSearchTerm !== "" && (
        <div className={`fixed inset-0 bg-opacity-90 z-50 flex items-center justify-center p-6 ${
          themeMode === "dark" ? "bg-[#1c1c1e]" : "bg-white"
        }`}>
          <div className="w-full max-w-4xl">
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-2xl font-bold ${themeMode === "dark" ? "text-white" : "text-gray-900"}`}>
                搜索模块: <span className="text-blue-500">"{moduleSearchTerm}"</span>
              </h2>
                    <Button
                      variant="ghost"
                size="icon"
                onClick={() => setModuleSearchTerm("")}
                className={themeMode === "dark" ? "text-gray-300 hover:text-white" : "text-gray-600 hover:text-gray-900"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
              </Button>
            </div>

            {mainModules.filter(module =>
              module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
              module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase())
            ).length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mainModules
                  .filter(module =>
                    module.title.toLowerCase().includes(moduleSearchTerm.toLowerCase()) ||
                    module.description.toLowerCase().includes(moduleSearchTerm.toLowerCase())
                  )
                  .map(module => (
                    <div
                      key={module.id}
                      className={`p-4 rounded-lg cursor-pointer transition-all duration-200 hover:translate-y-[-2px] ${
                        themeMode === "dark"
                          ? "bg-[#2c2c2e] hover:bg-[#3a3a3c]"
                          : "bg-gray-50 hover:bg-gray-100"
                      }`}
                      onClick={() => {
                        navigateToModule(module.path, module.id);
                        setModuleSearchTerm("");
                      }}
                    >
                      <div className="flex items-center">
                        <div className="p-2 rounded-md mr-3" style={{
                          color:
                            module.id === "safety-management" ? "#3b82f6" :
                            module.id === "project-management" ? "#f59e0b" :
                            module.id === "personnel-management" ? "#10b981" :
                            module.id === "financial-management" ? "#6366f1" :
                            module.id === "fixed-assets-management" ? "#8b5cf6" :
                            module.id === "energy-management" ? "#ec4899" :
                            module.id === "security-management" ? "#f43f5e" :
                            module.id === "office-admin-management" ? "#0ea5e9" :
                            module.id === "material-supply-chain" ? "#84cc16" :
                            module.id === "task-process-management" ? "#14b8a6" :
                            module.id === "comprehensive-display" ? "#a855f7" :
                            "#475569",
                          background: themeMode === "dark" ? "rgba(0,0,0,0.2)" : "rgba(0,0,0,0.05)"
                        }}>
                          {module.icon}
                        </div>
                        <div>
                          <h3 className={`font-semibold ${themeMode === "dark" ? "text-white" : "text-gray-900"}`}>
                            {module.title}
                          </h3>
                          <p className={`text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                            {module.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                }
              </div>
            ) : (
              <div className={`text-center py-12 ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
                <div className="flex justify-center mb-4">
                  <Search className="h-12 w-12 opacity-30" />
                </div>
                <h3 className="text-lg font-medium mb-2">未找到匹配的模块</h3>
                <p>请尝试其他搜索关键词</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 通知详情弹窗 */}
      <AlertDialog open={showNotificationDialog} onOpenChange={setShowNotificationDialog}>
        <AlertDialogContent className={`${
          themeMode === "dark"
            ? "bg-[#2c2c2e] border-[#3a3a3c] text-white"
            : "bg-white"
        } max-w-[500px] border shadow-xl`}>
          <AlertDialogHeader>
            <div className="flex items-center gap-3 mb-4">
              {selectedNotification && (
                <div className={`p-2 rounded-full ${
                  selectedNotification.type === 'warning'
                    ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-500"
                    : selectedNotification.type === 'info'
                    ? themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-500"
                    : selectedNotification.type === 'success'
                    ? themeMode === "dark" ? "bg-green-500/20 text-green-400" : "bg-green-100 text-green-500"
                    : selectedNotification.type === 'task'
                    ? themeMode === "dark" ? "bg-purple-500/20 text-purple-400" : "bg-purple-100 text-purple-500"
                    : selectedNotification.type === 'approval'
                    ? themeMode === "dark" ? "bg-indigo-500/20 text-indigo-400" : "bg-indigo-100 text-indigo-500"
                    : themeMode === "dark" ? "bg-gray-500/20 text-gray-400" : "bg-gray-100 text-gray-500"
                }`}>
                  {selectedNotification.type === 'warning' && <AlertTriangle className="h-5 w-5" />}
                  {selectedNotification.type === 'info' && <Info className="h-5 w-5" />}
                  {selectedNotification.type === 'success' && <CheckCircle className="h-5 w-5" />}
                  {selectedNotification.type === 'task' && <Clipboard className="h-5 w-5" />}
                  {selectedNotification.type === 'approval' && <FileText className="h-5 w-5" />}
                  {selectedNotification.type === 'meeting' && <CalendarIcon className="h-5 w-5" />}
                </div>
              )}
              <AlertDialogTitle className="text-xl">
                {selectedNotification?.title}
              </AlertDialogTitle>
            </div>
            <div className="flex justify-between items-center text-sm mb-4">
              <span className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>
                {selectedNotification?.time}
              </span>
              <span className={`px-2 py-0.5 rounded-full text-xs ${
                selectedNotification?.priority === 'high'
                  ? themeMode === "dark" ? "bg-red-500/20 text-red-400" : "bg-red-100 text-red-600"
                  : selectedNotification?.priority === 'medium'
                  ? themeMode === "dark" ? "bg-amber-500/20 text-amber-400" : "bg-amber-100 text-amber-600"
                  : themeMode === "dark" ? "bg-blue-500/20 text-blue-400" : "bg-blue-100 text-blue-600"
              }`}>
                {selectedNotification?.priority === 'high' ? '紧急' :
                 selectedNotification?.priority === 'medium' ? '重要' : '一般'}
              </span>
            </div>
            <AlertDialogDescription className={`text-base ${themeMode === "dark" ? "text-gray-300" : "text-gray-600"}`}>
              {selectedNotification?.content}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowNotificationDialog(false)}
              className={`${
                themeMode === "dark"
                  ? "bg-[#3a3a3c] text-white border-[#48484a] hover:bg-[#48484a]"
                  : "bg-gray-50 text-[#1d1d1f] border-gray-200"
              }`}
            >
              关闭
            </Button>
            <Button
              onClick={handleProcessNotification}
              className={`${
                themeMode === "dark"
                  ? "bg-white text-black hover:bg-gray-200"
                  : "bg-black text-white hover:bg-gray-800"
              }`}
            >
              去处理
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}