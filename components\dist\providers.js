"use client";
"use strict";
exports.__esModule = true;
exports.Providers = void 0;
var theme_context_1 = require("@/contexts/theme-context");
var tooltip_1 = require("@/components/ui/tooltip");
var auth_context_1 = require("@/contexts/auth-context");
function Providers(_a) {
    var children = _a.children;
    return (React.createElement(theme_context_1.Theme<PERSON><PERSON>ider, null,
        React.createElement(auth_context_1.AuthProvider, null,
            React.createElement(tooltip_1.TooltipP<PERSON>ider, null, children))));
}
exports.Providers = Providers;
