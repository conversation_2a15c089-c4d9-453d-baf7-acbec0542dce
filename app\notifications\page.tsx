"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/tabs"
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Clock, 
  Trash2, 
  CheckSquare, 
  Bell<PERSON>ff,
  Bell
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

// 通知类型定义
interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
  link?: string;
  actionText?: string;
  createdAt: Date;
}

export default function NotificationsPage() {
  // 示例通知数据
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: '系统更新',
      message: '系统将于今晚22:00进行维护升级，预计持续2小时，请提前保存您的工作。',
      time: '10分钟前',
      read: false,
      type: 'info',
      createdAt: new Date(Date.now() - 10 * 60 * 1000)
    },
    {
      id: 2,
      title: '安全检查',
      message: '您有一项安全检查任务已完成，请前往查看检查结果并确认。',
      time: '30分钟前',
      read: false,
      type: 'success',
      link: '/safety-management/safety-check',
      actionText: '查看详情',
      createdAt: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 3,
      title: '设备预警',
      message: '有3台设备需要维护，请尽快安排维护工作以确保设备正常运行。',
      time: '2小时前',
      read: true,
      type: 'warning',
      link: '/material-supply-chain/material-maintenance',
      actionText: '立即处理',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 4,
      title: '数据同步失败',
      message: '系统数据同步发生错误，请联系IT管理员检查服务器状态。',
      time: '1天前',
      read: true,
      type: 'error',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
    },
    {
      id: 5,
      title: '项目申请审批',
      message: '您的项目申请已通过审批，现可开始项目实施工作。',
      time: '2天前',
      read: true,
      type: 'success',
      link: '/project-management/project-approval',
      actionText: '查看项目',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    }
  ]);

  // 标记通知为已读
  const markAsRead = (id: number) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    )
  }

  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  // 删除通知
  const removeNotification = (id: number) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== id)
    )
  }

  // 清空所有通知
  const clearAllNotifications = () => {
    setNotifications([])
  }

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch(type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  // 未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">通知中心</h1>
        <div className="flex space-x-2">
          {unreadCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={markAllAsRead}
              className="flex items-center gap-2"
            >
              <CheckSquare className="h-4 w-4" />
              <span>全部标为已读</span>
            </Button>
          )}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={clearAllNotifications}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            <span>清空全部</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span>全部</span>
            {notifications.length > 0 && (
              <Badge variant="secondary" className="ml-1 bg-gray-200 text-gray-800">
                {notifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="flex items-center gap-2">
            <BellOff className="h-4 w-4" />
            <span>未读</span>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="ml-1 bg-blue-100 text-blue-800">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="info" className="flex items-center gap-2">
            <Info className="h-4 w-4 text-blue-500" />
            <span>信息</span>
          </TabsTrigger>
          <TabsTrigger value="success" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>成功</span>
          </TabsTrigger>
          <TabsTrigger value="warning" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <span>警告</span>
          </TabsTrigger>
          <TabsTrigger value="error" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <span>错误</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <NotificationList 
            notifications={notifications} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>

        <TabsContent value="unread">
          <NotificationList 
            notifications={notifications.filter(n => !n.read)} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>

        <TabsContent value="info">
          <NotificationList 
            notifications={notifications.filter(n => n.type === 'info')} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>

        <TabsContent value="success">
          <NotificationList 
            notifications={notifications.filter(n => n.type === 'success')} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>

        <TabsContent value="warning">
          <NotificationList 
            notifications={notifications.filter(n => n.type === 'warning')} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>

        <TabsContent value="error">
          <NotificationList 
            notifications={notifications.filter(n => n.type === 'error')} 
            markAsRead={markAsRead}
            removeNotification={removeNotification}
            getNotificationIcon={getNotificationIcon}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface NotificationListProps {
  notifications: Notification[];
  markAsRead: (id: number) => void;
  removeNotification: (id: number) => void;
  getNotificationIcon: (type: string) => JSX.Element;
}

function NotificationList({ notifications, markAsRead, removeNotification, getNotificationIcon }: NotificationListProps) {
  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Bell className="h-12 w-12 text-gray-300 mb-4" />
        <p className="text-gray-500">暂无通知</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification) => (
        <div 
          key={notification.id} 
          className={`p-4 border rounded-lg relative transition-all duration-200 hover:shadow-md ${
            !notification.read 
              ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' 
              : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
          }`}
        >
          <div className="flex items-start gap-4">
            <div className="mt-1">
              {getNotificationIcon(notification.type)}
            </div>

            <div className="flex-1">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className={`font-medium ${!notification.read ? 'font-bold' : ''}`}>
                    {notification.title}
                  </h3>
                  <p className="mt-1 text-gray-600 dark:text-gray-300">{notification.message}</p>
                </div>

                <div className="flex items-center space-x-1">
                  {!notification.read && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 rounded-full"
                      onClick={() => markAsRead(notification.id)}
                    >
                      <CheckSquare className="h-4 w-4" />
                      <span className="sr-only">标记为已读</span>
                    </Button>
                  )}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0 rounded-full text-gray-500 hover:text-red-500"
                    onClick={() => removeNotification(notification.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">删除</span>
                  </Button>
                </div>
              </div>

              <div className="flex justify-between items-center mt-3">
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{notification.time}</span>
                </div>

                {notification.actionText && notification.link && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className={`p-0 h-auto text-xs ${
                      notification.type === 'success' ? 'text-green-500' :
                      notification.type === 'warning' ? 'text-yellow-500' :
                      notification.type === 'error' ? 'text-red-500' :
                      'text-blue-500'
                    }`}
                    asChild
                  >
                    <a href={notification.link}>{notification.actionText}</a>
                  </Button>
                )}
              </div>
            </div>
          </div>

          {!notification.read && (
            <Badge 
              variant="secondary" 
              className="absolute top-3 right-3 bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300"
            >
              新
            </Badge>
          )}
        </div>
      ))}
    </div>
  )
} 