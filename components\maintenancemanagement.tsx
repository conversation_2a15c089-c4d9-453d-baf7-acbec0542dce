"use client"

import { useState } from "react"
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  MoreVertical, 
  Edit, 
  Trash, 
  Search, 
  Calendar,
  User,
  ClipboardList,
  CheckCircle2,
  XCircle,
  AlertCircle
} from "lucide-react"

interface MaintenanceRecord {
  id: string
  itemName: string
  maintenanceType: string
  maintenanceDate: string
  personnel: string
  content: string
  status: string
  remarks: string
}

export function MaintenanceManagement() {
  // 初始维护记录数据
  const initialRecords: MaintenanceRecord[] = [
    {
      id: "1",
      itemName: "安全帽",
      maintenanceType: "计划",
      maintenanceDate: "2024-03-20",
      personnel: "张三",
      content: "检查安全帽的完整性和使用寿命",
      status: "待执行",
      remarks: ""
    },
    {
      id: "2",
      itemName: "矿灯",
      maintenanceType: "执行",
      maintenanceDate: "2024-03-15",
      personnel: "李四",
      content: "更换矿灯电池",
      status: "已完成",
      remarks: ""
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<MaintenanceRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<MaintenanceRecord>({
    id: "",
    itemName: "",
    maintenanceType: "",
    maintenanceDate: "",
    personnel: "",
    content: "",
    status: "",
    remarks: ""
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch = 
        record.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.maintenanceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.personnel.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (activeTab === "all") return matchesSearch;
      if (activeTab === "pending") return matchesSearch && record.status === "待执行";
      if (activeTab === "completed") return matchesSearch && record.status === "已完成";
      
      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      status: "待执行"
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      itemName: "",
      maintenanceType: "",
      maintenanceDate: "",
      personnel: "",
      content: "",
      status: "",
      remarks: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      itemName: "",
      maintenanceType: "",
      maintenanceDate: "",
      personnel: "",
      content: "",
      status: "",
      remarks: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      itemName: "",
      maintenanceType: "",
      maintenanceDate: "",
      personnel: "",
      content: "",
      status: "",
      remarks: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (record: MaintenanceRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: MaintenanceRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待执行":
        return <Badge className="bg-yellow-500">待执行</Badge>
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">维护管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增维护记录
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>新增维护记录</DialogTitle>
              <DialogDescription>
                请填写维护记录的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="itemName">物资名称</Label>
                <Input
                  id="itemName"
                  value={currentRecord.itemName}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, itemName: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maintenanceType">维护类型</Label>
                  <Select
                    value={currentRecord.maintenanceType}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, maintenanceType: value })}
                  >
                    <SelectTrigger id="maintenanceType">
                      <SelectValue placeholder="选择维护类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="计划">计划</SelectItem>
                      <SelectItem value="执行">执行</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maintenanceDate">维护日期</Label>
                  <Input
                    id="maintenanceDate"
                    type="date"
                    value={currentRecord.maintenanceDate}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, maintenanceDate: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="personnel">维护人员</Label>
                <Input
                  id="personnel"
                  value={currentRecord.personnel}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, personnel: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">维护内容</Label>
                <Textarea
                  id="content"
                  value={currentRecord.content}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, content: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="remarks">备注</Label>
                <Textarea
                  id="remarks"
                  value={currentRecord.remarks}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, remarks: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索维护记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[300px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="pending">待执行</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{record.itemName}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(record.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    {record.personnel}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    {record.maintenanceDate}
                  </div>
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">维护内容:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.content}</div>
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">备注:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.remarks}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>维护记录列表</CardTitle>
          <CardDescription>管理所有维护记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>物资名称</TableHead>
                <TableHead>维护类型</TableHead>
                <TableHead>维护日期</TableHead>
                <TableHead>维护人员</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.itemName}</TableCell>
                  <TableCell>{record.maintenanceType}</TableCell>
                  <TableCell>{record.maintenanceDate}</TableCell>
                  <TableCell>{record.personnel}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>编辑维护记录</DialogTitle>
            <DialogDescription>
              修改维护记录的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-itemName">物资名称</Label>
              <Input
                id="edit-itemName"
                value={currentRecord.itemName}
                onChange={(e) => setCurrentRecord({ ...currentRecord, itemName: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-maintenanceType">维护类型</Label>
                <Select
                  value={currentRecord.maintenanceType}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, maintenanceType: value })}
                >
                  <SelectTrigger id="edit-maintenanceType">
                    <SelectValue placeholder="选择维护类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="计划">计划</SelectItem>
                    <SelectItem value="执行">执行</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-maintenanceDate">维护日期</Label>
                <Input
                  id="edit-maintenanceDate"
                  type="date"
                  value={currentRecord.maintenanceDate}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, maintenanceDate: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-personnel">维护人员</Label>
              <Input
                id="edit-personnel"
                value={currentRecord.personnel}
                onChange={(e) => setCurrentRecord({ ...currentRecord, personnel: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-content">维护内容</Label>
              <Textarea
                id="edit-content"
                value={currentRecord.content}
                onChange={(e) => setCurrentRecord({ ...currentRecord, content: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-remarks">备注</Label>
              <Textarea
                id="edit-remarks"
                value={currentRecord.remarks}
                onChange={(e) => setCurrentRecord({ ...currentRecord, remarks: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.itemName}" 的维护记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
