"use client"

import { useState, useEffect, useMemo } from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart as LineChartIcon,
  PieChart,
  Activity,
  AlertCircle,
  ArrowUpRight,
  ArrowDownRight,
  BatteryCharging,
  Brain,
  Calendar,
  ChevronRight,
  Clock,
  Cpu,
  Database,
  Download,
  Filter,
  FileText,
  Gauge,
  HardDrive,
  Info,
  Lightbulb,
  Settings,
  Sigma,
  SlidersHorizontal,
  Sparkles,
  TrendingUp,
  TrendingDown,
  UserCog,
  Zap,
  RefreshCw,
  Loader2,
  Search,
  DollarSign,
  Users,
  Shield,
  Package,
  FolderOpen,
  TagIcon,
  Play,
  Pause,
  PlusCircle,
  MoreHorizontal,
  Eye,
  Upload,
  CheckCircle,
  XCircle,
  RotateCw,
  Split,
  BarChart4,
  AlertTriangle,
  LineChart,
  TableProperties,
  Image,
  Globe,
  Trash2,
  ArrowRight
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  LineChart as RechartsLineChart,
  BarChart as RechartsBarChart,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Bar,
  Line
} from "recharts"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// 分析洞察接口
interface AnalyticsInsight {
  id: string;
  title: string;
  description: string;
  type: "anomaly" | "trend" | "recommendation" | "alert";
  module: string;
  category: string;
  status: "new" | "acknowledged" | "resolved";
  importance: "low" | "medium" | "high";
  source: string;
  timestamp: string;
  metrics: {
    value: number;
    change: number;
    unit: string;
  };
  actions?: string[];
}

// 分析模型接口
interface AnalyticsModel {
  id: string;
  name: string;
  description: string;
  type: "prediction" | "classification" | "anomaly" | "optimization";
  status: "active" | "inactive" | "training";
  accuracy: number;
  lastRun: string;
  datasetNames: string[];
  version: string;
  processingTime: number;
  capabilities: string[];
  enabled: boolean;
}

// 分析数据集接口
interface AnalyticsDataset {
  id: string;
  name: string;
  description: string;
  type: "timeseries" | "relational" | "document" | "image" | "spatial";
  status: "active" | "processing" | "error";
  lastUpdated: string;
  source: string;
  fields: number;
  metrics: {
    records: number;
    completeness: number;
    accuracy: number;
  };
  tags: string[];
  schema?: {
    fieldName: string;
    fieldType: string;
    description?: string;
  }[];
}

// 分析任务接口
interface AnalyticsTask {
  id: string;
  name: string;
  description: string;
  status: "pending" | "running" | "completed" | "failed";
  progress: number;
  modelName?: string;
  startTime?: string;
  endTime?: string;
  results?: {
    insights: number;
    anomalies: number;
    recommendations: number;
  };
  createdBy: string;
}

export function AnalyticsCenter() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("dashboard")
  const [searchTerm, setSearchTerm] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [timeRange, setTimeRange] = useState("30days")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedModelType, setSelectedModelType] = useState("all")
  const [selectedTaskStatus, setSelectedTaskStatus] = useState("all")
  const [isOfflineMode, setIsOfflineMode] = useState(true)
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const [isModelDialogOpen, setIsModelDialogOpen] = useState(false)

  // 假数据 - 在实际环境中应从API获取
  const [insights, setInsights] = useState<AnalyticsInsight[]>([])
  const [datasets, setDatasets] = useState<AnalyticsDataset[]>([])
  const [models, setModels] = useState<AnalyticsModel[]>([])
  const [tasks, setTasks] = useState<AnalyticsTask[]>([])
  const [analysisReady, setAnalysisReady] = useState(false)

  // 初始化离线数据
  useEffect(() => {
    initializeOfflineData()
    const timer = setTimeout(() => {
      setAnalysisReady(true)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  // 初始化离线数据
  const initializeOfflineData = () => {
    // 初始化洞察数据
    setInsights([
      {
        id: "ins1",
        title: "B区用电量异常增长",
        description: "检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况",
        type: "anomaly",
        timestamp: "2025-03-15T10:30:00.000Z",
        module: "能源管理",
        importance: "high",
        status: "new",
        category: "能源效率",
        metrics: {
          value: 28,
          change: 1,
          unit: "%"
        },
        source: "能源监控系统",
        actions: ["调查设备运行状态", "检查是否存在异常负载"]
      },
      {
        id: "ins2",
        title: "Q2营收预测超预期",
        description: "根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标",
        type: "trend",
        timestamp: "2025-03-15T08:30:00.000Z",
        module: "财务管理",
        importance: "medium",
        status: "acknowledged",
        category: "财务趋势",
        metrics: {
          value: 12.5,
          change: 1,
          unit: "%"
        },
        source: "财务预测模型",
        actions: ["调整Q2经营计划", "更新投资者报告"]
      },
      {
        id: "ins3",
        title: "3号生产线需要维护",
        description: "3号生产线设备预计在14天内需要进行预防性维护，建议提前安排",
        type: "recommendation",
        timestamp: "2025-03-14T10:30:00.000Z",
        module: "设备管理",
        importance: "medium",
        status: "new",
        category: "设备维护",
        metrics: {
          value: 14,
          change: -1,
          unit: "天"
        },
        source: "设备健康监控系统",
        actions: ["安排维护计划", "准备备件"]
      },
      {
        id: "ins4",
        title: "主厂房建设进度滞后",
        description: "主厂房建设项目进度已落后计划7.2%，建议增加资源投入",
        type: "alert",
        timestamp: "2025-03-15T04:30:00.000Z",
        module: "项目管理",
        importance: "high",
        status: "acknowledged",
        category: "项目进度",
        metrics: {
          value: 7.2,
          change: 1,
          unit: "%"
        },
        source: "项目管理系统",
        actions: ["调整项目资源分配", "召开紧急项目会议"]
      },
      {
        id: "ins5",
        title: "物资库存优化建议",
        description: "分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本",
        type: "recommendation",
        timestamp: "2025-03-13T10:30:00.000Z",
        module: "物资管理",
        importance: "low",
        status: "resolved",
        category: "库存管理",
        metrics: {
          value: 5,
          change: 0,
          unit: "种"
        },
        source: "库存分析系统",
        actions: ["调整库存水平", "更新采购计划"]
      }
    ])

    // 初始化数据集
    setDatasets([
      {
        id: "ds1",
        name: "设备能源消耗数据集",
        description: "包含所有设备的能源消耗数据",
        type: "timeseries",
        status: "active",
        lastUpdated: "2025-03-15T10:30:00.000Z",
        source: "能源管理系统",
        fields: 15,
        metrics: {
          records: 25000,
          completeness: 98,
          accuracy: 96
        },
        tags: ["能源", "设备", "消耗"]
      },
      {
        id: "ds2",
        name: "财务报表数据集",
        description: "包含公司所有财务报表数据",
        type: "relational",
        status: "active",
        lastUpdated: "2025-03-10T10:30:00.000Z",
        source: "财务管理系统",
        fields: 32,
        metrics: {
          records: 5000,
          completeness: 100,
          accuracy: 99
        },
        tags: ["财务", "报表", "季度"]
      },
      {
        id: "ds3",
        name: "设备维护记录数据集",
        description: "记录所有设备的维护历史",
        type: "document",
        status: "active",
        lastUpdated: "2025-03-12T10:30:00.000Z",
        source: "设备管理系统",
        fields: 22,
        metrics: {
          records: 8500,
          completeness: 92,
          accuracy: 94
        },
        tags: ["设备", "维护", "记录"]
      },
      {
        id: "ds4",
        name: "项目文档数据集",
        description: "收集所有项目相关文档",
        type: "document",
        status: "processing",
        lastUpdated: "2025-03-14T10:30:00.000Z",
        source: "项目管理系统",
        fields: 18,
        metrics: {
          records: 3500,
          completeness: 85,
          accuracy: 90
        },
        tags: ["项目", "文档", "计划"]
      },
      {
        id: "ds5",
        name: "物资库存数据集",
        description: "跟踪所有物资的库存水平",
        type: "relational",
        status: "active",
        lastUpdated: "2025-03-15T00:30:00.000Z",
        source: "库存管理系统",
        fields: 25,
        metrics: {
          records: 12000,
          completeness: 96,
          accuracy: 97
        },
        tags: ["库存", "物资", "供应链"]
      }
    ])

    // 初始化分析模型
    setModels([
      {
        id: "m1",
        name: "能源异常检测模型",
        description: "基于历史能源消耗数据，检测异常用能情况",
        type: "anomaly",
        status: "active",
        accuracy: 92,
        lastRun: "2025-03-15T10:30:00.000Z",
        datasetNames: ["ds2"],
        version: "1.0",
        processingTime: 120,
        capabilities: ["实时检测", "异常识别"],
        enabled: true
      },
      {
        id: "m2",
        name: "财务趋势预测模型",
        description: "预测未来季度财务走势",
        type: "prediction",
        status: "active",
        accuracy: 85,
        lastRun: "2025-03-15T07:30:00.000Z",
        datasetNames: ["ds1"],
        version: "2.0",
        processingTime: 180,
        capabilities: ["长期预测", "季度分析"],
        enabled: true
      },
      {
        id: "m3",
        name: "设备故障预测模型",
        description: "预测设备可能发生故障的时间",
        type: "prediction",
        status: "active",
        accuracy: 78,
        lastRun: "2025-03-14T10:30:00.000Z",
        datasetNames: ["ds3"],
        version: "1.5",
        processingTime: 120,
        capabilities: ["短期预测", "故障诊断"],
        enabled: true
      },
      {
        id: "m4",
        name: "项目风险评估模型",
        description: "评估项目可能面临的风险和延误",
        type: "classification",
        status: "active",
        accuracy: 84,
        lastRun: "2025-03-15T04:30:00.000Z",
        datasetNames: ["ds4"],
        version: "2.5",
        processingTime: 240,
        capabilities: ["风险评估", "项目管理"],
        enabled: true
      },
      {
        id: "m5",
        name: "库存优化模型",
        description: "分析并优化物资库存水平",
        type: "optimization",
        status: "active",
        accuracy: 82,
        lastRun: "2025-03-13T10:30:00.000Z",
        datasetNames: ["ds5"],
        version: "1.2",
        processingTime: 120,
        capabilities: ["库存管理", "成本优化"],
        enabled: true
      }
    ])

    // 初始化分析任务
    setTasks([
      {
        id: "t1",
        name: "每日能源异常检测",
        description: "每日运行能源异常检测分析",
        status: "completed",
        progress: 100,
        modelName: "m1",
        startTime: "2025-03-15T08:30:00.000Z",
        endTime: "2025-03-15T09:00:00.000Z",
        results: {
          insights: 3,
          anomalies: 1,
          recommendations: 2
        },
        createdBy: "系统"
      },
      {
        id: "t2",
        name: "月度财务预测",
        description: "生成下月财务预测报告",
        status: "completed",
        progress: 100,
        modelName: "m2",
        startTime: "2025-03-14T10:30:00.000Z",
        endTime: "2025-03-14T11:30:00.000Z",
        results: {
          insights: 4,
          anomalies: 0,
          recommendations: 3
        },
        createdBy: "系统"
      },
      {
        id: "t3",
        name: "设备维护预测",
        description: "预测未来30天内需要维护的设备",
        status: "running",
        progress: 68,
        startTime: "2025-03-15T10:00:00.000Z",
        modelName: "m3",
        createdBy: "系统"
      },
      {
        id: "t4",
        name: "项目风险分析",
        description: "分析当前项目可能面临的风险",
        status: "pending",
        progress: 0,
        modelName: "m4",
        createdBy: "张经理"
      },
      {
        id: "t5",
        name: "库存优化分析",
        description: "寻找物资库存优化机会",
        status: "completed",
        progress: 100,
        startTime: "2025-03-12T10:30:00.000Z",
        endTime: "2025-03-12T12:30:00.000Z",
        modelName: "m5",
        results: {
          insights: 5,
          anomalies: 0,
          recommendations: 5
        },
        createdBy: "李库管"
      }
    ])
  }

  // 过滤洞察数据
  const filteredInsights = useMemo(() => {
    return insights.filter(insight => {
      // 基于搜索关键词
      const matchesSearch = searchTerm === "" ||
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.module.toLowerCase().includes(searchTerm.toLowerCase());

      // 基于类别筛选
      const matchesCategory = selectedCategory === "all" ||
        insight.category === selectedCategory;

      // 返回匹配的洞察
      return matchesSearch && matchesCategory;
    });
  }, [insights, searchTerm, selectedCategory]);

  // 过滤模型数据
  const filteredModels = useMemo(() => {
    return models.filter(model => {
      const matchesSearch = searchTerm === "" ||
        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = selectedModelType === "all" ||
        model.type === selectedModelType;

      return matchesSearch && matchesType;
    });
  }, [models, searchTerm, selectedModelType]);

  // 过滤任务数据
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      const matchesSearch = searchTerm === "" ||
        task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = selectedTaskStatus === "all" ||
        task.status === selectedTaskStatus;

      return matchesSearch && matchesStatus;
    });
  }, [tasks, searchTerm, selectedTaskStatus]);

  // 统计数据
  const statistics = useMemo(() => {
    return {
      insightsTotal: insights.length,
      insightsNew: insights.filter(i => i.status === "new").length,
      insightsHigh: insights.filter(i => i.importance === "high").length,
      modelsTotal: models.length,
      modelsActive: models.filter(m => m.status === "active").length,
      tasksTotal: tasks.length,
      tasksRunning: tasks.filter(t => t.status === "running").length,
      datasetsTotal: datasets.length,
      datasetsReady: datasets.filter(d => d.status === "active").length,
      avgModelAccuracy: models.length > 0
        ? Math.round(models.reduce((sum, m) => sum + m.accuracy, 0) / models.length)
        : 0,
      avgDataCompleteness: datasets.length > 0
        ? Math.round(datasets.reduce((sum, d) => sum + d.metrics.records, 0) / datasets.length)
        : 0,
      totalRecords: datasets.reduce((sum, d) => sum + d.metrics.records, 0),
      insightsByType: {
        anomaly: insights.filter(i => i.type === "anomaly").length,
        trend: insights.filter(i => i.type === "trend").length,
        recommendation: insights.filter(i => i.type === "recommendation").length,
        alert: insights.filter(i => i.type === "alert").length,
      },
      modelsByType: {
        prediction: models.filter(m => m.type === "prediction").length,
        classification: models.filter(m => m.type === "classification").length,
        anomaly: models.filter(m => m.type === "anomaly").length,
        optimization: models.filter(m => m.type === "optimization").length,
      }
    };
  }, [insights, models, tasks, datasets]);

  // 获取洞察类型对应的图标和颜色
  const getInsightTypeInfo = (type: string) => {
    switch (type) {
      case "anomaly":
        return { icon: <AlertCircle className="h-5 w-5" />, color: "text-red-500 bg-red-100", bgColor: "bg-red-100" };
      case "trend":
        return { icon: <TrendingUp className="h-5 w-5" />, color: "text-blue-500 bg-blue-100", bgColor: "bg-blue-100" };
      case "recommendation":
        return { icon: <Lightbulb className="h-5 w-5" />, color: "text-yellow-500 bg-yellow-100", bgColor: "bg-yellow-100" };
      case "alert":
        return { icon: <AlertCircle className="h-5 w-5" />, color: "text-red-500 bg-red-100", bgColor: "bg-red-100" };
      default:
        return { icon: <Info className="h-5 w-5" />, color: "text-gray-500 bg-gray-100", bgColor: "bg-gray-100" };
    }
  };

  // 获取重要程度对应的徽章
  const getImportanceBadge = (importance: string) => {
    switch (importance) {
      case "high":
        return <Badge className="bg-red-500">高</Badge>;
      case "medium":
        return <Badge className="bg-yellow-500">中</Badge>;
      case "low":
        return <Badge className="bg-blue-500">低</Badge>;
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 获取状态对应的徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge className="bg-blue-500">新</Badge>;
      case "reviewing":
        return <Badge className="bg-yellow-500">审核中</Badge>;
      case "reviewed":
        return <Badge className="bg-green-500">已审核</Badge>;
      case "resolved":
        return <Badge className="bg-green-700">已解决</Badge>;
      case "dismissed":
        return <Badge className="bg-gray-500">已忽略</Badge>;
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">等待中</Badge>;
      case "running":
        return <Badge className="bg-blue-500 animate-pulse">运行中</Badge>;
      case "completed":
        return <Badge className="bg-green-500">已完成</Badge>;
      case "failed":
        return <Badge variant="destructive">失败</Badge>;
      case "active":
        return <Badge className="bg-green-500">活跃</Badge>;
      case "inactive":
        return <Badge variant="outline" className="text-gray-500 border-gray-500">不活跃</Badge>;
      case "training":
        return <Badge className="bg-purple-500">训练中</Badge>;
      case "error":
        return <Badge variant="destructive">错误</Badge>;
      case "ready":
        return <Badge className="bg-green-500">就绪</Badge>;
      case "updating":
        return <Badge className="bg-blue-500 animate-pulse">更新中</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // 格式化日期
  const formatDate = (date?: Date): string => {
    if (!date) return "未知";

    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diff / (1000 * 60));
    const diffHours = Math.floor(diff / (1000 * 60 * 60));
    const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
      return `${diffMinutes} 分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours} 小时前`;
    } else if (diffDays < 7) {
      return `${diffDays} 天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short', day: 'numeric' });
    }
  };

  // 处理刷新操作
  const handleRefresh = () => {
    setIsLoading(true);

    // 模拟刷新操作
    setTimeout(() => {
      initializeOfflineData();
      setIsLoading(false);
      toast({
        title: "数据已刷新",
        description: "已加载最新的分析数据",
      });
    }, 1500);
  };

  // 生成随机颜色
  const getRandomColor = (index: number) => {
    const colors = [
      "#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#0088fe",
      "#00c49f", "#ffbb28", "#ff8042", "#a4de6c", "#d0ed57"
    ];
    return colors[index % colors.length];
  };

  // 获取数据集类型的图标和颜色
  const getDatasetTypeInfo = (type: AnalyticsDataset['type']) => {
    const typeInfo: Record<AnalyticsDataset['type'], { icon: React.ReactNode; name: string }> = {
      timeseries: { icon: <LineChart className="h-4 w-4" />, name: '时间序列' },
      relational: { icon: <TableProperties className="h-4 w-4" />, name: '关系型' },
      document: { icon: <FileText className="h-4 w-4" />, name: '文档型' },
      image: { icon: <Image className="h-4 w-4" />, name: '图像型' },
      spatial: { icon: <Globe className="h-4 w-4" />, name: '空间型' }
    };

    return typeInfo[type];
  };

  // 获取模型类型的图标和颜色
  const getModelTypeInfo = (type: AnalyticsModel['type']) => {
    const typeInfo: Record<AnalyticsModel['type'], {
      icon: React.ReactNode;
      color: string;
      name: string;
      colorHex: string
    }> = {
      prediction: {
        icon: <TrendingUp className="h-4 w-4" />,
        color: 'text-blue-500',
        name: '预测分析',
        colorHex: '#3b82f6'
      },
      classification: {
        icon: <Split className="h-4 w-4" />,
        color: 'text-green-500',
        name: '分类分析',
        colorHex: '#22c55e'
      },
      anomaly: {
        icon: <AlertTriangle className="h-4 w-4" />,
        color: 'text-amber-500',
        name: '异常检测',
        colorHex: '#f59e0b'
      },
      optimization: {
        icon: <BarChart4 className="h-4 w-4" />,
        color: 'text-purple-500',
        name: '优化分析',
        colorHex: '#a855f7'
      }
    };

    return typeInfo[type];
  };

  // 生成洞察类型分布图表数据
  const insightTypeChartData = useMemo(() => {
    return [
      {
        name: "异常",
        value: statistics.insightsByType.anomaly,
        color: "#ff4d4f",
        description: "检测到的系统异常情况",
        icon: <AlertCircle className="h-4 w-4" />
      },
      {
        name: "趋势",
        value: statistics.insightsByType.trend,
        color: "#1890ff",
        description: "识别的数据变化趋势",
        icon: <TrendingUp className="h-4 w-4" />
      },
      {
        name: "建议",
        value: statistics.insightsByType.recommendation,
        color: "#faad14",
        description: "系统提供的优化建议",
        icon: <Lightbulb className="h-4 w-4" />
      },
      {
        name: "警报",
        value: statistics.insightsByType.alert,
        color: "#ff7a45",
        description: "需要立即关注的警报",
        icon: <AlertTriangle className="h-4 w-4" />
      }
    ];
  }, [statistics]);

  // 生成模型类型分布图表数据
  const modelTypeChartData = useMemo(() => {
    return [
      {
        name: "预测模型",
        value: statistics.modelsByType.prediction,
        color: "#1890ff",
        description: "预测未来趋势和结果的模型",
        icon: <TrendingUp className="h-4 w-4" />
      },
      {
        name: "分类模型",
        value: statistics.modelsByType.classification,
        color: "#722ed1",
        description: "对数据进行分类的模型",
        icon: <Split className="h-4 w-4" />
      },
      {
        name: "异常检测",
        value: statistics.modelsByType.anomaly,
        color: "#ff4d4f",
        description: "识别异常模式的模型",
        icon: <AlertTriangle className="h-4 w-4" />
      },
      {
        name: "优化模型",
        value: statistics.modelsByType.optimization,
        color: "#52c41a",
        description: "寻找最优解决方案的模型",
        icon: <BarChart4 className="h-4 w-4" />
      }
    ];
  }, [statistics]);

  // 格式化数字
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    } else {
      return num.toString();
    }
  };

  // 页面加载状态组件
  const renderLoading = () => {
    return (
      <div className="flex flex-col items-center justify-center h-[60vh]">
        <Loader2 className="h-16 w-16 text-blue-500 animate-spin mb-4" />
        <h3 className="text-xl font-medium mb-2">正在加载智能分析系统</h3>
        <p className="text-muted-foreground">正在初始化分析模型和数据...</p>
      </div>
    );
  };

  // 渲染离线模式通知
  const renderOfflineNotice = () => {
    return (
      <div className="mb-6 p-4 border border-blue-200 rounded-md bg-blue-50">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-500 mt-0.5 mr-3" />
          <div>
            <h3 className="font-medium text-blue-700">离线模式已启用</h3>
            <p className="text-sm text-blue-600 mb-2">
              系统当前使用本地数据进行分析，数据更新于：{new Date().toLocaleString('zh-CN')}
            </p>
            <div className="flex items-center">
              <Switch
                checked={isOfflineMode}
                onCheckedChange={setIsOfflineMode}
                disabled={true}
                className="mr-2"
              />
              <Label className="text-sm text-blue-600">
                离线模式 {isOfflineMode ? "开启" : "关闭"}
              </Label>
              <Button
                variant="link"
                className="text-xs text-blue-600 h-auto p-0 ml-4"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              >
                {showAdvancedSettings ? "隐藏高级设置" : "显示高级设置"}
              </Button>
            </div>

            {showAdvancedSettings && (
              <div className="mt-3 p-3 border border-blue-200 rounded bg-blue-50/70">
                <h4 className="text-sm font-medium text-blue-700 mb-2">高级系统设置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs text-blue-600">模型加载策略</Label>
                    <Select disabled defaultValue="ondemand">
                      <SelectTrigger>
                        <SelectValue placeholder="选择加载策略" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ondemand">按需加载</SelectItem>
                        <SelectItem value="preload">预加载全部</SelectItem>
                        <SelectItem value="hybrid">混合加载</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs text-blue-600">数据缓存时间 (小时)</Label>
                    <Input
                      type="number"
                      defaultValue="24"
                      disabled
                      className="bg-white/50"
                    />
                  </div>
                </div>
                <p className="text-xs text-blue-500 mt-2">
                  注：高级设置功能将在未来版本中启用
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 渲染洞察面板
  const renderInsightPanel = (insight: AnalyticsInsight) => {
    // 获取洞察类型的图标和颜色
    const typeInfo = getInsightTypeInfo(insight.type);
    const dateFormatted = formatDate(new Date(insight.timestamp));

    return (
      <Card key={insight.id} className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className={`p-1.5 rounded-md ${typeInfo.bgColor}`}>
                {typeInfo.icon}
              </div>
              <div>
                <CardTitle className="text-base">{insight.title}</CardTitle>
                <CardDescription className="text-xs flex items-center gap-1">
                  {dateFormatted} • {insight.module}
                </CardDescription>
              </div>
            </div>
            <Badge variant={
              insight.importance === "high" ? "destructive" :
              insight.importance === "medium" ? "default" : "outline"
            } className="ml-2 text-xs">
              {insight.importance === "high" ? "高" : insight.importance === "medium" ? "中" : "低"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pb-2 pt-0">
          <p className="text-sm text-muted-foreground mb-2">{insight.description}</p>
          <div className="flex flex-wrap gap-2 mt-3">
            <div className="bg-muted rounded-md px-2 py-1 text-xs flex items-center">
              <ArrowRight className="h-3 w-3 mr-1" />
              <span>值: {insight.metrics.value}{insight.metrics.unit}</span>
              {insight.metrics.change !== 0 && (
                <Badge variant={insight.metrics.change > 0 ? "destructive" : "default"} className="ml-1 h-4 px-1">
                  {insight.metrics.change > 0 ? "↑" : "↓"}
                </Badge>
              )}
            </div>
            {insight.actions && insight.actions.length > 0 && (
              <div className="mt-2 w-full">
                <p className="text-xs font-medium mb-1">建议行动:</p>
                <ul className="text-xs text-muted-foreground list-disc pl-4">
                  {insight.actions.map((action, i) => (
                    <li key={i}>{action}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="pt-0 pb-2">
          <div className="flex justify-between items-center w-full">
            <Badge variant="outline" className="text-xs">
              {insight.category}
            </Badge>
            <Badge variant={
              insight.status === "new" ? "secondary" :
              insight.status === "acknowledged" ? "outline" : "default"
            } className="text-xs">
              {insight.status === "new" ? "新发现" :
              insight.status === "acknowledged" ? "已确认" : "已解决"}
            </Badge>
          </div>
        </CardFooter>
      </Card>
    );
  };

  // 自定义图表提示组件
  const CustomChartTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 rounded-md shadow-lg border border-gray-200">
          <div className="flex items-center gap-2">
            <div style={{ color: data.color }}>
              {data.icon}
            </div>
            <span className="font-medium">{data.name}</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">{data.description}</p>
          <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-100">
            <span className="text-xs text-gray-500">数量:</span>
            <span className="font-medium" style={{ color: data.color }}>{data.value}</span>
          </div>
        </div>
      );
    }
    return null;
  };

  // 添加模型性能数据
  const modelPerformanceData = useMemo(() => {
    return models.map(model => ({
      name: model.name.length > 10 ? model.name.substring(0, 10) + '...' : model.name,
      准确率: model.accuracy,
      处理时间: model.processingTime,
      类型: model.type,
      color: getModelTypeInfo(model.type).colorHex
    }));
  }, [models]);

  // 渲染主要内容
  return (
    <TooltipProvider>
      <div className="analytics-center-container space-y-6">
        {/* 头部区域 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-md bg-gradient-to-r from-blue-600 to-indigo-600">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-zinc-900">智能分析中心</h2>
            </div>
            <p className="text-muted-foreground mt-1">
              数据驱动的智能分析平台，发现业务洞察
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={isLoading}
              onClick={handleRefresh}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              刷新数据
            </Button>
          </div>
        </div>

        {/* 离线模式通知 */}
        {isOfflineMode && renderOfflineNotice()}

        {/* 主要内容区域 */}
        {!analysisReady ? (
          renderLoading()
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="dashboard">
                <BarChart className="h-4 w-4 mr-2" />
                分析概览
              </TabsTrigger>
              <TabsTrigger value="insights">
                <Lightbulb className="h-4 w-4 mr-2" />
                智能洞察
              </TabsTrigger>
              <TabsTrigger value="models">
                <Cpu className="h-4 w-4 mr-2" />
                分析模型
              </TabsTrigger>
              <TabsTrigger value="datasets">
                <Database className="h-4 w-4 mr-2" />
                数据集
              </TabsTrigger>
              <TabsTrigger value="tasks">
                <Activity className="h-4 w-4 mr-2" />
                分析任务
              </TabsTrigger>
            </TabsList>

            {/* 仪表盘内容 */}
            <TabsContent value="dashboard" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">智能洞察总数</CardTitle>
                    <Lightbulb className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.insightsTotal}</div>
                    <p className="text-xs text-muted-foreground">
                      {statistics.insightsNew} 个新洞察，{statistics.insightsHigh} 个高优先级
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">分析模型</CardTitle>
                    <Brain className="h-4 w-4 text-purple-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.modelsTotal}</div>
                    <p className="text-xs text-muted-foreground">
                      {statistics.modelsActive} 个活跃模型，平均准确率 {statistics.avgModelAccuracy}%
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">分析任务</CardTitle>
                    <Activity className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.tasksTotal}</div>
                    <p className="text-xs text-muted-foreground">
                      {statistics.tasksRunning} 个运行中，{tasks.filter(t => t.status === "completed").length} 个已完成
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">数据集</CardTitle>
                    <Database className="h-4 w-4 text-amber-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.datasetsTotal}</div>
                    <p className="text-xs text-muted-foreground">
                      {statistics.datasetsReady} 个就绪，{formatNumber(statistics.totalRecords)} 条记录
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>模型性能分析</CardTitle>
                    <CardDescription>分析模型准确率与处理时间的关系</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[350px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart
                          data={modelPerformanceData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 60,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="name"
                            angle={-45}
                            textAnchor="end"
                            tick={{ fontSize: 12 }}
                            interval={0}
                          />
                          <YAxis yAxisId="left" orientation="left" label={{ value: '准确率 (%)', angle: -90, position: 'insideLeft' }} />
                          <YAxis yAxisId="right" orientation="right" label={{ value: '处理时间 (秒)', angle: 90, position: 'insideRight' }} />
                          <RechartsTooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                const model = payload[0].payload;
                                return (
                                  <div className="bg-white p-3 rounded-md shadow-lg border border-gray-200">
                                    <div className="font-medium">{label}</div>
                                    <p className="text-xs text-gray-500">类型: {
                                      model.类型 === "prediction" ? "预测模型" :
                                      model.类型 === "classification" ? "分类模型" :
                                      model.类型 === "anomaly" ? "异常检测" :
                                      model.类型 === "optimization" ? "优化模型" :
                                      model.类型
                                    }</p>
                                    <div className="mt-2 grid grid-cols-2 gap-2">
                                      <div className="flex flex-col">
                                        <span className="text-xs text-gray-500">准确率:</span>
                                        <span className="font-medium text-blue-600">{model.准确率}%</span>
                                      </div>
                                      <div className="flex flex-col">
                                        <span className="text-xs text-gray-500">处理时间:</span>
                                        <span className="font-medium text-orange-600">{model.处理时间}秒</span>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Legend />
                          <Bar
                            dataKey="准确率"
                            fill="#1890ff"
                            yAxisId="left"
                            barSize={20}
                            name="准确率 (%)"
                          >
                            {modelPerformanceData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Bar>
                          <Line
                            type="monotone"
                            dataKey="处理时间"
                            stroke="#ff7a45"
                            yAxisId="right"
                            name="处理时间 (秒)"
                            strokeWidth={2}
                            dot={{ r: 4 }}
                            activeDot={{ r: 6 }}
                          />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>最新智能洞察</CardTitle>
                  <CardDescription>系统发现的最近洞察</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>洞察</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>模块</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>重要程度</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {insights.slice(0, 5).map((insight) => (
                        <TableRow key={insight.id}>
                          <TableCell className="font-medium">{insight.title}</TableCell>
                          <TableCell>
                            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getInsightTypeInfo(insight.type).color}`}>
                              {getInsightTypeInfo(insight.type).icon}
                              <span className="ml-1">
                                {insight.type === "anomaly" ? "异常" :
                                 insight.type === "trend" ? "趋势" :
                                 insight.type === "recommendation" ? "建议" :
                                 insight.type === "alert" ? "警报" : insight.type}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>{insight.module}</TableCell>
                          <TableCell>{formatDate(new Date(insight.timestamp))}</TableCell>
                          <TableCell>{getImportanceBadge(insight.importance)}</TableCell>
                          <TableCell>{getStatusBadge(insight.status)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 其他标签内容在此处实现 */}
            <TabsContent value="insights" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>智能洞察</CardTitle>
                      <CardDescription>系统发现的异常和优化机会</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="搜索洞察..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-[200px]"
                      />
                      <Select
                        value={selectedCategory}
                        onValueChange={setSelectedCategory}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="选择类别" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部类别</SelectItem>
                          <SelectItem value="能源效率">能源效率</SelectItem>
                          <SelectItem value="财务趋势">财务趋势</SelectItem>
                          <SelectItem value="设备维护">设备维护</SelectItem>
                          <SelectItem value="项目进度">项目进度</SelectItem>
                          <SelectItem value="库存管理">库存管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    共有 {filteredInsights.length} 条智能洞察
                  </p>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {filteredInsights.map((insight) => (
                      <div key={insight.id}>
                        {renderInsightPanel(insight)}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 分析模型标签页 */}
            <TabsContent value="models" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>分析模型</CardTitle>
                      <CardDescription>系统中的可用分析模型</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="搜索模型..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-[200px]"
                      />
                      <Select
                        value={selectedModelType}
                        onValueChange={setSelectedModelType}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部类型</SelectItem>
                          <SelectItem value="prediction">预测</SelectItem>
                          <SelectItem value="classification">分类</SelectItem>
                          <SelectItem value="anomaly">异常检测</SelectItem>
                          <SelectItem value="optimization">优化</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    共有 {filteredModels.length} 个分析模型
                  </p>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {filteredModels.map((model) => (
                      <Card key={model.id} className="overflow-hidden border-l-4" style={{ borderLeftColor: getModelTypeInfo(model.type).colorHex }}>
                        <CardHeader className="p-4 pb-2">
                          <div className="flex justify-between items-start">
                            <div className="flex items-start gap-2">
                              <div className="p-2 rounded-md bg-gray-100 dark:bg-gray-800">
                                {getModelTypeInfo(model.type).icon}
                              </div>
                              <div>
                                <CardTitle className="text-base">{model.name}</CardTitle>
                                <CardDescription className="mt-1">
                                  {model.type}模型 · 版本 {model.version}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex gap-1">
                              {getStatusBadge(model.status)}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="p-4 pt-2">
                          <p className="text-sm mb-2">{model.description}</p>

                          <div className="grid grid-cols-2 gap-2 my-2">
                            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">准确率</span>
                              <span className="text-sm font-medium">{model.accuracy}%</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">最近执行</span>
                              <span className="text-sm font-medium">{formatDate(new Date(model.lastRun))}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">输入数据集</span>
                              <span className="text-sm font-medium">{model.datasetNames?.join(', ') || '无'}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">处理时间</span>
                              <span className="text-sm font-medium">{model.processingTime}秒</span>
                            </div>
                          </div>

                          {model.capabilities && model.capabilities.length > 0 && (
                            <div className="mt-3">
                              <h4 className="text-sm font-medium mb-1">模型能力</h4>
                              <div className="flex flex-wrap gap-1">
                                {model.capabilities.map((cap, i) => (
                                  <span key={i} className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                                    {cap}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="mt-3 flex justify-between items-center">
                            <div className="flex gap-1">
                              <Button variant="outline" size="sm" disabled={!isOfflineMode}>
                                <Play className="h-3.5 w-3.5 mr-1" />
                                运行
                              </Button>
                              <Button variant="outline" size="sm" disabled={!isOfflineMode}>
                                <Settings className="h-3.5 w-3.5 mr-1" />
                                配置
                              </Button>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {model.enabled ? "启用中" : "已禁用"}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 数据集标签页 */}
            <TabsContent value="datasets" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>数据集</CardTitle>
                      <CardDescription>系统中的可用分析数据集</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="搜索数据集..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-[200px]"
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>数据集名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>记录数</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>更新时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {datasets.map((dataset) => (
                        <TableRow key={dataset.id}>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="p-1.5 rounded-md bg-gray-100 mr-2">
                                {getDatasetTypeInfo(dataset.type).icon}
                              </div>
                              <div>
                                <div className="font-medium">{dataset.name}</div>
                                <div className="text-xs text-muted-foreground">{dataset.description}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{dataset.type}</TableCell>
                          <TableCell>{formatNumber(dataset.metrics.records)}</TableCell>
                          <TableCell>{getStatusBadge(dataset.status)}</TableCell>
                          <TableCell>{formatDate(new Date(dataset.lastUpdated))}</TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>数据集操作</DropdownMenuLabel>
                                <DropdownMenuItem disabled={!isOfflineMode}>
                                  <Eye className="h-4 w-4 mr-2" />查看详情
                                </DropdownMenuItem>
                                <DropdownMenuItem disabled={!isOfflineMode}>
                                  <RefreshCw className="h-4 w-4 mr-2" />刷新数据
                                </DropdownMenuItem>
                                <DropdownMenuItem disabled={!isOfflineMode}>
                                  <BarChart className="h-4 w-4 mr-2" />分析预览
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem disabled={!isOfflineMode} className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className="mt-4">
                    <Button variant="outline" size="sm" disabled={!isOfflineMode} className="mr-2">
                      <Database className="h-4 w-4 mr-2" />
                      配置数据源
                    </Button>
                    <Button variant="outline" size="sm" disabled={!isOfflineMode}>
                      <Upload className="h-4 w-4 mr-2" />
                      导入离线数据
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 分析任务标签页 */}
            <TabsContent value="tasks" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>分析任务</CardTitle>
                      <CardDescription>系统中的分析任务和执行记录</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="搜索任务..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-[200px]"
                      />
                      <Select
                        value={selectedTaskStatus}
                        onValueChange={setSelectedTaskStatus}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部状态</SelectItem>
                          <SelectItem value="pending">等待中</SelectItem>
                          <SelectItem value="running">执行中</SelectItem>
                          <SelectItem value="completed">已完成</SelectItem>
                          <SelectItem value="failed">失败</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    共有 {filteredTasks.length} 个分析任务
                  </p>

                  {tasks.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      暂无分析任务
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredTasks.map((task) => (
                        <Card key={task.id} className="overflow-hidden">
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                              <div className="flex items-start gap-3">
                                <div className={`p-2 rounded-full
                                  ${task.status === 'running' ? 'bg-blue-100 text-blue-600' :
                                    task.status === 'completed' ? 'bg-green-100 text-green-600' :
                                    task.status === 'failed' ? 'bg-red-100 text-red-600' :
                                    'bg-amber-100 text-amber-600'}`}>
                                  {task.status === 'running' ? <Play className="h-4 w-4" /> :
                                    task.status === 'completed' ? <CheckCircle className="h-4 w-4" /> :
                                    task.status === 'failed' ? <XCircle className="h-4 w-4" /> :
                                    <Clock className="h-4 w-4" />}
                                </div>
                                <div>
                                  <h3 className="font-medium">{task.name}</h3>
                                  <p className="text-sm text-muted-foreground">{task.description}</p>
                                </div>
                              </div>
                              <div>
                                {getStatusBadge(task.status)}
                              </div>
                            </div>

                            <div className="mt-4">
                              <div className="text-xs text-muted-foreground mb-1">任务进度</div>
                              <div className="flex items-center">
                                <Progress value={task.progress} className="mr-2" />
                                <div className="text-xs font-medium">
                                  {task.progress}%
                                </div>
                              </div>
                            </div>

                            <div className="grid grid-cols-3 gap-4 mt-4">
                              <div>
                                <div className="text-xs text-muted-foreground">使用模型</div>
                                <div className="text-sm">{task.modelName || "未指定"}</div>
                              </div>
                              <div>
                                <div className="text-xs text-muted-foreground">开始时间</div>
                                <div className="text-sm">{task.startTime ? formatDate(new Date(task.startTime)) : "未开始"}</div>
                              </div>
                              <div>
                                <div className="text-xs text-muted-foreground">结束时间</div>
                                <div className="text-sm">{task.endTime ? formatDate(new Date(task.endTime)) : "未完成"}</div>
                              </div>
                            </div>

                            {task.status === 'failed' && task.results?.insights !== undefined && (
                              <div className="mt-3 p-2 border border-l-4 border-red-500 bg-red-50 rounded text-sm text-red-800">
                                <div className="font-medium">错误信息:</div>
                                <div>{task.results.insights} 个洞察</div>
                              </div>
                            )}

                            <div className="mt-4 flex justify-between items-center">
                              <div className="flex gap-2">
                                <Button variant="outline" size="sm" disabled={task.status !== 'pending' || !isOfflineMode}>
                                  <Play className="h-3.5 w-3.5 mr-1" />
                                  开始
                                </Button>
                                <Button variant="outline" size="sm" disabled={task.status !== 'running' || !isOfflineMode}>
                                  <Pause className="h-3.5 w-3.5 mr-1" />
                                  暂停
                                </Button>
                                <Button variant="outline" size="sm" disabled={!['completed', 'failed'].includes(task.status) || !isOfflineMode}>
                                  <RotateCw className="h-3.5 w-3.5 mr-1" />
                                  重试
                                </Button>
                              </div>
                              <Button variant="link" size="sm" disabled={task.status !== 'completed' || !isOfflineMode}>
                                <FileText className="h-3.5 w-3.5 mr-1" />
                                查看结果
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}

                  <div className="mt-4">
                    <Button variant="outline" size="sm" disabled={!isOfflineMode}>
                      <PlusCircle className="h-4 w-4 mr-2" />
                      创建分析任务
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </TooltipProvider>
  );
}