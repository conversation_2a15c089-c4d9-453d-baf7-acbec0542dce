"use client"

import { MainLayout } from "@/components/main-layout"
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, Upload, message, Row, Col, Statistic, Tooltip, Badge, Progress, Drawer, Popconfirm } from "antd"
import { useState, useEffect } from "react"
import { UploadOutlined, FileTextOutlined, DeleteOutlined, EditOutlined, EyeOutlined, DownloadOutlined, ReloadOutlined, SearchOutlined, FilterOutlined, Bar<PERSON>hartOutlined, ExportOutlined, PrinterOutlined, CloudUploadOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons"
import type { UploadProps } from "antd"
import dayjs from "dayjs"
import * as XLSX from 'xlsx'
import axios from 'axios'

const { RangePicker } = DatePicker;
const { Option } = Select;

interface ProjectFile {
  id: string
  name: string
  type: string
  category: string
  uploadTime: string
  uploader: string
  status: string
  description: string
  size?: string
  version?: string
  lastModified?: string
  downloads?: number
  tags?: string[]
}

export default function ProjectFileManagementPage() {
  const [files, setFiles] = useState<ProjectFile[]>([
    {
      id: "1",
      name: "施工方案.pdf",
      type: "PDF",
      category: "施工文档",
      uploadTime: "2025-03-14",
      uploader: "张三",
      status: "已审核",
      description: "项目施工方案文档",
      size: "2.5MB",
      version: "1.0",
      lastModified: "2025-03-14",
      downloads: 5,
      tags: ["重要", "已完成"],
    },
    {
      id: "2",
      name: "质量报告.docx",
      type: "Word",
      category: "质量文档",
      uploadTime: "2025-03-13",
      uploader: "李四",
      status: "待审核",
      description: "工程质量检查报告",
      size: "1.8MB",
      version: "2.1",
      lastModified: "2025-03-13",
      downloads: 3,
      tags: ["待审核"],
    },
  ])

  // 状态管理
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isStatsVisible, setIsStatsVisible] = useState(false)
  const [editingFile, setEditingFile] = useState<ProjectFile | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])

  // 统计数据
  const statistics = {
    total: files.length,
    reviewed: files.filter(f => f.status === "已审核").length,
    pending: files.filter(f => f.status === "待审核").length,
    totalSize: files.reduce((acc, curr) => acc + parseFloat(curr.size?.replace("MB", "") || "0"), 0).toFixed(1),
    byCategory: {
      construction: files.filter(f => f.category === "施工文档").length,
      quality: files.filter(f => f.category === "质量文档").length,
      safety: files.filter(f => f.category === "安全文档").length,
      contract: files.filter(f => f.category === "合同文档").length,
    }
  }

  const columns = [
    {
      title: "文件名",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ProjectFile) => (
        <Space>
          <FileTextOutlined />
          <Tooltip title="点击查看详情">
            <a onClick={() => handleView(record)}>{text}</a>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      render: (type: string) => (
        <Tag color={
          type === "PDF" ? "blue" :
          type === "Word" ? "green" :
          type === "Excel" ? "gold" : "purple"
        }>{type}</Tag>
      ),
    },
    {
      title: "分类",
      dataIndex: "category",
      key: "category",
      render: (category: string) => (
        <Tag color={
          category === "施工文档" ? "cyan" :
          category === "质量文档" ? "green" :
          category === "安全文档" ? "red" : "orange"
        }>{category}</Tag>
      ),
    },
    {
      title: "大小",
      dataIndex: "size",
      key: "size",
    },
    {
      title: "版本",
      dataIndex: "version",
      key: "version",
    },
    {
      title: "上传时间",
      dataIndex: "uploadTime",
      key: "uploadTime",
      render: (time: string) => (
        <Space>
          <ClockCircleOutlined />
          {time}
        </Space>
      ),
    },
    {
      title: "上传人",
      dataIndex: "uploader",
      key: "uploader",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Badge
          status={status === "已审核" ? "success" : "warning"}
          text={status}
        />
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: ProjectFile) => (
        <Space size="middle">
          <Tooltip title="查看">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleView(record)} />
          </Tooltip>
          <Tooltip title="下载">
            <Button type="link" icon={<DownloadOutlined />} onClick={() => handleDownload(record)} />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个文件吗？"
              onConfirm={() => handleDelete(record)}
            >
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 处理函数
  const handleView = (file: ProjectFile) => {
    Modal.info({
      title: "文件详情",
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>文件名：</strong>{file.name}
            </div>
            <div>
              <strong>类型：</strong>
              <Tag color={file.type === "PDF" ? "blue" : "green"}>{file.type}</Tag>
            </div>
            <div>
              <strong>分类：</strong>
              <Tag color={
                file.category === "施工文档" ? "cyan" :
                file.category === "质量文档" ? "green" :
                file.category === "安全文档" ? "red" : "orange"
              }>{file.category}</Tag>
            </div>
            <div>
              <strong>状态：</strong>
              <Badge status={file.status === "已审核" ? "success" : "warning"} text={file.status} />
            </div>
            <div>
              <strong>大小：</strong>{file.size}
            </div>
            <div>
              <strong>版本：</strong>{file.version}
            </div>
            <div>
              <strong>上传时间：</strong>{file.uploadTime}
            </div>
            <div>
              <strong>上传人：</strong>{file.uploader}
            </div>
            <div>
              <strong>最后修改：</strong>{file.lastModified}
            </div>
            <div>
              <strong>下载次数：</strong>{file.downloads}
            </div>
          </div>
          <div>
            <strong>标签：</strong>
            <div className="mt-2">
              {file.tags?.map(tag => (
                <Tag key={tag} color="blue">{tag}</Tag>
              ))}
            </div>
          </div>
        <div>
            <strong>描述：</strong>
            <p className="mt-2">{file.description}</p>
          </div>
        </div>
      ),
      width: 700,
    })
  }

  const handleEdit = (file: ProjectFile) => {
    setEditingFile(file)
    form.setFieldsValue(file)
    setIsModalVisible(true)
  }

  const handleDelete = async (file: ProjectFile) => {
    try {
      await axios.delete(`/api/files/${file.id}`);
      setFiles(files.filter(f => f.id !== file.id));
      message.success('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  const handleDownload = async (file: ProjectFile) => {
    try {
      message.loading({ content: '准备下载...', key: 'download' });

      // 这里应该替换为实际的文件下载API
      const response = await axios.get(`/api/files/${file.id}/download`, {
        responseType: 'blob'
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      // 更新下载次数
      setFiles(files.map(f =>
        f.id === file.id
          ? { ...f, downloads: (f.downloads || 0) + 1 }
          : f
      ));

      message.success({ content: '下载成功', key: 'download' });
    } catch (error) {
      console.error('下载失败:', error);
      message.error({ content: '下载失败', key: 'download' });
    }
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingFile) {
        // 更新文件
        const response = await axios.put(`/api/files/${editingFile.id}`, values);
        setFiles(files.map(f => f.id === editingFile.id ? response.data : f));
        message.success('更新成功');
      } else {
        // 新增文件
        const newFile: ProjectFile = {
          id: Date.now().toString(),
          ...values,
          uploadTime: "2025-03-15",
          uploader: '当前用户', // 这里应该使用实际的用户信息
          downloads: 0,
        };

        const response = await axios.post('/api/files', newFile);
        setFiles([...files, response.data]);
        message.success('添加成功');
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
    }
  }

  // 批量操作函数
  const handleBatchDelete = async () => {
    try {
      await axios.delete('/api/files/batch', {
        data: { ids: selectedRowKeys }
      });

      setFiles(files.filter(file => !selectedRowKeys.includes(file.id)));
      setSelectedRowKeys([]);
      message.success('批量删除成功');
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  }

  const handleBatchDownload = async () => {
    try {
      message.loading({ content: '准备下载...', key: 'batchDownload' });

      const selectedFiles = files.filter(file => selectedRowKeys.includes(file.id));

      // 这里应该替换为实际的批量下载API
      for (const file of selectedFiles) {
        await handleDownload(file);
      }

      setSelectedRowKeys([]);
      message.success({ content: '批量下载成功', key: 'batchDownload' });
    } catch (error) {
      console.error('批量下载失败:', error);
      message.error({ content: '批量下载失败', key: 'batchDownload' });
    }
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      // 准备导出数据
      const exportData = files.map(file => ({
        '文件名': file.name,
        '类型': file.type,
        '分类': file.category,
        '大小': file.size,
        '版本': file.version,
        '上传时间': file.uploadTime,
        '上传人': file.uploader,
        '状态': file.status,
        '描述': file.description,
        '下载次数': file.downloads,
        '标签': file.tags?.join(', ') || '',
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 20 }, // 文件名
        { wch: 10 }, // 类型
        { wch: 15 }, // 分类
        { wch: 10 }, // 大小
        { wch: 10 }, // 版本
        { wch: 15 }, // 上传时间
        { wch: 10 }, // 上传人
        { wch: 10 }, // 状态
        { wch: 30 }, // 描述
        { wch: 10 }, // 下载次数
        { wch: 20 }, // 标签
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '文档列表');

      // 导出文件
      XLSX.writeFile(wb, `工程文档列表_${dayjs().format('YYYY-MM-DD')}.xlsx`);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  }

  // 打印
  const handlePrint = () => {
    window.print()
  }

  // 刷新数据
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setSearchText(""); // 重置搜索条件
      setSelectedTypes([]); // 重置类型筛选
      setSelectedCategories([]); // 重置分类筛选
      setSelectedStatuses([]); // 重置状态筛选
      setDateRange([null, null]); // 重置日期范围

      // 模拟API调用，由于真实环境可能没有后端API
      setTimeout(() => {
        // 使用静态示例数据替代API调用
        const mockData: ProjectFile[] = [
          {
            id: "1",
            name: "施工方案.pdf",
            type: "PDF",
            category: "施工文档",
            uploadTime: "2024-03-14",
            uploader: "张三",
            status: "已审核",
            description: "项目施工方案文档",
            size: "2.5MB",
            version: "1.0",
            lastModified: "2024-03-14",
            downloads: 5,
            tags: ["重要", "已完成"],
          },
          {
            id: "2",
            name: "质量报告.docx",
            type: "Word",
            category: "质量文档",
            uploadTime: "2024-03-13",
            uploader: "李四",
            status: "待审核",
            description: "工程质量检查报告",
            size: "1.8MB",
            version: "2.1",
            lastModified: "2024-03-13",
            downloads: 3,
            tags: ["待审核"],
          },
          {
            id: "3",
            name: "安全检查记录.xlsx",
            type: "Excel",
            category: "安全文档",
            uploadTime: "2024-03-12",
            uploader: "王五",
            status: "已审核",
            description: "项目安全检查记录",
            size: "3.2MB",
            version: "1.0",
            lastModified: "2024-03-12",
            downloads: 2,
            tags: ["定期检查"],
          },
          {
            id: "4",
            name: "合同协议书.pdf",
            type: "PDF",
            category: "合同文档",
            uploadTime: "2024-03-10",
            uploader: "赵六",
            status: "已审核",
            description: "施工合同协议书",
            size: "5.6MB",
            version: "2.0",
            lastModified: "2024-03-10",
            downloads: 8,
            tags: ["重要", "法律文件"],
          },
          {
            id: "5",
            name: "设计图纸.jpg",
            type: "Image",
            category: "施工文档",
            uploadTime: "2024-03-08",
            uploader: "张三",
            status: "待审核",
            description: "施工设计图纸",
            size: "4.7MB",
            version: "1.5",
            lastModified: "2024-03-08",
            downloads: 4,
            tags: ["设计", "待审核"],
          },
        ];

        setFiles(mockData);
        message.success({
          content: '数据刷新成功',
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
        });
        setLoading(false);
      }, 800);
    } catch (error) {
      console.error('刷新失败:', error);
      message.error({
        content: '刷新失败，正在使用本地数据',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      });
      setLoading(false);
    }
  }

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: ProjectFile[]) => {
      setSelectedRowKeys(selectedKeys as string[])
    },
  }

  // 改进搜索和筛选
  const filteredFiles = files.filter(file => {
    let matchesSearch = true;
    if (searchText.trim() !== '') {
      matchesSearch = file.name.toLowerCase().includes(searchText.toLowerCase()) ||
                      file.description.toLowerCase().includes(searchText.toLowerCase()) ||
                      file.type.toLowerCase().includes(searchText.toLowerCase()) ||
                      file.category.toLowerCase().includes(searchText.toLowerCase()) ||
                      (file.tags?.some(tag => tag.toLowerCase().includes(searchText.toLowerCase())) || false);
    }

    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(file.type);
    const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(file.category);
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(file.status);

    let matchesDate = true;
    if (dateRange[0] && dateRange[1]) {
      const fileDate = dayjs(file.uploadTime);
      matchesDate = fileDate.isAfter(dateRange[0], 'day') || fileDate.isSame(dateRange[0], 'day') &&
                    (fileDate.isBefore(dateRange[1], 'day') || fileDate.isSame(dateRange[1], 'day'));
    }

    return matchesSearch && matchesType && matchesCategory && matchesStatus && matchesDate;
  });

  // 渲染统计抽屉
  const renderStatsDrawer = () => (
    <Drawer
      title="文档统计"
      placement="right"
      onClose={() => setIsStatsVisible(false)}
      open={isStatsVisible}
      width={400}
    >
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <Card>
          <Statistic
            title="文档审核率"
            value={((statistics.reviewed / statistics.total) * 100).toFixed(1)}
            suffix="%"
            prefix={<CheckCircleOutlined />}
          />
          <Progress
            percent={Number(((statistics.reviewed / statistics.total) * 100).toFixed(1))}
            status="active"
          />
        </Card>
        <Row gutter={16}>
          <Col span={12}>
            <Card>
              <Statistic
                title="总文档数"
                value={statistics.total}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card>
              <Statistic
                title="总容量"
                value={statistics.totalSize}
                suffix="MB"
                prefix={<CloudUploadOutlined />}
              />
            </Card>
          </Col>
        </Row>
        <Card title="文档分类统计">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Statistic
                title="施工文档"
                value={statistics.byCategory.construction}
                valueStyle={{ color: "#1890ff" }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="质量文档"
                value={statistics.byCategory.quality}
                valueStyle={{ color: "#52c41a" }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="安全文档"
                value={statistics.byCategory.safety}
                valueStyle={{ color: "#ff4d4f" }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="合同文档"
                value={statistics.byCategory.contract}
                valueStyle={{ color: "#faad14" }}
              />
            </Col>
          </Row>
        </Card>
      </Space>
    </Drawer>
  )

  // 添加文件上传相关配置
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/upload', // 这里需要替换为实际的上传接口
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 更新文件大小
        form.setFieldsValue({
          size: `${(info.file.size! / (1024 * 1024)).toFixed(2)}MB`
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
    beforeUpload(file) {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件必须小于100MB!');
      }
      return isLt100M;
    },
  };

  // 在组件加载时获取数据
  useEffect(() => {
    handleRefresh();
  }, []);

  return (
    <MainLayout>
      <div style={{ padding: "24px" }}>
        <Card
          title={
            <Space>
              <FileTextOutlined style={{ fontSize: '18px', color: '#1890ff' }} />
              <span style={{ fontSize: '18px', fontWeight: 'bold' }}>工程文档管理</span>
            </Space>
          }
          extra={
            <Space>
              <Button
                icon={<BarChartOutlined />}
                onClick={() => setIsStatsVisible(true)}
                style={{ background: '#f0f5ff', color: '#1890ff', borderColor: '#d6e4ff' }}
              >
                统计分析
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExportExcel}
                style={{ background: '#f6ffed', color: '#52c41a', borderColor: '#d9f7be' }}
              >
                导出Excel
              </Button>
              <Button
                icon={<PrinterOutlined />}
                onClick={handlePrint}
                style={{ background: '#fff0f6', color: '#eb2f96', borderColor: '#ffadd2' }}
              >
                打印
              </Button>
              <Button
                icon={<ReloadOutlined spin={loading} />}
                onClick={handleRefresh}
                loading={loading}
                style={{ background: '#f9f0ff', color: '#722ed1', borderColor: '#efdbff' }}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<CloudUploadOutlined />}
                onClick={() => {
                  setEditingFile(null)
                  form.resetFields()
                  setIsModalVisible(true)
                }}
              >
                上传文档
              </Button>
            </Space>
          }
          style={{ boxShadow: '0 1px 2px rgba(0,0,0,0.05)' }}
        >
          <Space direction="vertical" style={{ width: "100%" }} size="middle">
            {/* 统计卡片 - 美化 */}
            <Row gutter={16}>
              <Col span={6}>
                <Card hoverable style={{ borderLeft: '4px solid #1890ff', borderRadius: '4px' }}>
                  <Statistic
                    title={<span style={{ color: '#1890ff', fontWeight: 'bold' }}>总文档数</span>}
                    value={statistics.total}
                    prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card hoverable style={{ borderLeft: '4px solid #52c41a', borderRadius: '4px' }}>
                  <Statistic
                    title={<span style={{ color: '#52c41a', fontWeight: 'bold' }}>已审核</span>}
                    value={statistics.reviewed}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card hoverable style={{ borderLeft: '4px solid #faad14', borderRadius: '4px' }}>
                  <Statistic
                    title={<span style={{ color: '#faad14', fontWeight: 'bold' }}>待审核</span>}
                    value={statistics.pending}
                    valueStyle={{ color: '#faad14' }}
                    prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card hoverable style={{ borderLeft: '4px solid #13c2c2', borderRadius: '4px' }}>
                  <Statistic
                    title={<span style={{ color: '#13c2c2', fontWeight: 'bold' }}>总容量</span>}
                    value={statistics.totalSize}
                    suffix="MB"
                    valueStyle={{ color: '#13c2c2' }}
                    prefix={<CloudUploadOutlined style={{ color: '#13c2c2' }} />}
                  />
                </Card>
              </Col>
            </Row>

            {/* 搜索和筛选 - 改进 */}
            <Card style={{ background: '#fafafa', borderRadius: '8px' }}>
              <Space direction="vertical" style={{ width: "100%" }} size="middle">
                <Row gutter={16} align="middle">
                  <Col span={8}>
                    <Input
                      placeholder="搜索文件名、描述、标签..."
                      prefix={<SearchOutlined />}
                      value={searchText}
                      onChange={e => setSearchText(e.target.value)}
                      allowClear
                      style={{ borderRadius: '6px' }}
                    />
                  </Col>
                  <Col span={16}>
                    <Space wrap>
                      <Select
                        mode="multiple"
                        placeholder="文件类型"
                        style={{ width: 150, borderRadius: '6px' }}
                        value={selectedTypes}
                        onChange={setSelectedTypes}
                        allowClear
                        maxTagCount={2}
                      >
                        <Option value="PDF">PDF</Option>
                        <Option value="Word">Word</Option>
                        <Option value="Excel">Excel</Option>
                        <Option value="Image">图片</Option>
                      </Select>
                      <Select
                        mode="multiple"
                        placeholder="文档分类"
                        style={{ width: 150, borderRadius: '6px' }}
                        value={selectedCategories}
                        onChange={setSelectedCategories}
                        allowClear
                        maxTagCount={2}
                      >
                        <Option value="施工文档">施工文档</Option>
                        <Option value="质量文档">质量文档</Option>
                        <Option value="安全文档">安全文档</Option>
                        <Option value="合同文档">合同文档</Option>
                      </Select>
                      <Select
                        mode="multiple"
                        placeholder="状态"
                        style={{ width: 150, borderRadius: '6px' }}
                        value={selectedStatuses}
                        onChange={setSelectedStatuses}
                        allowClear
                        maxTagCount={2}
                      >
                        <Option value="已审核">已审核</Option>
                        <Option value="待审核">待审核</Option>
                      </Select>
                      <RangePicker
                        value={dateRange}
                        onChange={(dates) => setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null])}
                        style={{ borderRadius: '6px' }}
                        allowClear
                      />
                      <Button
                        type="default"
                        onClick={handleRefresh}
                        icon={<ReloadOutlined />}
                        style={{ borderRadius: '6px' }}
                      >
                        重置筛选
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </Space>
            </Card>

            {/* 筛选状态提示 */}
            {(selectedTypes.length > 0 || selectedCategories.length > 0 || selectedStatuses.length > 0 || (dateRange[0] && dateRange[1]) || searchText) && (
              <div style={{ background: '#e6f7ff', padding: '8px 16px', borderRadius: '4px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <span style={{ fontWeight: 'bold', marginRight: '8px' }}>筛选条件:</span>
                  {searchText && <Tag color="blue">关键词: {searchText}</Tag>}
                  {selectedTypes.length > 0 && <Tag color="purple">类型: {selectedTypes.join(', ')}</Tag>}
                  {selectedCategories.length > 0 && <Tag color="cyan">分类: {selectedCategories.join(', ')}</Tag>}
                  {selectedStatuses.length > 0 && <Tag color="green">状态: {selectedStatuses.join(', ')}</Tag>}
                  {(dateRange[0] && dateRange[1]) && (
                    <Tag color="orange">日期: {dateRange[0]?.format('YYYY-MM-DD')} 至 {dateRange[1]?.format('YYYY-MM-DD')}</Tag>
                  )}
                </div>
                <div>
                  <span style={{ marginRight: '8px' }}>找到 {filteredFiles.length} 个文件</span>
                  <Button type="link" onClick={handleRefresh} size="small">清除所有筛选</Button>
                </div>
              </div>
            )}

            {/* 批量操作 */}
            {selectedRowKeys.length > 0 && (
              <div style={{ background: '#fff1f0', padding: '12px 16px', borderRadius: '4px' }}>
                <Space>
                  <span style={{ fontWeight: 'bold' }}>已选择 {selectedRowKeys.length} 个文件</span>
                  <Button
                    onClick={handleBatchDownload}
                    icon={<DownloadOutlined />}
                    style={{ background: '#f0f5ff', color: '#1890ff', borderColor: '#d6e4ff' }}
                  >
                    批量下载
                  </Button>
                  <Popconfirm
                    title="确定要删除选中的文件吗？"
                    description="此操作不可恢复，请谨慎操作"
                    onConfirm={handleBatchDelete}
                    okText="确定删除"
                    cancelText="取消"
                  >
                    <Button danger icon={<DeleteOutlined />}>
                      批量删除
                    </Button>
                  </Popconfirm>
                </Space>
              </div>
            )}

            {/* 文件表格 - 美化 */}
            {filteredFiles.length > 0 ? (
              <Table
                rowSelection={rowSelection}
                columns={columns}
                dataSource={filteredFiles}
                rowKey="id"
                loading={loading}
                pagination={{
                  defaultPageSize: 10,
                  showSizeChanger: true,
                  pageSizeOptions: ['10', '20', '50'],
                  showTotal: total => `共 ${total} 个文件`
                }}
                style={{ borderRadius: '8px', overflow: 'hidden' }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', background: '#fafafa', borderRadius: '8px' }}>
                <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <p style={{ fontSize: '16px', color: '#8c8c8c', marginTop: '16px' }}>暂无符合条件的文档</p>
                <Button type="primary" onClick={handleRefresh} style={{ marginTop: '16px' }}>刷新数据</Button>
              </div>
            )}
          </Space>
        </Card>
      </div>

      {/* 文档统计抽屉 */}
      {renderStatsDrawer()}

      {/* 添加/编辑文档模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {editingFile ? <EditOutlined style={{ marginRight: '8px', color: '#1890ff' }} /> : <CloudUploadOutlined style={{ marginRight: '8px', color: '#1890ff' }} />}
            <span>{editingFile ? "编辑文档" : "上传文档"}</span>
          </div>
        }
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        width={700}
        okText={editingFile ? "保存" : "上传"}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
            <Form.Item
              name="name"
              label="文件名"
              rules={[{ required: true, message: "请输入文件名" }]}
            >
              <Input />
            </Form.Item>
            </Col>
            <Col span={12}>
            <Form.Item
              name="type"
              label="类型"
              rules={[{ required: true, message: "请选择类型" }]}
            >
              <Select>
                  <Option value="PDF">PDF</Option>
                  <Option value="Word">Word</Option>
                  <Option value="Excel">Excel</Option>
                  <Option value="Image">图片</Option>
              </Select>
            </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
            <Form.Item
              name="category"
              label="分类"
              rules={[{ required: true, message: "请选择分类" }]}
            >
              <Select>
                  <Option value="施工文档">施工文档</Option>
                  <Option value="质量文档">质量文档</Option>
                  <Option value="安全文档">安全文档</Option>
                  <Option value="合同文档">合同文档</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: "请选择状态" }]}
              >
                <Select>
                  <Option value="待审核">待审核</Option>
                  <Option value="已审核">已审核</Option>
              </Select>
            </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="version"
                label="版本"
                rules={[{ required: true, message: "请输入版本号" }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="size"
                label="文件大小"
                rules={[{ required: true, message: "请输入文件大小" }]}
              >
                <Input suffix="MB" />
              </Form.Item>
            </Col>
          </Row>
            <Form.Item
              name="description"
              label="描述"
              rules={[{ required: true, message: "请输入描述" }]}
            >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="tags"
            label="标签"
          >
            <Select mode="tags" style={{ width: "100%" }} placeholder="请输入标签">
              <Option value="重要">重要</Option>
              <Option value="已完成">已完成</Option>
              <Option value="待审核">待审核</Option>
            </Select>
            </Form.Item>
            <Form.Item label="上传文件">
            <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
            </Form.Item>
          </Form>
        </Modal>
    </MainLayout>
  )
}