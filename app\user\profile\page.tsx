"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAuth } from "@/contexts/auth-context"
import { Separator } from "@/components/ui/separator"

export default function ProfilePage() {
  const { user } = useAuth()
  const [formData, setFormData] = useState({
    username: user?.username || "",
    fullName: "用户全名",
    email: user?.email || "",
    phone: "13800138000",
    department: "工程部",
    position: "工程师",
    avatar: user?.avatar || ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    alert("资料更新成功！")
  }

  return (
    <div className="container py-10">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/4">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">个人资料</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <Avatar className="w-32 h-32 mb-4">
                <AvatarImage src={formData.avatar} alt={formData.username} />
                <AvatarFallback className="text-4xl">
                  {formData.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <h3 className="text-xl font-medium">{formData.username}</h3>
              <p className="text-sm text-muted-foreground mt-1">{formData.position}</p>
              <p className="text-sm text-muted-foreground">{formData.department}</p>
              
              <Separator className="my-4" />
              
              <div className="w-full space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">状态</span>
                  <span className="text-sm font-medium">在线</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">上次登录</span>
                  <span className="text-sm">今天 08:30</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">账户角色</span>
                  <span className="text-sm">{user?.role || "管理员"}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="md:w-3/4">
          <Tabs defaultValue="basic">
            <TabsList>
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="security">安全设置</TabsTrigger>
              <TabsTrigger value="preferences">偏好设置</TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>更新您的个人基本信息</CardDescription>
                </CardHeader>
                <form onSubmit={handleSubmit}>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="username">用户名</Label>
                        <Input 
                          id="username" 
                          value={formData.username} 
                          onChange={(e) => setFormData({...formData, username: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="fullName">姓名</Label>
                        <Input 
                          id="fullName" 
                          value={formData.fullName} 
                          onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">邮箱</Label>
                        <Input 
                          id="email" 
                          type="email" 
                          value={formData.email} 
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">手机号</Label>
                        <Input 
                          id="phone" 
                          value={formData.phone} 
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="department">部门</Label>
                        <Input 
                          id="department" 
                          value={formData.department} 
                          onChange={(e) => setFormData({...formData, department: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="position">职位</Label>
                        <Input 
                          id="position" 
                          value={formData.position} 
                          onChange={(e) => setFormData({...formData, position: e.target.value})}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit">保存更改</Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
            
            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>安全设置</CardTitle>
                  <CardDescription>管理您的账户安全设置</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">修改密码</h4>
                        <p className="text-sm text-muted-foreground">更新您的登录密码</p>
                      </div>
                      <Button variant="outline" onClick={() => window.location.href = "/user/change-password"}>
                        修改
                      </Button>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">双因素认证</h4>
                        <p className="text-sm text-muted-foreground">增强账户安全性</p>
                      </div>
                      <Button variant="outline">设置</Button>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">登录记录</h4>
                        <p className="text-sm text-muted-foreground">查看您的登录历史记录</p>
                      </div>
                      <Button variant="outline">查看</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="preferences">
              <Card>
                <CardHeader>
                  <CardTitle>偏好设置</CardTitle>
                  <CardDescription>自定义您的使用体验</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">语言设置</h4>
                        <p className="text-sm text-muted-foreground">选择系统界面语言</p>
                      </div>
                      <Button variant="outline">设置</Button>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">通知设置</h4>
                        <p className="text-sm text-muted-foreground">管理系统通知和提醒</p>
                      </div>
                      <Button variant="outline">设置</Button>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">界面主题</h4>
                        <p className="text-sm text-muted-foreground">自定义界面外观</p>
                      </div>
                      <Button variant="outline">设置</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
} 