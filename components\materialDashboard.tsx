"use client"

import { useState } from 'react';
import { Card, Tabs, TabsProps } from 'antd';
import { BarChartOutlined, TableOutlined } from '@ant-design/icons';
import { MaterialMaintenance } from './materialmaintenance';
import { VisualDashboard } from './visualDashboard/index';

export function MaterialDashboard() {
  const [activeTab, setActiveTab] = useState<string>('table');

  const items: TabsProps['items'] = [
    {
      key: 'table',
      label: (
        <span>
          <TableOutlined />
          表格模式
        </span>
      ),
      children: <MaterialMaintenance />,
    },
    {
      key: 'dashboard',
      label: (
        <span>
          <BarChartOutlined />
          可视化大屏
        </span>
      ),
      children: <VisualDashboard />,
    },
  ];

  return (
    <div className="material-dashboard-container" style={{ height: 'calc(100vh - 64px)' }}>
      <Tabs
        defaultActiveKey="table"
        items={items}
        onChange={setActiveTab}
        tabBarStyle={{ margin: '0 16px' }}
        tabBarGutter={30}
        size="large"
        className="custom-tabs"
      />
      
      <style jsx global>{`
        .material-dashboard-container .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow: ${activeTab === 'dashboard' ? 'hidden' : 'auto'};
        }
        
        .material-dashboard-container .ant-tabs-content {
          height: 100%;
        }
        
        .material-dashboard-container .ant-tabs-tabpane {
          height: 100%;
        }
        
        .custom-tabs .ant-tabs-tab {
          padding: 12px 16px;
          transition: all 0.3s;
        }
        
        .custom-tabs .ant-tabs-tab:hover {
          color: #1890ff;
        }
        
        .custom-tabs .ant-tabs-tab-active {
          background-color: #e6f7ff;
          border-radius: 4px 4px 0 0;
        }
      `}</style>
    </div>
  );
} 