"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  Share2,
  Upload,
  RefreshCcw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, <PERSON><PERSON>ontent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"

interface EmergencyPlan {
  id: string
  title: string
  type: string
  department: string
  approver: string
  approvalDate: string
  reviewDate: string
  nextReviewDate: string
  status: string
  version: string
  description?: string
  purpose?: string
  scope?: string
  responsibility?: string
  procedure?: string
  disabled?: boolean
}

export function EmergencyPlanManagement() {
  const [isAddPlanOpen, setIsAddPlanOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<EmergencyPlan | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [loading, setLoading] = useState(false)
  const [plans, setPlans] = useState<EmergencyPlan[]>([
    {
      id: "1",
      title: "矿区火灾应急预案",
      type: "综合预案",
      department: "安全管理部",
      approver: "张总经理",
      approvalDate: "2025-01-15",
      reviewDate: "2025-02-15",
      nextReviewDate: "2025-04-15",
      status: "已审批",
      version: "V2.1",
      description: "针对矿区可能发生的火灾事故制定的应急预案...",
      purpose: "为有效预防和应对矿区火灾事故...",
      scope: "适用于矿区所有区域的火灾事故处置...",
      responsibility: "总经理负总责，安全部具体实施...",
      procedure: "发现火情立即报告，启动应急响应..."
    },
    {
      id: "2",
      title: "井下透水事故应急预案",
      type: "专项预案",
      department: "安全管理部",
      approver: "李副总",
      approvalDate: "2025-01-20",
      reviewDate: "2025-02-20",
      nextReviewDate: "2025-04-10",
      status: "已审批",
      version: "V1.5",
    },
    {
      id: "3",
      title: "爆破事故应急预案",
      type: "专项预案",
      department: "爆破作业部",
      approver: "王部长",
      approvalDate: "2025-01-10",
      reviewDate: "2025-02-10",
      nextReviewDate: "2025-04-10",
      status: "已审批",
      version: "V1.2",
    },
    {
      id: "4",
      title: "有毒气体泄漏应急预案",
      type: "专项预案",
      department: "安全管理部",
      approver: "张总经理",
      approvalDate: "2025-01-25",
      reviewDate: "2025-02-25",
      nextReviewDate: "2025-04-05",
      status: "已审批",
      version: "V1.0",
    },
    {
      id: "5",
      title: "设备故障应急预案",
      type: "现场处置方案",
      department: "设备管理部",
      approver: "赵部长",
      approvalDate: "2025-02-18",
      reviewDate: "2025-03-18",
      nextReviewDate: "2025-04-15",
      status: "待审批",
      version: "V1.0",
    },
  ])

  // 统计数据
  const statistics = {
    total: plans.length,
    approved: plans.filter(p => p.status === "已审批" && !p.disabled).length,
    pending: plans.filter(p => p.status === "待审批").length,
    needUpdate: plans.filter(p => {
      const nextReview = new Date(p.nextReviewDate)
      const today = new Date()
      return nextReview <= today
    }).length
  }

  // 处理搜索和筛选
  const filteredPlans = plans.filter(plan => {
    const matchesSearch = plan.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         plan.description?.toLowerCase().includes(searchText.toLowerCase()) ||
                         plan.department.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || plan.type === selectedType
    const matchesDepartment = selectedDepartment === "all" || plan.department === selectedDepartment
    const matchesStatus = selectedStatus === "all" || plan.status === selectedStatus
    return matchesSearch && matchesType && matchesDepartment && matchesStatus
  })

  // 处理添加预案
  const handleAddPlan = (formData: any) => {
    const newPlan: EmergencyPlan = {
      id: Date.now().toString(),
      ...formData,
    }
    setPlans([...plans, newPlan])
    setIsAddPlanOpen(false)
    message.success("预案添加成功")
  }

  // 处理删除预案
  const handleDeletePlan = (id: string) => {
    const updatedPlans = plans.filter(p => p.id !== id)
    setPlans(updatedPlans)
    message.success("预案删除成功")
  }

  // 处理禁用预案
  const handleDisablePlan = (id: string) => {
    const updatedPlans = plans.map(p =>
      p.id === id ? { ...p, disabled: !p.disabled } : p
    )
    setPlans(updatedPlans)
    message.success(`预案${plans.find(p => p.id === id)?.disabled ? "启用" : "禁用"}成功`)
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      const exportData = plans.map(plan => ({
        '预案标题': plan.title,
        '类型': plan.type,
        '责任部门': plan.department,
        '审批人': plan.approver,
        '审批日期': plan.approvalDate,
        '评审日期': plan.reviewDate,
        '下次评审': plan.nextReviewDate,
        '状态': plan.status,
        '版本': plan.version,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 40 }, // 预案标题
        { wch: 15 }, // 类型
        { wch: 15 }, // 责任部门
        { wch: 15 }, // 审批人
        { wch: 15 }, // 审批日期
        { wch: 15 }, // 评审日期
        { wch: 15 }, // 下次评审
        { wch: 15 }, // 状态
        { wch: 10 }, // 版本
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '应急预案列表')
      XLSX.writeFile(wb, `应急预案列表_${new Date().toISOString().split('T')[0]}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 导入Excel
  const handleImportExcel = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 转换导入的数据格式
        const importedPlans: EmergencyPlan[] = jsonData.map((item: any) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          title: item['预案标题'],
          type: item['类型'],
          department: item['责任部门'],
          approver: item['审批人'],
          approvalDate: item['审批日期'],
          reviewDate: item['评审日期'],
          nextReviewDate: item['下次评审'],
          status: item['状态'],
          version: item['版本'],
        }))

        setPlans([...plans, ...importedPlans])
        message.success('导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">应急预案管理</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">应急预案总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.approved}</h3>
            <p className="text-sm text-muted-foreground">已审批预案</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.pending}</h3>
            <p className="text-sm text-muted-foreground">待审批预案</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.needUpdate}</h3>
            <p className="text-sm text-muted-foreground">需要更新</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>应急预案列表</CardTitle>
              <CardDescription>管理公司各类应急预案</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索预案..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="预案类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="综合预案">综合预案</SelectItem>
                    <SelectItem value="专项预案">专项预案</SelectItem>
                    <SelectItem value="现场处置方案">现场处置方案</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有部门</SelectItem>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="爆破作业部">爆破作业部</SelectItem>
                    <SelectItem value="设备管理部">设备管理部</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="已审批">已审批</SelectItem>
                    <SelectItem value="待审批">待审批</SelectItem>
                    <SelectItem value="已废止">已废止</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={handleRefresh}>
                  <RefreshCcw className="h-4 w-4" />
                </Button>
                <Button variant="outline" onClick={handleExportExcel}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Input
                  type="file"
                  accept=".xlsx,.xls"
                  className="hidden"
                  id="import-excel"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleImportExcel(file)
                  }}
                />
                <Button variant="outline" onClick={() => document.getElementById('import-excel')?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  导入
                </Button>
                <Button onClick={() => setIsAddPlanOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加预案
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>预案标题</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>责任部门</TableHead>
                    <TableHead>审批人</TableHead>
                    <TableHead>审批日期</TableHead>
                    <TableHead>评审日期</TableHead>
                    <TableHead>下次评审</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>版本</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPlans.map((plan) => (
                    <TableRow key={plan.id} className={plan.disabled ? "opacity-50" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{plan.title}</span>
                        </div>
                      </TableCell>
                      <TableCell>{plan.type}</TableCell>
                      <TableCell>{plan.department}</TableCell>
                      <TableCell>{plan.approver}</TableCell>
                      <TableCell>{plan.approvalDate}</TableCell>
                      <TableCell>{plan.reviewDate}</TableCell>
                      <TableCell>{plan.nextReviewDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            plan.status === "已审批"
                              ? "default"
                              : plan.status === "待审批"
                                ? "secondary"
                                : "outline"
                          }
                        >
                          {plan.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{plan.version}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedPlan(plan)
                              setIsViewModalOpen(true)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedPlan(plan)
                                setIsViewModalOpen(true)
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDisablePlan(plan.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                {plan.disabled ? "启用" : "禁用"}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeletePlan(plan.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredPlans.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>预案详情</DialogTitle>
          </DialogHeader>
          {selectedPlan && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>预案标题</Label>
                <p className="text-sm">{selectedPlan.title}</p>
              </div>
              <div className="space-y-2">
                <Label>类型</Label>
                <p className="text-sm">{selectedPlan.type}</p>
              </div>
              <div className="space-y-2">
                <Label>责任部门</Label>
                <p className="text-sm">{selectedPlan.department}</p>
              </div>
              <div className="space-y-2">
                <Label>审批人</Label>
                <p className="text-sm">{selectedPlan.approver}</p>
              </div>
              <div className="space-y-2">
                <Label>审批日期</Label>
                <p className="text-sm">{selectedPlan.approvalDate}</p>
              </div>
              <div className="space-y-2">
                <Label>评审日期</Label>
                <p className="text-sm">{selectedPlan.reviewDate}</p>
              </div>
              <div className="space-y-2">
                <Label>下次评审日期</Label>
                <p className="text-sm">{selectedPlan.nextReviewDate}</p>
              </div>
              <div className="space-y-2">
                <Label>版本</Label>
                <p className="text-sm">{selectedPlan.version}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>编制目的</Label>
                <p className="text-sm">{selectedPlan.purpose}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>适用范围</Label>
                <p className="text-sm">{selectedPlan.scope}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>职责分工</Label>
                <p className="text-sm">{selectedPlan.responsibility}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>应急处置程序</Label>
                <p className="text-sm">{selectedPlan.procedure}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

