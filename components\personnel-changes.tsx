"use client"

import { useState, useEffect } from "react"
import {
  Search,
  Plus,
  Download,
  Upload,
  FileText,
  Edit,
  Trash2,
  Users,
  ArrowUpRight,
  ArrowDownRight,
  UserMinus,
  Calendar,
  Eye,
  Ban,
  CheckCircle2,
  Clock
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Pie, <PERSON>, <PERSON>, <PERSON>hn<PERSON> } from "react-chartjs-2"
import { ChartData } from "chart.js"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

// 人员异动类型定义
interface PersonnelChange {
  id: string
  employeeName: string
  employeeId: string
  changeType: '入职' | '调岗' | '离职' | '晋升'
  fromDepartment?: string
  toDepartment?: string
  fromPosition?: string
  toPosition?: string
  effectiveDate: string
  approvalStatus: '待审批' | '已通过' | '已拒绝'
  reason: string
  remarks?: string
  submitter: string
  submitDate: string
}

// 表单数据类型
interface FormData {
  employeeName: string
  employeeId: string
  changeType: '入职' | '调岗' | '离职' | '晋升'
  fromDepartment?: string
  toDepartment?: string
  fromPosition?: string
  toPosition?: string
  effectiveDate: string
  reason: string
  remarks?: string
}

export function PersonnelChanges() {
  // 状态定义
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentChange, setCurrentChange] = useState<PersonnelChange | null>(null)
  const [formData, setFormData] = useState<FormData>({
    employeeName: "",
    employeeId: "",
    changeType: "入职",
    effectiveDate: "",
    reason: ""
  })

  const { toast } = useToast()

  // 模拟数据
  const personnelChanges: PersonnelChange[] = [
    {
      id: "1",
      employeeName: "张三",
      employeeId: "EMP001",
      changeType: "入职",
      toDepartment: "工程管理部",
      toPosition: "工程师",
      effectiveDate: "2025-01-15",
      approvalStatus: "已通过",
      reason: "业务发展需要",
      submitter: "李四",
      submitDate: "2025-01-10"
    },
    {
      id: "2",
      employeeName: "王五",
      employeeId: "EMP002",
      changeType: "调岗",
      fromDepartment: "安全管理部",
      toDepartment: "工程管理部",
      fromPosition: "安全员",
      toPosition: "安全主管",
      effectiveDate: "2025-01-20",
      approvalStatus: "待审批",
      reason: "岗位调整",
      submitter: "赵六",
      submitDate: "2025-01-18"
    },
    {
      id: "3",
      employeeName: "李四",
      employeeId: "EMP003",
      changeType: "离职",
      fromDepartment: "人事管理部",
      fromPosition: "人事专员",
      effectiveDate: "2025-01-25",
      approvalStatus: "已通过",
      reason: "个人原因",
      submitter: "王五",
      submitDate: "2025-01-20"
    }
  ]

  // 统计数据
  const statistics = {
    total: personnelChanges.length,
    pending: personnelChanges.filter(c => c.approvalStatus === "待审批").length,
    approved: personnelChanges.filter(c => c.approvalStatus === "已通过").length,
    rejected: personnelChanges.filter(c => c.approvalStatus === "已拒绝").length,
    byType: {
      entry: personnelChanges.filter(c => c.changeType === "入职").length,
      transfer: personnelChanges.filter(c => c.changeType === "调岗").length,
      promotion: personnelChanges.filter(c => c.changeType === "晋升").length,
      resignation: personnelChanges.filter(c => c.changeType === "离职").length
    }
  }

  // 筛选记录
  const filteredChanges = personnelChanges.filter(change => {
    const matchesSearch =
      change.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      change.employeeId.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = selectedType === "all" ? true : change.changeType === selectedType
    const matchesStatus = selectedStatus === "all" ? true : change.approvalStatus === selectedStatus
    const matchesDepartment = selectedDepartment === "all" ? true :
      change.fromDepartment === selectedDepartment || change.toDepartment === selectedDepartment

    return matchesSearch && matchesType && matchesStatus && matchesDepartment
  })

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待审批":
        return <Badge className="bg-yellow-100 text-yellow-700">待审批</Badge>
      case "已通过":
        return <Badge className="bg-green-100 text-green-700">已通过</Badge>
      case "已拒绝":
        return <Badge variant="destructive">已拒绝</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取异动类型对应的徽章样式
  const getTypeBadge = (type: string) => {
    switch (type) {
      case "入职":
        return <Badge className="bg-blue-100 text-blue-700">入职</Badge>
      case "调岗":
        return <Badge className="bg-purple-100 text-purple-700">调岗</Badge>
      case "晋升":
        return <Badge className="bg-amber-100 text-amber-700">晋升</Badge>
      case "离职":
        return <Badge className="bg-red-100 text-red-700">离职</Badge>
      default:
        return <Badge>{type}</Badge>
    }
  }

  // 导出功能实现
  const handleExport = async () => {
    try {
      // 准备导出数据
      const exportData = personnelChanges.map(change => {
        const data = {
          员工姓名: change.employeeName,
          员工工号: change.employeeId,
          异动类型: change.changeType,
          原部门: change.fromDepartment || '',
          新部门: change.toDepartment || '',
          原职位: change.fromPosition || '',
          新职位: change.toPosition || '',
          生效日期: change.effectiveDate,
          审批状态: change.approvalStatus,
          异动原因: change.reason,
          备注: change.remarks || '',
          提交人: change.submitter,
          提交日期: change.submitDate
        }
        return data as Record<string, string>
      })

      // 转换为CSV格式
      const headers = Object.keys(exportData[0])
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
      ].join('\n')

      // 创建Blob并下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `人员异动记录_${new Date().toLocaleDateString()}.csv`
      link.click()

      toast({
        title: "导出成功",
        description: "数据已成功导出为CSV文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 数据分析函数
  const getAnalysisData = () => {
    // 异动类型统计
    const typeStats = personnelChanges.reduce((acc, curr) => {
      acc[curr.changeType] = (acc[curr.changeType] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 月度异动趋势
    const monthlyStats = personnelChanges.reduce((acc, curr) => {
      const month = curr.effectiveDate.substring(0, 7)
      acc[month] = (acc[month] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 部门异动分析
    const departmentStats = personnelChanges.reduce((acc, curr) => {
      if (curr.toDepartment) {
        acc[curr.toDepartment] = (acc[curr.toDepartment] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    // 审批状态统计
    const approvalStats = personnelChanges.reduce((acc, curr) => {
      acc[curr.approvalStatus] = (acc[curr.approvalStatus] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      typeStats,
      monthlyStats,
      departmentStats,
      approvalStats
    }
  }

  // 数据可视化组件
  const DataVisualization = () => {
    const analysisData = getAnalysisData()

    // 异动类型数据
    const typeChartData: ChartData<'pie'> = {
      labels: Object.keys(analysisData.typeStats),
      datasets: [{
        data: Object.values(analysisData.typeStats),
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 206, 86, 0.8)'
        ]
      }]
    }

    // 月度趋势数据
    const monthlyChartData: ChartData<'line'> = {
      labels: ['2023-09', '2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03'],
      datasets: [{
        label: '异动数量',
        data: [5, 8, 12, 7, 15, 10, 18],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.3,
        borderWidth: 3,
        pointRadius: 6,
        pointBackgroundColor: 'white',
        pointBorderColor: 'rgb(59, 130, 246)',
        pointBorderWidth: 2,
        pointHoverRadius: 8,
        pointHoverBackgroundColor: 'rgb(59, 130, 246)',
        pointHoverBorderColor: 'white',
        pointHoverBorderWidth: 2
      }]
    }

    // 月度趋势配置
    const monthlyChartOptions = {
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          },
          ticks: {
            font: {
              size: 12
            }
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 12
            }
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          padding: 12,
          titleFont: {
            size: 14
          },
          bodyFont: {
            size: 13
          }
        }
      }
    }

    // 部门异动分析数据处理
    const departmentAnalysis = Object.entries(analysisData.departmentStats)
      .sort((a, b) => b[1] - a[1])
      .map(([dept, count]) => {
        const percentage = (count / personnelChanges.length) * 100
        let trend = ''
        let suggestion = ''

        // 根据异动比例提供不同的建议
        if (percentage > 30) {
          trend = 'high'
          suggestion = '异动率偏高，建议关注团队稳定性，可考虑开展团建活动或优化工作环境'
        } else if (percentage > 15) {
          trend = 'medium'
          suggestion = '异动率处于中等水平，建议定期进行员工满意度调查'
        } else {
          trend = 'low'
          suggestion = '异动率保持在较低水平，建议保持现有管理方式'
        }

        return {
          department: dept,
          count,
          percentage: percentage.toFixed(1),
          trend,
          suggestion
        }
      })

    // 审批状态数据
    const approvalChartData: ChartData<'doughnut'> = {
      labels: Object.keys(analysisData.approvalStats),
      datasets: [{
        data: Object.values(analysisData.approvalStats),
        backgroundColor: [
          'rgba(255, 206, 86, 0.8)', // 待审批 - 黄色
          'rgba(75, 192, 192, 0.8)',  // 已通过 - 绿色
          'rgba(255, 99, 132, 0.8)'   // 已拒绝 - 红色
        ]
      }]
    }

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">异动类型分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <Pie data={typeChartData} options={{ maintainAspectRatio: false }} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">月度异动趋势</CardTitle>
              <CardDescription>近期人员异动数量变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <Line data={monthlyChartData} options={monthlyChartOptions} />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">部门异动分析</CardTitle>
              <CardDescription>各部门异动情况分析及建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {departmentAnalysis.map(({ department, count, percentage, trend, suggestion }) => (
                  <div key={department} className="space-y-3 p-4 rounded-lg border bg-slate-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">{department}</span>
                        <Badge className={
                          trend === 'high' ? 'bg-red-100 text-red-700' :
                          trend === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-green-100 text-green-700'
                        }>
                          {count} 人次 ({percentage}%)
                        </Badge>
                      </div>
                      <Badge variant="outline" className={
                        trend === 'high' ? 'border-red-200 text-red-700' :
                        trend === 'medium' ? 'border-yellow-200 text-yellow-700' :
                        'border-green-200 text-green-700'
                      }>
                        {trend === 'high' ? '需关注' : trend === 'medium' ? '一般' : '稳定'}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-600">{suggestion}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">审批状态统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <Doughnut data={approvalChartData} options={{ maintainAspectRatio: false }} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-blue-100 p-3">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">总异动数</p>
                <p className="text-2xl font-bold text-blue-600">{statistics.total}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">本月新增</span>
                <span className="font-medium">12</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-green-100 p-3">
                <ArrowUpRight className="h-6 w-6 text-green-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">入职/晋升</p>
                <p className="text-2xl font-bold text-green-600">
                  {statistics.byType.entry + statistics.byType.promotion}
                </p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">入职</span>
                <span className="font-medium">{statistics.byType.entry}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">晋升</span>
                <span className="font-medium">{statistics.byType.promotion}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-amber-100 p-3">
                <ArrowDownRight className="h-6 w-6 text-amber-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">调岗/离职</p>
                <p className="text-2xl font-bold text-amber-600">
                  {statistics.byType.transfer + statistics.byType.resignation}
                </p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">调岗</span>
                <span className="font-medium">{statistics.byType.transfer}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">离职</span>
                <span className="font-medium">{statistics.byType.resignation}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-purple-100 p-3">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">审批状态</p>
                <p className="text-2xl font-bold text-purple-600">{statistics.pending}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">待审批</span>
                <span className="font-medium">{statistics.pending}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">已处理</span>
                <span className="font-medium">{statistics.approved + statistics.rejected}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索..."
              className="pl-8 w-[250px] border-slate-200 focus-visible:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select
            value={selectedType}
            onValueChange={setSelectedType}
          >
            <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
              <SelectValue placeholder="异动类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="入职">入职</SelectItem>
              <SelectItem value="调岗">调岗</SelectItem>
              <SelectItem value="晋升">晋升</SelectItem>
              <SelectItem value="离职">离职</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={selectedStatus}
            onValueChange={setSelectedStatus}
          >
            <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
              <SelectValue placeholder="审批状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="待审批">待审批</SelectItem>
              <SelectItem value="已通过">已通过</SelectItem>
              <SelectItem value="已拒绝">已拒绝</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={selectedDepartment}
            onValueChange={setSelectedDepartment}
          >
            <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
              <SelectValue placeholder="所属部门" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有部门</SelectItem>
              <SelectItem value="安全管理部">安全管理部</SelectItem>
              <SelectItem value="工程管理部">工程管理部</SelectItem>
              <SelectItem value="人事管理部">人事管理部</SelectItem>
              <SelectItem value="财务管理部">财务管理部</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="hover:bg-blue-50 hover:text-blue-600" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".csv"
              className="hidden"
              id="import-file"
            />
            <Button
              variant="outline"
              onClick={() => document.getElementById('import-file')?.click()}
              className="hover:bg-green-50 hover:text-green-600"
            >
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)} className="bg-blue-500 hover:bg-blue-600">
            <Plus className="h-4 w-4 mr-2" />
            添加异动
          </Button>
        </div>
      </div>

      {/* 主表格 */}
      <Card>
        <CardHeader>
          <CardTitle>人员异动记录</CardTitle>
          <CardDescription>查看和管理所有人员异动记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader className="bg-slate-50">
                <TableRow>
                  <TableHead className="font-semibold">员工姓名</TableHead>
                  <TableHead className="font-semibold">工号</TableHead>
                  <TableHead className="font-semibold">异动类型</TableHead>
                  <TableHead className="font-semibold">原部门/职位</TableHead>
                  <TableHead className="font-semibold">新部门/职位</TableHead>
                  <TableHead className="font-semibold">生效日期</TableHead>
                  <TableHead className="font-semibold">审批状态</TableHead>
                  <TableHead className="font-semibold text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredChanges.map((change) => (
                  <TableRow key={change.id} className="hover:bg-slate-50">
                    <TableCell className="font-medium">{change.employeeName}</TableCell>
                    <TableCell>{change.employeeId}</TableCell>
                    <TableCell>{getTypeBadge(change.changeType)}</TableCell>
                    <TableCell>
                      {change.fromDepartment && change.fromPosition ? (
                        <>
                          {change.fromDepartment}
                          <br />
                          <span className="text-sm text-muted-foreground">{change.fromPosition}</span>
                        </>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>
                      {change.toDepartment && change.toPosition ? (
                        <>
                          {change.toDepartment}
                          <br />
                          <span className="text-sm text-muted-foreground">{change.toPosition}</span>
                        </>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>{change.effectiveDate}</TableCell>
                    <TableCell>{getStatusBadge(change.approvalStatus)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setCurrentChange(change)
                          setIsViewDetailsOpen(true)
                        }}
                        className="hover:bg-blue-50 hover:text-blue-600"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        详情
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setCurrentChange(change)
                          setIsEditDialogOpen(true)
                        }}
                        className="hover:bg-amber-50 hover:text-amber-600"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setCurrentChange(change)
                          setIsDeleteDialogOpen(true)
                        }}
                        className="hover:bg-red-50 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t bg-slate-50 px-6 py-3">
          <div className="text-sm text-slate-600">
            共 {filteredChanges.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled className="border-slate-200 hover:bg-slate-100">
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3 border-slate-200 bg-white hover:bg-slate-100">
              1
            </Button>
            <Button variant="outline" size="sm" disabled className="border-slate-200 hover:bg-slate-100">
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 添加异动对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加人员异动</DialogTitle>
            <DialogDescription>请填写人员异动的相关信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeName" className="flex items-center gap-1">
                  员工姓名
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="employeeName"
                  value={formData.employeeName}
                  onChange={(e) => setFormData({ ...formData, employeeName: e.target.value })}
                  className="border-slate-200 focus-visible:ring-blue-500"
                  placeholder="请输入员工姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="employeeId" className="flex items-center gap-1">
                  工号
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="employeeId"
                  value={formData.employeeId}
                  onChange={(e) => setFormData({ ...formData, employeeId: e.target.value })}
                  className="border-slate-200 focus-visible:ring-blue-500"
                  placeholder="请输入工号"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="changeType" className="flex items-center gap-1">
                  异动类型
                  <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.changeType}
                  onValueChange={(value: '入职' | '调岗' | '离职' | '晋升') =>
                    setFormData({ ...formData, changeType: value })}
                >
                  <SelectTrigger id="changeType" className="border-slate-200 focus:ring-blue-500">
                    <SelectValue placeholder="请选择异动类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="入职">入职</SelectItem>
                    <SelectItem value="调岗">调岗</SelectItem>
                    <SelectItem value="晋升">晋升</SelectItem>
                    <SelectItem value="离职">离职</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="effectiveDate" className="flex items-center gap-1">
                  生效日期
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="effectiveDate"
                  type="date"
                  value={formData.effectiveDate}
                  onChange={(e) => setFormData({ ...formData, effectiveDate: e.target.value })}
                  className="border-slate-200 focus-visible:ring-blue-500"
                />
              </div>
            </div>
            {(formData.changeType === '调岗' || formData.changeType === '晋升') && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fromDepartment">原部门</Label>
                  <Select
                    value={formData.fromDepartment}
                    onValueChange={(value) => setFormData({ ...formData, fromDepartment: value })}
                  >
                    <SelectTrigger id="fromDepartment" className="border-slate-200 focus:ring-blue-500">
                      <SelectValue placeholder="请选择原部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="工程管理部">工程管理部</SelectItem>
                      <SelectItem value="人事管理部">人事管理部</SelectItem>
                      <SelectItem value="财务管理部">财务管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fromPosition">原职位</Label>
                  <Input
                    id="fromPosition"
                    value={formData.fromPosition}
                    onChange={(e) => setFormData({ ...formData, fromPosition: e.target.value })}
                    className="border-slate-200 focus-visible:ring-blue-500"
                    placeholder="请输入原职位"
                  />
                </div>
              </div>
            )}
            {(formData.changeType === '入职' || formData.changeType === '调岗' || formData.changeType === '晋升') && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="toDepartment" className="flex items-center gap-1">
                    新部门
                    <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.toDepartment}
                    onValueChange={(value) => setFormData({ ...formData, toDepartment: value })}
                  >
                    <SelectTrigger id="toDepartment" className="border-slate-200 focus:ring-blue-500">
                      <SelectValue placeholder="请选择新部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="工程管理部">工程管理部</SelectItem>
                      <SelectItem value="人事管理部">人事管理部</SelectItem>
                      <SelectItem value="财务管理部">财务管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="toPosition" className="flex items-center gap-1">
                    新职位
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="toPosition"
                    value={formData.toPosition}
                    onChange={(e) => setFormData({ ...formData, toPosition: e.target.value })}
                    className="border-slate-200 focus-visible:ring-blue-500"
                    placeholder="请输入新职位"
                  />
                </div>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="reason" className="flex items-center gap-1">
                异动原因
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="reason"
                value={formData.reason}
                onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
                className="border-slate-200 focus-visible:ring-blue-500"
                placeholder="请输入异动原因"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="remarks">备注</Label>
              <Input
                id="remarks"
                value={formData.remarks}
                onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
                className="border-slate-200 focus-visible:ring-blue-500"
                placeholder="请输入备注信息（选填）"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                // 验证必填字段
                if (!formData.employeeName || !formData.employeeId || !formData.effectiveDate || !formData.reason) {
                  toast({
                    title: "请填写必填项",
                    description: "带 * 号的字段为必填项",
                    variant: "destructive"
                  })
                  return
                }

                // 根据异动类型验证其他必填字段
                if (formData.changeType !== '离职' && (!formData.toDepartment || !formData.toPosition)) {
                  toast({
                    title: "请填写必填项",
                    description: "新部门和新职位为必填项",
                    variant: "destructive"
                  })
                  return
                }

                // TODO: 调用API保存数据
                toast({
                  title: "添加成功",
                  description: "人员异动信息已添加",
                })
                setIsAddDialogOpen(false)
                // 重置表单
                setFormData({
                  employeeName: "",
                  employeeId: "",
                  changeType: "入职",
                  effectiveDate: "",
                  reason: ""
                })
              }}
            >
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>异动详情</DialogTitle>
          </DialogHeader>
          {currentChange && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">员工姓名</Label>
                  <p className="mt-1 font-medium">{currentChange.employeeName}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">工号</Label>
                  <p className="mt-1 font-medium">{currentChange.employeeId}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">异动类型</Label>
                  <p className="mt-1">{getTypeBadge(currentChange.changeType)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">生效日期</Label>
                  <p className="mt-1 font-medium">{currentChange.effectiveDate}</p>
                </div>
              </div>
              {(currentChange.fromDepartment || currentChange.fromPosition) && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">原部门</Label>
                    <p className="mt-1 font-medium">{currentChange.fromDepartment || "—"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">原职位</Label>
                    <p className="mt-1 font-medium">{currentChange.fromPosition || "—"}</p>
                  </div>
                </div>
              )}
              {(currentChange.toDepartment || currentChange.toPosition) && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">新部门</Label>
                    <p className="mt-1 font-medium">{currentChange.toDepartment || "—"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">新职位</Label>
                    <p className="mt-1 font-medium">{currentChange.toPosition || "—"}</p>
                  </div>
                </div>
              )}
              <div>
                <Label className="text-muted-foreground">异动原因</Label>
                <p className="mt-1 font-medium">{currentChange.reason}</p>
              </div>
              {currentChange.remarks && (
                <div>
                  <Label className="text-muted-foreground">备注</Label>
                  <p className="mt-1 font-medium">{currentChange.remarks}</p>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">提交人</Label>
                  <p className="mt-1 font-medium">{currentChange.submitter}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">提交时间</Label>
                  <p className="mt-1 font-medium">{currentChange.submitDate}</p>
                </div>
              </div>
              <div>
                <Label className="text-muted-foreground">审批状态</Label>
                <p className="mt-1">{getStatusBadge(currentChange.approvalStatus)}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewDetailsOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑异动信息</DialogTitle>
            <DialogDescription>修改人员异动的相关信息</DialogDescription>
          </DialogHeader>
          {currentChange && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-employeeName" className="flex items-center gap-1">
                    员工姓名
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="edit-employeeName"
                    defaultValue={currentChange.employeeName}
                    className="border-slate-200 focus-visible:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-employeeId" className="flex items-center gap-1">
                    工号
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="edit-employeeId"
                    defaultValue={currentChange.employeeId}
                    className="border-slate-200 focus-visible:ring-blue-500"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-changeType" className="flex items-center gap-1">
                    异动类型
                    <span className="text-red-500">*</span>
                  </Label>
                  <Select defaultValue={currentChange.changeType}>
                    <SelectTrigger id="edit-changeType" className="border-slate-200 focus:ring-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="入职">入职</SelectItem>
                      <SelectItem value="调岗">调岗</SelectItem>
                      <SelectItem value="晋升">晋升</SelectItem>
                      <SelectItem value="离职">离职</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-effectiveDate" className="flex items-center gap-1">
                    生效日期
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="edit-effectiveDate"
                    type="date"
                    defaultValue={currentChange.effectiveDate}
                    className="border-slate-200 focus-visible:ring-blue-500"
                  />
                </div>
              </div>
              {currentChange.changeType !== '离职' && (
                <>
                  {(currentChange.fromDepartment || currentChange.fromPosition) && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-fromDepartment">原部门</Label>
                        <Select defaultValue={currentChange.fromDepartment}>
                          <SelectTrigger id="edit-fromDepartment" className="border-slate-200 focus:ring-blue-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="安全管理部">安全管理部</SelectItem>
                            <SelectItem value="工程管理部">工程管理部</SelectItem>
                            <SelectItem value="人事管理部">人事管理部</SelectItem>
                            <SelectItem value="财务管理部">财务管理部</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-fromPosition">原职位</Label>
                        <Input
                          id="edit-fromPosition"
                          defaultValue={currentChange.fromPosition}
                          className="border-slate-200 focus-visible:ring-blue-500"
                        />
                      </div>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-toDepartment" className="flex items-center gap-1">
                        新部门
                        <span className="text-red-500">*</span>
                      </Label>
                      <Select defaultValue={currentChange.toDepartment}>
                        <SelectTrigger id="edit-toDepartment" className="border-slate-200 focus:ring-blue-500">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="安全管理部">安全管理部</SelectItem>
                          <SelectItem value="工程管理部">工程管理部</SelectItem>
                          <SelectItem value="人事管理部">人事管理部</SelectItem>
                          <SelectItem value="财务管理部">财务管理部</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-toPosition" className="flex items-center gap-1">
                        新职位
                        <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="edit-toPosition"
                        defaultValue={currentChange.toPosition}
                        className="border-slate-200 focus-visible:ring-blue-500"
                      />
                    </div>
                  </div>
                </>
              )}
              <div className="space-y-2">
                <Label htmlFor="edit-reason" className="flex items-center gap-1">
                  异动原因
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="edit-reason"
                  defaultValue={currentChange.reason}
                  className="border-slate-200 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-remarks">备注</Label>
                <Input
                  id="edit-remarks"
                  defaultValue={currentChange.remarks}
                  className="border-slate-200 focus-visible:ring-blue-500"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              toast({
                title: "更新成功",
                description: "人员异动信息已更新",
              })
              setIsEditDialogOpen(false)
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这条异动记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                toast({
                  title: "删除成功",
                  description: "人员异动记录已删除",
                })
                setIsDeleteDialogOpen(false)
              }}
            >
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加数据可视化部分 */}
      <Card>
        <CardHeader>
          <CardTitle>数据分析</CardTitle>
          <CardDescription>人员异动数据可视化分析</CardDescription>
        </CardHeader>
        <CardContent>
          <DataVisualization />
        </CardContent>
      </Card>
    </div>
  )
}

