"use client";
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
exports.__esModule = true;
exports.VisualDashboard = void 0;
var react_1 = require("react");
var echarts = require("echarts/core");
var charts_1 = require("echarts/charts");
var components_1 = require("echarts/components");
var renderers_1 = require("echarts/renderers");
var features_1 = require("echarts/features");
var icons_1 = require("@ant-design/icons");
var navigation_1 = require("next/navigation");
var StatCards_1 = require("./StatCards");
// 注册必要的组件
echarts.use([
    components_1.TitleComponent, components_1.TooltipComponent, components_1.GridComponent,
    components_1.LegendComponent, components_1.VisualMapComponent, components_1.ToolboxComponent,
    components_1.DataZoomComponent, renderers_1.CanvasRenderer, features_1.UniversalTransition,
    charts_1.BarChart, charts_1.LineChart, charts_1.PieChart
]);
var visualDashboard_module_css_1 = require("./visualDashboard.module.css");
function VisualDashboard() {
    var router = navigation_1.useRouter();
    // 引用物资数据（与原MaterialMaintenance组件相同）
    var _a = react_1.useState([
        {
            id: '1',
            name: '螺丝',
            type: '标准件',
            status: '正常',
            quantity: 2500,
            unit: '个',
            lastMaintenanceDate: '2025-02-14',
            nextMaintenanceDate: '2025-04-14',
            minQuantity: 500,
            maxQuantity: 3000,
            location: 'A区-01-01',
            supplier: '某某五金有限公司',
            turnoverRate: 92
        },
        {
            id: '2',
            name: '轴承',
            type: '机械件',
            status: '待维护',
            quantity: 35,
            unit: '个',
            lastMaintenanceDate: '2025-01-14',
            nextMaintenanceDate: '2025-04-14',
            minQuantity: 100,
            maxQuantity: 300,
            location: 'B区-02-03',
            supplier: '某某轴承厂',
            turnoverRate: 45
        },
        {
            id: '3',
            name: '电机',
            type: '电气件',
            status: '维修中',
            quantity: 8,
            unit: '台',
            lastMaintenanceDate: '2025-03-01',
            nextMaintenanceDate: '2025-04-01',
            minQuantity: 20,
            maxQuantity: 100,
            location: 'C区-03-02',
            supplier: '某某电机厂',
            turnoverRate: 15
        },
        {
            id: '4',
            name: '传感器',
            type: '电气件',
            status: '正常',
            quantity: 450,
            unit: '个',
            lastMaintenanceDate: '2025-01-20',
            nextMaintenanceDate: '2025-04-10',
            minQuantity: 100,
            maxQuantity: 500,
            location: 'D区-01-05',
            supplier: '某某电子有限公司',
            turnoverRate: 78
        },
        {
            id: '5',
            name: '液压泵',
            type: '机械件',
            status: '正常',
            quantity: 25,
            unit: '台',
            lastMaintenanceDate: '2025-02-20',
            nextMaintenanceDate: '2025-05-20',
            minQuantity: 15,
            maxQuantity: 40,
            location: 'B区-03-01',
            supplier: '某某机械制造厂',
            turnoverRate: 65
        },
    ]), materials = _a[0], setMaterials = _a[1];
    // 添加人员数据
    var _b = react_1.useState([
        {
            id: "1",
            name: "生产部",
            department: "生产部",
            position: "生产人员",
            status: "在岗",
            count: 1250
        },
        {
            id: "2",
            name: "安全部",
            department: "安全部",
            position: "安全管理",
            status: "在岗",
            count: 380
        },
        {
            id: "3",
            name: "技术部",
            department: "技术部",
            position: "技术人员",
            status: "在岗",
            count: 420
        },
        {
            id: "4",
            name: "机电部",
            department: "机电部",
            position: "机电维护",
            status: "在岗",
            count: 560
        },
        {
            id: "5",
            name: "运输部",
            department: "运输部",
            position: "运输人员",
            status: "在岗",
            count: 480
        },
        {
            id: "6",
            name: "后勤部",
            department: "后勤部",
            position: "后勤人员",
            status: "在岗",
            count: 320
        },
        {
            id: "7",
            name: "行政部",
            department: "行政部",
            position: "行政人员",
            status: "在岗",
            count: 180
        }
    ]), personnel = _b[0], setPersonnel = _b[1];
    // 添加项目数据
    var _c = react_1.useState([
        {
            id: "1",
            name: "主井深部开拓工程",
            progress: 35,
            status: "进行中",
            startDate: "2025-01-01",
            endDate: "2025-08-15",
            manager: "李工",
            teamSize: "145人",
            budget: "8.5亿"
        },
        {
            id: "2",
            name: "选矿厂技改项目",
            progress: 85,
            status: "进行中",
            startDate: "2024-11-01",
            endDate: "2025-04-30",
            manager: "王工",
            teamSize: "98人",
            budget: "3.2亿"
        },
        {
            id: "3",
            name: "尾矿库扩容工程",
            progress: 15,
            status: "进行中",
            startDate: "2025-02-15",
            endDate: "2025-12-31",
            manager: "张工",
            teamSize: "160人",
            budget: "5.8亿"
        },
        {
            id: "4",
            name: "矿区智能化改造",
            progress: 92,
            status: "进行中",
            startDate: "2024-09-01",
            endDate: "2025-03-31",
            manager: "赵工",
            teamSize: "82人",
            budget: "2.8亿"
        },
        {
            id: "5",
            name: "安全监控系统升级",
            progress: 68,
            status: "进行中",
            startDate: "2024-12-15",
            endDate: "2025-05-30",
            manager: "钱工",
            teamSize: "45人",
            budget: "1.5亿"
        },
        {
            id: "6",
            name: "矿井通风系统改造",
            progress: 25,
            status: "进行中",
            startDate: "2025-02-01",
            endDate: "2025-09-30",
            manager: "孙工",
            teamSize: "75人",
            budget: "2.3亿"
        },
        {
            id: "7",
            name: "地面生产系统优化",
            progress: 55,
            status: "进行中",
            startDate: "2024-10-15",
            endDate: "2025-06-30",
            manager: "周工",
            teamSize: "120人",
            budget: "4.2亿"
        }
    ]), projects = _c[0], setProjects = _c[1];
    // 添加安全记录数据
    var _d = react_1.useState([
        {
            id: "1",
            type: "安全检查",
            count: 156,
            status: "已完成",
            date: "2025-03-14"
        },
        {
            id: "2",
            type: "安全培训",
            count: 42,
            status: "已完成",
            date: "2025-03-13"
        },
        {
            id: "3",
            type: "隐患排查",
            count: 89,
            status: "进行中",
            date: "2025-03-14"
        },
        {
            id: "4",
            type: "应急演练",
            count: 12,
            status: "已完成",
            date: "2025-03-10"
        },
        {
            id: "5",
            type: "设备巡检",
            count: 235,
            status: "进行中",
            date: "2025-03-15"
        },
    ]), safetyRecords = _d[0], setSafetyRecords = _d[1];
    // 时间显示
    var _e = react_1.useState(""), currentTime = _e[0], setCurrentTime = _e[1];
    // 事件通知状态
    var _f = react_1.useState([]), notifications = _f[0], setNotifications = _f[1];
    // 状态用于控制logo点击动画
    var _g = react_1.useState(false), logoClicked = _g[0], setLogoClicked = _g[1];
    var _h = react_1.useState(false), showRings = _h[0], setShowRings = _h[1];
    var _j = react_1.useState(0), clickCount = _j[0], setClickCount = _j[1];
    // 新增公司信息数组
    var companyInfo = [
        '国家特级资质矿业集团，成立于1985年',
        '年产煤炭3000万吨，营业额超过150亿元',
        '拥有现代化矿井15座，员工总数2.8万人',
        '智能开采技术领先，安全生产记录1200天',
        '国家能源安全战略合作伙伴，绿色矿山示范基地'
    ];
    // Logo点击处理函数增强版
    var handleLogoClick = function () {
        // 增加点击计数
        setClickCount(function (prev) { return prev + 1; });
        // 设置logo被点击的动画状态
        setLogoClicked(true);
        // 显示扩散环动画
        setShowRings(true);
        setTimeout(function () { return setShowRings(false); }, 2000);
        // 创建粒子爆发效果
        if (particlesRef.current) {
            var burstParticles = [];
            var _loop_1 = function (i) {
                var particle = document.createElement('div');
                particle.className = visualDashboard_module_css_1["default"].burstParticle;
                // 随机大小和不透明度
                var size = Math.random() * 8 + 2;
                particle.style.width = size + "px";
                particle.style.height = size + "px";
                // 随机颜色
                var hue = 180 + Math.random() * 60; // 青色到蓝色范围
                var saturation = 70 + Math.random() * 30;
                var lightness = 50 + Math.random() * 20;
                particle.style.backgroundColor = "hsl(" + hue + ", " + saturation + "%, " + lightness + "%)";
                // 从中心向外随机方向发射
                var angle = Math.random() * Math.PI * 2;
                var distance = Math.random() * 150 + 50;
                var x = Math.cos(angle) * distance;
                var y = Math.sin(angle) * distance;
                // 设置随机动画时长 (1-2秒)
                var duration = 1 + Math.random();
                particle.style.animation = "burstOut " + duration + "s ease-out forwards";
                // 设置初始位置和目标位置
                particle.style.left = '50%';
                particle.style.top = '50%';
                particle.style.transform = "translate(-50%, -50%) scale(0.2)";
                // 使用CSS变量存储目标位置，供keyframe动画使用
                particle.style.setProperty('--target-x', x + "px");
                particle.style.setProperty('--target-y', y + "px");
                if (particlesRef.current) {
                    particlesRef.current.appendChild(particle);
                    burstParticles.push(particle);
                }
                // 动画结束后移除粒子
                setTimeout(function () {
                    try {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }
                    catch (e) {
                        console.log('移除爆发粒子时发生错误，可能已被移除', e);
                    }
                }, duration * 1000);
            };
            // 创建30个爆发粒子
            for (var i = 0; i < 30; i++) {
                _loop_1(i);
            }
        }
        // 触发通知
        triggerCustomNotification({
            title: "企业信息",
            message: companyInfo[clickCount % companyInfo.length],
            type: 'info'
        });
        // 5秒后重置状态
        setTimeout(function () {
            setLogoClicked(false);
        }, 5000);
    };
    // 粒子效果的引用
    var particlesRef = react_1.useRef(null);
    // 添加粒子效果
    react_1.useEffect(function () {
        if (!particlesRef.current)
            return;
        // 清除旧粒子
        if (particlesRef.current) {
            while (particlesRef.current.firstChild) {
                particlesRef.current.removeChild(particlesRef.current.firstChild);
            }
        }
        // 创建粒子
        var particleCount = 100;
        var particles = []; // 跟踪已创建的粒子元素
        for (var i = 0; i < particleCount; i++) {
            // 如果在创建过程中组件已卸载，则停止创建
            if (!particlesRef.current)
                break;
            var particle = document.createElement('div');
            particle.className = visualDashboard_module_css_1["default"].particle;
            // 随机大小和不透明度
            var size = Math.random() * 5 + 1;
            particle.style.width = size + "px";
            particle.style.height = size + "px";
            // 随机位置在整个地图区域，不再仅限于Logo周围
            var x = void 0, y = void 0;
            // 更广泛的分布
            x = Math.random() * 100 - 50; // -50% 到 50%
            y = Math.random() * 100 - 50; // -50% 到 50%
            // 创建粒子群分布效果
            var angle = Math.random() * Math.PI * 2;
            var distance = 100 + Math.random() * 300;
            // 15%的粒子在Logo附近
            if (Math.random() < 0.15) {
                x = Math.cos(angle) * (50 + Math.random() * 50);
                y = Math.sin(angle) * (50 + Math.random() * 50);
            }
            // 其余的随机分布在地图各处
            else {
                x = Math.cos(angle) * distance;
                y = Math.sin(angle) * distance;
            }
            particle.style.left = "calc(50% + " + x + "px)";
            particle.style.top = "calc(50% + " + y + "px)";
            // 随机动画延迟和持续时间，创建更自然的效果
            particle.style.animationDelay = Math.random() * 10 + "s";
            particle.style.animationDuration = 10 + Math.random() * 15 + "s";
            // 随机不透明度
            particle.style.opacity = (0.1 + Math.random() * 0.6).toString();
            if (particlesRef.current) {
                particlesRef.current.appendChild(particle);
                particles.push(particle); // 添加到跟踪数组
            }
        }
        // 清理函数
        return function () {
            // 安全地移除所有粒子
            particles.forEach(function (particle) {
                try {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }
                catch (e) {
                    console.log('清理粒子时发生错误，可能已被移除', e);
                }
            });
        };
    }, []);
    // 进度条动画初始化
    react_1.useEffect(function () {
        // 使用setTimeout序列化动画，形成流水效果
        var initialDelay = setTimeout(function () {
            var progressBars = document.querySelectorAll("." + visualDashboard_module_css_1["default"].progressBar);
            // 确保在组件存在时才进行操作
            if (progressBars.length > 0) {
                progressBars.forEach(function (bar) {
                    bar.style.width = '0%';
                });
                progressBars.forEach(function (bar, index) {
                    var timer = setTimeout(function () {
                        try {
                            if (bar && bar.parentElement) { // 确保元素仍然存在
                                var targetWidth = bar.getAttribute('data-progress')
                                    || (bar.parentElement.nextElementSibling
                                        && bar.parentElement.nextElementSibling.querySelector("." + visualDashboard_module_css_1["default"].progressPercent)
                                        ? bar.parentElement.nextElementSibling.querySelector("." + visualDashboard_module_css_1["default"].progressPercent).textContent.replace('%', '')
                                        : '0');
                                bar.style.width = targetWidth + "%";
                            }
                        }
                        catch (e) {
                            console.log('设置进度条时发生错误', e);
                        }
                    }, index * 300); // 每个进度条间隔300ms
                });
            }
        }, 500);
        // 清理函数
        return function () {
            clearTimeout(initialDelay);
        };
    }, []);
    // 随机触发事件通知
    react_1.useEffect(function () {
        // 存储所有清理函数以便清理
        var cleanupFunctions = [];
        var interval = setInterval(function () {
            if (Math.random() > 0.7) {
                var possibleEvents = [
                    "系统检测到新的物资入库请求",
                    "北京仓库物资调拨已完成",
                    "3号仓库温湿度超出预警值",
                    "月度物资盘点将于明日开始",
                    "系统更新维护计划已发布"
                ];
                var randomEvent = possibleEvents[Math.floor(Math.random() * possibleEvents.length)];
                var cleanupFn = triggerCustomNotification({
                    title: "系统通知",
                    message: randomEvent,
                    type: 'info'
                });
                // 将清理函数存储起来
                if (cleanupFn) {
                    cleanupFunctions.push(cleanupFn);
                }
            }
        }, 15000);
        // 返回清理函数
        return function () {
            clearInterval(interval);
            // 清理所有通知计时器
            cleanupFunctions.forEach(function (cleanup) {
                try {
                    cleanup();
                }
                catch (e) {
                    console.log('清理通知计时器时发生错误', e);
                }
            });
        };
    }, []);
    // 自定义通知触发函数
    var triggerCustomNotification = function (notification) {
        var newNotification = __assign(__assign({}, notification), { time: new Date(), id: Date.now() // 添加唯一ID以便安全地追踪和移除通知
         });
        // 使用函数式更新，确保使用最新的状态
        setNotifications(function (prev) { return __spreadArrays(prev, [newNotification]); });
        // 修改为3秒后自动移除通知
        var timerId = setTimeout(function () {
            setNotifications(function (prev) { return prev.filter(function (n) { return n.id !== newNotification.id; }); });
        }, 3000);
        // 返回清理函数
        return function () {
            try {
                clearTimeout(timerId);
            }
            catch (e) {
                console.log('清理通知定时器时发生错误', e);
            }
        };
    };
    // 手动触发通知函数
    var triggerNotification = function () {
        if (typeof window.triggerDashboardNotification === 'function') {
            window.triggerDashboardNotification();
        }
    };
    // 定期模拟数据更新功能
    react_1.useEffect(function () {
        // 模拟数据更新函数
        var updateData = function () {
            // 随机更新项目进度
            setProjects(function (prev) { return prev.map(function (project) {
                // 有75%概率不更新，25%概率更新进度
                if (Math.random() > 0.25)
                    return project;
                // 计算新进度，允许进度在合理范围内小幅变化
                var newProgress = project.progress + Math.floor(Math.random() * 6) - 2; // -2到+3之间的变化
                newProgress = Math.max(0, Math.min(100, newProgress)); // 确保进度在0-100范围内
                return __assign(__assign({}, project), { progress: newProgress, 
                    // 如果已完成则修改状态
                    status: newProgress >= 100 ? "已完成" : newProgress > 0 ? "进行中" : "待开始" });
            }); });
            // 随机更新安全记录
            setSafetyRecords(function (prev) { return prev.map(function (record) {
                // 有80%概率不更新，20%概率更新检查数量
                if (Math.random() > 0.2)
                    return record;
                // 随机增加1-3的检查数量
                var countIncrease = Math.floor(Math.random() * 3) + 1;
                return __assign(__assign({}, record), { count: record.count + countIncrease });
            }); });
        };
        // 设置5分钟更新一次数据
        var dataUpdateInterval = 5 * 60 * 1000; // 5分钟
        var dataTimer = setInterval(updateData, dataUpdateInterval);
        // 清理函数
        return function () {
            clearInterval(dataTimer);
        };
    }, []);
    // 更新时间，只在组件加载和每分钟更新一次，而不是每秒
    react_1.useEffect(function () {
        // 更新当前时间的函数
        var updateTime = function () {
            var time = new Date();
            var year = time.getFullYear();
            var month = time.getMonth() + 1;
            var date = time.getDate();
            var hours = time.getHours();
            var minutes = time.getMinutes();
            setCurrentTime(year + "\u5E74" + month + "\u6708" + date + "\u65E5 " + (hours < 10 ? '0' + hours : hours) + ":" + (minutes < 10 ? '0' + minutes : minutes));
        };
        // 立即更新一次时间
        updateTime();
        // 计算到下一分钟的毫秒数
        var now = new Date();
        var delay = (60 - now.getSeconds()) * 1000 - now.getMilliseconds();
        // 先设置一个延迟，确保在下一分钟的整点开始定时器
        var initialTimeout = setTimeout(function () {
            updateTime();
            // 然后每分钟更新一次
            var interval = setInterval(updateTime, 60000);
            // 返回清理函数
            return function () {
                clearInterval(interval);
            };
        }, delay);
        // 清理初始延迟
        return function () {
            clearTimeout(initialTimeout);
        };
    }, []);
    // 计算统计数据
    var stats = {
        total: materials.length,
        normal: materials.filter(function (item) { return item.status === '正常'; }).length,
        pending: materials.filter(function (item) { return item.status === '待维护'; }).length,
        repairing: materials.filter(function (item) { return item.status === '维修中'; }).length,
        // 新增综合统计数据
        totalProjects: projects.length,
        ongoingProjects: projects.filter(function (p) { return p.status === "进行中"; }).length,
        totalSafetyChecks: safetyRecords.reduce(function (acc, item) { return acc + item.count; }, 0),
        totalPersonnel: personnel.reduce(function (acc, dept) { return acc + dept.count; }, 0),
        onDutyPersonnel: personnel.reduce(function (acc, dept) { return acc + (dept.status === "在岗" ? dept.count : 0); }, 0)
    };
    // 图表引用
    var barChartRef = react_1.useRef(null);
    var lineChartRef = react_1.useRef(null);
    var pieChartRef = react_1.useRef(null);
    var mapChartRef = react_1.useRef(null);
    var line2ChartRef = react_1.useRef(null);
    var pie2ChartRef = react_1.useRef(null);
    // 图表初始化
    react_1.useEffect(function () {
        if (!barChartRef.current || !lineChartRef.current ||
            !pieChartRef.current || !mapChartRef.current ||
            !line2ChartRef.current || !pie2ChartRef.current) {
            return; // 如果任何一个图表容器不存在，则退出
        }
        // 初始化echarts实例
        var barChart = null;
        var lineChart = null;
        var pieChart = null;
        var mapChart = null;
        var line2Chart = null;
        var pie2Chart = null;
        try {
            barChart = echarts.init(barChartRef.current);
            lineChart = echarts.init(lineChartRef.current);
            pieChart = echarts.init(pieChartRef.current);
            mapChart = echarts.init(mapChartRef.current);
            line2Chart = echarts.init(line2ChartRef.current);
            pie2Chart = echarts.init(pie2ChartRef.current);
        }
        catch (e) {
            console.log('初始化图表时发生错误', e);
            return; // 如果初始化失败，退出
        }
        // 物资类型统计
        var materialTypes = Array.from(new Set(materials.map(function (item) { return item.type; })));
        var typeQuantities = materialTypes.map(function (type) {
            return materials.filter(function (item) { return item.type === type; })
                .reduce(function (sum, item) { return sum + item.quantity; }, 0);
        });
        // 项目名称和部门统计
        var projectNames = projects.map(function (project) { return project.name; });
        // 部门人员统计
        var departments = Array.from(new Set(personnel.map(function (p) { return p.department; })));
        var departmentCounts = departments.map(function (dept) {
            var deptPersonnel = personnel.find(function (p) { return p.department === dept; });
            return deptPersonnel ? deptPersonnel.count : 0;
        });
        // 左侧柱状图配置
        var barOption = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#20dbff'
                }, {
                    offset: 1,
                    color: '#0064c8'
                }]),
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'rgba(32,219,255,0.1)'
                    }
                },
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            grid: {
                left: '0%',
                top: '10px',
                right: '0%',
                bottom: '4%',
                containLabel: true
            },
            xAxis: [{
                    type: 'category',
                    data: departments,
                    axisTick: {
                        alignWithLabel: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,.1)'
                        }
                    },
                    axisLabel: {
                        color: "rgba(255,255,255,.6)",
                        fontSize: 12,
                        rotate: 30
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,.1)'
                        }
                    }
                }],
            yAxis: [{
                    type: 'value',
                    axisLabel: {
                        color: "rgba(255,255,255,.6)",
                        fontSize: 12
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    }
                }],
            series: [{
                    name: '部门人数',
                    type: 'bar',
                    barWidth: '35%',
                    data: departmentCounts,
                    itemStyle: {
                        borderRadius: [4, 4, 0, 0],
                        shadowColor: 'rgba(32,219,255,0.2)',
                        shadowBlur: 10
                    },
                    emphasis: {
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                        offset: 0,
                                        color: '#5eead4'
                                    }, {
                                        offset: 1,
                                        color: '#0ea5e9'
                                    }]
                            }
                        }
                    },
                    animationType: 'scale',
                    animationEasing: 'elasticOut'
                }]
        };
        // 左侧折线图配置
        var lineOption = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                textStyle: {
                    color: 'rgba(255,255,255,.5)',
                    fontSize: '12'
                }
            },
            grid: {
                left: '10',
                top: '30',
                right: '10',
                bottom: '10',
                containLabel: true
            },
            xAxis: [{
                    type: 'category',
                    boundaryGap: false,
                    data: safetyRecords.map(function (record) { return record.date; }),
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)",
                            fontSize: 12
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.2)"
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,0.1)',
                            type: 'dashed'
                        }
                    }
                }],
            yAxis: [{
                    type: 'value',
                    axisTick: { show: false },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)",
                            fontSize: 12
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    }
                }],
            series: [
                {
                    name: '安全检查',
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    showSymbol: false,
                    symbolSize: 8,
                    lineStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                    offset: 0,
                                    color: '#20dbff'
                                }, {
                                    offset: 1,
                                    color: '#0064c8'
                                }]
                        },
                        width: 3,
                        shadowColor: 'rgba(32,219,255,0.3)',
                        shadowBlur: 10
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "rgba(32,219,255,0.4)"
                            },
                            {
                                offset: 0.8,
                                color: "rgba(32,219,255,0.1)"
                            }
                        ], false),
                        shadowColor: "rgba(32,219,255,0.1)",
                        shadowBlur: 20
                    },
                    emphasis: {
                        scale: true,
                        focus: 'series',
                        itemStyle: {
                            color: '#fff',
                            borderColor: '#20dbff',
                            borderWidth: 2,
                            shadowColor: 'rgba(32,219,255,0.8)',
                            shadowBlur: 10
                        }
                    },
                    data: safetyRecords.map(function (record) { return record.count; })
                }
            ]
        };
        // 饼图配置
        var pieOption = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)',
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                textStyle: {
                    color: "rgba(255,255,255,.6)"
                },
                itemWidth: 10,
                itemHeight: 10,
                itemGap: 12
            },
            series: [
                {
                    name: '物资分布',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: 'rgba(255,255,255,0.2)',
                        borderWidth: 2,
                        shadowColor: 'rgba(32,219,255,0.2)',
                        shadowBlur: 10
                    },
                    label: {
                        show: false,
                        position: 'center',
                        color: '#fff',
                        fontSize: 14
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 20,
                            fontWeight: 'bold',
                            color: '#fff'
                        },
                        itemStyle: {
                            shadowColor: 'rgba(32,219,255,0.5)',
                            shadowBlur: 20
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: materialTypes.map(function (type, index) { return ({
                        value: typeQuantities[index],
                        name: type,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: ['#20dbff', '#0064c8', '#5eead4', '#0ea5e9'][index % 4]
                                }, {
                                    offset: 1,
                                    color: ['#0064c8', '#20dbff', '#0ea5e9', '#5eead4'][index % 4]
                                }])
                        }
                    }); })
                }
            ]
        };
        // 替代中心地图配置（使用环形图）
        var alternativeMapOption = {
            tooltip: {
                trigger: "item",
                formatter: function (params) {
                    return "<div style=\"padding: 8px;\">\n            <div style=\"font-weight:bold;margin-bottom:8px;color:#20dbff;\">" + params.name + "</div>\n            <div style=\"margin-bottom:4px;\">\u8FDB\u5EA6: " + params.value + "%</div>\n            <div style=\"margin-bottom:4px;\">\u72B6\u6001: " + params.data.d + "</div>\n            <div style=\"margin-bottom:4px;\">\u5F00\u59CB: " + (params.data.startDate || '-') + "</div>\n            <div>\u9884\u8BA1\u5B8C\u6210: " + (params.data.endDate || '-') + "</div>\n          </div>";
                },
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                show: false // 设置为false以隐藏图例
            },
            color: [
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#20dbff' },
                    { offset: 1, color: '#0064c8' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#5eead4' },
                    { offset: 1, color: '#0ea5e9' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#fcd34d' },
                    { offset: 1, color: '#f59e0b' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#a78bfa' },
                    { offset: 1, color: '#7c3aed' }
                ])
            ],
            series: [
                {
                    name: "项目进度",
                    type: "pie",
                    radius: ["40%", "60%"],
                    center: ["50%", "50%"],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 6,
                        borderColor: 'rgba(255,255,255,0.2)',
                        borderWidth: 2,
                        shadowColor: 'rgba(32,219,255,0.2)',
                        shadowBlur: 10
                    },
                    label: {
                        show: true,
                        position: "outside",
                        formatter: function (params) {
                            var name = params.name.length > 5 ? params.name.substring(0, 5) + '...' : params.name;
                            return name + ": " + params.value + "%";
                        },
                        color: "rgba(255,255,255,.8)",
                        fontSize: 12,
                        backgroundColor: 'rgba(0,0,0,0.3)',
                        borderRadius: 4,
                        padding: [3, 5]
                    },
                    labelLine: {
                        show: true,
                        length: 15,
                        length2: 20,
                        smooth: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,0.4)',
                            width: 1
                        }
                    },
                    data: projects.map(function (project) { return ({
                        name: project.name,
                        value: project.progress,
                        d: project.status,
                        startDate: project.startDate,
                        endDate: project.endDate
                    }); })
                }
            ]
        };
        // 右侧柱状图配置
        var bar2Option = {
            grid: {
                top: "10%",
                left: "22%",
                bottom: "10%"
            },
            xAxis: {
                show: false
            },
            yAxis: [
                {
                    type: "category",
                    inverse: true,
                    data: projectNames,
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: "#fff"
                    }
                },
                {
                    data: projects.map(function (p) { return p.progress; }),
                    inverse: true,
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: "#999"
                    }
                }
            ],
            series: [
                {
                    name: "项目进度",
                    type: "bar",
                    data: projects.map(function (p) { return p.progress; }),
                    yAxisIndex: 0,
                    itemStyle: {
                        barBorderRadius: 20,
                        color: function (params) {
                            return {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 1,
                                y2: 0,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: "#00c9e0"
                                    },
                                    {
                                        offset: 1,
                                        color: "#005fc1"
                                    }
                                ]
                            };
                        }
                    },
                    barWidth: 15,
                    label: {
                        show: true,
                        position: "right",
                        formatter: "{c}%"
                    }
                }
            ]
        };
        // 右侧折线图配置
        var line2Option = {
            tooltip: {
                trigger: "axis",
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                top: "0%",
                data: ["营业收入", "净利润"],
                textStyle: {
                    color: "rgba(255,255,255,.5)",
                    fontSize: "12"
                }
            },
            grid: {
                left: "10",
                top: "30",
                right: "10",
                bottom: "10",
                containLabel: true
            },
            xAxis: [
                {
                    type: "category",
                    boundaryGap: false,
                    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)",
                            fontSize: 12
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.2)"
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,0.1)',
                            type: 'dashed'
                        }
                    }
                }
            ],
            yAxis: [
                {
                    type: "value",
                    axisTick: { show: false },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)",
                            fontSize: 12
                        },
                        formatter: '{value} 万'
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    }
                }
            ],
            series: [
                {
                    name: "营业收入",
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 5,
                    showSymbol: false,
                    lineStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                    offset: 0,
                                    color: '#20dbff'
                                }, {
                                    offset: 1,
                                    color: '#0064c8'
                                }]
                        },
                        width: 3,
                        shadowColor: 'rgba(32,219,255,0.3)',
                        shadowBlur: 10
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "rgba(32,219,255,0.4)"
                            },
                            {
                                offset: 0.8,
                                color: "rgba(32,219,255,0.1)"
                            }
                        ], false),
                        shadowColor: "rgba(32,219,255,0.1)",
                        shadowBlur: 20
                    },
                    emphasis: {
                        scale: true,
                        focus: 'series',
                        itemStyle: {
                            color: '#fff',
                            borderColor: '#20dbff',
                            borderWidth: 2,
                            shadowColor: 'rgba(32,219,255,0.8)',
                            shadowBlur: 10
                        }
                    },
                    data: [820, 932, 901, 1234, 1290, 1330, 1520, 1864, 2030, 2100, 2432, 2690]
                },
                {
                    name: "净利润",
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 5,
                    showSymbol: false,
                    lineStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                    offset: 0,
                                    color: '#5eead4'
                                }, {
                                    offset: 1,
                                    color: '#0ea5e9'
                                }]
                        },
                        width: 3,
                        shadowColor: 'rgba(94,234,212,0.3)',
                        shadowBlur: 10
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "rgba(94,234,212,0.4)"
                            },
                            {
                                offset: 0.8,
                                color: "rgba(94,234,212,0.1)"
                            }
                        ], false),
                        shadowColor: "rgba(94,234,212,0.1)",
                        shadowBlur: 20
                    },
                    emphasis: {
                        scale: true,
                        focus: 'series',
                        itemStyle: {
                            color: '#fff',
                            borderColor: '#5eead4',
                            borderWidth: 2,
                            shadowColor: 'rgba(94,234,212,0.8)',
                            shadowBlur: 10
                        }
                    },
                    data: [120, 182, 191, 264, 290, 330, 410, 464, 530, 610, 680, 810]
                }
            ]
        };
        // 右侧饼图配置
        var pie2Option = {
            color: [
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#20dbff' },
                    { offset: 1, color: '#0064c8' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#5eead4' },
                    { offset: 1, color: '#0ea5e9' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#fcd34d' },
                    { offset: 1, color: '#f59e0b' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#a78bfa' },
                    { offset: 1, color: '#7c3aed' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#f472b6' },
                    { offset: 1, color: '#db2777' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#4ade80' },
                    { offset: 1, color: '#16a34a' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#fb923c' },
                    { offset: 1, color: '#ea580c' }
                ])
            ],
            tooltip: {
                trigger: "item",
                formatter: "{a} <br/>{b}: {c}人 ({d}%)",
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderColor: '#20dbff',
                textStyle: {
                    color: '#fff'
                }
            },
            legend: {
                bottom: "0%",
                itemWidth: 10,
                itemHeight: 10,
                textStyle: {
                    color: "rgba(255,255,255,.5)",
                    fontSize: "12"
                }
            },
            series: [
                {
                    name: "部门人员",
                    type: "pie",
                    radius: ["10%", "70%"],
                    center: ["50%", "40%"],
                    roseType: "radius",
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: 'rgba(255,255,255,0.2)',
                        borderWidth: 2,
                        shadowColor: 'rgba(32,219,255,0.2)',
                        shadowBlur: 10
                    },
                    label: {
                        formatter: '{b}: {c}人\n{d}%',
                        color: "rgba(255,255,255,.6)",
                        fontSize: 12
                    },
                    labelLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,0.3)"
                        },
                        smooth: 0.2,
                        length: 10,
                        length2: 20
                    },
                    emphasis: {
                        scale: true,
                        scaleSize: 10,
                        itemStyle: {
                            shadowColor: 'rgba(32,219,255,0.8)',
                            shadowBlur: 20
                        }
                    },
                    data: departments.map(function (dept, index) { return ({
                        value: departmentCounts[index],
                        name: dept
                    }); })
                }
            ]
        };
        // 设置图表配置
        try {
            if (barChart) {
                barChart.setOption(barOption);
            }
            if (lineChart) {
                lineChart.setOption(lineOption);
            }
            if (pieChart) {
                pieChart.setOption(pieOption);
            }
            if (mapChart) {
                alternativeMapOption.animation = true;
                alternativeMapOption.animationDuration = 2000;
                alternativeMapOption.animationEasing = 'elasticOut';
                mapChart.setOption(alternativeMapOption);
            }
            if (line2Chart) {
                line2Option.animation = true;
                line2Option.animationDuration = 2000;
                line2Option.animationEasing = 'cubicInOut';
                line2Chart.setOption(line2Option);
            }
            if (pie2Chart) {
                pie2Option.animation = true;
                pie2Option.animationDuration = 2000;
                pie2Option.animationEasing = 'quadraticOut';
                pie2Chart.setOption(pie2Option);
            }
        }
        catch (e) {
            console.log('设置图表配置时发生错误', e);
        }
        // 窗口大小变化时，图表自适应
        var resizeHandler = function () {
            try {
                // 检查图表和DOM元素是否还存在
                if (barChartRef.current && barChart)
                    barChart.resize();
                if (lineChartRef.current && lineChart)
                    lineChart.resize();
                if (pieChartRef.current && pieChart)
                    pieChart.resize();
                if (mapChartRef.current && mapChart)
                    mapChart.resize();
                if (line2ChartRef.current && line2Chart)
                    line2Chart.resize();
                if (pie2ChartRef.current && pie2Chart)
                    pie2Chart.resize();
            }
            catch (e) {
                console.log('调整图表大小时发生错误', e);
            }
        };
        window.addEventListener('resize', resizeHandler);
        // 添加自动轮播效果，仅当mapChart存在时
        var rotationTimer = null;
        if (mapChartRef.current && mapChart) {
            var currentIndex_1 = -1;
            var highlightPie = function () {
                try {
                    if (!mapChartRef.current)
                        return;
                    var chart = echarts.getInstanceByDom(mapChartRef.current);
                    if (!chart)
                        return;
                    currentIndex_1 = (currentIndex_1 + 1) % projects.length;
                    chart.dispatchAction({
                        type: 'highlight',
                        seriesIndex: 0,
                        name: projects[currentIndex_1].name
                    });
                    // 2秒后取消高亮并高亮下一个
                    setTimeout(function () {
                        try {
                            if (mapChartRef.current) {
                                var chart_1 = echarts.getInstanceByDom(mapChartRef.current);
                                if (chart_1) {
                                    chart_1.dispatchAction({
                                        type: 'downplay',
                                        seriesIndex: 0,
                                        name: projects[currentIndex_1].name
                                    });
                                }
                            }
                        }
                        catch (e) {
                            console.log('取消高亮项目时发生错误', e);
                        }
                    }, 1800);
                }
                catch (e) {
                    console.log('高亮项目时发生错误', e);
                }
            };
            // 开始自动轮播
            rotationTimer = setInterval(highlightPie, 2500);
        }
        // 组件卸载时的清理函数
        return function () {
            try {
                // 清理自动轮播定时器
                if (rotationTimer) {
                    clearInterval(rotationTimer);
                }
                // 移除窗口调整事件监听
                window.removeEventListener('resize', resizeHandler);
                // 安全地释放图表资源
                var disposeChart = function (chart) {
                    if (chart) {
                        try {
                            chart.dispose();
                        }
                        catch (e) {
                            console.log('释放图表资源时发生错误', e);
                        }
                    }
                };
                disposeChart(barChart);
                disposeChart(lineChart);
                disposeChart(pieChart);
                disposeChart(mapChart);
                disposeChart(line2Chart);
                disposeChart(pie2Chart);
            }
            catch (e) {
                console.log('清理图表资源时发生错误', e);
            }
        };
    }, [materials, projects, safetyRecords, personnel]); // 只在这些数据变化时重新渲染图表
    // 添加进度条脉冲效果动画
    react_1.useEffect(function () {
        var pulseElements = document.querySelectorAll("." + visualDashboard_module_css_1["default"].pulseBar);
        var intervals = [];
        // 为每个进度条添加脉冲动画
        pulseElements.forEach(function (element) {
            var pulse = function () {
                try {
                    if (element && document.body.contains(element)) {
                        element.classList.add(visualDashboard_module_css_1["default"].pulsing);
                        setTimeout(function () {
                            if (element && document.body.contains(element)) {
                                element.classList.remove(visualDashboard_module_css_1["default"].pulsing);
                            }
                        }, 1500);
                    }
                }
                catch (e) {
                    console.log('进度条脉冲效果时发生错误', e);
                }
            };
            // 初始运行一次
            pulse();
            // 每5秒运行一次脉冲效果
            var intervalId = setInterval(pulse, 5000);
            intervals.push(intervalId);
        });
        return function () {
            // 清理所有间隔计时器
            intervals.forEach(function (interval) {
                try {
                    clearInterval(interval);
                }
                catch (e) {
                    console.log('清理脉冲效果计时器时发生错误', e);
                }
            });
        };
    }, []);
    // 添加项目详情状态
    var _k = react_1.useState(null), activeProject = _k[0], setActiveProject = _k[1];
    // 处理项目卡片点击
    var handleProjectCardClick = function (id) {
        setActiveProject(activeProject === id ? null : id);
        var project = projects.find(function (p) { return p.id === id; });
        if (project) {
            // 将环形图高亮对应的项目
            if (mapChartRef.current) {
                var chart = echarts.getInstanceByDom(mapChartRef.current);
                if (chart) {
                    chart.dispatchAction({
                        type: 'highlight',
                        seriesIndex: 0,
                        name: project.name
                    });
                }
            }
            triggerCustomNotification({
                title: "\u9879\u76EE\u4FE1\u606F",
                message: project.name + "\uFF0C\u8FDB\u5EA6" + project.progress + "%\uFF0C" + project.status,
                type: 'info'
            });
        }
    };
    // 处理返回首页
    var handleGoHome = function () {
        router.push('/');
    };
    // 添加智能洞察数据
    var _l = react_1.useState([
        {
            id: 1,
            title: '能源使用异常检测',
            description: '检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况',
            type: 'anomaly',
            timestamp: new Date(),
            module: '能源管理',
            importance: 'high',
            icon: React.createElement(icons_1.AlertOutlined, { style: { color: '#ff4d4f' } }),
            color: '#ff4d4f'
        },
        {
            id: 2,
            title: '财务趋势预测',
            description: '根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标',
            type: 'trend',
            timestamp: new Date(),
            module: '财务管理',
            importance: 'medium',
            icon: React.createElement(icons_1.RiseOutlined, { style: { color: '#52c41a' } }),
            color: '#52c41a'
        },
        {
            id: 3,
            title: '设备维护建议',
            description: '3号生产线设备预计在14天内需要进行预防性维护，建议提前安排',
            type: 'recommendation',
            timestamp: new Date(),
            module: '设备管理',
            importance: 'medium',
            icon: React.createElement(icons_1.BulbOutlined, { style: { color: '#faad14' } }),
            color: '#faad14'
        },
        {
            id: 4,
            title: '项目风险预警',
            description: '主厂房建设项目进度已落后计划7.2%，建议增加资源投入',
            type: 'alert',
            timestamp: new Date(),
            module: '项目管理',
            importance: 'high',
            icon: React.createElement(icons_1.AlertOutlined, { style: { color: '#ff4d4f' } }),
            color: '#ff4d4f'
        },
        {
            id: 5,
            title: '物资库存优化',
            description: '分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本',
            type: 'recommendation',
            timestamp: new Date(),
            module: '物资管理',
            importance: 'low',
            icon: React.createElement(icons_1.BulbOutlined, { style: { color: '#1890ff' } }),
            color: '#1890ff'
        },
    ]), insights = _l[0], setInsights = _l[1];
    // 添加智能分析功能状态
    var _m = react_1.useState('today'), analysisActiveTab = _m[0], setAnalysisActiveTab = _m[1];
    var _o = react_1.useState(false), showAnalyticsSidebar = _o[0], setShowAnalyticsSidebar = _o[1];
    return (React.createElement("div", { className: visualDashboard_module_css_1["default"].dashboard },
        React.createElement("header", { className: visualDashboard_module_css_1["default"].header },
            React.createElement("div", { className: visualDashboard_module_css_1["default"].goHomeButton, onClick: handleGoHome },
                React.createElement(icons_1.HomeOutlined, null),
                React.createElement("span", null, "\u8FD4\u56DE\u9996\u9875")),
            React.createElement("h1", null, "\u7EFC\u5408\u53EF\u89C6\u5316\u5927\u5C4F"),
            React.createElement("div", { className: visualDashboard_module_css_1["default"].showTime }, currentTime)),
        React.createElement("section", { className: visualDashboard_module_css_1["default"].mainbox },
            React.createElement("div", { className: visualDashboard_module_css_1["default"].column },
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].bar },
                    React.createElement("h2", null, "\u90E8\u95E8\u4EBA\u5458\u5206\u5E03"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: barChartRef }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter })),
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].line },
                    React.createElement("h2", null, "\u8FD1\u671F\u5B89\u5168\u8BB0\u5F55"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: lineChartRef }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter })),
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].pie },
                    React.createElement("h2", null, "\u7269\u8D44\u7C7B\u578B\u5206\u5E03"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: pieChartRef }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter }))),
            React.createElement("div", { className: visualDashboard_module_css_1["default"].column },
                React.createElement("div", { className: visualDashboard_module_css_1["default"].no },
                    React.createElement(StatCards_1["default"], { stats: stats }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].map },
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].map1 }),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].map2 }),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].map3 }),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].particlesContainer, ref: particlesRef }),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].centerLogo + " " + (logoClicked ? visualDashboard_module_css_1["default"].logoClicked : ''), onClick: handleLogoClick },
                            React.createElement("svg", { viewBox: "0 0 1024 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", width: "60", height: "60", fill: "currentColor" },
                                React.createElement("path", { d: "M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-***********.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z" })),
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].logoText }, "\u77FF\u4E1A\u7BA1\u7406\u7CFB\u7EDF"),
                            logoClicked && (React.createElement("div", { className: visualDashboard_module_css_1["default"].companyInfo }, companyInfo[clickCount]))),
                        showRings && (React.createElement(React.Fragment, null,
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].ring1 }),
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].ring2 }),
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].ring3 }))),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: mapChartRef }),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].mapDescription }, "\u70B9\u51FB\u73AF\u5F62\u56FE\u67E5\u770B\u5404\u9879\u76EE\u8BE6\u60C5 \u00B7 \u70B9\u51FB\u4E2D\u592ELOGO\u4E86\u89E3\u66F4\u591A\u4F01\u4E1A\u4FE1\u606F")),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].statusSummary },
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryHeader },
                            React.createElement("h3", null, "\u9879\u76EE\u72B6\u6001\u6458\u8981"),
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryInfo },
                                "\u5171 ",
                                projects.length,
                                " \u4E2A\u9879\u76EE\uFF0C",
                                stats.ongoingProjects,
                                " \u4E2A\u8FDB\u884C\u4E2D")),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryCards }, ['进行中', '待开始', '已完成'].map(function (status) {
                            var count = projects.filter(function (p) { return p.status === status; }).length;
                            var percent = Math.round((count / projects.length) * 100);
                            return (React.createElement("div", { key: status, className: visualDashboard_module_css_1["default"].summaryCard },
                                React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryTitle }, status),
                                React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryCount }, count),
                                React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryPercent },
                                    percent,
                                    "%"),
                                React.createElement("div", { className: visualDashboard_module_css_1["default"].summaryBar, style: {
                                        width: percent + "%",
                                        backgroundColor: status === '进行中' ? '#1890ff' :
                                            status === '待开始' ? '#faad14' : '#52c41a'
                                    } })));
                        }))))),
            React.createElement("div", { className: visualDashboard_module_css_1["default"].column },
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].line2 },
                    React.createElement("h2", null, "\u8D22\u52A1\u5206\u6790\u8D8B\u52BF"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: line2ChartRef }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter })),
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].pie2 },
                    React.createElement("h2", null, "\u90E8\u95E8\u4EBA\u5458\u5206\u5E03"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].chart, ref: pie2ChartRef }),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter })),
                React.createElement("div", { className: visualDashboard_module_css_1["default"].panel + " " + visualDashboard_module_css_1["default"].materialPanel },
                    React.createElement("h2", null, "\u7269\u8D44\u5468\u8F6C\u7EDF\u8BA1"),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].materialTurnoverContainer }, materials.map(function (material, index) { return (React.createElement("div", { key: index, className: visualDashboard_module_css_1["default"].turnoverItem },
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].turnoverTitle }, material.name),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].turnoverBarContainer },
                            React.createElement("div", { className: visualDashboard_module_css_1["default"].turnoverBar, style: {
                                    width: material.turnoverRate + "%",
                                    backgroundColor: "hsl(" + material.turnoverRate * 1.2 + ", 70%, 45%)"
                                } })),
                        React.createElement("div", { className: visualDashboard_module_css_1["default"].turnoverValue },
                            material.turnoverRate,
                            "%"))); })),
                    React.createElement("div", { className: visualDashboard_module_css_1["default"].panelFooter })))),
        process.env.NODE_ENV !== 'production' && (React.createElement("button", { onClick: triggerNotification, style: {
                position: 'fixed',
                bottom: '10px',
                right: '10px',
                padding: '8px 16px',
                background: 'rgba(0, 0, 0, 0.6)',
                color: '#fff',
                border: '1px solid #1890ff',
                borderRadius: '4px',
                cursor: 'pointer',
                zIndex: 1000,
                fontSize: '12px'
            } }, "\u6D4B\u8BD5\u901A\u77E5")),
        React.createElement("div", { className: visualDashboard_module_css_1["default"].notificationArea }, notifications.map(function (notification) { return (React.createElement("div", { key: notification.id, className: visualDashboard_module_css_1["default"].notification + " " + visualDashboard_module_css_1["default"]["notification-" + notification.type] },
            React.createElement("div", { className: visualDashboard_module_css_1["default"].notificationTitle }, notification.title),
            React.createElement("div", { className: visualDashboard_module_css_1["default"].notificationMessage }, notification.message),
            React.createElement("div", { className: visualDashboard_module_css_1["default"].notificationTime }, notification.time.toLocaleTimeString()))); }))));
}
exports.VisualDashboard = VisualDashboard;
