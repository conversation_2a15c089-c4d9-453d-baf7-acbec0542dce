"use client"

import React, { useState, useEffect, useMemo, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Bar<PERSON>hart,
  FileText,
  Plus,
  Search,
  Trash2,
  Edit,
  Download,
  Upload,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  FileUp,
  Eye,
  PieChart,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  LineChart as LineChartIcon,
  Activity,
  AlertCircle,
  TrendingDown,
  Lightbulb,
  HelpCircle,
  Gauge,
  ArrowRight,
  Info,
  ChevronRight,
  Sparkles
} from "lucide-react"
import {
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  BarChart as RechartsBarChart
} from "recharts"
import { cn } from "@/lib/utils"
// 导入 echarts
import ReactECharts from "echarts-for-react"
import * as echarts from "echarts/core"
// echarts 主题
import { theme } from "./echarts-theme"

// 注册 echarts 主题
try {
  echarts.registerTheme('financial-theme', theme);
} catch (e) {
  console.log('已注册 echarts 主题或注册失败', e);
}

// 定义财务数据接口
interface FinancialRecord {
  id: number
  year: string
  quarter: string
  revenue: string
  expenses: string
  profit: string
  status: "草稿" | "审核中" | "已审核" | "已拒绝"
  revenueValue: number // 用于计算和图表展示
  expensesValue: number // 用于计算和图表展示
  profitValue: number // 用于计算和图表展示
  profitRate?: number // 利润率
  notes?: string // 备注
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 图表数据转换类型
interface ChartData {
  name: string
  收入: number
  支出: number
  利润: number
  [key: string]: any // 允许添加其他属性
}

// 添加财务智能分析结果接口
interface FinancialAnalysis {
  id: string;
  type: "alert" | "trend" | "opportunity" | "risk" | "info";
  title: string;
  description: string;
  severity: "high" | "medium" | "low";
  category: string;
  date: string;
  impact?: string;
  recommendation?: string;
  confidence: number;
}

// 财务健康指标接口
interface FinancialHealthMetric {
  name: string;
  value: number;
  benchmark: number;
  status: "good" | "warning" | "danger";
  description: string;
  trend: "up" | "down" | "stable";
  trendValue: number;
}

// 模拟财务数据
const initialFinancialData: FinancialRecord[] = [
  {
    id: 1,
    year: "2025",
    quarter: "Q1",
    revenue: "¥12,450,000",
    expenses: "¥8,320,000",
    profit: "¥4,130,000",
    revenueValue: 12450000,
    expensesValue: 8320000,
    profitValue: 4130000,
    profitRate: 33.2,
    status: "已审核",
    notes: "年末季度业绩良好",
    createdAt: "2025-03-31",
  },
  {
    id: 2,
    year: "2025",
    quarter: "Q1",
    revenue: "¥11,780,000",
    expenses: "¥7,950,000",
    profit: "¥3,830,000",
    revenueValue: 11780000,
    expensesValue: 7950000,
    profitValue: 3830000,
    profitRate: 32.5,
    status: "已审核",
    createdAt: "2025-03-15",
  },
  {
    id: 3,
    year: "2025",
    quarter: "Q1",
    revenue: "¥10,920,000",
    expenses: "¥7,450,000",
    profit: "¥3,470,000",
    revenueValue: 10920000,
    expensesValue: 7450000,
    profitValue: 3470000,
    profitRate: 31.8,
    status: "已审核",
    createdAt: "2025-02-28",
  },
  {
    id: 4,
    year: "2025",
    quarter: "Q1",
    revenue: "¥9,850,000",
    expenses: "¥6,980,000",
    profit: "¥2,870,000",
    revenueValue: 9850000,
    expensesValue: 6980000,
    profitValue: 2870000,
    profitRate: 29.1,
    status: "已审核",
    createdAt: "2025-01-31",
  },
  {
    id: 5,
    year: "2025",
    quarter: "Q1",
    revenue: "¥10,250,000",
    expenses: "¥7,120,000",
    profit: "¥3,130,000",
    revenueValue: 10250000,
    expensesValue: 7120000,
    profitValue: 3130000,
    profitRate: 30.5,
    status: "已审核",
    createdAt: "2025-01-15",
  },
]

// 为了图表使用的收入构成数据
const revenueComposition = [
  { name: "主营业务收入", value: 65 },
  { name: "投资收益", value: 15 },
  { name: "其他收入", value: 10 },
  { name: "补贴收入", value: 8 },
  { name: "利息收入", value: 2 },
]

// 为了图表使用的支出构成数据
const expensesComposition = [
  { name: "人工成本", value: 40 },
  { name: "原材料", value: 25 },
  { name: "设备折旧", value: 15 },
  { name: "运营费用", value: 12 },
  { name: "其他支出", value: 8 },
]

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d']

// 生成趋势折线图配置
const generateTrendLineChartOption = (data: FinancialRecord[]) => {
  // 对数据按年份和季度排序
  const sortedData = [...data].sort((a, b) => {
    if (a.year !== b.year) {
      return parseInt(a.year) - parseInt(b.year);
    }
    return a.quarter.localeCompare(b.quarter);
  });

  // 准备X轴数据和系列数据
  const xAxisData = sortedData.map(item => `${item.year} ${item.quarter}`);
  const seriesData = {
    revenue: sortedData.map(item => (item.revenueValue / 10000).toFixed(2)),
    expenses: sortedData.map(item => (item.expensesValue / 10000).toFixed(2)),
    profit: sortedData.map(item => (item.profitValue / 10000).toFixed(2))
  };

  return {
    title: {
      text: '财务趋势分析',
      subtext: '按季度展示收入、支出和利润（单位：万元）'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          result += param.marker + ' ' + param.seriesName + ': ' + param.value + ' 万元<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['收入', '支出', '利润']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 万元'
      }
    },
    series: [
      {
        name: '收入',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        symbolSize: 8,
        data: seriesData.revenue
      },
      {
        name: '支出',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        symbolSize: 8,
        data: seriesData.expenses
      },
      {
        name: '利润',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        symbolSize: 8,
        areaStyle: {
          opacity: 0.1
        },
        data: seriesData.profit
      }
    ]
  };
};

// 生成饼图配置
const generatePieChartOption = (data: any[], title: string, subtext: string) => {
  return {
    title: {
      text: title,
      subtext: subtext,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  };
};

// 生成雷达图配置
const generateRadarChartOption = (data: FinancialRecord[]) => {
  // 计算季度平均值
  const quarterData = {
    'Q1': { revenue: 0, expenses: 0, profit: 0, count: 0 },
    'Q2': { revenue: 0, expenses: 0, profit: 0, count: 0 },
    'Q3': { revenue: 0, expenses: 0, profit: 0, count: 0 },
    'Q4': { revenue: 0, expenses: 0, profit: 0, count: 0 }
  };

  data.forEach(item => {
    if (quarterData[item.quarter as keyof typeof quarterData]) {
      quarterData[item.quarter as keyof typeof quarterData].revenue += item.revenueValue;
      quarterData[item.quarter as keyof typeof quarterData].expenses += item.expensesValue;
      quarterData[item.quarter as keyof typeof quarterData].profit += item.profitValue;
      quarterData[item.quarter as keyof typeof quarterData].count += 1;
    }
  });

  // 计算平均值
  Object.keys(quarterData).forEach(quarter => {
    const stats = quarterData[quarter as keyof typeof quarterData];
    if (stats.count > 0) {
      stats.revenue = stats.revenue / stats.count / 10000;
      stats.expenses = stats.expenses / stats.count / 10000;
      stats.profit = stats.profit / stats.count / 10000;
    }
  });

  return {
    title: {
      text: '季度财务表现',
      subtext: '各季度平均财务指标（万元）'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['收入', '支出', '利润']
    },
    radar: {
      shape: 'circle',
      indicator: [
        { name: 'Q1', max: Math.max(...Object.values(quarterData).map(d => d.revenue)) * 1.2 },
        { name: 'Q2', max: Math.max(...Object.values(quarterData).map(d => d.revenue)) * 1.2 },
        { name: 'Q3', max: Math.max(...Object.values(quarterData).map(d => d.revenue)) * 1.2 },
        { name: 'Q4', max: Math.max(...Object.values(quarterData).map(d => d.revenue)) * 1.2 }
      ]
    },
    series: [
      {
        name: '季度表现',
        type: 'radar',
        data: [
          {
            value: [
              quarterData.Q1.revenue,
              quarterData.Q2.revenue,
              quarterData.Q3.revenue,
              quarterData.Q4.revenue
            ],
            name: '收入',
            symbol: 'circle',
            symbolSize: 8,
            areaStyle: {
              opacity: 0.3
            }
          },
          {
            value: [
              quarterData.Q1.expenses,
              quarterData.Q2.expenses,
              quarterData.Q3.expenses,
              quarterData.Q4.expenses
            ],
            name: '支出',
            symbol: 'circle',
            symbolSize: 8,
            areaStyle: {
              opacity: 0.3
            }
          },
          {
            value: [
              quarterData.Q1.profit,
              quarterData.Q2.profit,
              quarterData.Q3.profit,
              quarterData.Q4.profit
            ],
            name: '利润',
            symbol: 'circle',
            symbolSize: 8,
            areaStyle: {
              opacity: 0.3
            }
          }
        ]
      }
    ]
  };
};

// 生成柱状图配置
const generateBarChartOption = (data: FinancialRecord[]) => {
  // 按年份分组计算总值
  const yearData: Record<string, { revenue: number, expenses: number, profit: number }> = {};

  data.forEach(item => {
    if (!yearData[item.year]) {
      yearData[item.year] = { revenue: 0, expenses: 0, profit: 0 };
    }
    yearData[item.year].revenue += item.revenueValue / 10000;
    yearData[item.year].expenses += item.expensesValue / 10000;
    yearData[item.year].profit += item.profitValue / 10000;
  });

  // 转换为数组并排序
  const years = Object.keys(yearData).sort();

  return {
    title: {
      text: '年度财务对比',
      subtext: '各年收入、支出和利润对比（万元）'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['收入', '支出', '利润']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: years
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 万元'
      }
    },
    series: [
      {
        name: '收入',
        type: 'bar',
        barGap: 0,
        barWidth: '15%',
        emphasis: {
          focus: 'series'
        },
        data: years.map(year => yearData[year].revenue.toFixed(2))
      },
      {
        name: '支出',
        type: 'bar',
        barWidth: '15%',
        emphasis: {
          focus: 'series'
        },
        data: years.map(year => yearData[year].expenses.toFixed(2))
      },
      {
        name: '利润',
        type: 'bar',
        barWidth: '15%',
        emphasis: {
          focus: 'series'
        },
        data: years.map(year => yearData[year].profit.toFixed(2))
      }
    ]
  };
};

// 生成漏斗图配置
const generateFunnelChartOption = (data: FinancialRecord[]) => {
  // 计算各状态数量及其对应收入总额
  const statusStats: Record<string, { count: number, totalRevenue: number }> = {
    '草稿': { count: 0, totalRevenue: 0 },
    '审核中': { count: 0, totalRevenue: 0 },
    '已审核': { count: 0, totalRevenue: 0 },
    '已拒绝': { count: 0, totalRevenue: 0 }
  };

  data.forEach(item => {
    statusStats[item.status].count += 1;
    statusStats[item.status].totalRevenue += item.revenueValue / 10000;
  });

  const funnelData = Object.keys(statusStats).map(status => ({
    value: statusStats[status].totalRevenue,
    name: `${status} (${statusStats[status].count}条)`
  }));

  return {
    title: {
      text: '财务记录状态分布',
      subtext: '各状态的记录数量及总收入（万元）'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} 万元'
    },
    legend: {
      data: funnelData.map(item => item.name)
    },
    series: [
      {
        name: '财务状态',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: Math.max(...funnelData.map(item => item.value)) * 1.2,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 14
          }
        },
        data: funnelData
      }
    ]
  };
};

export function FinancialStatusMaintenance() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<FinancialRecord | null>(null)
  const [financialData, setFinancialData] = useState<FinancialRecord[]>(initialFinancialData)
  const [filteredData, setFilteredData] = useState<FinancialRecord[]>(initialFinancialData)
  const [yearFilter, setYearFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    year: "",
    quarter: "",
    revenue: "",
    expenses: "",
    notes: ""
  })

  // 添加财务智能分析数据
  const [financialAnalysisResults, setFinancialAnalysisResults] = useState<FinancialAnalysis[]>([
    {
      id: "1",
      type: "trend",
      title: "利润持续增长",
      description: "近4个季度利润率持续上升，同比增长15.2%，主要原因为成本控制效果显著，且收入增长稳定。",
      severity: "low",
      category: "利润",
      date: "2025-03-02",
      impact: "积极",
      recommendation: "建议继续保持当前成本控制策略，并适度增加市场投入，扩大市场份额。",
      confidence: 92
    },
    {
      id: "2",
      type: "alert",
      title: "应收账款周转率下降",
      description: "应收账款周转率较上季度下降18%，可能导致现金流压力增加。部分大客户付款周期已延长至90天以上。",
      severity: "high",
      category: "现金流",
      date: "2025-03-03",
      impact: "负面",
      recommendation: "建议加强应收账款管理，与大客户重新协商付款条件，对超期账款实施积极催收。",
      confidence: 89
    },
    {
      id: "3",
      type: "opportunity",
      title: "融资成本优化空间",
      description: "分析显示当前融资结构中短期高息贷款占比较高，重组为长期低息贷款可节省年息35万元。",
      severity: "medium",
      category: "融资",
      date: "2025-03-25",
      impact: "积极",
      recommendation: "建议与银行协商转换部分短期贷款为长期贷款，利用当前的低利率环境锁定融资成本。",
      confidence: 85
    },
    {
      id: "4",
      type: "risk",
      title: "季节性现金流风险",
      description: "预测显示Q3可能出现短期现金流缺口，主要由于传统销售淡季与大型采购计划重叠。",
      severity: "medium",
      category: "现金流",
      date: "2024-03-30",
      impact: "负面",
      recommendation: "建议提前3个月准备临时信用额度，或调整大型采购计划，分批进行以平滑现金流。",
      confidence: 83
    },
    {
      id: "5",
      type: "info",
      title: "税收优惠政策机会",
      description: "分析发现公司符合最新研发费用加计扣除政策条件，可额外减免所得税约28万元。",
      severity: "low",
      category: "税务",
      date: "2024-04-01",
      impact: "积极",
      recommendation: "建议联系税务顾问，准备相关研发项目文档，在下一纳税申报期申请此项优惠。",
      confidence: 94
    }
  ]);

  // 添加财务健康指标数据
  const [healthMetrics, setHealthMetrics] = useState<FinancialHealthMetric[]>([
    {
      name: "流动比率",
      value: 1.85,
      benchmark: 1.5,
      status: "good",
      description: "短期偿债能力良好",
      trend: "up",
      trendValue: 0.15
    },
    {
      name: "速动比率",
      value: 1.2,
      benchmark: 1.0,
      status: "good",
      description: "短期流动性充足",
      trend: "up",
      trendValue: 0.08
    },
    {
      name: "资产负债率",
      value: 0.48,
      benchmark: 0.5,
      status: "good",
      description: "负债水平适中",
      trend: "down",
      trendValue: 0.03
    },
    {
      name: "毛利率",
      value: 0.32,
      benchmark: 0.3,
      status: "good",
      description: "盈利能力稳定",
      trend: "up",
      trendValue: 0.02
    },
    {
      name: "净利率",
      value: 0.11,
      benchmark: 0.12,
      status: "warning",
      description: "略低于行业平均",
      trend: "up",
      trendValue: 0.01
    },
    {
      name: "应收账款周转率",
      value: 4.2,
      benchmark: 6.0,
      status: "danger",
      description: "回款速度缓慢",
      trend: "down",
      trendValue: 0.8
    }
  ]);

  // 添加财务预测数据
  const [financialForecast, setFinancialForecast] = useState({
    revenue: {
      q1: 12650000,
      q2: 13450000,
      q3: 12950000,
      q4: 14250000,
      confidence: 0.85
    },
    expenses: {
      q1: 8320000,
      q2: 8760000,
      q3: 8480000,
      q4: 9180000,
      confidence: 0.82
    },
    profit: {
      q1: 4330000,
      q2: 4690000,
      q3: 4470000,
      q4: 5070000,
      confidence: 0.80
    }
  });

  // 搜索功能
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    applyFilters(value, yearFilter, statusFilter)
  }

  // 应用所有筛选条件
  const applyFilters = (search: string, year: string, status: string) => {
    let result = financialData

    // 搜索条件
    if (search) {
      result = result.filter(
        (item) =>
          item.year.includes(search) ||
          item.quarter.includes(search) ||
          item.status.includes(search) ||
          (item.notes && item.notes.includes(search))
      )
    }

    // 年份筛选
    if (year !== "all") {
      result = result.filter(item => item.year === year)
    }

    // 状态筛选
    if (status !== "all") {
      result = result.filter(item => item.status === status)
    }

    setFilteredData(result)
  }

  // 处理年份筛选变化
  const handleYearFilterChange = (value: string) => {
    setYearFilter(value)
    applyFilters(searchTerm, value, statusFilter)
  }

  // 处理状态筛选变化
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value)
    applyFilters(searchTerm, yearFilter, value)
  }

  // 获取所有年份（用于筛选）
  const availableYears = useMemo(() => {
    const years = new Set(financialData.map(item => item.year))
    return Array.from(years).sort().reverse() // 降序排列年份
  }, [financialData])

  // 处理刷新数据
  const handleRefresh = () => {
    setIsLoading(true)
    // 模拟API请求延迟
    setTimeout(() => {
      setFinancialData(initialFinancialData)
      setFilteredData(initialFinancialData)
      setSearchTerm("")
      setYearFilter("all")
      setStatusFilter("all")
      setIsLoading(false)
      toast({
        title: "刷新成功",
        description: "财务数据已更新",
      })
    }, 800)
  }

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    })
  }

  // 计算利润
  const calculateProfit = (revenue: string, expenses: string): [string, number] => {
    // 移除人民币符号和逗号
    const revenueValue = Number(revenue.replace(/[^\d.-]/g, ''))
    const expensesValue = Number(expenses.replace(/[^\d.-]/g, ''))
    const profitValue = revenueValue - expensesValue

    // 格式化为带货币符号的字符串
    const formattedProfit = `¥${profitValue.toLocaleString()}`
    return [formattedProfit, profitValue]
  }

  // 处理添加财务记录
  const handleAddRecord = () => {
    if (!formData.year || !formData.quarter || !formData.revenue || !formData.expenses) {
      toast({
        title: "添加失败",
        description: "请填写所有必填字段",
        variant: "destructive",
      })
      return
    }

    // 移除人民币符号和逗号
    const revenueValue = Number(formData.revenue.replace(/[^\d.-]/g, ''))
    const expensesValue = Number(formData.expenses.replace(/[^\d.-]/g, ''))
    const [profit, profitValue] = calculateProfit(formData.revenue, formData.expenses)
    const profitRate = revenueValue > 0 ? (profitValue / revenueValue) * 100 : 0

    // 创建新记录
    const newRecord: FinancialRecord = {
      id: Date.now(), // 使用时间戳作为临时ID
      year: formData.year,
      quarter: formData.quarter,
      revenue: `¥${revenueValue.toLocaleString()}`,
      expenses: `¥${expensesValue.toLocaleString()}`,
      profit,
      revenueValue,
      expensesValue,
      profitValue,
      profitRate: parseFloat(profitRate.toFixed(1)),
      status: "草稿",
      notes: formData.notes,
      createdAt: new Date().toISOString().split('T')[0],
    }

    // 更新状态
    setFinancialData(prev => [newRecord, ...prev])
    setFilteredData(prev => [newRecord, ...prev])
    setFormData({
      year: "",
      quarter: "",
      revenue: "",
      expenses: "",
      notes: ""
    })
    setIsAddDialogOpen(false)
    toast({
      title: "添加成功",
      description: "财务记录已添加",
    })
  }

  // 处理查看详情
  const handleViewDetail = (record: FinancialRecord) => {
    setSelectedRecord(record)
    setIsViewDetailOpen(true)
  }

  // 处理编辑记录
  const handleEditRecord = (record: FinancialRecord) => {
    setSelectedRecord(record)
    // 将收入和支出的数字格式转换为纯数字字符串
    setFormData({
      year: record.year,
      quarter: record.quarter,
      revenue: record.revenueValue.toString(),
      expenses: record.expensesValue.toString(),
      notes: record.notes || ""
    })
    setIsEditDialogOpen(true)
  }

  // 处理更新记录
  const handleUpdateRecord = () => {
    if (!selectedRecord || !formData.year || !formData.quarter || !formData.revenue || !formData.expenses) {
      toast({
        title: "更新失败",
        description: "请填写所有必填字段",
        variant: "destructive",
      })
      return
    }

    // 移除人民币符号和逗号
    const revenueValue = Number(formData.revenue.replace(/[^\d.-]/g, ''))
    const expensesValue = Number(formData.expenses.replace(/[^\d.-]/g, ''))
    const [profit, profitValue] = calculateProfit(formData.revenue, formData.expenses)
    const profitRate = revenueValue > 0 ? (profitValue / revenueValue) * 100 : 0

    // 更新记录
    const updatedRecord: FinancialRecord = {
      ...selectedRecord,
      year: formData.year,
      quarter: formData.quarter,
      revenue: `¥${revenueValue.toLocaleString()}`,
      expenses: `¥${expensesValue.toLocaleString()}`,
      profit,
      revenueValue,
      expensesValue,
      profitValue,
      profitRate: parseFloat(profitRate.toFixed(1)),
      notes: formData.notes,
      updatedAt: new Date().toISOString().split('T')[0],
    }

    // 更新状态
    setFinancialData(prev => prev.map(item => item.id === selectedRecord.id ? updatedRecord : item))
    setFilteredData(prev => prev.map(item => item.id === selectedRecord.id ? updatedRecord : item))
    setIsEditDialogOpen(false)
    setSelectedRecord(null)
    toast({
      title: "更新成功",
      description: "财务记录已更新",
    })
  }

  // 处理删除记录
  const handleDeleteRecord = (record: FinancialRecord) => {
    setSelectedRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (!selectedRecord) return

    // 更新状态
    setFinancialData(prev => prev.filter(item => item.id !== selectedRecord.id))
    setFilteredData(prev => prev.filter(item => item.id !== selectedRecord.id))
    setIsDeleteDialogOpen(false)
    setSelectedRecord(null)
    toast({
      title: "删除成功",
      description: "财务记录已删除",
    })
  }

  // 处理导出数据
  const handleExport = () => {
    try {
      // 在真实环境中，这里应该执行导出逻辑
      // 模拟导出延迟
      setIsLoading(true)
      setTimeout(() => {
        setIsLoading(false)
        toast({
          title: "导出成功",
          description: "财务数据已导出",
        })
      }, 800)
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已审核":
        return <Badge className="bg-green-100 text-green-800">已审核</Badge>
      case "审核中":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">审核中</Badge>
      case "草稿":
        return <Badge variant="secondary">草稿</Badge>
      case "已拒绝":
        return <Badge variant="destructive">已拒绝</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 计算总计数据
  const totals = useMemo(() => {
    const totalRevenue = financialData.reduce((sum, item) => sum + item.revenueValue, 0)
    const totalExpenses = financialData.reduce((sum, item) => sum + item.expensesValue, 0)
    const totalProfit = financialData.reduce((sum, item) => sum + item.profitValue, 0)
    const avgProfitRate = financialData.length > 0
      ? financialData.reduce((sum, item) => sum + (item.profitRate || 0), 0) / financialData.length
      : 0

    return {
      totalRevenue,
      totalExpenses,
      totalProfit,
      avgProfitRate: parseFloat(avgProfitRate.toFixed(1))
    }
  }, [financialData])

  // 准备图表数据 - 趋势数据
  const trendChartData = useMemo(() => {
    // 按季度排序数据
    return [...financialData]
      .sort((a, b) => {
        // 先按年份排序
        if (a.year !== b.year) {
          return parseInt(a.year) - parseInt(b.year)
        }
        // 然后按季度排序
        return a.quarter.localeCompare(b.quarter)
      })
      .map(item => ({
        name: `${item.year} ${item.quarter}`,
        收入: item.revenueValue / 10000, // 转为万元
        支出: item.expensesValue / 10000,
        利润: item.profitValue / 10000,
      }))
  }, [financialData])

  // 获取分析类型图标
  const getAnalysisTypeIcon = (type: string) => {
    switch (type) {
      case "alert":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case "trend":
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      case "opportunity":
        return <Lightbulb className="h-5 w-5 text-green-500" />;
      case "risk":
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case "info":
        return <Info className="h-5 w-5 text-gray-500" />;
      default:
        return <HelpCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  // 获取严重程度标签
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "high":
        return <Badge className="bg-red-500">高优先级</Badge>;
      case "medium":
        return <Badge className="bg-orange-500">中优先级</Badge>;
      case "low":
        return <Badge className="bg-blue-500">低优先级</Badge>;
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 获取健康指标状态颜色
  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "text-green-500";
      case "warning":
        return "text-orange-500";
      case "danger":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  // 获取趋势图标
  const getTrendIcon = (trend: string, value: number) => {
    if (trend === "up") {
      return <TrendingUp className={`h-4 w-4 ${value > 0 ? "text-green-500" : "text-red-500"}`} />;
    } else if (trend === "down") {
      return <TrendingDown className={`h-4 w-4 ${value < 0 ? "text-green-500" : "text-red-500"}`} />;
    } else {
      return <ArrowRight className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <DollarSign className="h-6 w-6 text-blue-600" />
        <h1 className="text-2xl font-bold">财务状况维护</h1>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索..."
              className="w-64 pl-8"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
          <Select value={yearFilter} onValueChange={handleYearFilterChange}>
            <SelectTrigger className="w-[100px]">
              <SelectValue placeholder="年份" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有年份</SelectItem>
              {availableYears.map(year => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="草稿">草稿</SelectItem>
              <SelectItem value="审核中">审核中</SelectItem>
              <SelectItem value="已审核">已审核</SelectItem>
              <SelectItem value="已拒绝">已拒绝</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={cn("mr-2 h-4 w-4", isLoading && "animate-spin")} />
            刷新
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport} disabled={isLoading}>
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                添加财务记录
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>添加财务记录</DialogTitle>
                <DialogDescription>请填写财务记录的详细信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="year">年份 <span className="text-red-500">*</span></Label>
                    <Input
                      id="year"
                      placeholder="例如：2023"
                      value={formData.year}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quarter">季度 <span className="text-red-500">*</span></Label>
                    <Select
                      value={formData.quarter}
                      onValueChange={(value) => setFormData({...formData, quarter: value})}
                    >
                      <SelectTrigger id="quarter">
                        <SelectValue placeholder="选择季度" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Q1">Q1</SelectItem>
                        <SelectItem value="Q2">Q2</SelectItem>
                        <SelectItem value="Q3">Q3</SelectItem>
                        <SelectItem value="Q4">Q4</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="revenue">收入（元）<span className="text-red-500">*</span></Label>
                    <Input
                      id="revenue"
                      type="number"
                      placeholder="0.00"
                      value={formData.revenue}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expenses">支出（元）<span className="text-red-500">*</span></Label>
                    <Input
                      id="expenses"
                      type="number"
                      placeholder="0.00"
                      value={formData.expenses}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">备注</Label>
                  <Textarea
                    id="notes"
                    placeholder="可选"
                    value={formData.notes}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddRecord}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totals.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">所有季度收入总和</p>
            <div className="mt-2 h-1.5 w-full rounded-full bg-gray-100">
              <div className="h-full rounded-full bg-blue-500" style={{ width: '100%' }} />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总支出</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totals.totalExpenses.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">所有季度支出总和</p>
            <div className="mt-2 h-1.5 w-full rounded-full bg-gray-100">
              <div
                className="h-full rounded-full bg-red-500"
                style={{ width: `${(totals.totalExpenses / totals.totalRevenue) * 100}%` }}
              />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总利润</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totals.totalProfit.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">所有季度利润总和</p>
            <div className="mt-2 h-1.5 w-full rounded-full bg-gray-100">
              <div
                className="h-full rounded-full bg-green-500"
                style={{ width: `${(totals.totalProfit / totals.totalRevenue) * 100}%` }}
              />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均利润率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.avgProfitRate}%</div>
            <p className="text-xs text-muted-foreground">所有季度平均利润率</p>
            <div className="mt-2 h-1.5 w-full rounded-full bg-gray-100">
              <div
                className="h-full rounded-full bg-purple-500"
                style={{ width: `${totals.avgProfitRate}%` }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="table">
        <TabsList>
          <TabsTrigger value="table">
            <FileText className="mr-2 h-4 w-4" />
            表格视图
          </TabsTrigger>
          <TabsTrigger value="chart">
            <LineChartIcon className="mr-2 h-4 w-4" />
            趋势图表
          </TabsTrigger>
          <TabsTrigger value="composition">
            <PieChart className="mr-2 h-4 w-4" />
            构成分析
          </TabsTrigger>
          <TabsTrigger value="advanced">
            <Activity className="mr-2 h-4 w-4" />
            高级分析
          </TabsTrigger>
        </TabsList>

        {/* 表格视图 */}
        <TabsContent value="table" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>年份</TableHead>
                    <TableHead>季度</TableHead>
                    <TableHead>收入</TableHead>
                    <TableHead>支出</TableHead>
                    <TableHead>利润</TableHead>
                    <TableHead>利润率</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <FileText className="h-10 w-10 mb-2" />
                          <p>暂无财务记录</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.year}</TableCell>
                      <TableCell>{item.quarter}</TableCell>
                        <TableCell className="font-medium">{item.revenue}</TableCell>
                      <TableCell>{item.expenses}</TableCell>
                        <TableCell className={item.profitValue > 0 ? "text-green-600" : "text-red-600"}>
                          {item.profit}
                        </TableCell>
                        <TableCell>{item.profitRate ? `${item.profitRate}%` : "-"}</TableCell>
                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button variant="ghost" size="icon" onClick={() => handleViewDetail(item)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleEditRecord(item)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleDeleteRecord(item)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                          </div>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 趋势图表视图 - 使用 ECharts */}
        <TabsContent value="chart" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>财务趋势分析</CardTitle>
              <CardDescription>按季度展示收入、支出和利润（单位：万元）</CardDescription>
            </CardHeader>
            <CardContent className="h-96 w-full">
              <ReactECharts
                option={generateTrendLineChartOption(financialData)}
                style={{ height: '100%', width: '100%' }}
                theme="financial-theme"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 构成分析视图 - 使用 ECharts */}
        <TabsContent value="composition" className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>收入构成分析</CardTitle>
                <CardDescription>主要收入来源分布</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generatePieChartOption(revenueComposition, '收入构成', '各收入来源占比')}
                  style={{ height: '100%', width: '100%' }}
                  theme="financial-theme"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支出构成分析</CardTitle>
                <CardDescription>主要支出项目分布</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generatePieChartOption(expensesComposition, '支出构成', '各支出项目占比')}
                  style={{ height: '100%', width: '100%' }}
                  theme="financial-theme"
                />
              </CardContent>
            </Card>
              </div>
        </TabsContent>

        {/* 高级分析视图 - 新增 */}
        <TabsContent value="advanced" className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>年度财务对比</CardTitle>
                <CardDescription>各年收入、支出和利润对比（万元）</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateBarChartOption(financialData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="financial-theme"
                />
            </CardContent>
          </Card>

            <Card>
              <CardHeader>
                <CardTitle>季度财务表现</CardTitle>
                <CardDescription>各季度平均财务指标雷达图（万元）</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateRadarChartOption(financialData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="financial-theme"
                />
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>财务记录状态分布</CardTitle>
                <CardDescription>各状态的记录数量及总收入分布（万元）</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateFunnelChartOption(financialData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="financial-theme"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑财务记录</DialogTitle>
            <DialogDescription>修改财务记录的详细信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="year">年份 <span className="text-red-500">*</span></Label>
                <Input
                  id="year"
                  placeholder="例如：2023"
                  value={formData.year}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quarter">季度 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.quarter}
                  onValueChange={(value) => setFormData({...formData, quarter: value})}
                >
                  <SelectTrigger id="quarter">
                    <SelectValue placeholder="选择季度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Q1">Q1</SelectItem>
                    <SelectItem value="Q2">Q2</SelectItem>
                    <SelectItem value="Q3">Q3</SelectItem>
                    <SelectItem value="Q4">Q4</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="revenue">收入（元）<span className="text-red-500">*</span></Label>
                <Input
                  id="revenue"
                  type="number"
                  placeholder="0.00"
                  value={formData.revenue}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expenses">支出（元）<span className="text-red-500">*</span></Label>
                <Input
                  id="expenses"
                  type="number"
                  placeholder="0.00"
                  value={formData.expenses}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">备注</Label>
              <Textarea
                id="notes"
                placeholder="可选"
                value={formData.notes}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdateRecord}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>财务记录详情</DialogTitle>
            <DialogDescription>
              {selectedRecord && `${selectedRecord.year}年 ${selectedRecord.quarter}季度`}
            </DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium mb-1">收入</h3>
                  <p className="text-2xl font-bold text-blue-600">{selectedRecord.revenue}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">支出</h3>
                  <p className="text-2xl font-bold text-red-600">{selectedRecord.expenses}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium mb-1">利润</h3>
                  <p className={`text-2xl font-bold ${selectedRecord.profitValue > 0 ? "text-green-600" : "text-red-600"}`}>{selectedRecord.profit}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">利润率</h3>
                  <p className="text-2xl font-bold text-purple-600">{selectedRecord.profitRate}%</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">状态</h3>
                <div>{getStatusBadge(selectedRecord.status)}</div>
              </div>

              {selectedRecord.notes && (
                <div>
                  <h3 className="text-sm font-medium mb-1">备注</h3>
                  <p className="p-3 bg-slate-50 rounded text-sm">{selectedRecord.notes}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-6 mt-2 text-sm text-muted-foreground">
                <div>
                  <p>创建日期：{selectedRecord.createdAt || "-"}</p>
                </div>
                <div>
                  <p>更新日期：{selectedRecord.updatedAt || "-"}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewDetailOpen(false)
              if (selectedRecord) {
                handleEditRecord(selectedRecord)
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>确认删除</span>
            </DialogTitle>
            <DialogDescription>
              您确定要删除此财务记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="py-4">
              <p><strong>年份：</strong>{selectedRecord.year}</p>
              <p><strong>季度：</strong>{selectedRecord.quarter}</p>
              <p><strong>收入：</strong>{selectedRecord.revenue}</p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

