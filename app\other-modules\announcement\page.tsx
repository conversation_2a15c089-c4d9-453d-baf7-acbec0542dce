"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { MainLayout } from "@/components/main-layout"
import { Bell, Search, Plus, Edit, Trash2, ChevronLeft, Calendar, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

interface Announcement {
  id: string
  title: string
  content: string
  type: string
  status: string
  publishDate: string
  publisher: string
}

export default function AnnouncementPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [announcements, setAnnouncements] = useState<Announcement[]>([
    {
      id: "1",
      title: "系统维护通知",
      content: "系统将于2025年3月10日晚上22:00-次日凌晨2:00进行系统维护，请提前做好相关工作安排。",
      type: "系统通知",
      status: "已发布",
      publishDate: "2025-03-05",
      publisher: "管理员"
    },
    {
      id: "2",
      title: "安全生产月活动通知",
      content: "为响应国家安全生产月号召，公司将于2025年4月开展一系列安全生产活动，请各部门积极参与。",
      type: "重要通知",
      status: "已发布",
      publishDate: "2025-03-01",
      publisher: "安全管理部"
    },
    {
      id: "3",
      title: "员工培训计划",
      content: "公司将于2025年4月开展新一轮员工技能培训，请各部门做好人员安排。",
      type: "普通通知",
      status: "草稿",
      publishDate: "2025-03-08",
      publisher: "人事管理部"
    }
  ])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null)
  const [formData, setFormData] = useState<Partial<Announcement>>({
    title: "",
    content: "",
    type: "普通通知",
    status: "草稿"
  })
  const [activeTab, setActiveTab] = useState("all")

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id.replace('announcement-', '')]: value
    }))
  }

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAddAnnouncement = () => {
    if (!formData.title || !formData.content) {
      toast({
        title: "请填写必要信息",
        description: "标题和内容为必填项",
        variant: "destructive"
      })
      return
    }

    const newAnnouncement: Announcement = {
      id: Date.now().toString(),
      title: formData.title || "",
      content: formData.content || "",
      type: formData.type || "普通通知",
      status: formData.status || "草稿",
      publishDate: new Date().toISOString().split('T')[0],
      publisher: "当前用户"
    }

    setAnnouncements([newAnnouncement, ...announcements])
    setIsAddDialogOpen(false)
    setFormData({
      title: "",
      content: "",
      type: "普通通知",
      status: "草稿"
    })

    toast({
      title: "添加成功",
      description: "公告已成功添加"
    })
  }

  const viewAnnouncement = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement)
    setIsViewDialogOpen(true)
  }

  const filteredAnnouncements = announcements.filter(announcement => {
    if (activeTab === "all") return true
    if (activeTab === "published") return announcement.status === "已发布"
    if (activeTab === "draft") return announcement.status === "草稿"
    return true
  })

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => router.push("/")}>
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <h2 className="text-2xl font-bold">公告管理</h2>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            新建公告
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>公告列表</CardTitle>
                <CardDescription>管理系统公告和通知</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="搜索公告..." className="pl-8 w-[250px]" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-4">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="published">已发布</TabsTrigger>
                <TabsTrigger value="draft">草稿</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="space-y-4">
              {filteredAnnouncements.length > 0 ? (
                filteredAnnouncements.map(announcement => (
                  <Card key={announcement.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => viewAnnouncement(announcement)}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <Bell className="h-5 w-5 text-blue-500 mt-1" />
                          <div>
                            <h3 className="font-medium">{announcement.title}</h3>
                            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                              {announcement.content}
                            </p>
                            <div className="flex items-center gap-4 mt-2">
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                <span>{announcement.publishDate}</span>
                              </div>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <User className="h-3 w-3" />
                                <span>{announcement.publisher}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge variant={announcement.status === "已发布" ? "default" : "outline"}>
                            {announcement.status}
                          </Badge>
                          <Badge variant="secondary">{announcement.type}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <Bell className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-medium">暂无公告</h3>
                  <p className="mt-2 text-sm text-muted-foreground">当前没有符合条件的公告</p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">共 {filteredAnnouncements.length} 条公告</div>
          </CardFooter>
        </Card>

        {/* 添加公告对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>新建公告</DialogTitle>
              <DialogDescription>创建新的系统公告</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="announcement-title">标题</Label>
                <Input
                  id="announcement-title"
                  placeholder="请输入公告标题"
                  value={formData.title || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="announcement-type">类型</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger id="announcement-type">
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="系统通知">系统通知</SelectItem>
                      <SelectItem value="重要通知">重要通知</SelectItem>
                      <SelectItem value="普通通知">普通通知</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="announcement-status">状态</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger id="announcement-status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="草稿">草稿</SelectItem>
                      <SelectItem value="已发布">发布</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="announcement-content">内容</Label>
                <Textarea
                  id="announcement-content"
                  placeholder="请输入公告内容"
                  rows={8}
                  value={formData.content || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddAnnouncement}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 查看公告对话框 */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedAnnouncement?.title}</DialogTitle>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={selectedAnnouncement?.status === "已发布" ? "default" : "outline"}>
                  {selectedAnnouncement?.status}
                </Badge>
                <Badge variant="secondary">{selectedAnnouncement?.type}</Badge>
              </div>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="prose max-w-none">
                <p>{selectedAnnouncement?.content}</p>
              </div>
              <div className="flex items-center justify-between text-sm text-muted-foreground mt-4 pt-4 border-t">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>发布人: {selectedAnnouncement?.publisher}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>发布日期: {selectedAnnouncement?.publishDate}</span>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                关闭
              </Button>
              <Button variant="default">
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}