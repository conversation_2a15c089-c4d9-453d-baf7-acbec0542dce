const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 创建导出目录
const EXPORT_DIR = path.join(process.cwd(), 'static-export');

// 清理先前的导出目录
if (fs.existsSync(EXPORT_DIR)) {
  console.log('清理先前的导出目录...');
  fs.rmSync(EXPORT_DIR, { recursive: true, force: true });
}

// 创建新的导出目录
fs.mkdirSync(EXPORT_DIR, { recursive: true });

try {
  // 执行Next.js构建
  console.log('执行Next.js构建...');
  execSync('npm run build', { stdio: 'inherit' });
  
  // 复制out目录内容到导出目录
  const outDir = path.join(process.cwd(), 'out');
  console.log('复制静态文件到导出目录...');
  
  // 递归复制函数
  function copyFolderSync(from, to) {
    fs.mkdirSync(to, { recursive: true });
    fs.readdirSync(from).forEach(element => {
      const sourceFile = path.join(from, element);
      const destFile = path.join(to, element);
      if (fs.lstatSync(sourceFile).isDirectory()) {
        copyFolderSync(sourceFile, destFile);
      } else {
        fs.copyFileSync(sourceFile, destFile);
      }
    });
  }
  
  copyFolderSync(outDir, EXPORT_DIR);
  
  // 创建Netlify _redirects文件支持SPA导航
  const redirectsPath = path.join(EXPORT_DIR, '_redirects');
  fs.writeFileSync(redirectsPath, '/* /index.html 200');
  
  // 创建Netlify配置文件
  const netlifyConfigPath = path.join(EXPORT_DIR, 'netlify.toml');
  fs.writeFileSync(netlifyConfigPath, `[build]
  publish = "/"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
`);

  // 复制成功页面到导出目录
  const successPagePath = path.join(process.cwd(), 'scripts', 'netlify-success.html');
  const destSuccessPagePath = path.join(EXPORT_DIR, 'success.html');
  if (fs.existsSync(successPagePath)) {
    console.log('复制部署成功页面...');
    fs.copyFileSync(successPagePath, destSuccessPagePath);
  } else {
    console.warn('警告: 未找到成功页面模板，跳过复制');
  }

  console.log('静态导出完成！文件已保存到: ' + EXPORT_DIR);
  console.log('您可以将此目录中的文件上传到Netlify或其他静态网站托管服务。');
  console.log('部署后，访问 /success.html 查看部署成功页面。');
  
} catch (error) {
  console.error('导出过程中出错:', error);
  process.exit(1);
} 