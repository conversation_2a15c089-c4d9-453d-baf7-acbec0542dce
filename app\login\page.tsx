"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { useTheme } from "@/contexts/theme-context"
import { Eye, EyeOff, User, Lock, Info, CheckCircle, Loader2, Shield, HardHat, Users, DollarSign } from "lucide-react"
import Particles from "@/components/Particles"

// 图片路径数组
const LOGIN_IMAGES = [
  "/login/微信图片_20250420154836_2017.jpg",
  "/login/微信图片_20250420154836_2018.jpg",
  "/login/微信图片_20250420154836_2016.jpg",
  "/login/微信图片_20250420154836_1993.jpg",
  "/login/微信图片_20250420154836_2007.jpg",
  "/login/微信图片_20250420154836_2005.jpg",
  "/login/微信图片_20250420154836_1997.jpg",
  "/login/微信图片_20250420154836_1994.jpg",
  "/login/微信图片_20250420154836_1992.jpg",
  "/login/微信图片_20250420154836_1996.jpg",
  "/login/微信图片_20250420144207_2247.jpg",
  "/login/微信图片_20250420144207_2248.jpg",
  "/login/微信图片_20250420144207_2257.jpg",
  "/login/微信图片_20250420144207_2254.jpg",
  "/login/微信图片_20250420144207_2256.jpg",
  "/login/微信图片_20250420144207_2258.jpg",
  "/login/微信图片_20250420144207_2255.jpg",
  "/login/微信图片_20250420144207_2251.jpg",
  "/login/微信图片_20250420144207_2250.jpg",
  "/login/微信图片_20250420144207_2249.jpg",
  "/login/微信图片_20250420144207_2253.jpg",
  "/login/微信图片_20250420144207_2252.jpg",
]

// 图片幻灯片组件
function ImageSlider({ position, themeMode }: { position: 'left' | 'right', themeMode: string }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [nextImageIndex, setNextImageIndex] = useState(1)
  const [isTransitioning, setIsTransitioning] = useState(false)
  
  // 根据位置（左/右）选择不同的图片子集
  const images = useMemo(() => {
    const startIndex = position === 'left' ? 0 : Math.floor(LOGIN_IMAGES.length / 2)
    const endIndex = position === 'left' ? Math.floor(LOGIN_IMAGES.length / 2) : LOGIN_IMAGES.length
    return LOGIN_IMAGES.slice(startIndex, endIndex)
  }, [position])
  
  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev + 1) % images.length)
        setNextImageIndex((prev) => (prev + 1) % images.length)
        setIsTransitioning(false)
      }, 1000) // 1秒后完成切换
    }, 5000) // 每5秒切换一次图片
    
    return () => clearInterval(interval)
  }, [images.length])
  
  return (
    <div className={`absolute inset-y-0 ${position === 'left' ? 'left-0' : 'right-0'} w-1/4 overflow-hidden pointer-events-none select-none hidden lg:block`}>
      <div className="relative w-full h-full">
        {/* 当前图片 */}
        <div
          className={`absolute inset-0 transition-opacity duration-1000 ${
            isTransitioning ? 'opacity-0' : 'opacity-30'
          }`}
          style={{
            backgroundImage: `url(${images[currentImageIndex]})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: `blur(2px) ${themeMode === 'dark' ? 'brightness(0.7)' : 'brightness(1.1)'}`,
          }}
        />
        
        {/* 下一张图片 */}
        <div
          className={`absolute inset-0 transition-opacity duration-1000 ${
            isTransitioning ? 'opacity-30' : 'opacity-0'
          }`}
          style={{
            backgroundImage: `url(${images[nextImageIndex]})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: `blur(2px) ${themeMode === 'dark' ? 'brightness(0.7)' : 'brightness(1.1)'}`,
          }}
        />
        
        {/* 渐变遮罩 */}
        <div 
          className="absolute inset-0" 
          style={{
            background: position === 'left' 
              ? `linear-gradient(to right, transparent 0%, ${themeMode === 'dark' ? '#1c1c1e' : '#f5f5f7'} 100%)` 
              : `linear-gradient(to left, transparent 0%, ${themeMode === 'dark' ? '#1c1c1e' : '#f5f5f7'} 100%)`
          }}
        />
      </div>
    </div>
  )
}

export default function LoginPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { login, isAuthenticated, user } = useAuth()
  const { themeMode, toggleTheme } = useTheme()
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showNotice, setShowNotice] = useState(false)

  // 如果已经登录，则直接跳转到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.replace("/")
    }
  }, [isAuthenticated, router])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const success = await login(username, password)

      if (success) {
        // 如果选择了"记住我"，则保存用户名
        if (rememberMe) {
          localStorage.setItem("rememberedUsername", username)
        } else {
          localStorage.removeItem("rememberedUsername")
        }

        toast({
          title: "登录成功",
          description: "欢迎回到2025 矿业公司综合管理系统",
        })

        // 设置登录标记，用于首页显示欢迎弹窗
        sessionStorage.setItem("showWelcomePopup", "true")
        sessionStorage.setItem("username", user?.username || username)

        // 直接跳转到首页
        router.replace("/")
      } else {
        toast({
          title: "登录失败",
          description: "用户名或密码错误，请重试",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    } catch (error) {
      toast({
        title: "登录失败",
        description: "发生错误，请稍后重试",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  // 检查是否有记住的用户名
  useEffect(() => {
    const rememberedUsername = localStorage.getItem("rememberedUsername")
    if (rememberedUsername) {
      setUsername(rememberedUsername)
      setRememberMe(true)
    }
  }, [])

  // 系统通知内容
  const systemNotice = [
    {
      title: "系统升级通知",
      content: "系统将于2025年3月15日22:00-次日凌晨2:00进行升级维护，请做好相关工作安排。",
      date: "2025-03-05"
    },
    {
      title: "安全检查提醒",
      content: "请各部门于本周内完成安全自查工作，并将结果报送至安全管理部门。",
      date: "2025-03-02"
    }
  ]

  return (
    <div className={`min-h-screen flex flex-col items-center justify-center transition-colors duration-300 overflow-hidden ${
      themeMode === "dark"
        ? "bg-[#1c1c1e] text-white"
        : "bg-[#f5f5f7] text-[#1d1d1f]"
    }`}>
      {/* 粒子动效背景 */}
      <Particles
        particleCount={700}
        particleSpread={10}
        speed={0.9}
        particleColors={themeMode === "dark"
          ? ["#ffffff", "#bbbbbb", "#888888", "#555555", "#333333"]
          : ["#111111", "#333333", "#555555", "#777777", "#999999"]
        }
        particleBaseSize={300}
        sizeRandomness={1.5}
        moveParticlesOnHover={true}
        particleHoverFactor={1.2}
        alphaParticles={true}
        disableRotation={false}
        cameraDistance={12}
      />
      
      {/* 左侧图片幻灯片 */}
      <ImageSlider position="left" themeMode={themeMode} />
      
      {/* 右侧图片幻灯片 */}
      <ImageSlider position="right" themeMode={themeMode} />

      {/* 主题切换按钮 */}
      <button
        onClick={toggleTheme}
        className="absolute top-4 right-4 p-2 rounded-full transition-colors z-10"
        aria-label="切换主题模式"
      >
        {themeMode === "dark" ? (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-yellow-400">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-900">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
          </svg>
        )}
      </button>

      <div className="w-full max-w-md px-4 relative z-10">
        <div className="text-center mb-8 animate-fadeIn">
          <div className="flex justify-center mb-6">
            <div className={`w-20 h-20 flex items-center justify-center p-1 animate-pulse ${themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"}`}>
              <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                <path d="M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-112-119-220.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z" fill="currentColor" />
              </svg>
            </div>
          </div>
          <h1 className={`text-3xl font-bold tracking-tight animate-fadeIn ${themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"}`}>
            2025 矿业公司综合管理系统
          </h1>
          <p className={`mt-2 animate-fadeIn ${themeMode === "dark" ? "text-gray-400" : "text-gray-500"}`}>
            安全、高效、智能化的企业管理平台
          </p>
        </div>

        <Card className={`border animate-slideUp ${
          themeMode === "dark"
            ? "bg-[#2c2c2e]/90 backdrop-blur-md border-[#3a3a3c] shadow-xl shadow-black/10"
            : "bg-white/90 backdrop-blur-md border-gray-200 shadow-lg"
        }`}>
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className={`text-xl font-medium ${themeMode === "dark" ? "text-white" : "text-[#1d1d1f]"}`}>
              账号登录
            </CardTitle>
            <CardDescription className={themeMode === "dark" ? "text-gray-400" : "text-gray-500"}>
              请输入您的账号密码进入系统
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleLogin}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className={themeMode === "dark" ? "text-gray-300" : ""}>
                  用户名
                </Label>
                <div className="relative">
                  <User className={`absolute left-3 top-2.5 h-4 w-4 transition-all ${
                    themeMode === "dark" ? "text-gray-400" : "text-gray-500"
                  }`} />
                <Input
                  id="username"
                  placeholder="请输入用户名"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                    className={`pl-10 transition-all ${
                      themeMode === "dark"
                        ? "bg-[#3a3a3c] border-0 text-white placeholder:text-gray-500 focus-visible:ring-gray-500"
                        : "bg-gray-50 border-gray-200 focus-visible:ring-gray-300"
                    }`}
                />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className={themeMode === "dark" ? "text-gray-300" : ""}>
                    密码
                  </Label>
                  <Button
                    variant="link"
                    className={`p-0 h-auto text-sm transition-colors ${themeMode === "dark" ? "text-gray-400 hover:text-gray-300" : "text-gray-600"}`}
                    type="button"
                    onClick={() => router.push("/forgot-password")}
                  >
                    忘记密码?
                  </Button>
                </div>
                <div className="relative">
                  <Lock className={`absolute left-3 top-2.5 h-4 w-4 transition-colors ${
                    themeMode === "dark" ? "text-gray-400" : "text-gray-500"
                  }`} />
                <Input
                  id="password"
                    type={showPassword ? "text" : "password"}
                  placeholder="请输入密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                    className={`pl-10 transition-all ${
                      themeMode === "dark"
                        ? "bg-[#3a3a3c] border-0 text-white placeholder:text-gray-500 focus-visible:ring-gray-500"
                        : "bg-gray-50 border-gray-200 focus-visible:ring-gray-300"
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className={`absolute right-3 top-2.5 transition-colors ${
                      themeMode === "dark" ? "text-gray-400 hover:text-gray-300" : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
              <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                    className={themeMode === "dark" ? "border-gray-600 data-[state=checked]:bg-gray-400 data-[state=checked]:text-black" : ""}
                  />
                  <Label
                    htmlFor="remember"
                    className={`text-sm ${themeMode === "dark" ? "text-gray-300" : ""}`}
                  >
                    记住我
                  </Label>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className={`text-xs h-8 transition-colors ${themeMode === "dark" ? "text-gray-400 hover:text-gray-300" : "text-gray-600"}`}
                  onClick={() => setShowNotice(!showNotice)}
                >
                  <Info className="h-3.5 w-3.5 mr-1" />
                  系统公告
                </Button>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                type="submit"
                className={`w-full h-11 font-medium transition-all ${
                  themeMode === "dark"
                    ? "bg-white text-black hover:bg-gray-200"
                    : "bg-black text-white hover:bg-gray-800"
                } ${isLoading ? "opacity-80" : ""}`}
                disabled={isLoading}
              >
                {isLoading ? "登录中..." : "登录"}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* 系统公告弹窗 */}
        {showNotice && (
          <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm animate-fadeIn`}>
            <div
              className={`w-full max-w-md p-6 rounded-lg shadow-xl animate-scaleUp ${
                themeMode === "dark" ? "bg-[#2c2c2e] text-white" : "bg-white text-[#1d1d1f]"
              }`}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">系统公告</h3>
                <button
                  onClick={() => setShowNotice(false)}
                  className={`p-1 rounded-full transition-colors ${
                    themeMode === "dark" ? "hover:bg-[#3a3a3c]" : "hover:bg-gray-100"
                  }`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div className={`max-h-[280px] overflow-y-auto space-y-4 ${
                themeMode === "dark" ? "scrollbar-thin scrollbar-thumb-gray-600" : "scrollbar-thin scrollbar-thumb-gray-300"
              }`}>
                {systemNotice.map((notice, i) => (
                  <div key={i} className={`pb-3 ${
                    i < systemNotice.length - 1 ? "border-b" : ""
                  } ${themeMode === "dark" ? "border-gray-700" : "border-gray-200"}`}>
                    <h4 className="font-medium">{notice.title}</h4>
                    <p className={`mt-1 text-sm ${themeMode === "dark" ? "text-gray-400" : "text-gray-600"}`}>
                      {notice.content}
                    </p>
                    <div className={`flex items-center mt-2 text-xs ${
                      themeMode === "dark" ? "text-gray-500" : "text-gray-500"
                    }`}>
                      <span>{notice.date}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className={`text-center mt-6 text-sm ${themeMode === "dark" ? "text-gray-500" : "text-gray-500"}`}>
          <p>© 2025 矿业公司综合管理系统. 保留所有权利.</p>
          <p className="mt-2 text-xs">测试账号: admin / 密码: admin123</p>
        </div>
      </div>
    </div>
  )
}