"use client"

import type React from "react"
import { useState, useEffect, useMemo, type ChangeEvent } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  Filter,
  Wallet,
  Building2,
  AlertTriangle,
  Eye,
  XCircle,
  AlertCircle,
  HelpCircle,
  CheckCircle2,
  Wrench,
  RefreshCcw,
  Database,
  Activity,
  FileText,
  PieChart,
  LineChart,
  Settings,
  Cog,
  Upload,
  Save
} from "lucide-react"
import ReactECharts from "echarts-for-react"
import * as echarts from "echarts/core"
import { cn } from "@/lib/utils"
import type { ToastActionElement, Toast } from "@/components/ui/toast"
import * as XLSX from "xlsx"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose
} from "@/components/ui/sheet"

// echarts 主题
const theme = {
  color: [
    '#1890ff',
    '#2fc25b',
    '#facc14',
    '#223273',
    '#8543e0',
    '#13c2c2',
    '#3436c7',
    '#f04864'
  ],
  backgroundColor: 'transparent',
  textStyle: {},
  title: {
    textStyle: {
      color: '#666666'
    },
    subtextStyle: {
      color: '#999999'
    }
  },
  line: {
    itemStyle: {
      borderWidth: 2
    },
    lineStyle: {
      width: 3
    },
    symbolSize: 8,
    symbol: 'circle',
    smooth: true
  },
  radar: {
    itemStyle: {
      borderWidth: 2
    },
    lineStyle: {
      width: 3
    },
    symbolSize: 8,
    symbol: 'circle',
    smooth: true
  },
  bar: {
    itemStyle: {
      barBorderWidth: 0,
      barBorderColor: '#ccc'
    }
  },
  pie: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  gauge: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    }
  },
  candlestick: {
    itemStyle: {
      color: '#eb5454',
      color0: '#47b262',
      borderColor: '#eb5454',
      borderColor0: '#47b262',
      borderWidth: 1
    }
  },
  graph: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    lineStyle: {
      width: 1,
      color: '#aaa'
    },
    symbolSize: 8,
    symbol: 'circle',
    smooth: true,
    color: [
      '#1890ff',
      '#2fc25b',
      '#facc14',
      '#223273',
      '#8543e0',
      '#13c2c2',
      '#3436c7',
      '#f04864'
    ],
    label: {
      color: '#ffffff'
    }
  },
  map: {
    itemStyle: {
      areaColor: '#eee',
      borderColor: '#444',
      borderWidth: 0.5
    },
    label: {
      color: '#000'
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(255,215,0,0.8)',
        borderColor: '#444',
        borderWidth: 1
      },
      label: {
        color: 'rgb(100,0,0)'
      }
    }
  }
}

// 注册 echarts 主题
try {
  echarts.registerTheme('assets-theme', theme);
} catch (e) {
  console.log('已注册 echarts 主题或注册失败', e);
}

// 资产状态类型
type AssetStatus = "使用中" | "闲置" | "维修中" | "报废";

// 定义资产接口
interface FixedAsset {
  id: string
  name: string
  code: string
  category: string
  purchaseDate: string
  originalValue: number
  currentValue: number
  depreciationRate: number
  location: string
  status: AssetStatus
  manufacturer?: string
  model?: string
  responsible?: string
  department?: string
  lastMaintenanceDate?: string
  nextMaintenanceDate?: string
  utilizationRate: number
  usageYears: number
  notes?: string
}

// 资产类别统计
interface CategoryStats {
  category: string
  count: number
  totalValue: number
  avgUtilization: number
}

// 模拟资产数据
const assetsData: FixedAsset[] = [
  {
    id: "1",
    name: "采矿设备A型",
    code: "ZC2025001",
    category: "采矿设备",
    purchaseDate: "2025-01-15",
    originalValue: 1200000,
    currentValue: 1080000,
    depreciationRate: 10,
    location: "A矿区",
    status: "使用中",
    manufacturer: "重工机械制造有限公司",
    model: "CMJ-2023-A",
    responsible: "张三",
    department: "生产部",
    lastMaintenanceDate: "2025-01-15",
    nextMaintenanceDate: "2025-04-15",
    utilizationRate: 85,
    usageYears: 0.8,
    notes: "设备运行状况良好，定期维护保养"
  },
  {
    id: "2",
    name: "运输车辆B型",
    code: "ZC2025002",
    category: "运输设备",
    purchaseDate: "2025-02-20",
    originalValue: 850000,
    currentValue: 765000,
    depreciationRate: 10,
    location: "车队",
    status: "使用中",
    manufacturer: "重型汽车制造厂",
    model: "YSC-2023-B",
    responsible: "李四",
    department: "运输部",
    lastMaintenanceDate: "2025-01-20",
    nextMaintenanceDate: "2025-04-01",
    utilizationRate: 90,
    usageYears: 0.7,
    notes: "车况良好，按时保养"
  },
  {
    id: "3",
    name: "安全监控系统",
    code: "ZC2025045",
    category: "安全设备",
    purchaseDate: "2025-01-10",
    originalValue: 650000,
    currentValue: 520000,
    depreciationRate: 20,
    location: "监控中心",
    status: "使用中",
    manufacturer: "安防科技有限公司",
    model: "AQ-2022-C",
    responsible: "王五",
    department: "安全部",
    lastMaintenanceDate: "2025-01-10",
    nextMaintenanceDate: "2025-04-10",
    utilizationRate: 95,
    usageYears: 1.2,
    notes: "系统运行稳定，需定期升级"
  },
  {
    id: "4",
    name: "办公楼",
    code: "ZC2025101",
    category: "房屋建筑",
    purchaseDate: "2025-01-18",
    originalValue: 8500000,
    currentValue: 6375000,
    depreciationRate: 5,
    location: "总部",
    status: "使用中",
    manufacturer: "建筑工程有限公司",
    model: "标准办公楼",
    responsible: "赵六",
    department: "行政部",
    lastMaintenanceDate: "2025-01-18",
    nextMaintenanceDate: "2025-04-18",
    utilizationRate: 100,
    usageYears: 8.4,
    notes: "定期检查维护，保养良好"
  },
  {
    id: "5",
    name: "旧采矿设备",
    code: "ZC2025105",
    category: "采矿设备",
    purchaseDate: "2025-01-05",
    originalValue: 950000,
    currentValue: 95000,
    depreciationRate: 10,
    location: "仓库",
    status: "闲置",
    manufacturer: "重工机械制造有限公司",
    model: "CMJ-2010-B",
    responsible: "孙七",
    department: "生产部",
    lastMaintenanceDate: "2025-01-05",
    nextMaintenanceDate: "2025-04-05",
    utilizationRate: 0,
    usageYears: 12.9,
    notes: "设备老化，考虑报废处理"
  },
  {
    id: "6",
    name: "新型钻探设备",
    code: "ZC2025003",
    category: "钻探设备",
    purchaseDate: "2025-02-01",
    originalValue: 2800000,
    currentValue: 2660000,
    depreciationRate: 10,
    location: "B矿区",
    status: "维修中",
    manufacturer: "精密机械制造厂",
    model: "ZT-2023-A",
    responsible: "周八",
    department: "生产部",
    lastMaintenanceDate: "2025-02-01",
    nextMaintenanceDate: "2025-04-01",
    utilizationRate: 75,
    usageYears: 0.4,
    notes: "正在进行首次大保养"
  },
  {
    id: "7",
    name: "变电站设备",
    code: "ZC2025008",
    category: "电力设备",
    purchaseDate: "2025-03-15",
    originalValue: 1500000,
    currentValue: 1050000,
    depreciationRate: 10,
    location: "变电所",
    status: "使用中",
    manufacturer: "电力设备制造厂",
    model: "BD-2020-A",
    responsible: "吴九",
    department: "动力部",
    lastMaintenanceDate: "2025-03-15",
    nextMaintenanceDate: "2025-04-15",
    utilizationRate: 98,
    usageYears: 3.6,
    notes: "运行稳定，定期检修"
  }
]

// 工具函数
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// 计算资产类别统计
const calculateCategoryStats = (assets: FixedAsset[]): CategoryStats[] => {
  const stats = new Map<string, CategoryStats>();

  assets.forEach(asset => {
    if (!stats.has(asset.category)) {
      stats.set(asset.category, {
        category: asset.category,
        count: 0,
        totalValue: 0,
        avgUtilization: 0
      });
    }

    const categoryStats = stats.get(asset.category)!;
    categoryStats.count++;
    categoryStats.totalValue += asset.currentValue;
    categoryStats.avgUtilization = (categoryStats.avgUtilization * (categoryStats.count - 1) + asset.utilizationRate) / categoryStats.count;
  });

  return Array.from(stats.values());
};

// 生成资产分布图表配置
const generateAssetDistributionChart = (categoryStats: CategoryStats[]) => {
  return {
    title: {
      text: '资产类别分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '资产分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c}台'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: categoryStats.map(stat => ({
          name: stat.category,
          value: stat.count
        }))
      }
    ]
  };
};

// 生成资产状态图表配置
const generateAssetStatusChart = (assets: FixedAsset[]) => {
  const statusCount = {
    '使用中': 0,
    '闲置': 0,
    '维修中': 0,
    '报废': 0
  };

  assets.forEach(asset => {
    statusCount[asset.status as keyof typeof statusCount]++;
  });

  return {
    title: {
      text: '资产状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}台 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c}台'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: Object.entries(statusCount).map(([status, count]) => ({
          name: status,
          value: count
        }))
      }
    ]
  };
};

// 生成资产价值趋势图表配置
const generateAssetValueTrendChart = (assets: FixedAsset[]) => {
  const sortedAssets = [...assets].sort((a, b) =>
    new Date(a.purchaseDate).getTime() - new Date(b.purchaseDate).getTime()
  );

  return {
    title: {
      text: '资产价值趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        return `${params[0].axisValue}<br/>
                原值: ${formatCurrency(params[0].value)}<br/>
                现值: ${formatCurrency(params[1].value)}`;
      }
    },
    legend: {
      data: ['原值', '现值'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: sortedAssets.map(asset => asset.purchaseDate)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatCurrency(value)
      }
    },
    series: [
      {
        name: '原值',
        type: 'line',
        data: sortedAssets.map(asset => asset.originalValue),
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3
        }
      },
      {
        name: '现值',
        type: 'line',
        data: sortedAssets.map(asset => asset.currentValue),
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3
        }
      }
    ]
  };
};

// 生成资产年限分布图表配置
const generateAssetAgeChart = (assets: FixedAsset[]) => {
  const ageRanges = {
    '0-2年': 0,
    '2-5年': 0,
    '5-10年': 0,
    '10年以上': 0
  };

  assets.forEach(asset => {
    if (asset.usageYears <= 2) ageRanges['0-2年']++;
    else if (asset.usageYears <= 5) ageRanges['2-5年']++;
    else if (asset.usageYears <= 10) ageRanges['5-10年']++;
    else ageRanges['10年以上']++;
  });

  return {
    title: {
      text: '资产使用年限分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Object.keys(ageRanges)
    },
    yAxis: {
      type: 'value',
      name: '数量',
      nameLocation: 'middle',
      nameGap: 30
    },
    series: [
      {
        name: '资产数量',
        type: 'bar',
        data: Object.values(ageRanges),
        itemStyle: {
          borderRadius: [5, 5, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}台'
        }
      }
    ]
  };
};

// 生成资产利用率图表配置
const generateUtilizationChart = (categoryStats: CategoryStats[]) => {
  return {
    title: {
      text: '资产利用率分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categoryStats.map(stat => stat.category)
    },
    yAxis: {
      type: 'value',
      name: '平均利用率',
      nameLocation: 'middle',
      nameGap: 30,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '平均利用率',
        type: 'bar',
        data: categoryStats.map(stat => ({
          value: stat.avgUtilization,
          itemStyle: {
            color: stat.avgUtilization >= 80 ? '#67C23A' :
                   stat.avgUtilization >= 60 ? '#E6A23C' : '#F56C6C'
          }
        })),
        itemStyle: {
          borderRadius: [5, 5, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      }
    ]
  };
};

// 状态标签渲染函数
const getStatusBadge = (status: AssetStatus) => {
  const statusConfig = {
    '使用中': { color: 'bg-green-100 text-green-800', icon: <CheckCircle2 className="h-4 w-4 mr-1" /> },
    '闲置': { color: 'bg-yellow-100 text-yellow-800', icon: <AlertCircle className="h-4 w-4 mr-1" /> },
    '维修中': { color: 'bg-blue-100 text-blue-800', icon: <Wrench className="h-4 w-4 mr-1" /> },
    '报废': { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-4 w-4 mr-1" /> }
  } as const;

  const config = statusConfig[status] || {
    color: 'bg-gray-100 text-gray-800',
    icon: <HelpCircle className="h-4 w-4 mr-1" />
  };

        return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.icon}
      {status}
    </span>
  );
};

// 更新 toast 类型
type ToastProps = {
  title?: string;
  description: string;
  variant?: "default" | "destructive";
  action?: ToastActionElement;
}

// 替换 Tool 图标引用
const icons = {
  search: Search,
  settings: Settings,
  database: Database,
  activity: Activity,
  fileText: FileText,
  pieChart: PieChart,
  lineChart: LineChart,
  cog: Cog,
  wrench: Wrench  // 使用 Wrench 替代 Tool
} as const;

const renderIcon = (icon: keyof typeof icons) => {
  const Icon = icons[icon];
  return <Icon className="h-4 w-4" />;
};

// 生成资产折旧趋势图表配置
const generateDepreciationTrendChart = (assets: FixedAsset[]) => {
  // 按年份分组，计算每年的资产折旧总额
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 4 + i);

  const yearlyDepreciation = years.map(year => {
    const yearStart = new Date(year, 0, 1).toISOString().split('T')[0];
    const yearEnd = new Date(year, 11, 31).toISOString().split('T')[0];

    // 过滤该年内购买的资产
    const assetsInYear = assets.filter(asset =>
      asset.purchaseDate >= yearStart && asset.purchaseDate <= yearEnd
    );

    // 计算折旧金额和原值
    const originalValue = assetsInYear.reduce((sum, asset) => sum + asset.originalValue, 0);
    const currentValue = assetsInYear.reduce((sum, asset) => sum + asset.currentValue, 0);
    const depreciationAmount = originalValue - currentValue;

    return {
      year,
      originalValue,
      currentValue,
      depreciationAmount,
      depreciationRate: originalValue > 0 ? (depreciationAmount / originalValue * 100).toFixed(1) : '0'
    };
  });

  return {
    title: {
      text: '资产折旧趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const yearData = yearlyDepreciation[params[0].dataIndex];
        return `${yearData.year}年<br/>
                原值总额: ${formatCurrency(yearData.originalValue)}<br/>
                当前净值: ${formatCurrency(yearData.currentValue)}<br/>
                折旧金额: ${formatCurrency(yearData.depreciationAmount)}<br/>
                折旧率: ${yearData.depreciationRate}%`;
      }
    },
    legend: {
      data: ['原值', '净值', '折旧金额'],
      bottom: '0'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: years.map(year => `${year}年`)
    },
    yAxis: [
      {
        type: 'value',
        name: '金额',
        axisLabel: {
          formatter: (value: number) => formatCurrency(value)
        }
      },
      {
        type: 'value',
        name: '折旧率',
        min: 0,
        max: 100,
        position: 'right',
        axisLine: {
          show: true
        },
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '原值',
        type: 'bar',
        barWidth: '20%',
        data: yearlyDepreciation.map(item => item.originalValue)
      },
      {
        name: '净值',
        type: 'bar',
        barWidth: '20%',
        data: yearlyDepreciation.map(item => item.currentValue)
      },
      {
        name: '折旧金额',
        type: 'bar',
        barWidth: '20%',
        data: yearlyDepreciation.map(item => item.depreciationAmount)
      },
      {
        name: '折旧率',
        type: 'line',
        yAxisIndex: 1,
        data: yearlyDepreciation.map(item => parseFloat(item.depreciationRate)),
        symbolSize: 8,
        itemStyle: {
          color: '#ff7300'
        },
        lineStyle: {
          width: 3
        }
      }
    ]
  };
};

// 生成部门资产分布图表配置
const generateDepartmentAssetsChart = (assets: FixedAsset[]) => {
  // 按部门分组
  const departmentGroups = {} as Record<string, {
    count: number;
    totalValue: number;
    avgValue: number;
    valueByStatus: Record<string, number>;
  }>;

  // 初始化部门数据
  assets.forEach(asset => {
    const dept = asset.department || '未分配';
    if (!departmentGroups[dept]) {
      departmentGroups[dept] = {
        count: 0,
        totalValue: 0,
        avgValue: 0,
        valueByStatus: {
          '使用中': 0,
          '闲置': 0,
          '维修中': 0,
          '报废': 0
        }
      };
    }

    departmentGroups[dept].count++;
    departmentGroups[dept].totalValue += asset.currentValue;
    departmentGroups[dept].valueByStatus[asset.status] += asset.currentValue;
  });

  // 计算平均值
  Object.keys(departmentGroups).forEach(dept => {
    departmentGroups[dept].avgValue = departmentGroups[dept].totalValue / departmentGroups[dept].count;
  });

  // 排序部门 - 按资产总值从高到低
  const sortedDepartments = Object.keys(departmentGroups).sort((a, b) =>
    departmentGroups[b].totalValue - departmentGroups[a].totalValue
  );

  return {
    title: {
      text: '部门资产分布分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const dept = params[0].name;
        const deptData = departmentGroups[dept];
        return `${dept}<br/>
                资产总数: ${deptData.count}项<br/>
                资产总值: ${formatCurrency(deptData.totalValue)}<br/>
                平均单价: ${formatCurrency(deptData.avgValue)}<br/>
                <br/>
                <span style="color:#5470c6">使用中: ${formatCurrency(deptData.valueByStatus['使用中'])}</span><br/>
                <span style="color:#91cc75">闲置: ${formatCurrency(deptData.valueByStatus['闲置'])}</span><br/>
                <span style="color:#fac858">维修中: ${formatCurrency(deptData.valueByStatus['维修中'])}</span><br/>
                <span style="color:#ee6666">报废: ${formatCurrency(deptData.valueByStatus['报废'])}</span>`;
      }
    },
    legend: {
      data: ['资产总值', '平均单价'],
      bottom: '0'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: sortedDepartments,
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '资产总值',
        axisLabel: {
          formatter: (value: number) => formatCurrency(value)
        }
      },
      {
        type: 'value',
        name: '平均单价',
        position: 'right',
        axisLine: {
          show: true
        },
        axisLabel: {
          formatter: (value: number) => formatCurrency(value)
        }
      }
    ],
    series: [
      {
        name: '资产总值',
        type: 'bar',
        barWidth: '40%',
        data: sortedDepartments.map(dept => departmentGroups[dept].totalValue),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      },
      {
        name: '平均单价',
        type: 'line',
        yAxisIndex: 1,
        data: sortedDepartments.map(dept => departmentGroups[dept].avgValue),
        symbolSize: 8,
        itemStyle: {
          color: '#ff7300'
        },
        lineStyle: {
          width: 3
        }
      },
      {
        name: '状态分布',
        type: 'pie',
        radius: ['15%', '25%'],
        center: ['75%', '25%'],
        label: {
          show: false
        },
        emphasis: {
          focus: 'self'
        },
        data: [
          { name: '使用中', value: assets.filter(a => a.status === '使用中').reduce((sum, a) => sum + a.currentValue, 0) },
          { name: '闲置', value: assets.filter(a => a.status === '闲置').reduce((sum, a) => sum + a.currentValue, 0) },
          { name: '维修中', value: assets.filter(a => a.status === '维修中').reduce((sum, a) => sum + a.currentValue, 0) },
          { name: '报废', value: assets.filter(a => a.status === '报废').reduce((sum, a) => sum + a.currentValue, 0) }
        ]
      }
    ]
  };
};

// 生成资产健康状况图表配置
const generateAssetHealthChart = (assets: FixedAsset[]) => {
  // 定义资产健康评分规则
  const calculateHealthScore = (asset: FixedAsset): number => {
    // 根据使用年限、使用率和折旧情况计算健康分数（满分100）
    let score = 100;

    // 根据使用年限扣分
    if (asset.usageYears > 10) {
      score -= 30;
    } else if (asset.usageYears > 5) {
      score -= 15;
    } else if (asset.usageYears > 3) {
      score -= 5;
    }

    // 根据折旧情况扣分
    const depreciationPercent = 1 - (asset.currentValue / asset.originalValue);
    if (depreciationPercent > 0.8) {
      score -= 30;
    } else if (depreciationPercent > 0.5) {
      score -= 15;
    } else if (depreciationPercent > 0.3) {
      score -= 5;
    }

    // 根据使用率扣分 (未使用或使用率过高都不健康)
    if (asset.utilizationRate < 10) {
      score -= 20;
    } else if (asset.utilizationRate > 95) {
      score -= 10;
    }

    // 根据维护情况扣分 - 检查下一次维护日期是否在当前日期之前或一个月内
    if (asset.nextMaintenanceDate) {
      const now = new Date();
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      const maintenanceDate = new Date(asset.nextMaintenanceDate);
      if (!isNaN(maintenanceDate.getTime()) && maintenanceDate <= nextMonth) {
        score -= 20; // 需要维护的资产扣分
      }
    }

    // 确保分数在0-100之间
    return Math.max(0, Math.min(100, score));
  };

  // 计算每个资产的健康分数
  const assetHealthData = assets.map(asset => {
    const healthScore = calculateHealthScore(asset);
    return {
      asset,
      healthScore,
      healthStatus: healthScore >= 80 ? '良好' : healthScore >= 60 ? '一般' : '不佳'
    };
  });

  // 按类别分组统计健康状况
  const categoryHealthMap = {} as Record<string, {
    avgScore: number;
    count: number;
    statusCounts: Record<string, number>;
  }>;

  assetHealthData.forEach(item => {
    const category = item.asset.category;
    if (!categoryHealthMap[category]) {
      categoryHealthMap[category] = {
        avgScore: 0,
        count: 0,
        statusCounts: { '良好': 0, '一般': 0, '不佳': 0 }
      };
    }

    categoryHealthMap[category].avgScore += item.healthScore;
    categoryHealthMap[category].count++;
    categoryHealthMap[category].statusCounts[item.healthStatus]++;
  });

  // 计算平均分
  Object.keys(categoryHealthMap).forEach(category => {
    categoryHealthMap[category].avgScore = categoryHealthMap[category].avgScore / categoryHealthMap[category].count;
  });

  // 按健康状况对资产进行分组
  const healthStatusGroups = {
    '良好': assetHealthData.filter(item => item.healthScore >= 80).length,
    '一般': assetHealthData.filter(item => item.healthScore >= 60 && item.healthScore < 80).length,
    '不佳': assetHealthData.filter(item => item.healthScore < 60).length
  };

  return {
    title: {
      text: '资产健康状况分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom'
    },
    grid: {
      top: 80,
      bottom: 70
    },
    series: [
      {
        name: '健康状况',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c}项 ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: [
          {
            name: '良好',
            value: healthStatusGroups['良好'],
            itemStyle: { color: '#4caf50' }
          },
          {
            name: '一般',
            value: healthStatusGroups['一般'],
            itemStyle: { color: '#ff9800' }
          },
          {
            name: '不佳',
            value: healthStatusGroups['不佳'],
            itemStyle: { color: '#f44336' }
          }
        ]
      }
    ]
  };
};

// 在FixedAssetsInfo组件内添加扩展内容
export function FixedAssetsInfo() {
  const { toast } = useToast();

  // 状态管理
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<FixedAsset | null>(null);
  const [formData, setFormData] = useState<FixedAsset | null>(null);
  const [assetData, setAssetData] = useState<FixedAsset[]>(assetsData);
  const [filteredData, setFilteredData] = useState<FixedAsset[]>(assetsData);
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(false);

  // 高级筛选状态
  const [isAdvancedFilterOpen, setIsAdvancedFilterOpen] = useState(false);
  const [valueRange, setValueRange] = useState<[number, number]>([0, 3000000]);
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  });
  const [maintenanceDateRange, setMaintenanceDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  });
  const [utilizationRange, setUtilizationRange] = useState<[number, number]>([0, 100]);
  const [ageRange, setAgeRange] = useState<[number, number]>([0, 15]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedResponsibles, setSelectedResponsibles] = useState<string[]>([]);
  const [savedFilters, setSavedFilters] = useState<{name: string, filter: any}[]>([]);
  const [currentFilterName, setCurrentFilterName] = useState("");

  // 计算统计数据
  const calculateMaintenanceNeeded = (data: FixedAsset[]): number => {
    return data.filter(asset => {
      if (!asset.nextMaintenanceDate) return false;
      const nextMaintenance = new Date(asset.nextMaintenanceDate);
      const today = new Date();
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
      return !isNaN(nextMaintenance.getTime()) && nextMaintenance <= nextMonth;
    }).length;
  };

  // 获取唯一的资产类别
  const categories = useMemo(() => {
    const uniqueCategories = new Set(assetData.map(asset => asset.category));
    return ['全部', ...Array.from(uniqueCategories)];
  }, [assetData]);

  // 计算唯一部门和负责人列表
  const departments = useMemo(() => {
    const deptSet = new Set<string>();
    assetData.forEach(asset => {
      if (asset.department) deptSet.add(asset.department);
    });
    return Array.from(deptSet);
  }, [assetData]);

  const responsibles = useMemo(() => {
    const respSet = new Set<string>();
    assetData.forEach(asset => {
      if (asset.responsible) respSet.add(asset.responsible);
    });
    return Array.from(respSet);
  }, [assetData]);

  // 搜索和过滤处理函数
  const handleSearch = (searchTerm: string) => {
    const searchResults = assetData.filter(asset =>
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (asset.responsible?.toLowerCase() || "").includes(searchTerm.toLowerCase())
    );
    setFilteredData(applyFilters(searchResults, categoryFilter, statusFilter));
  };

  const handleCategoryFilterChange = (category: string) => {
    setCategoryFilter(category);
    setFilteredData(applyFilters(assetData, category, statusFilter));
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setFilteredData(applyFilters(assetData, categoryFilter, status));
  };

  const applyFilters = (data: FixedAsset[], category: string, status: string) => {
    let result = [...data];

    // 基本筛选
    if (category !== '全部' && category !== '') {
      result = result.filter(asset => asset.category === category);
    }

    if (status !== '全部' && status !== '') {
      result = result.filter(asset => asset.status === status);
    }

    // 高级筛选
    // 价值范围筛选
    result = result.filter(asset =>
      asset.currentValue >= valueRange[0] &&
      asset.currentValue <= valueRange[1]
    );

    // 日期范围筛选
    if (dateRange.start) {
      result = result.filter(asset =>
        asset.purchaseDate >= dateRange.start
      );
    }

    if (dateRange.end) {
      result = result.filter(asset =>
        asset.purchaseDate <= dateRange.end
      );
    }

    // 维护日期范围筛选
    if (maintenanceDateRange.start && maintenanceDateRange.end) {
      result = result.filter(asset =>
        asset.nextMaintenanceDate &&
        asset.nextMaintenanceDate >= maintenanceDateRange.start &&
        asset.nextMaintenanceDate <= maintenanceDateRange.end
      );
    }

    // 使用率范围筛选
    result = result.filter(asset =>
      asset.utilizationRate >= utilizationRange[0] &&
      asset.utilizationRate <= utilizationRange[1]
    );

    // 使用年限范围筛选
    result = result.filter(asset =>
      asset.usageYears >= ageRange[0] &&
      asset.usageYears <= ageRange[1]
    );

    // 部门筛选
    if (selectedDepartments.length > 0) {
      result = result.filter(asset =>
        asset.department && selectedDepartments.includes(asset.department)
      );
    }

    // 负责人筛选
    if (selectedResponsibles.length > 0) {
      result = result.filter(asset =>
        asset.responsible && selectedResponsibles.includes(asset.responsible)
      );
    }

    return result;
  };

  // 修复表单数据处理
  const handleFormChange = (field: keyof FixedAsset, value: any) => {
    setFormData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        [field]: value
      };
    });
  };

  // 修复刷新操作
  const handleRefresh = async () => {
    try {
      setIsLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: "刷新成功",
        description: "资产信息已更新至最新状态",
      });
    } catch (error) {
      toast({
        title: "刷新失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 添加导出功能
  const handleExport = () => {
    try {
      const exportData = assetData.map(asset => ({
        '资产名称': asset.name,
        '资产编号': asset.code,
        '资产类别': asset.category,
        '购买日期': asset.purchaseDate,
        '原值': asset.originalValue,
        '当前价值': asset.currentValue,
        '折旧率': asset.depreciationRate,
        '位置': asset.location,
        '状态': asset.status,
        '制造商': asset.manufacturer || '',
        '型号': asset.model || '',
        '负责人': asset.responsible || '',
        '部门': asset.department || '',
        '上次维护日期': asset.lastMaintenanceDate || '',
        '下次维护日期': asset.nextMaintenanceDate || '',
        '使用率': asset.utilizationRate,
        '使用年限': asset.usageYears,
        '备注': asset.notes || ''
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 20 }, // 资产名称
        { wch: 15 }, // 资产编号
        { wch: 15 }, // 资产类别
        { wch: 12 }, // 购买日期
        { wch: 12 }, // 原值
        { wch: 12 }, // 当前价值
        { wch: 10 }, // 折旧率
        { wch: 15 }, // 位置
        { wch: 10 }, // 状态
        { wch: 15 }, // 制造商
        { wch: 15 }, // 型号
        { wch: 12 }, // 负责人
        { wch: 15 }, // 部门
        { wch: 12 }, // 上次维护日期
        { wch: 12 }, // 下次维护日期
        { wch: 10 }, // 使用率
        { wch: 10 }, // 使用年限
        { wch: 30 }, // 备注
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '固定资产列表')
      XLSX.writeFile(wb, `固定资产列表_${new Date().toLocaleDateString()}.xlsx`)

      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 添加导入功能
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        const importedAssets: FixedAsset[] = jsonData.map((row: any) => ({
          id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: row['资产名称'] || '',
          code: row['资产编号'] || '',
          category: row['资产类别'] || '',
          purchaseDate: row['购买日期'] || '',
          originalValue: Number(row['原值']) || 0,
          currentValue: Number(row['当前价值']) || 0,
          depreciationRate: Number(row['折旧率']) || 0,
          location: row['位置'] || '',
          status: row['状态'] as AssetStatus || '使用中',
          manufacturer: row['制造商'],
          model: row['型号'],
          responsible: row['负责人'],
          department: row['部门'],
          lastMaintenanceDate: row['上次维护日期'],
          nextMaintenanceDate: row['下次维护日期'],
          utilizationRate: Number(row['使用率']) || 0,
          usageYears: Number(row['使用年限']) || 0,
          notes: row['备注']
        }))

        setAssetData(prev => [...prev, ...importedAssets])
        setFilteredData(prev => [...prev, ...importedAssets])

        toast({
          title: "导入成功",
          description: `已导入 ${importedAssets.length} 条记录`,
        })
      } catch (error) {
        console.error('导入失败:', error)
        toast({
          title: "导入失败",
          description: "请检查文件格式是否正确",
          variant: "destructive",
        })
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 添加资产功能
  const handleAddAsset = (formData: Partial<FixedAsset>) => {
    const newAsset: FixedAsset = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: formData.name || "",
      code: formData.code || "",
      category: formData.category || "",
      purchaseDate: formData.purchaseDate || new Date().toISOString().split('T')[0],
      originalValue: formData.originalValue || 0,
      currentValue: formData.currentValue || 0,
      depreciationRate: formData.depreciationRate || 0,
      location: formData.location || "",
      status: formData.status || "使用中",
      manufacturer: formData.manufacturer,
      model: formData.model,
      responsible: formData.responsible,
      department: formData.department,
      lastMaintenanceDate: formData.lastMaintenanceDate,
      nextMaintenanceDate: formData.nextMaintenanceDate,
      utilizationRate: formData.utilizationRate || 0,
      usageYears: formData.usageYears || 0,
      notes: formData.notes
    }

    setAssetData(prev => [...prev, newAsset])
    setFilteredData(prev => [...prev, newAsset])
    toast({
      title: "添加成功",
      description: "已成功添加新的固定资产",
    })
    setIsAddDialogOpen(false)
  }

  // 更新资产处理函数
  const handleUpdateAsset = (formData: Partial<FixedAsset>) => {
    if (!selectedAsset) return;

    const updatedAsset: FixedAsset = {
      ...selectedAsset,
      ...formData,
      // 确保必需字段有值
      name: formData.name || selectedAsset.name,
      code: formData.code || selectedAsset.code,
      category: formData.category || selectedAsset.category,
      purchaseDate: formData.purchaseDate || selectedAsset.purchaseDate,
      originalValue: formData.originalValue ?? selectedAsset.originalValue,
      currentValue: formData.currentValue ?? selectedAsset.currentValue,
      depreciationRate: formData.depreciationRate ?? selectedAsset.depreciationRate,
      location: formData.location || selectedAsset.location,
      status: formData.status || selectedAsset.status,
      utilizationRate: formData.utilizationRate ?? selectedAsset.utilizationRate,
      usageYears: formData.usageYears ?? selectedAsset.usageYears,
    };

    setAssetData(prev => prev.map(asset =>
      asset.id === selectedAsset.id ? updatedAsset : asset
    ));
    setFilteredData(prev => prev.map(asset =>
      asset.id === selectedAsset.id ? updatedAsset : asset
    ));

    toast({
      title: "更新成功",
      description: "已成功更新固定资产信息",
    });
    setIsEditDialogOpen(false);
    setSelectedAsset(null);
  };

  // 更新表单提交处理函数
  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;

    const formData: Partial<FixedAsset> = {
      name: (form.querySelector('#name') as HTMLInputElement)?.value || '',
      code: (form.querySelector('#code') as HTMLInputElement)?.value || '',
      category: (form.querySelector('#category') as HTMLSelectElement)?.value || '',
      purchaseDate: (form.querySelector('#purchaseDate') as HTMLInputElement)?.value || '',
      originalValue: Number((form.querySelector('#originalValue') as HTMLInputElement)?.value || 0),
      currentValue: Number((form.querySelector('#currentValue') as HTMLInputElement)?.value || 0),
      depreciationRate: Number((form.querySelector('#depreciationRate') as HTMLInputElement)?.value || 0),
      location: (form.querySelector('#location') as HTMLInputElement)?.value || '',
      status: (form.querySelector('#status') as HTMLSelectElement)?.value as AssetStatus || '使用中',
      manufacturer: (form.querySelector('#manufacturer') as HTMLInputElement)?.value,
      model: (form.querySelector('#model') as HTMLInputElement)?.value,
      responsible: (form.querySelector('#responsible') as HTMLInputElement)?.value,
      department: (form.querySelector('#department') as HTMLInputElement)?.value,
      lastMaintenanceDate: (form.querySelector('#lastMaintenanceDate') as HTMLInputElement)?.value,
      nextMaintenanceDate: (form.querySelector('#nextMaintenanceDate') as HTMLInputElement)?.value,
      utilizationRate: Number((form.querySelector('#utilizationRate') as HTMLInputElement)?.value || 0),
      usageYears: Number((form.querySelector('#usageYears') as HTMLInputElement)?.value || 0),
      notes: (form.querySelector('#notes') as HTMLTextAreaElement)?.value
    };

    if (isEditDialogOpen && selectedAsset) {
      handleUpdateAsset(formData);
    } else {
      handleAddAsset(formData);
    }
  };

  // 添加删除资产处理函数
  const handleDeleteAsset = (asset: FixedAsset) => {
    setAssetData(prev => prev.filter(a => a.id !== asset.id));
    setFilteredData(prev => prev.filter(a => a.id !== asset.id));
    toast({
      title: "删除成功",
      description: "已成功删除固定资产记录",
    });
    setIsDeleteDialogOpen(false);
    setSelectedAsset(null);
  };

  // 确认删除处理函数
  const handleConfirmDelete = () => {
    if (!selectedAsset) return;
    handleDeleteAsset(selectedAsset);
  };

  // 添加保存筛选条件的函数
  const saveCurrentFilter = () => {
    if (!currentFilterName.trim()) {
      toast({
        title: "无法保存",
        description: "请输入筛选条件名称",
        variant: "destructive",
      });
      return;
    }

    const newFilter = {
      name: currentFilterName,
      filter: {
        categoryFilter,
        statusFilter,
        valueRange,
        dateRange,
        maintenanceDateRange,
        utilizationRange,
        ageRange,
        selectedDepartments,
        selectedResponsibles
      }
    };

    setSavedFilters(prev => [...prev, newFilter]);
    setCurrentFilterName("");

    toast({
      title: "筛选条件已保存",
      description: `已保存筛选条件: ${currentFilterName}`,
    });
  };

  // 应用保存的筛选条件
  const applySavedFilter = (filter: any) => {
    setCategoryFilter(filter.categoryFilter);
    setStatusFilter(filter.statusFilter);
    setValueRange(filter.valueRange);
    setDateRange(filter.dateRange);
    setMaintenanceDateRange(filter.maintenanceDateRange);
    setUtilizationRange(filter.utilizationRange);
    setAgeRange(filter.ageRange);
    setSelectedDepartments(filter.selectedDepartments);
    setSelectedResponsibles(filter.selectedResponsibles);

    // 应用筛选条件
    const filteredResults = applyFilters(assetData, filter.categoryFilter, filter.statusFilter);
    setFilteredData(filteredResults);

    toast({
      title: "已应用筛选条件",
      description: "数据已按保存的筛选条件更新",
    });
  };

  // 重置所有筛选条件
  const resetAllFilters = () => {
    setCategoryFilter('全部');
    setStatusFilter('全部');
    setValueRange([0, 3000000]);
    setDateRange({start: '', end: ''});
    setMaintenanceDateRange({start: '', end: ''});
    setUtilizationRange([0, 100]);
    setAgeRange([0, 15]);
    setSelectedDepartments([]);
    setSelectedResponsibles([]);

    setFilteredData(assetData);

    toast({
      title: "已重置筛选条件",
      description: "所有筛选条件已恢复默认值",
    });
  };

  // 应用所有筛选条件
  const applyAllFilters = () => {
    const filteredResults = applyFilters(assetData, categoryFilter, statusFilter);
    setFilteredData(filteredResults);

    toast({
      title: "筛选已应用",
      description: `已找到 ${filteredResults.length} 条资产记录`,
    });

    setIsAdvancedFilterOpen(false);
  };

  // 添加统计数据计算，放在组件内现有的statistics定义位置
  const statistics = useMemo(() => {
    const totalOriginalValue = filteredData.reduce((sum: number, asset: FixedAsset) => sum + asset.originalValue, 0);
    const totalCurrentValue = filteredData.reduce((sum: number, asset: FixedAsset) => sum + asset.currentValue, 0);
    const avgUtilizationRate = filteredData.reduce((sum: number, asset: FixedAsset) => sum + asset.utilizationRate, 0) / (filteredData.length || 1);
    const maintenanceNeeded = calculateMaintenanceNeeded(filteredData);

    // 计算资产健康指标
    let healthyAssets = 0;
    let warningAssets = 0;
    let criticalAssets = 0;

    filteredData.forEach((asset: FixedAsset) => {
      // 资产年限超过8年，或折旧率超过80%，视为危险状态
      if (asset.usageYears > 8 || (asset.currentValue / asset.originalValue) < 0.2) {
        criticalAssets++;
      }
      // 资产年限在5-8年之间，或折旧率在50%-80%之间，视为警告状态
      else if (asset.usageYears > 5 || (asset.currentValue / asset.originalValue) < 0.5) {
        warningAssets++;
      }
      else {
        healthyAssets++;
      }
    });

    const healthScore = filteredData.length ?
      Math.round((healthyAssets * 100 + warningAssets * 60) / filteredData.length) : 100;

    return {
      totalOriginalValue,
      totalCurrentValue,
      avgUtilizationRate,
      maintenanceNeeded,
      healthyAssets,
      warningAssets,
      criticalAssets,
      healthScore,
      totalDepreciation: totalOriginalValue - totalCurrentValue,
      depreciationRate: totalOriginalValue > 0 ? ((totalOriginalValue - totalCurrentValue) / totalOriginalValue) * 100 : 0
    };
  }, [filteredData, calculateMaintenanceNeeded]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 搜索和操作栏 */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex-1">
            <Input
            placeholder="搜索资产名称、编号或负责人..."
            onChange={(e) => handleSearch(e.target.value)}
            className="max-w-md"
            />
          </div>
        <div className="flex items-center gap-2">
          <Input
            type="file"
            accept=".xlsx,.xls"
            className="hidden"
            id="import-file"
            onChange={handleImport}
          />
          <Button
            variant="outline"
            onClick={() => document.getElementById('import-file')?.click()}
          >
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
          >
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCcw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            刷新
          </Button>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加资产
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">资产总数</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredData.length}</div>
            <p className="text-xs text-muted-foreground">
              总计 {assetData.length} 项资产
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">资产原值</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(statistics.totalOriginalValue)}
            </div>
            <p className="text-xs text-muted-foreground">
              当前净值: {formatCurrency(statistics.totalCurrentValue)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均利用率</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.avgUtilizationRate.toFixed(1)}%
            </div>
            <Progress
              value={statistics.avgUtilizationRate}
              className="h-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待维护资产</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.maintenanceNeeded}</div>
            <p className="text-xs text-muted-foreground">
              近一个月内需要维护
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 基本筛选和高级筛选 */}
      <div className="flex flex-wrap gap-4 items-center justify-between">
        <div className="flex flex-1 flex-wrap gap-4 min-w-[300px]">
        <Select
          value={categoryFilter}
          onValueChange={handleCategoryFilterChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="选择资产类别" />
          </SelectTrigger>
          <SelectContent>
              <SelectItem value="全部">全部类别</SelectItem>
            {categories.map((category) => (
                category !== '全部' && <SelectItem key={category} value={category}>{category}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={statusFilter}
          onValueChange={handleStatusFilterChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="选择资产状态" />
          </SelectTrigger>
          <SelectContent>
              <SelectItem value="全部">全部状态</SelectItem>
            <SelectItem value="使用中">使用中</SelectItem>
            <SelectItem value="闲置">闲置</SelectItem>
            <SelectItem value="维修中">维修中</SelectItem>
            <SelectItem value="报废">报废</SelectItem>
          </SelectContent>
        </Select>

          <Sheet open={isAdvancedFilterOpen} onOpenChange={setIsAdvancedFilterOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                高级筛选
                {(selectedDepartments.length > 0 || selectedResponsibles.length > 0 ||
                  dateRange.start || dateRange.end ||
                  valueRange[0] > 0 || valueRange[1] < 3000000 ||
                  utilizationRange[0] > 0 || utilizationRange[1] < 100) && (
                  <Badge className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                    !
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="sm:max-w-md overflow-y-auto">
              <SheetHeader>
                <SheetTitle>高级筛选</SheetTitle>
                <SheetDescription>
                  根据多个条件筛选资产
                </SheetDescription>
              </SheetHeader>
              <div className="grid gap-4 py-4">
                {/* 价值范围筛选 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">资产价值范围</h3>
                  <div className="flex justify-between text-xs text-muted-foreground mb-2">
                    <span>{formatCurrency(valueRange[0])}</span>
                    <span>{formatCurrency(valueRange[1])}</span>
                  </div>
                  <Slider
                    defaultValue={valueRange}
                    max={3000000}
                    step={10000}
                    onValueChange={(value) => setValueRange(value as [number, number])}
                  />
                </div>

                <Separator />

                {/* 购买日期范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">购买日期范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="start-date" className="text-xs">起始日期</Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={dateRange.start}
                        onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="end-date" className="text-xs">结束日期</Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={dateRange.end}
                        onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 下次维护日期范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">下次维护日期范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="maint-start-date" className="text-xs">起始日期</Label>
                      <Input
                        id="maint-start-date"
                        type="date"
                        value={maintenanceDateRange.start}
                        onChange={(e) => setMaintenanceDateRange(prev => ({ ...prev, start: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="maint-end-date" className="text-xs">结束日期</Label>
                      <Input
                        id="maint-end-date"
                        type="date"
                        value={maintenanceDateRange.end}
                        onChange={(e) => setMaintenanceDateRange(prev => ({ ...prev, end: e.target.value }))}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 使用率范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">使用率范围</h3>
                  <div className="flex justify-between text-xs text-muted-foreground mb-2">
                    <span>{utilizationRange[0]}%</span>
                    <span>{utilizationRange[1]}%</span>
                  </div>
                  <Slider
                    defaultValue={utilizationRange}
                    max={100}
                    step={5}
                    onValueChange={(value) => setUtilizationRange(value as [number, number])}
                  />
                </div>

                <Separator />

                {/* 使用年限范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">使用年限范围</h3>
                  <div className="flex justify-between text-xs text-muted-foreground mb-2">
                    <span>{ageRange[0]}年</span>
                    <span>{ageRange[1]}年</span>
                  </div>
                  <Slider
                    defaultValue={ageRange}
                    max={15}
                    step={0.5}
                    onValueChange={(value) => setAgeRange(value as [number, number])}
                  />
                </div>

                <Separator />

                {/* 部门选择 */}
                <div className="space-y-3">
                  <h3 className="text-sm font-medium">部门</h3>
                  <div className="h-[150px] overflow-y-auto space-y-2 border rounded-md p-2">
                    {departments.map((dept) => (
                      <div key={dept} className="flex items-center space-x-2">
                        <Checkbox
                          id={`dept-${dept}`}
                          checked={selectedDepartments.includes(dept)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedDepartments(prev => [...prev, dept]);
                            } else {
                              setSelectedDepartments(prev => prev.filter(d => d !== dept));
                            }
                          }}
                        />
                        <Label htmlFor={`dept-${dept}`} className="text-sm">{dept}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* 负责人选择 */}
                <div className="space-y-3">
                  <h3 className="text-sm font-medium">负责人</h3>
                  <div className="h-[150px] overflow-y-auto space-y-2 border rounded-md p-2">
                    {responsibles.map((resp) => (
                      <div key={resp} className="flex items-center space-x-2">
                        <Checkbox
                          id={`resp-${resp}`}
                          checked={selectedResponsibles.includes(resp)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedResponsibles(prev => [...prev, resp]);
                            } else {
                              setSelectedResponsibles(prev => prev.filter(r => r !== resp));
                            }
                          }}
                        />
                        <Label htmlFor={`resp-${resp}`} className="text-sm">{resp}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* 保存筛选条件 */}
                <div className="space-y-3">
                  <h3 className="text-sm font-medium">保存筛选条件</h3>
                  <div className="flex gap-2">
                    <Input
                      placeholder="输入筛选条件名称"
                      value={currentFilterName}
                      onChange={(e) => setCurrentFilterName(e.target.value)}
                    />
                    <Button variant="outline" size="sm" onClick={saveCurrentFilter}>
                      <Save className="h-4 w-4 mr-1" />
                      保存
                    </Button>
                  </div>
                </div>

                {/* 已保存的筛选条件 */}
                {savedFilters.length > 0 && (
                  <>
                    <Separator />
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium">已保存的筛选条件</h3>
                      <div className="space-y-2">
                        {savedFilters.map((filter, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm">{filter.name}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => applySavedFilter(filter.filter)}
                            >
                              应用
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
              <SheetFooter>
                <Button variant="outline" onClick={resetAllFilters}>重置所有筛选</Button>
                <Button onClick={applyAllFilters}>应用筛选</Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
        </div>

        <div className="flex items-center">
          <Badge variant="outline" className="rounded-md px-3 py-1">
            已找到 {filteredData.length} 项资产
          </Badge>
        </div>
      </div>

      {/* 表格和图表视图 */}
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">
            <FileText className="mr-2 h-4 w-4" />
            表格视图
          </TabsTrigger>
          <TabsTrigger value="distribution">
            <PieChart className="mr-2 h-4 w-4" />
            分布分析
          </TabsTrigger>
          <TabsTrigger value="trend">
            <LineChart className="mr-2 h-4 w-4" />
            趋势分析
          </TabsTrigger>
          <TabsTrigger value="utilization">
            <Activity className="mr-2 h-4 w-4" />
            使用分析
          </TabsTrigger>
          <TabsTrigger value="depreciation">
            <Wallet className="mr-2 h-4 w-4" />
            折旧分析
          </TabsTrigger>
          <TabsTrigger value="health">
            <AlertCircle className="mr-2 h-4 w-4" />
            健康状况
          </TabsTrigger>
        </TabsList>

        {/* 表格视图 */}
        <TabsContent value="table">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>资产名称</TableHead>
                    <TableHead>资产编号</TableHead>
                    <TableHead>资产类别</TableHead>
                    <TableHead>购置日期</TableHead>
                    <TableHead>原值</TableHead>
                    <TableHead>当前净值</TableHead>
                    <TableHead>存放地点</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Database className="h-10 w-10 mb-2" />
                          <p>暂无资产记录</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredData.map((asset) => (
                      <TableRow key={asset.id}>
                        <TableCell>{asset.name}</TableCell>
                        <TableCell>{asset.code}</TableCell>
                        <TableCell>{asset.category}</TableCell>
                        <TableCell>{asset.purchaseDate}</TableCell>
                        <TableCell>{formatCurrency(asset.originalValue)}</TableCell>
                        <TableCell>{formatCurrency(asset.currentValue)}</TableCell>
                        <TableCell>{asset.location}</TableCell>
                        <TableCell>{getStatusBadge(asset.status)}</TableCell>
                      <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedAsset(asset);
                                setIsViewDetailOpen(true);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedAsset(asset);
                                setFormData(asset);
                                setIsEditDialogOpen(true);
                              }}
                            >
                          <Edit className="h-4 w-4" />
                        </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedAsset(asset);
                                setIsDeleteDialogOpen(true);
                              }}
                            >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                          </div>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 分布分析 */}
        <TabsContent value="distribution">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>资产分布分析</CardTitle>
                <CardDescription>按资产类别的分布情况</CardDescription>
            </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateAssetDistributionChart(calculateCategoryStats(assetData))}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>资产状态分布</CardTitle>
                <CardDescription>各状态资产数量统计</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateAssetStatusChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>
              </div>
        </TabsContent>

        {/* 趋势分析 */}
        <TabsContent value="trend">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>资产价值趋势</CardTitle>
                <CardDescription>原值与当前净值对比</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateAssetValueTrendChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>使用年限分布</CardTitle>
                <CardDescription>资产使用年限统计</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateAssetAgeChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 使用分析 */}
        <TabsContent value="utilization">
          <Card>
            <CardHeader>
              <CardTitle>资产利用率分析</CardTitle>
              <CardDescription>各类别资产平均利用率</CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              <ReactECharts
                option={generateUtilizationChart(calculateCategoryStats(assetData))}
                style={{ height: '100%', width: '100%' }}
                theme="assets-theme"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 折旧分析 */}
        <TabsContent value="depreciation">
          <div className="grid grid-cols-1 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>资产折旧分析</CardTitle>
                <CardDescription>资产历年折旧趋势及折旧率分析</CardDescription>
              </CardHeader>
              <CardContent className="h-96">
                <ReactECharts
                  option={generateDepreciationTrendChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>部门资产分布分析</CardTitle>
                <CardDescription>各部门的资产分布及使用情况</CardDescription>
              </CardHeader>
              <CardContent className="h-96">
                <ReactECharts
                  option={generateDepartmentAssetsChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 健康状况 */}
        <TabsContent value="health">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>资产健康状况</CardTitle>
                <CardDescription>资产整体健康状况与分布</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ReactECharts
                  option={generateAssetHealthChart(assetData)}
                  style={{ height: '100%', width: '100%' }}
                  theme="assets-theme"
                />
              </CardContent>
              <CardFooter className="border-t pt-4">
                <div className="w-full">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">资产健康评分</span>
                    <span className="text-sm font-bold">{statistics.healthScore}/100</span>
                  </div>
                  <Progress
                    value={statistics.healthScore}
                    className="h-2"
                    indicatorClassName={`${
                      statistics.healthScore >= 80
                        ? "bg-green-500"
                        : statistics.healthScore >= 60
                        ? "bg-yellow-500"
                        : "bg-red-500"
                    }`}
                  />
                  <div className="flex justify-between mt-4 text-sm">
                    <span>
                      <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                      健康: {statistics.healthyAssets}项
                    </span>
                    <span>
                      <span className="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-1"></span>
                      警告: {statistics.warningAssets}项
                    </span>
                    <span>
                      <span className="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                      危险: {statistics.criticalAssets}项
                    </span>
                  </div>
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>资产潜在风险</CardTitle>
                <CardDescription>需要关注的资产提醒</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ScrollArea className="h-full">
                  <div className="space-y-4">
                    {/* 需要维护的资产 - 替换checkMaintenanceDate函数调用 */}
                    {assetData.filter(asset => {
                      if (!asset.nextMaintenanceDate) return false;
                      const maintenanceDate = new Date(asset.nextMaintenanceDate);
                      const today = new Date();
                      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
                      return !isNaN(maintenanceDate.getTime()) && maintenanceDate <= nextMonth;
                    }).length > 0 ? (
                      <div className="rounded-lg border p-3 bg-yellow-50">
                        <h3 className="font-medium flex items-center text-amber-800">
                          <Wrench className="h-4 w-4 mr-2 text-amber-600" />
                          需要维护的资产
                        </h3>
                        <div className="mt-2 space-y-2">
                          {assetData
                            .filter(asset => {
                              if (!asset.nextMaintenanceDate) return false;
                              const maintenanceDate = new Date(asset.nextMaintenanceDate);
                              const today = new Date();
                              const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
                              return !isNaN(maintenanceDate.getTime()) && maintenanceDate <= nextMonth;
                            })
                            .slice(0, 5)
                            .map(asset => (
                              <div key={asset.id} className="flex justify-between items-center text-sm">
                                <span>{asset.name}</span>
                                <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                                  {asset.nextMaintenanceDate}
                                </Badge>
                              </div>
                            ))}
                          {assetData.filter(asset => {
                            if (!asset.nextMaintenanceDate) return false;
                            const maintenanceDate = new Date(asset.nextMaintenanceDate);
                            const today = new Date();
                            const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
                            return !isNaN(maintenanceDate.getTime()) && maintenanceDate <= nextMonth;
                          }).length > 5 && (
                            <div className="text-sm text-muted-foreground text-center">
                              还有 {assetData.filter(asset => {
                                if (!asset.nextMaintenanceDate) return false;
                                const maintenanceDate = new Date(asset.nextMaintenanceDate);
                                const today = new Date();
                                const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
                                return !isNaN(maintenanceDate.getTime()) && maintenanceDate <= nextMonth;
                              }).length - 5} 项资产需要维护...
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="rounded-lg border p-3 bg-green-50">
                        <h3 className="font-medium flex items-center text-green-800">
                          <CheckCircle2 className="h-4 w-4 mr-2 text-green-600" />
                          没有需要维护的资产
                        </h3>
                      </div>
                    )}

                    {/* 低利用率资产 */}
                    {assetData.filter(asset => asset.utilizationRate < 20 && asset.status !== '报废').length > 0 ? (
                      <div className="rounded-lg border p-3 bg-blue-50">
                        <h3 className="font-medium flex items-center text-blue-800">
                          <AlertCircle className="h-4 w-4 mr-2 text-blue-600" />
                          低利用率资产 ({assetData.filter(asset => asset.utilizationRate < 20 && asset.status !== '报废').length})
                        </h3>
                        <div className="mt-2 space-y-2">
                          {assetData
                            .filter(asset => asset.utilizationRate < 20 && asset.status !== '报废')
                            .slice(0, 5)
                            .map(asset => (
                              <div key={asset.id} className="flex justify-between items-center text-sm">
                                <span>{asset.name}</span>
                                <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                                  {asset.utilizationRate}%
                                </Badge>
                              </div>
                            ))}
                          {assetData.filter(asset => asset.utilizationRate < 20 && asset.status !== '报废').length > 5 && (
                            <div className="text-sm text-muted-foreground text-center">
                              还有 {assetData.filter(asset => asset.utilizationRate < 20 && asset.status !== '报废').length - 5} 项低利用率资产...
                            </div>
                          )}
                        </div>
                      </div>
                    ) : null}

                    {/* 高度折旧资产 */}
                    {assetData.filter(asset => (asset.currentValue / asset.originalValue) < 0.2 && asset.status !== '报废').length > 0 ? (
                      <div className="rounded-lg border p-3 bg-red-50">
                        <h3 className="font-medium flex items-center text-red-800">
                          <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
                          高度折旧资产
                        </h3>
                        <div className="mt-2 space-y-2">
                          {assetData
                            .filter(asset => (asset.currentValue / asset.originalValue) < 0.2 && asset.status !== '报废')
                            .slice(0, 5)
                            .map(asset => (
                              <div key={asset.id} className="flex justify-between items-center text-sm">
                                <span>{asset.name}</span>
                                <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-200">
                                  {Math.round((1 - asset.currentValue / asset.originalValue) * 100)}%折旧
                                </Badge>
                              </div>
                            ))}
                          {assetData.filter(asset => (asset.currentValue / asset.originalValue) < 0.2 && asset.status !== '报废').length > 5 && (
                            <div className="text-sm text-muted-foreground text-center">
                              还有 {assetData.filter(asset => (asset.currentValue / asset.originalValue) < 0.2 && asset.status !== '报废').length - 5} 项高度折旧资产...
                            </div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>资产详细信息</DialogTitle>
            <DialogDescription>
              {selectedAsset && `${selectedAsset.name} (${selectedAsset.code})`}
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[400px] pr-4">
            {selectedAsset && (
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">资产原值</h3>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatCurrency(selectedAsset.originalValue)}
                    </p>
    </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">当前净值</h3>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(selectedAsset.currentValue)}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">折旧率</h3>
                    <p className="text-lg">{selectedAsset.depreciationRate}%/年</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">使用年限</h3>
                    <p className="text-lg">{selectedAsset.usageYears.toFixed(1)}年</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">资产类别</h3>
                    <p className="text-lg">{selectedAsset.category}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">存放地点</h3>
                    <p className="text-lg">{selectedAsset.location}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">生产厂家</h3>
                    <p className="text-lg">{selectedAsset.manufacturer || "-"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">规格型号</h3>
                    <p className="text-lg">{selectedAsset.model || "-"}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">负责人</h3>
                    <p className="text-lg">{selectedAsset.responsible || "-"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">使用部门</h3>
                    <p className="text-lg">{selectedAsset.department || "-"}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">上次维护</h3>
                    <p className="text-lg">{selectedAsset.lastMaintenanceDate || "-"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">下次维护</h3>
                    <p className="text-lg">{selectedAsset.nextMaintenanceDate || "-"}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">状态</h3>
                  <div>{getStatusBadge(selectedAsset.status)}</div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">资产利用率</h3>
                  <Progress value={selectedAsset.utilizationRate} className="mt-2" />
                  <p className="text-sm text-muted-foreground mt-1">{selectedAsset.utilizationRate}%</p>
                </div>

                {selectedAsset.notes && (
                  <div>
                    <h3 className="text-sm font-medium mb-1">备注</h3>
                    <p className="text-sm text-muted-foreground">{selectedAsset.notes}</p>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDetailOpen(false)}>
              关闭
            </Button>
            <Button onClick={() => {
              setIsViewDetailOpen(false);
              if (selectedAsset) {
                setFormData(selectedAsset);
                setIsEditDialogOpen(true);
              }
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑资产信息</DialogTitle>
            <DialogDescription>
              修改资产的详细信息，所有带 * 的字段为必填项
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[500px] pr-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">资产名称 *</Label>
                  <Input
                    id="name"
                    value={formData?.name || ""}
                    onChange={(e) => handleFormChange("name", e.target.value)}
                    placeholder="输入资产名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">资产编号 *</Label>
                  <Input
                    id="code"
                    value={formData?.code || ""}
                    onChange={(e) => handleFormChange("code", e.target.value)}
                    placeholder="输入资产编号"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">资产类别 *</Label>
                  <Select
                    value={formData?.category || ""}
                    onValueChange={(value) => handleFormChange("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择资产类别" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">资产状态 *</Label>
                  <Select
                    value={formData?.status || ""}
                    onValueChange={(value) => handleFormChange("status", value as AssetStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择资产状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="使用中">使用中</SelectItem>
                      <SelectItem value="闲置">闲置</SelectItem>
                      <SelectItem value="维修中">维修中</SelectItem>
                      <SelectItem value="报废">报废</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="originalValue">原值（元）*</Label>
                  <Input
                    id="originalValue"
                    type="number"
                    value={formData?.originalValue || ""}
                    onChange={(e) => handleFormChange("originalValue", Number(e.target.value))}
                    placeholder="输入资产原值"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currentValue">当前净值（元）*</Label>
                  <Input
                    id="currentValue"
                    type="number"
                    value={formData?.currentValue || ""}
                    onChange={(e) => handleFormChange("currentValue", Number(e.target.value))}
                    placeholder="输入当前净值"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="depreciationRate">折旧率（%/年）*</Label>
                  <Input
                    id="depreciationRate"
                    type="number"
                    value={formData?.depreciationRate || ""}
                    onChange={(e) => handleFormChange("depreciationRate", Number(e.target.value))}
                    placeholder="输入折旧率"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="utilizationRate">使用率（%）*</Label>
                  <Input
                    id="utilizationRate"
                    type="number"
                    value={formData?.utilizationRate || ""}
                    onChange={(e) => handleFormChange("utilizationRate", Number(e.target.value))}
                    placeholder="输入使用率"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchaseDate">购置日期 *</Label>
                  <Input
                    id="purchaseDate"
                    type="date"
                    value={formData?.purchaseDate || ""}
                    onChange={(e) => handleFormChange("purchaseDate", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">存放地点 *</Label>
                  <Input
                    id="location"
                    value={formData?.location || ""}
                    onChange={(e) => handleFormChange("location", e.target.value)}
                    placeholder="输入存放地点"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manufacturer">生产厂家</Label>
                  <Input
                    id="manufacturer"
                    value={formData?.manufacturer || ""}
                    onChange={(e) => handleFormChange("manufacturer", e.target.value)}
                    placeholder="输入生产厂家"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="model">规格型号</Label>
                  <Input
                    id="model"
                    value={formData?.model || ""}
                    onChange={(e) => handleFormChange("model", e.target.value)}
                    placeholder="输入规格型号"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="responsible">负责人</Label>
                  <Input
                    id="responsible"
                    value={formData?.responsible || ""}
                    onChange={(e) => handleFormChange("responsible", e.target.value)}
                    placeholder="输入负责人"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">使用部门</Label>
                  <Input
                    id="department"
                    value={formData?.department || ""}
                    onChange={(e) => handleFormChange("department", e.target.value)}
                    placeholder="输入使用部门"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lastMaintenanceDate">上次维护日期</Label>
                  <Input
                    id="lastMaintenanceDate"
                    type="date"
                    value={formData?.lastMaintenanceDate || ""}
                    onChange={(e) => handleFormChange("lastMaintenanceDate", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nextMaintenanceDate">下次维护日期</Label>
                  <Input
                    id="nextMaintenanceDate"
                    type="date"
                    value={formData?.nextMaintenanceDate || ""}
                    onChange={(e) => handleFormChange("nextMaintenanceDate", e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">备注</Label>
                <Textarea
                  id="notes"
                  value={formData?.notes || ""}
                  onChange={(e) => handleFormChange("notes", e.target.value)}
                  placeholder="输入备注信息"
                />
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              if (!formData) return;
              handleUpdateAsset(formData as FixedAsset);
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这条资产记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加资产对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加新资产</DialogTitle>
            <DialogDescription>
              请填写新资产的详细信息
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();
            const formElement = e.currentTarget;
            const formData: Partial<FixedAsset> = {
              name: (formElement.querySelector('#asset-name') as HTMLInputElement).value,
              code: (formElement.querySelector('#asset-code') as HTMLInputElement).value,
              category: (formElement.querySelector('#asset-category') as HTMLSelectElement).value,
              purchaseDate: (formElement.querySelector('#purchase-date') as HTMLInputElement).value,
              originalValue: parseFloat((formElement.querySelector('#original-value') as HTMLInputElement).value),
              currentValue: parseFloat((formElement.querySelector('#current-value') as HTMLInputElement).value),
              depreciationRate: parseFloat((formElement.querySelector('#depreciation-rate') as HTMLInputElement).value),
              location: (formElement.querySelector('#location') as HTMLInputElement).value,
              status: (formElement.querySelector('#status') as HTMLSelectElement).value as AssetStatus,
              manufacturer: (formElement.querySelector('#manufacturer') as HTMLInputElement).value,
              model: (formElement.querySelector('#model') as HTMLInputElement).value,
              responsible: (formElement.querySelector('#responsible') as HTMLInputElement).value,
              department: (formElement.querySelector('#department') as HTMLInputElement).value,
              lastMaintenanceDate: (formElement.querySelector('#last-maintenance-date') as HTMLInputElement).value,
              nextMaintenanceDate: (formElement.querySelector('#next-maintenance-date') as HTMLInputElement).value,
              utilizationRate: parseFloat((formElement.querySelector('#utilization-rate') as HTMLInputElement).value),
              usageYears: parseFloat((formElement.querySelector('#usage-years') as HTMLInputElement).value),
              notes: (formElement.querySelector('#notes') as HTMLTextAreaElement).value,
            };
            handleAddAsset(formData);
          }}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="asset-name">资产名称 *</Label>
                  <Input id="asset-name" required />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="asset-code">资产编号 *</Label>
                  <Input id="asset-code" required />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="asset-category">资产类别 *</Label>
                  <Select defaultValue="办公设备">
                    <SelectTrigger id="asset-category">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="办公设备">办公设备</SelectItem>
                      <SelectItem value="电子设备">电子设备</SelectItem>
                      <SelectItem value="机械设备">机械设备</SelectItem>
                      <SelectItem value="运输设备">运输设备</SelectItem>
                      <SelectItem value="其他设备">其他设备</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="purchase-date">购买日期 *</Label>
                  <Input type="date" id="purchase-date" required />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="original-value">原值 *</Label>
                  <Input type="number" id="original-value" required min="0" step="0.01" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="current-value">当前价值 *</Label>
                  <Input type="number" id="current-value" required min="0" step="0.01" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="depreciation-rate">折旧率 *</Label>
                  <Input type="number" id="depreciation-rate" required min="0" max="1" step="0.01" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="location">位置 *</Label>
                  <Input id="location" required />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="status">状态 *</Label>
                  <Select defaultValue="使用中">
                    <SelectTrigger id="status">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="使用中">使用中</SelectItem>
                      <SelectItem value="闲置中">闲置中</SelectItem>
                      <SelectItem value="维修中">维修中</SelectItem>
                      <SelectItem value="已报废">已报废</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="manufacturer">制造商</Label>
                  <Input id="manufacturer" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="model">型号</Label>
                  <Input id="model" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="responsible">负责人</Label>
                  <Input id="responsible" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="department">部门</Label>
                  <Input id="department" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="last-maintenance-date">上次维护日期</Label>
                  <Input
                    id="last-maintenance-date"
                    type="date"
                    value={formData?.lastMaintenanceDate || ""}
                    onChange={(e) => handleFormChange("lastMaintenanceDate", e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="next-maintenance-date">下次维护日期</Label>
                  <Input
                    id="next-maintenance-date"
                    type="date"
                    value={formData?.nextMaintenanceDate || ""}
                    onChange={(e) => handleFormChange("nextMaintenanceDate", e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="utilization-rate">使用率 *</Label>
                  <Input type="number" id="utilization-rate" required min="0" max="1" step="0.01" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="usage-years">使用年限 *</Label>
                  <Input type="number" id="usage-years" required min="0" step="0.1" />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="notes">备注</Label>
                <Textarea id="notes" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit">添加</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

