"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  <PERSON>ertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Calendar,
  MapPin,
  User,
  ArrowUpDown,
  Eye,
  Upload,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { message, Modal, Upload as AntUpload } from 'antd'
import type { UploadProps } from 'antd'
import * as XLSX from 'xlsx-js-style'
import dayjs from 'dayjs'

interface SafetyHazard {
  id: string
  title: string
  level: string
  area: string
  type: string
  discoveryDate: string
  status: string
  deadline: string
  responsible: string
  progress: number
  description?: string
  measures?: string
  attachments?: string[]
  lastModified?: string
  modifiedBy?: string
}

export function SafetyHazardManagement() {
  const [isAddHazardOpen, setIsAddHazardOpen] = useState(false)
  const [isViewModalVisible, setIsViewModalVisible] = useState(false)
  const [selectedHazard, setSelectedHazard] = useState<SafetyHazard | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedLevels, setSelectedLevels] = useState<string[]>([])
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    level: "高",
    area: "",
    type: "设备隐患",
    discoveryDate: "",
    status: "待整改",
    deadline: "",
    responsible: "",
    progress: 0,
    description: "",
    measures: ""
  })

  const [hazards, setHazards] = useState<SafetyHazard[]>([
    {
      id: "1",
      title: "矿区A3设备老化",
      level: "高",
      area: "矿区A3",
      type: "设备隐患",
      discoveryDate: "2025-03-15",
      status: "待整改",
      deadline: "2025-04-15",
      responsible: "张三",
      progress: 30,
      description: "主要设备已运行超过设计寿命，存在安全隐患",
      measures: "1. 立即进行全面检修\n2. 更换老化部件\n3. 制定设备更新计划",
    },
    {
      id: "2",
      title: "爆破区安全距离不足",
      level: "高",
      area: "爆破区",
      type: "作业隐患",
      discoveryDate: "2025-03-10",
      status: "整改中",
      deadline: "2025-04-10",
      responsible: "李四",
      progress: 60,
    },
    {
      id: "3",
      title: "矿区B2排水系统故障",
      level: "中",
      area: "矿区B2",
      type: "设施隐患",
      discoveryDate: "2025-03-05",
      status: "已整改",
      deadline: "2025-03-25",
      responsible: "王五",
      progress: 100,
    },
    {
      id: "4",
      title: "员工安全培训不足",
      level: "中",
      area: "全公司",
      type: "管理隐患",
      discoveryDate: "2025-02-28",
      status: "整改中",
      deadline: "2025-03-28",
      responsible: "赵六",
      progress: 75,
    },
    {
      id: "5",
      title: "消防通道堵塞",
      level: "高",
      area: "办公楼",
      type: "消防隐患",
      discoveryDate: "2025-03-18",
      status: "待整改",
      deadline: "2025-04-10",
      responsible: "张三",
      progress: 0,
    },
  ])

  // 统计数据
  const statistics = {
    total: hazards.length,
    pending: hazards.filter(h => h.status === "待整改").length,
    inProgress: hazards.filter(h => h.status === "整改中").length,
    completed: hazards.filter(h => h.status === "已整改").length,
    byLevel: {
      high: hazards.filter(h => h.level === "高").length,
      medium: hazards.filter(h => h.level === "中").length,
      low: hazards.filter(h => h.level === "低").length,
    },
    nearDeadline: hazards.filter(h => {
      const deadline = dayjs(h.deadline);
      const today = dayjs();
      return h.status !== "已整改" && deadline.diff(today, 'day') <= 7;
    }).length
  }

  // 处理导入
  const handleImport = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        const newHazards = jsonData.map((item: any) => ({
          id: `hazard${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: item['隐患标题'] || '',
          level: item['隐患级别'] || '高',
          area: item['所在区域'] || '',
          type: item['隐患类型'] || '设备隐患',
          discoveryDate: item['发现日期'] || dayjs().format('YYYY-MM-DD'),
          status: item['状态'] || '待整改',
          deadline: item['整改期限'] || '',
          responsible: item['责任人'] || '',
          progress: item['整改进度'] || 0,
          description: item['隐患描述'] || '',
          measures: item['整改措施'] || '',
        }))

        setHazards(prev => [...prev, ...newHazards])
        message.success(`成功导入 ${newHazards.length} 条记录`)
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败，请检查文件格式')
      }
    }
    reader.readAsArrayBuffer(file)
    return false
  }

  // 处理导出
  const handleExport = () => {
    try {
      const exportData = hazards.map(hazard => ({
        '隐患标题': hazard.title,
        '隐患级别': hazard.level,
        '所在区域': hazard.area,
        '隐患类型': hazard.type,
        '发现日期': hazard.discoveryDate,
        '状态': hazard.status,
        '整改期限': hazard.deadline,
        '责任人': hazard.responsible,
        '整改进度': hazard.progress,
        '隐患描述': hazard.description || '',
        '整改措施': hazard.measures || '',
        '最后修改时间': hazard.lastModified || '',
        '修改人': hazard.modifiedBy || '',
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 隐患标题
        { wch: 10 }, // 隐患级别
        { wch: 15 }, // 所在区域
        { wch: 15 }, // 隐患类型
        { wch: 15 }, // 发现日期
        { wch: 10 }, // 状态
        { wch: 15 }, // 整改期限
        { wch: 10 }, // 责任人
        { wch: 10 }, // 整改进度
        { wch: 40 }, // 隐患描述
        { wch: 40 }, // 整改措施
        { wch: 20 }, // 最后修改时间
        { wch: 15 }, // 修改人
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '安全隐患记录')
      XLSX.writeFile(wb, `安全隐患记录_${dayjs().format('YYYY-MM-DD')}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 上传配置
  const uploadProps: UploadProps = {
    accept: '.xlsx,.xls',
    showUploadList: false,
    beforeUpload: handleImport
  }

  // 处理表单变化
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理表单提交
  const handleSubmit = () => {
    const newHazard: SafetyHazard = {
      id: `hazard${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...formData,
      lastModified: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      modifiedBy: "当前用户"
    }

    setHazards(prev => [...prev, newHazard])
    setFormData({
      title: "",
      level: "高",
      area: "",
      type: "设备隐患",
      discoveryDate: "",
      status: "待整改",
      deadline: "",
      responsible: "",
      progress: 0,
      description: "",
      measures: ""
    })
    setIsAddHazardOpen(false)
    message.success("隐患已创建")
  }

  // 处理删除
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条隐患记录吗？",
      onOk: () => {
        setHazards(hazards.filter(h => h.id !== id))
        message.success("记录已删除")
      }
    })
  }

  // 处理状态更新
  const handleStatusChange = (id: string, status: string) => {
    setHazards(hazards.map(h =>
      h.id === id
        ? { ...h, status, lastModified: dayjs().format('YYYY-MM-DD HH:mm:ss'), modifiedBy: "当前用户" }
        : h
    ))
    message.success("状态已更新")
  }

  // 筛选数据
  const filteredHazards = hazards.filter(hazard => {
    const matchesSearch = hazard.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         hazard.responsible.toLowerCase().includes(searchText.toLowerCase()) ||
                         hazard.area.toLowerCase().includes(searchText.toLowerCase())
    const matchesLevel = selectedLevels.length === 0 || selectedLevels.includes(hazard.level)
    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(hazard.type)
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(hazard.status)
    return matchesSearch && matchesLevel && matchesType && matchesStatus
  })

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">安全隐患管理</h2>
        <div className="flex items-center gap-2">
          <AntUpload {...uploadProps}>
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </AntUpload>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.pending}</h3>
            <p className="text-sm text-muted-foreground">待整改隐患</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.inProgress}</h3>
            <p className="text-sm text-muted-foreground">整改中隐患</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.completed}</h3>
            <p className="text-sm text-muted-foreground">已整改隐患</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.nearDeadline}</h3>
            <p className="text-sm text-muted-foreground">即将到期</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>安全隐患列表</CardTitle>
              <CardDescription>管理和跟踪安全隐患整改情况</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索隐患..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select
                  value={selectedLevels.length === 0 ? "all" : selectedLevels[0]}
                  onValueChange={(value) => setSelectedLevels(value === "all" ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="隐患级别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有级别</SelectItem>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={selectedTypes.length === 0 ? "all" : selectedTypes[0]}
                  onValueChange={(value) => setSelectedTypes(value === "all" ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="隐患类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="设备隐患">设备隐患</SelectItem>
                    <SelectItem value="作业隐患">作业隐患</SelectItem>
                    <SelectItem value="设施隐患">设施隐患</SelectItem>
                    <SelectItem value="管理隐患">管理隐患</SelectItem>
                    <SelectItem value="消防隐患">消防隐患</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Dialog open={isAddHazardOpen} onOpenChange={setIsAddHazardOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      添加隐患
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>添加安全隐患</DialogTitle>
                      <DialogDescription>记录新发现的安全隐患</DialogDescription>
                    </DialogHeader>
                    <Tabs defaultValue="basic" className="mt-4">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="basic">基本信息</TabsTrigger>
                        <TabsTrigger value="details">详细描述</TabsTrigger>
                      </TabsList>
                      <TabsContent value="basic" className="space-y-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="hazard-title">隐患标题</Label>
                            <Input
                              id="hazard-title"
                              placeholder="请输入隐患标题"
                              value={formData.title}
                              onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-level">隐患级别</Label>
                            <Select value={formData.level} onValueChange={(value) => handleFormChange('level', value)}>
                              <SelectTrigger id="hazard-level">
                                <SelectValue placeholder="选择隐患级别" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="高">高</SelectItem>
                                <SelectItem value="中">中</SelectItem>
                                <SelectItem value="低">低</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-area">所在区域</Label>
                            <Input
                              id="hazard-area"
                              placeholder="请输入所在区域"
                              value={formData.area}
                              onChange={(e) => handleFormChange('area', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-type">隐患类型</Label>
                            <Select value={formData.type} onValueChange={(value) => handleFormChange('type', value)}>
                              <SelectTrigger id="hazard-type">
                                <SelectValue placeholder="选择隐患类型" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="设备隐患">设备隐患</SelectItem>
                                <SelectItem value="作业隐患">作业隐患</SelectItem>
                                <SelectItem value="设施隐患">设施隐患</SelectItem>
                                <SelectItem value="管理隐患">管理隐患</SelectItem>
                                <SelectItem value="消防隐患">消防隐患</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-discovery-date">发现日期</Label>
                            <Input
                              id="hazard-discovery-date"
                              type="date"
                              value={formData.discoveryDate}
                              onChange={(e) => handleFormChange('discoveryDate', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-deadline">整改期限</Label>
                            <Input
                              id="hazard-deadline"
                              type="date"
                              value={formData.deadline}
                              onChange={(e) => handleFormChange('deadline', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-responsible">责任人</Label>
                            <Input
                              id="hazard-responsible"
                              placeholder="请输入责任人"
                              value={formData.responsible}
                              onChange={(e) => handleFormChange('responsible', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-status">状态</Label>
                            <Select value={formData.status} onValueChange={(value) => handleFormChange('status', value)}>
                              <SelectTrigger id="hazard-status">
                                <SelectValue placeholder="选择状态" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="待整改">待整改</SelectItem>
                                <SelectItem value="整改中">整改中</SelectItem>
                                <SelectItem value="已整改">已整改</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </TabsContent>
                      <TabsContent value="details" className="space-y-4 mt-4">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="hazard-description">隐患描述</Label>
                            <Textarea
                              id="hazard-description"
                              placeholder="请详细描述安全隐患情况"
                              rows={4}
                              value={formData.description}
                              onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-measures">整改措施</Label>
                            <Textarea
                              id="hazard-measures"
                              placeholder="请输入整改措施"
                              rows={4}
                              value={formData.measures}
                              onChange={(e) => handleFormChange('measures', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="hazard-progress">整改进度</Label>
                            <div className="flex items-center gap-4">
                              <Input
                                id="hazard-progress"
                                type="number"
                                min="0"
                                max="100"
                                value={formData.progress}
                                onChange={(e) => handleFormChange('progress', parseInt(e.target.value))}
                                className="w-20"
                              />
                              <span>%</span>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                    <DialogFooter className="mt-6">
                      <Button variant="outline" onClick={() => setIsAddHazardOpen(false)}>
                        取消
                      </Button>
                      <Button onClick={handleSubmit}>保存</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>隐患标题</TableHead>
                    <TableHead>级别</TableHead>
                    <TableHead>所在区域</TableHead>
                    <TableHead>隐患类型</TableHead>
                    <TableHead>发现日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>整改期限</TableHead>
                    <TableHead>责任人</TableHead>
                    <TableHead>整改进度</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHazards.map((hazard) => (
                    <TableRow key={hazard.id}>
                      <TableCell className="font-medium">{hazard.title}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            hazard.level === "高" ? "destructive" : hazard.level === "中" ? "secondary" : "outline"
                          }
                        >
                          {hazard.level}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{hazard.area}</span>
                        </div>
                      </TableCell>
                      <TableCell>{hazard.type}</TableCell>
                      <TableCell>{hazard.discoveryDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            hazard.status === "待整改"
                              ? "outline"
                              : hazard.status === "整改中"
                                ? "secondary"
                                : "default"
                          }
                        >
                          {hazard.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{hazard.deadline}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{hazard.responsible}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={hazard.progress} className="h-2 w-24" />
                          <span className="text-xs">{hazard.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedHazard(hazard)
                              setIsViewModalVisible(true)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleStatusChange(hazard.id, "整改中")}>
                                <Clock className="h-4 w-4 mr-2" />
                                设为整改中
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(hazard.id, "已整改")}>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                设为已整改
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDelete(hazard.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredHazards.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 查看详情模态框 */}
      <Modal
        title="隐患详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false)
          setSelectedHazard(null)
        }}
        footer={null}
        width={800}
      >
        {selectedHazard && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-muted-foreground">隐患标题</Label>
                <p className="font-medium">{selectedHazard.title}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">隐患级别</Label>
                <p>
                  <Badge variant={
                    selectedHazard.level === "高" ? "destructive" :
                    selectedHazard.level === "中" ? "secondary" : "outline"
                  }>
                    {selectedHazard.level}
                  </Badge>
                </p>
              </div>
              <div>
                <Label className="text-muted-foreground">所在区域</Label>
                <p className="font-medium">{selectedHazard.area}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">隐患类型</Label>
                <p className="font-medium">{selectedHazard.type}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">发现日期</Label>
                <p className="font-medium">{selectedHazard.discoveryDate}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">整改期限</Label>
                <p className="font-medium">{selectedHazard.deadline}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">责任人</Label>
                <p className="font-medium">{selectedHazard.responsible}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">状态</Label>
                <p>
                  <Badge variant={
                    selectedHazard.status === "待整改" ? "outline" :
                    selectedHazard.status === "整改中" ? "secondary" : "default"
                  }>
                    {selectedHazard.status}
                  </Badge>
                </p>
              </div>
            </div>

            <div>
              <Label className="text-muted-foreground">整改进度</Label>
              <div className="mt-2 flex items-center gap-2">
                <Progress value={selectedHazard.progress} className="h-2 flex-1" />
                <span>{selectedHazard.progress}%</span>
              </div>
            </div>

            {selectedHazard.description && (
              <div>
                <Label className="text-muted-foreground">隐患描述</Label>
                <p className="mt-1 whitespace-pre-wrap">{selectedHazard.description}</p>
              </div>
            )}

            {selectedHazard.measures && (
              <div>
                <Label className="text-muted-foreground">整改措施</Label>
                <p className="mt-1 whitespace-pre-wrap">{selectedHazard.measures}</p>
              </div>
            )}

            {selectedHazard.lastModified && (
              <div className="text-sm text-muted-foreground">
                <p>最后修改时间：{selectedHazard.lastModified}</p>
                <p>修改人：{selectedHazard.modifiedBy}</p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

