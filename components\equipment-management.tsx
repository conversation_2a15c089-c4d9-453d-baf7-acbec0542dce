"use client"

// 添加XLSX的类型定义
declare global {
  interface Window {
    XLSX: any;
  }
}

import type React from "react"

import { useState, useMemo, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Trash2, Edit, Download, Plus, FileText, BarChart, Settings, PenToolIcon as Tool, AlertTriangle, Loader2, FileCheck, Info, <PERSON><PERSON>, Refresh<PERSON><PERSON>, CheckCircle, ChevronDown, Eye, MoreHorizontal, History } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter
} from "@/components/ui/sheet"
import { Calendar, Clock, Users } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction
} from "@/components/ui/alert-dialog"

// 模拟设备数据
const initialEquipmentData = [
  {
    id: 1,
    name: "钻机XD-500",
    code: "SB2025001",
    category: "钻探设备",
    model: "XD-500",
    manufacturer: "中国矿业机械厂",
    purchaseDate: "2025-01-15",
    price: "¥850,000",
    location: "A矿区",
    status: "正常使用",
    maintenanceDate: "2025-01-15",
    nextMaintenanceDate: "2025-04-15",
    responsible: "张三",
    description: "大型钻探设备，用于矿山开采",
    maintenanceHistory: [
      {
        id: 1,
        date: "2025-01-15",
        type: "例行保养",
        notes: "更换机油和滤芯",
        responsible: "维修部-王工"
      },
      {
        id: 2,
        date: "2025-03-15",
        type: "标准检修",
        notes: "检查液压系统，更换密封件",
        responsible: "维修部-李工"
      }
    ]
  },
  {
    id: 2,
    name: "装载机ZL50",
    code: "SB2025002",
    category: "装载设备",
    model: "ZL50",
    manufacturer: "徐工集团",
    purchaseDate: "2025-02-20",
    price: "¥650,000",
    location: "B矿区",
    status: "正常使用",
    maintenanceDate: "2025-02-05",
    nextMaintenanceDate: "2025-04-05",
    responsible: "李四",
    description: "中型装载设备，用于物料运输",
    maintenanceHistory: []
  },
  {
    id: 3,
    name: "矿用卡车MT-100",
    code: "SB2025045",
    category: "运输设备",
    model: "MT-100",
    manufacturer: "三一重工",
    purchaseDate: "2025-01-10",
    price: "¥1,200,000",
    location: "运输部",
    status: "维修中",
    maintenanceDate: "2025-03-01",
    nextMaintenanceDate: "2025-04-01",
    responsible: "王五",
    description: "大型运输车辆，正在进行底盘维修",
    maintenanceHistory: [
      {
        id: 1,
        date: "2025-01-15",
        type: "保养",
        notes: "例行保养，更换机油和滤芯",
        responsible: "维修部-李工"
      },
      {
        id: 2,
        date: "2025-03-01",
        type: "大修",
        notes: "底盘维修，更换传动轴",
        responsible: "维修部-张工"
      }
    ]
  },
  {
    id: 4,
    name: "破碎机PC-200",
    code: "SB2025078",
    category: "破碎设备",
    model: "PC-200",
    manufacturer: "上海矿山机械厂",
    purchaseDate: "2025-01-05",
    price: "¥750,000",
    location: "加工厂",
    status: "正常使用",
    maintenanceDate: "2025-02-20",
    nextMaintenanceDate: "2025-04-01",
    responsible: "赵六",
    description: "中型破碎设备，用于矿石加工",
    maintenanceHistory: []
  },
  {
    id: 5,
    name: "筛分机SF-100",
    code: "SB2025034",
    category: "筛分设备",
    model: "SF-100",
    manufacturer: "北京矿业设备有限公司",
    purchaseDate: "2025-01-12",
    price: "¥450,000",
    location: "加工厂",
    status: "闲置",
    maintenanceDate: "2025-03-15",
    nextMaintenanceDate: "2025-04-15",
    responsible: "钱七",
    description: "小型筛分设备，当前处于闲置状态",
    maintenanceHistory: []
  },
]

// 价格区间定义
const priceRanges = [
  { label: "50万以下", min: 0, max: 500000 },
  { label: "50-100万", min: 500000, max: 1000000 },
  { label: "100-150万", min: 1000000, max: 1500000 },
  { label: "150万以上", min: 1500000, max: Infinity }
];

export function EquipmentManagement() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [filteredData, setFilteredData] = useState(initialEquipmentData)
  const [equipmentData, setEquipmentData] = useState(initialEquipmentData)
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isViewSheetOpen, setIsViewSheetOpen] = useState(false)
  const [currentEquipment, setCurrentEquipment] = useState<(typeof initialEquipmentData)[0] | null>(null)
  const [isMaintenanceDialogOpen, setIsMaintenanceDialogOpen] = useState(false)
  const [maintenanceEquipment, setMaintenanceEquipment] = useState<(typeof initialEquipmentData)[0] | null>(null)
  const [maintenanceDate, setMaintenanceDate] = useState("")
  const [maintenanceNote, setMaintenanceNote] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [equipmentToDelete, setEquipmentToDelete] = useState<(typeof initialEquipmentData)[0] | null>(null)
  const [isMaintenanceHistoryDrawerOpen, setIsMaintenanceHistoryDrawerOpen] = useState(false)
  const [selectedMaintenanceEquipment, setSelectedMaintenanceEquipment] = useState<(typeof initialEquipmentData)[0] | null>(null)
  const [showDeleteMultipleDialog, setShowDeleteMultipleDialog] = useState(false)

  // 设备表单状态
  const [equipmentForm, setEquipmentForm] = useState({
    id: 0,
    name: "",
    code: "",
    category: "",
    model: "",
    manufacturer: "",
    purchaseDate: "",
    price: "",
    location: "",
    status: "正常使用",
    responsible: "",
    description: "",
    maintenanceDate: "",
    nextMaintenanceDate: "",
    maintenanceHistory: [] as { id: number; date: string; type: string; notes: string; responsible: string }[]
  })

  // 计算统计数据
  const statistics = useMemo(() => {
    return {
      total: equipmentData.length,
      normal: equipmentData.filter(item => item.status === "正常使用").length,
      maintenance: equipmentData.filter(item => item.status === "维修中").length,
      idle: equipmentData.filter(item => item.status === "闲置").length,
      totalValue: equipmentData.reduce((sum, item) => {
        const price = parseFloat(item.price.replace(/[^\d.]/g, ''));
        return sum + price;
      }, 0)
    }
  }, [equipmentData]);

  // 使用useEffect确保初始化时应用过滤器
  useEffect(() => {
    applyFilters(searchTerm, selectedCategory, selectedStatus);
  }, []);

  // 搜索功能
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    applyFilters(value, selectedCategory, selectedStatus)
  }

  // 应用筛选条件
  const applyFilters = (search: string, category: string, status: string) => {
    let results = [...equipmentData];

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      results = results.filter(
        item =>
          item.name.toLowerCase().includes(searchLower) ||
          item.code.toLowerCase().includes(searchLower) ||
          item.location.toLowerCase().includes(searchLower) ||
          item.model.toLowerCase().includes(searchLower) ||
          item.responsible.toLowerCase().includes(searchLower)
      );
    }

    // 类别过滤
    if (category !== "all") {
      results = results.filter(item => item.category === category);
    }

    // 状态过滤
    if (status !== "all") {
      results = results.filter(item => item.status === status);
    }

    // 更新过滤后的数据
    setFilteredData(results);

    // 如果当前有选中项，但过滤后不再包含这些项，更新选中状态
    if (selectedItems.length > 0) {
      const newSelectedItems = selectedItems.filter(id =>
        results.some(item => item.id === id)
      );
      if (newSelectedItems.length !== selectedItems.length) {
        setSelectedItems(newSelectedItems);
      }
    }
  };

  // 类别筛选
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    applyFilters(searchTerm, value, selectedStatus);
  }

  // 状态筛选
  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    applyFilters(searchTerm, selectedCategory, value);
  }

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 重新应用过滤器刷新数据
      applyFilters(searchTerm, selectedCategory, selectedStatus);
      setIsLoading(false);

      toast({
        title: "刷新成功",
        description: "设备列表已更新至最新状态",
      });
    }, 800);
  }

  // 导出数据
  const handleExport = () => {
    try {
      // 引入XLSX库的脚本
      const script = document.createElement('script');
      script.src = 'https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js';
      script.async = true;
      script.onload = () => {
        // 创建工作簿
        const wb = window.XLSX.utils.book_new();

        // 准备导出数据
        const exportData = filteredData.map(item => ({
          "设备名称": item.name,
          "设备编号": item.code,
          "设备类别": item.category,
          "型号": item.model,
          "制造商": item.manufacturer,
          "价格": item.price,
          "存放位置": item.location,
          "状态": item.status,
          "购置日期": item.purchaseDate,
          "上次检修日期": item.maintenanceDate,
          "下次检修日期": item.nextMaintenanceDate,
          "责任人": item.responsible,
          "描述": item.description
        }));

        // 将数据转换为工作表
        const ws = window.XLSX.utils.json_to_sheet(exportData);

        // 添加工作表到工作簿
        window.XLSX.utils.book_append_sheet(wb, ws, "设备清单");

        // 生成Excel文件并下载
        window.XLSX.writeFile(wb, `设备清单_${new Date().toISOString().split('T')[0]}.xlsx`);

        toast({
          title: "导出成功",
          description: "设备列表已导出为Excel文件",
        });
      };

      script.onerror = () => {
        throw new Error("加载Excel导出库失败");
      };

      document.body.appendChild(script);
    } catch (error) {
      // 如果出错，回退到CSV导出
      try {
        // 创建CSV内容
        let csvContent = "设备名称,设备编号,类别,型号,存放位置,购置日期,状态,价格,责任人\n";

        filteredData.forEach(item => {
          csvContent += `"${item.name}","${item.code}","${item.category}","${item.model}","${item.location}","${item.purchaseDate}","${item.status}","${item.price}","${item.responsible}"\n`;
        });

        // 创建Blob对象
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", `设备清单_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: "导出成功",
          description: "设备列表已导出为CSV文件（Excel导出失败，已回退到CSV格式）",
        });
      } catch (csvError) {
        toast({
          title: "导出失败",
          description: "导出过程中发生错误",
          variant: "destructive"
        });
      }
    }
  };

  // 获取状态对应的徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常使用":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-200">正常使用</Badge>
      case "维修中":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-200">维修中</Badge>
      case "闲置":
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200">闲置</Badge>
      case "报废":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-200">报废</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理查看详情
  const handleViewDetails = (equipment: (typeof initialEquipmentData)[0]) => {
    setCurrentEquipment(equipment);
    setIsViewSheetOpen(true);
  }

  // 查看维护记录
  const handleViewMaintenanceHistory = (equipment: (typeof initialEquipmentData)[0]) => {
    setSelectedMaintenanceEquipment(equipment);
    setIsMaintenanceHistoryDrawerOpen(true);
  }

  // 处理安排检修
  const handleScheduleMaintenance = (equipment: (typeof initialEquipmentData)[0]) => {
    setMaintenanceEquipment(equipment);
    setMaintenanceDate("");
    setMaintenanceNote("");
    setIsMaintenanceDialogOpen(true);
  }

  // 提交检修安排
  const handleSubmitMaintenance = () => {
    if (!maintenanceEquipment || !maintenanceDate) {
      toast({
        title: "请填写完整信息",
        description: "检修日期是必填项",
        variant: "destructive"
      });
      return;
    }

    const newMaintenanceRecord = {
      id: Date.now(),
      date: maintenanceDate,
      type: "定期保养",
      notes: maintenanceNote || "常规保养检修",
      responsible: maintenanceEquipment.responsible
    };

    // 更新设备数据
    const updatedEquipmentData = equipmentData.map(item => {
      if (item.id === maintenanceEquipment.id) {
        // 确保主记录更新
        const updatedItem = {
          ...item,
          maintenanceDate: maintenanceDate,
          nextMaintenanceDate: new Date(new Date(maintenanceDate).setMonth(new Date(maintenanceDate).getMonth() + 3)).toISOString().split('T')[0],
          maintenanceHistory: [...(item.maintenanceHistory || []), newMaintenanceRecord]
        };

        // 如果已打开维护历史记录抽屉，更新选定的设备信息
        if (selectedMaintenanceEquipment && selectedMaintenanceEquipment.id === item.id) {
          setSelectedMaintenanceEquipment(updatedItem);
        }

        return updatedItem;
      }
      return item;
    });

    setEquipmentData(updatedEquipmentData);

    // 重新应用过滤器以更新显示
    setTimeout(() => {
      applyFilters(searchTerm, selectedCategory, selectedStatus);
    }, 0);

    toast({
      title: "检修安排成功",
      description: `已为 ${maintenanceEquipment.name} 安排检修`,
    });

    setIsMaintenanceDialogOpen(false);
  }

  // 重置表单
  const resetEquipmentForm = () => {
    setEquipmentForm({
      id: 0,
      name: "",
      code: "",
      category: "",
      model: "",
      manufacturer: "",
      purchaseDate: "",
      price: "",
      location: "",
      status: "正常使用",
      responsible: "",
      description: "",
      maintenanceDate: "",
      nextMaintenanceDate: "",
      maintenanceHistory: []
    });
  }

  // 处理添加设备
  const handleAddEquipment = () => {
    if (!equipmentForm.name || !equipmentForm.code) {
      toast({
        title: "请填写必要信息",
        description: "设备名称和编号是必填项",
        variant: "destructive"
      });
      return;
    }

    // 检查设备编号是否重复
    if (equipmentData.some(item => item.code === equipmentForm.code)) {
      toast({
        title: "设备编号重复",
        description: "该设备编号已存在，请使用其他编号",
        variant: "destructive"
      });
      return;
    }

    // 格式化价格
    let formattedPrice = equipmentForm.price;
    if (formattedPrice && !formattedPrice.startsWith("¥")) {
      formattedPrice = `¥${formattedPrice}`;
    }

    // 创建新设备
    const newEquipment = {
      ...equipmentForm,
      id: Date.now(),
      price: formattedPrice,
      maintenanceHistory: equipmentForm.maintenanceHistory || [],
      maintenanceDate: equipmentForm.maintenanceDate || new Date().toISOString().split('T')[0],
      nextMaintenanceDate: equipmentForm.nextMaintenanceDate || new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0]
    };

    // 直接添加到设备数据并更新filteredData
    const updatedEquipmentData = [...equipmentData, newEquipment];
    setEquipmentData(updatedEquipmentData);

    // 如果没有筛选条件，也直接添加到过滤后的数据中
    if (searchTerm === "" && selectedCategory === "all" && selectedStatus === "all") {
      setFilteredData([...filteredData, newEquipment]);
    } else {
      // 否则重新应用过滤器
      applyFilters(searchTerm, selectedCategory, selectedStatus);
    }

    toast({
      title: "添加成功",
      description: `设备 ${equipmentForm.name} 已添加到系统`,
    });

    setIsAddDialogOpen(false);
    resetEquipmentForm();
  }

  // 处理编辑设备
  const handleEditEquipment = (equipment: (typeof initialEquipmentData)[0]) => {
    // 设置表单数据
    setEquipmentForm({
      id: equipment.id,
      name: equipment.name,
      code: equipment.code,
      category: equipment.category,
      model: equipment.model,
      manufacturer: equipment.manufacturer,
      purchaseDate: equipment.purchaseDate,
      price: equipment.price,
      location: equipment.location,
      status: equipment.status,
      responsible: equipment.responsible,
      description: equipment.description,
      maintenanceDate: equipment.maintenanceDate,
      nextMaintenanceDate: equipment.nextMaintenanceDate,
      maintenanceHistory: equipment.maintenanceHistory || []
    });

    setIsEditDialogOpen(true);
  }

  // 保存编辑的设备
  const handleSaveEditedEquipment = () => {
    if (!equipmentForm.name || !equipmentForm.code) {
      toast({
        title: "请填写必要信息",
        description: "设备名称和编号是必填项",
        variant: "destructive"
      });
      return;
    }

    // 检查设备编号是否重复(不包括自身)
    if (equipmentData.some(item => item.code === equipmentForm.code && item.id !== equipmentForm.id)) {
      toast({
        title: "设备编号重复",
        description: "该设备编号已存在，请使用其他编号",
        variant: "destructive"
      });
      return;
    }

    // 格式化价格
    let formattedPrice = equipmentForm.price;
    if (formattedPrice && !formattedPrice.startsWith("¥")) {
      formattedPrice = `¥${formattedPrice}`;
    }

    const editedEquipment = {
      ...equipmentForm,
      price: formattedPrice
    };

    // 更新设备数据
    const updatedEquipmentData = equipmentData.map(item => {
      if (item.id === equipmentForm.id) {
        return editedEquipment;
      }
      return item;
    });

    setEquipmentData(updatedEquipmentData);

    // 同时直接更新过滤后的数据
    const updatedFilteredData = filteredData.map(item => {
      if (item.id === equipmentForm.id) {
        return editedEquipment;
      }
      return item;
    });

    setFilteredData(updatedFilteredData);

    toast({
      title: "编辑成功",
      description: `设备 ${equipmentForm.name} 的信息已更新`,
    });

    setIsEditDialogOpen(false);
  }

  // 处理删除
  const handleDeleteEquipment = (equipment: (typeof initialEquipmentData)[0]) => {
    setEquipmentToDelete(equipment);
    setIsDeleteDialogOpen(true);
  }

  // 确认删除
  const confirmDelete = () => {
    if (!equipmentToDelete) return;

    // 更新设备数据
    const updatedEquipmentData = equipmentData.filter(
      (item) => item.id !== equipmentToDelete.id
    );
    setEquipmentData(updatedEquipmentData);

    // 同时直接更新过滤后的数据
    const updatedFilteredData = filteredData.filter(
      (item) => item.id !== equipmentToDelete.id
    );
    setFilteredData(updatedFilteredData);

    toast({
      title: "删除成功",
      description: `设备 ${equipmentToDelete.name} 已从系统中删除`,
    });

    setIsDeleteDialogOpen(false);
    setEquipmentToDelete(null);
  }

  // 选择/取消选择单个设备
  const handleSelectItem = (id: number) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter(itemId => itemId !== id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean | "indeterminate") => {
    if (checked === true) {
      setSelectedItems(filteredData.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedItems.length === 0) {
      toast({
        variant: "destructive",
        title: "未选择设备",
        description: "请先选择要删除的设备",
      });
      return;
    }
    setShowDeleteMultipleDialog(true);
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    // 更新设备数据
    const newData = equipmentData.filter(item => !selectedItems.includes(item.id));
    setEquipmentData(newData);

    // 同时直接更新过滤后的数据
    const newFilteredData = filteredData.filter(item => !selectedItems.includes(item.id));
    setFilteredData(newFilteredData);

    setSelectedItems([]);
    setShowDeleteMultipleDialog(false);

    toast({
      title: "批量删除成功",
      description: `已删除 ${selectedItems.length} 台设备`,
    });
  }

  // 处理价格区间统计
  const getPriceDistribution = () => {
    const distribution = priceRanges.map(range => {
      const count = filteredData.filter(item => {
        const price = parseFloat(item.price.replace(/[^\d.]/g, ''));
        return price >= range.min && price < range.max;
      }).length;
      return {
        range: range.label,
        count,
        percent: filteredData.length ? Math.round((count / filteredData.length) * 100) : 0
      };
    });
    return distribution;
  };

  // 获取设备价值区间的颜色
  const getPriceRangeColor = (index: number) => {
    const colors = ["bg-blue-600", "bg-blue-500", "bg-blue-400", "bg-blue-300"];
    return colors[index % colors.length];
  };

  // 设备年龄分布数据计算
  const getEquipmentAgeDistribution = () => {
    const currentYear = new Date().getFullYear();
    const ageGroups = [
      { label: "1年以内", min: 0, max: 1 },
      { label: "1-2年", min: 1, max: 2 },
      { label: "2-3年", min: 2, max: 3 },
      { label: "3年以上", min: 3, max: Infinity }
    ];

    const distribution = ageGroups.map(group => {
      const count = filteredData.filter(item => {
        const purchaseYear = new Date(item.purchaseDate).getFullYear();
        const age = currentYear - purchaseYear;
        return age >= group.min && age < group.max;
      }).length;

      return {
        group: group.label,
        count,
        percent: filteredData.length ? Math.round((count / filteredData.length) * 100) : 0
      };
    });

    return distribution;
  };

  // 获取年龄区间颜色
  const getAgeGroupColor = (index: number) => {
    const colors = ["bg-green-600", "bg-green-500", "bg-green-400", "bg-green-300"];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      {/* 顶部区域 */}
      <div className="flex items-start justify-between py-2 mb-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Settings className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">设备管理</h1>
          </div>
          <p className="text-muted-foreground">管理和维护公司设备资产信息</p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-blue-50 transition-colors"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-green-50 transition-colors"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button
            variant="default"
            size="sm"
            className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 shadow-md transition-all"
            onClick={() => setIsAddDialogOpen(true)}
          >
            <Plus className="h-4 w-4" />
                添加设备
              </Button>
                  </div>
                </div>

      {/* 搜索和筛选区域 */}
      <Card className="shadow-sm border border-blue-100/50 overflow-hidden bg-gradient-to-r from-blue-50 via-white to-blue-50">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索设备名称、编号、位置..."
                className="pl-8 w-full transition-colors hover:border-blue-300 focus-visible:ring-blue-500"
                value={searchTerm}
                onChange={handleSearch}
              />
                  </div>

            <Select value={selectedCategory} onValueChange={handleCategoryChange}>
              <SelectTrigger className="w-[180px] transition-colors hover:border-blue-300 focus:ring-blue-500">
                <SelectValue placeholder="设备类别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类别</SelectItem>
                <SelectItem value="钻探设备">钻探设备</SelectItem>
                <SelectItem value="装载设备">装载设备</SelectItem>
                <SelectItem value="运输设备">运输设备</SelectItem>
                <SelectItem value="破碎设备">破碎设备</SelectItem>
                <SelectItem value="筛分设备">筛分设备</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[180px] transition-colors hover:border-blue-300 focus:ring-blue-500">
                <SelectValue placeholder="设备状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="正常使用">正常使用</SelectItem>
                <SelectItem value="维修中">维修中</SelectItem>
                <SelectItem value="闲置">闲置</SelectItem>
                <SelectItem value="报废">报废</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedCategory("all");
                setSelectedStatus("all");
                setSearchTerm("");
                applyFilters("", "all", "all");
              }}
              className="hover:bg-blue-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重置筛选
            </Button>
                  </div>
        </CardContent>
      </Card>

      {/* 添加批量操作区域 */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 py-2 px-3 rounded-md flex items-center justify-between mb-4">
          <div className="text-sm text-blue-700">
            已选择 <span className="font-medium">{selectedItems.length}</span> 台设备
          </div>
          <div className="flex gap-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBatchDelete}
              className="h-8"
            >
              <Trash2 className="h-4 w-4 mr-1" /> 批量删除
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedItems([])}
              className="h-8"
            >
              取消选择
            </Button>
          </div>
        </div>
      )}

      {/* 统计卡片区域 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-blue-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">设备总数</CardTitle>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <Settings className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.total}</div>
              <div className="text-sm text-muted-foreground">台设备</div>
                  </div>
            <div className="mt-1 text-xs text-muted-foreground">
              所有类型设备的数量总和
                  </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-green-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">正常使用</CardTitle>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
        </div>
      </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.normal}</div>
              <div className="text-sm text-muted-foreground">台设备</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              当前处于正常使用状态的设备
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-blue-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">维修中</CardTitle>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <Tool className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.maintenance}</div>
              <div className="text-sm text-muted-foreground">台设备</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              当前处于维修状态的设备数量
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-amber-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">设备总价值</CardTitle>
              <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                <BarChart className="h-4 w-4 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">¥{statistics.totalValue.toLocaleString()}</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              所有设备价值的总和
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="table">
        <TabsList>
          <TabsTrigger value="table">
            <FileText className="mr-2 h-4 w-4" />
            表格视图
          </TabsTrigger>
          <TabsTrigger value="chart">
            <BarChart className="mr-2 h-4 w-4" />
            图表视图
          </TabsTrigger>
        </TabsList>
        <TabsContent value="table" className="mt-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
              <CardTitle>设备列表</CardTitle>
              <CardDescription>
                共 {filteredData.length} 台设备
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="rounded-md border shadow-sm">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-slate-50">
                      <TableHead className="w-[40px]">
                        <Checkbox
                          checked={filteredData.length > 0 && selectedItems.length === filteredData.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>设备名称</TableHead>
                      <TableHead>设备编号</TableHead>
                      <TableHead>类别</TableHead>
                      <TableHead>型号</TableHead>
                      <TableHead>存放位置</TableHead>
                      <TableHead>购置日期</TableHead>
                      <TableHead>下次检修</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center p-4 text-muted-foreground">
                          未找到匹配的设备记录
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredData.map((equipment) => (
                        <TableRow key={equipment.id} className="hover:bg-slate-50">
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(equipment.id)}
                              onCheckedChange={() => handleSelectItem(equipment.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{equipment.name}</div>
                          </TableCell>
                          <TableCell>{equipment.code}</TableCell>
                          <TableCell>{equipment.category}</TableCell>
                          <TableCell>{equipment.model}</TableCell>
                          <TableCell>{equipment.location}</TableCell>
                          <TableCell>{equipment.purchaseDate}</TableCell>
                          <TableCell>
                            {equipment.nextMaintenanceDate}
                          </TableCell>
                          <TableCell>{getStatusBadge(equipment.status)}</TableCell>
                          <TableCell className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-slate-500 hover:text-blue-600"
                              onClick={() => handleViewDetails(equipment)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-slate-500 hover:text-amber-600"
                              onClick={() => handleEditEquipment(equipment)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-500">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleViewMaintenanceHistory(equipment)}>
                                  <FileCheck className="h-4 w-4 mr-2" />
                                  维护记录
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleScheduleMaintenance(equipment)}>
                                  <Wrench className="h-4 w-4 mr-2" />
                                  安排检修
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteEquipment(equipment)}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="chart" className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>设备类别分布</CardTitle>
                <CardDescription>
                  按设备类别的数量统计
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {/* 这里是虚拟的图表数据，实际项目中应使用图表库 */}
                  {["钻探设备", "装载设备", "运输设备", "破碎设备", "筛分设备"].map(category => {
                    const count = filteredData.filter(item => item.category === category).length;
                    const percent = Math.round((count / filteredData.length) * 100) || 0;
                    return (
                      <div key={category} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{category}</span>
                          <span className="text-sm text-muted-foreground">{count} 台 ({percent}%)</span>
                        </div>
                        <div className="h-2 rounded-full bg-gray-100">
                          <div
                            className="h-full rounded-full bg-blue-600"
                            style={{ width: `${percent}%` }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>设备状态分布</CardTitle>
                <CardDescription>
                  按设备状态的数量统计
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {/* 这里是虚拟的图表数据，实际项目中应使用图表库 */}
                  {["正常使用", "维修中", "闲置", "报废"].map(status => {
                    const count = filteredData.filter(item => item.status === status).length;
                    const percent = Math.round((count / filteredData.length) * 100) || 0;
                    const color = status === "正常使用" ? "bg-green-600" :
                                 status === "维修中" ? "bg-blue-600" :
                                 status === "闲置" ? "bg-amber-600" : "bg-red-600";
                    return (
                      <div key={status} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{status}</span>
                          <span className="text-sm text-muted-foreground">{count} 台 ({percent}%)</span>
                        </div>
                        <div className="h-2 rounded-full bg-gray-100">
                          <div
                            className={`h-full rounded-full ${color}`}
                            style={{ width: `${percent}%` }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>设备价值分布</CardTitle>
                <CardDescription>
                  按价格区间的设备数量分布
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {/* 实际的价值分布数据 */}
                  {getPriceDistribution().map((item, index) => (
                    <div key={item.range} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{item.range}</span>
                        <span className="text-sm text-muted-foreground">{item.count} 台 ({item.percent}%)</span>
                      </div>
                      <div className="h-2 rounded-full bg-gray-100">
                        <div
                          className={`h-full rounded-full ${getPriceRangeColor(index)}`}
                          style={{ width: `${item.percent}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>设备年龄分布</CardTitle>
                <CardDescription>
                  按使用年限的设备数量统计
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {/* 设备年龄分布数据 */}
                  {getEquipmentAgeDistribution().map((item, index) => (
                    <div key={item.group} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{item.group}</span>
                        <span className="text-sm text-muted-foreground">{item.count} 台 ({item.percent}%)</span>
                      </div>
                      <div className="h-2 rounded-full bg-gray-100">
                        <div
                          className={`h-full rounded-full ${getAgeGroupColor(index)}`}
                          style={{ width: `${item.percent}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 lg:col-span-2">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>综合设备分析</CardTitle>
                <CardDescription>
                  设备维护和检修分析
                </CardDescription>
            </CardHeader>
              <CardContent className="p-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div className="border rounded-xl p-4 bg-gradient-to-r from-blue-50/50 to-white">
                    <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
                      <Wrench className="h-5 w-5 text-blue-500" />
                      近期检修需求
                    </h3>
                    <div className="space-y-3 mt-4">
                      {filteredData
                        .filter(item => {
                          // 假设我们检查15天内需要检修的设备
                          const nextMaintenance = new Date(item.nextMaintenanceDate);
                          const today = new Date();
                          const diffDays = Math.ceil((nextMaintenance.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                          return diffDays <= 15;
                        })
                        .slice(0, 3)
                        .map(item => (
                          <div key={item.id} className="flex justify-between items-center p-2 bg-blue-50 rounded-lg">
                            <div>
                              <p className="font-medium">{item.name}</p>
                              <p className="text-xs text-muted-foreground">{item.nextMaintenanceDate} 检修</p>
                            </div>
                            <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-200">
                              {item.category}
                            </Badge>
                          </div>
                        ))}
                      {filteredData.filter(item => {
                        const nextMaintenance = new Date(item.nextMaintenanceDate);
                        const today = new Date();
                        const diffDays = Math.ceil((nextMaintenance.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                        return diffDays <= 15;
                      }).length === 0 && (
                        <div className="p-4 text-center text-muted-foreground">
                          近期无设备需要检修
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="border rounded-xl p-4 bg-gradient-to-r from-amber-50/50 to-white">
                    <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-amber-500" />
                      设备状态概览
                    </h3>
                    <div className="grid grid-cols-2 gap-3 mt-4">
                      <div className="border rounded-lg p-3 bg-white">
                        <div className="text-xs text-muted-foreground mb-1">维修中设备</div>
                        <div className="text-xl font-bold text-blue-600">
                          {statistics.maintenance} <span className="text-xs font-normal">台</span>
                        </div>
                      </div>
                      <div className="border rounded-lg p-3 bg-white">
                        <div className="text-xs text-muted-foreground mb-1">闲置设备</div>
                        <div className="text-xl font-bold text-amber-600">
                          {statistics.idle} <span className="text-xs font-normal">台</span>
                        </div>
                      </div>
                      <div className="border rounded-lg p-3 bg-white">
                        <div className="text-xs text-muted-foreground mb-1">设备总数</div>
                        <div className="text-xl font-bold text-gray-600">
                          {statistics.total} <span className="text-xs font-normal">台</span>
                        </div>
                      </div>
                      <div className="border rounded-lg p-3 bg-white">
                        <div className="text-xs text-muted-foreground mb-1">正常设备</div>
                        <div className="text-xl font-bold text-green-600">
                          {statistics.normal} <span className="text-xs font-normal">台</span>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
            </CardContent>
          </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 设备详情查看抽屉 */}
      <Sheet open={isViewSheetOpen} onOpenChange={setIsViewSheetOpen}>
        <SheetContent className="sm:max-w-xl">
          <SheetHeader className="pb-4 border-b">
            <SheetTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              设备详情
            </SheetTitle>
            <SheetDescription>
              {currentEquipment && `查看设备 ${currentEquipment.name} 的详细信息`}
            </SheetDescription>
          </SheetHeader>

          {currentEquipment && (
            <div className="py-6 space-y-6">
              <div className="grid grid-cols-2 gap-x-4 gap-y-6">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">设备名称</h4>
                  <p className="font-medium">{currentEquipment.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">设备编号</h4>
                  <p className="font-medium">{currentEquipment.code}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">设备类别</h4>
                  <p>{currentEquipment.category}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">型号</h4>
                  <p>{currentEquipment.model}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">制造商</h4>
                  <p>{currentEquipment.manufacturer}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">价格</h4>
                  <p>{currentEquipment.price}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">购置日期</h4>
                  <p>{currentEquipment.purchaseDate}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">存放位置</h4>
                  <p>{currentEquipment.location}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">状态</h4>
                  <p>{getStatusBadge(currentEquipment.status)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">责任人</h4>
                  <p>{currentEquipment.responsible}</p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground mb-1">设备说明</h4>
                <p className="text-sm">{currentEquipment.description || "暂无说明"}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">上次检修日期</h4>
                  <p>
                    <Calendar className="h-3.5 w-3.5 inline-block mr-1 text-blue-600" />
                    {currentEquipment.maintenanceDate || "暂无记录"}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">下次检修日期</h4>
                  <p>
                    <Calendar className="h-3.5 w-3.5 inline-block mr-1 text-amber-600" />
                    {currentEquipment.nextMaintenanceDate || "暂未安排"}
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t flex justify-between">
                <Button
                  variant="outline"
                  className="gap-2"
                  onClick={() => {
                    setIsViewSheetOpen(false);
                    handleViewMaintenanceHistory(currentEquipment);
                  }}
                >
                  <FileCheck className="h-4 w-4" />
                  查看维护记录
                </Button>
                <Button
                  className="gap-2"
                  onClick={() => {
                    setIsViewSheetOpen(false);
                    handleScheduleMaintenance(currentEquipment);
                  }}
                >
                  <Wrench className="h-4 w-4" />
                  安排检修
                </Button>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 添加设备对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        setIsAddDialogOpen(open);
        if (!open) resetEquipmentForm();
      }}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">添加设备</DialogTitle>
            <DialogDescription>
              请填写新设备的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">设备名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="输入设备名称"
                  value={equipmentForm.name}
                  onChange={(e) => setEquipmentForm({...equipmentForm, name: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">设备编号 <span className="text-red-500">*</span></Label>
                <Input
                  id="code"
                  placeholder="例如：SB2023001"
                  value={equipmentForm.code}
                  onChange={(e) => setEquipmentForm({...equipmentForm, code: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">设备类别</Label>
                <Select
                  value={equipmentForm.category}
                  onValueChange={(value) => setEquipmentForm({...equipmentForm, category: value})}
                >
                  <SelectTrigger id="category" className="transition-all border-blue-100 focus:ring-blue-500">
                    <SelectValue placeholder="选择设备类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="钻探设备">钻探设备</SelectItem>
                    <SelectItem value="装载设备">装载设备</SelectItem>
                    <SelectItem value="运输设备">运输设备</SelectItem>
                    <SelectItem value="破碎设备">破碎设备</SelectItem>
                    <SelectItem value="筛分设备">筛分设备</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">型号</Label>
                <Input
                  id="model"
                  placeholder="例如：XD-500"
                  value={equipmentForm.model}
                  onChange={(e) => setEquipmentForm({...equipmentForm, model: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="manufacturer">制造商</Label>
                <Input
                  id="manufacturer"
                  placeholder="输入制造商名称"
                  value={equipmentForm.manufacturer}
                  onChange={(e) => setEquipmentForm({...equipmentForm, manufacturer: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="purchaseDate">购置日期</Label>
                <Input
                  id="purchaseDate"
                  type="date"
                  value={equipmentForm.purchaseDate}
                  onChange={(e) => setEquipmentForm({...equipmentForm, purchaseDate: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">价格</Label>
                <Input
                  id="price"
                  placeholder="例如：850000"
                  value={equipmentForm.price}
                  onChange={(e) => setEquipmentForm({...equipmentForm, price: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">存放地点</Label>
                <Input
                  id="location"
                  placeholder="例如：A矿区"
                  value={equipmentForm.location}
                  onChange={(e) => setEquipmentForm({...equipmentForm, location: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select
                  value={equipmentForm.status}
                  onValueChange={(value) => setEquipmentForm({...equipmentForm, status: value})}
                >
                  <SelectTrigger id="status" className="transition-all border-blue-100 focus:ring-blue-500">
                    <SelectValue placeholder="选择设备状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="正常使用">正常使用</SelectItem>
                    <SelectItem value="维修中">维修中</SelectItem>
                    <SelectItem value="闲置">闲置</SelectItem>
                    <SelectItem value="报废">报废</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="responsible">责任人</Label>
                <Input
                  id="responsible"
                  placeholder="输入责任人姓名"
                  value={equipmentForm.responsible}
                  onChange={(e) => setEquipmentForm({...equipmentForm, responsible: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">设备描述</Label>
              <Textarea
                id="description"
                placeholder="输入设备的详细描述信息"
                value={equipmentForm.description}
                onChange={(e) => setEquipmentForm({...equipmentForm, description: e.target.value})}
                className="min-h-[100px] transition-all border-blue-100 focus-visible:ring-blue-500"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleAddEquipment}
              className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
            >
              <Plus className="h-4 w-4" />
              添加设备
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加维护安排对话框 */}
      <Dialog open={isMaintenanceDialogOpen} onOpenChange={setIsMaintenanceDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent flex items-center gap-2">
              <Wrench className="h-5 w-5 text-blue-600" />
              安排设备检修
            </DialogTitle>
            <DialogDescription>
              {maintenanceEquipment && `为 ${maintenanceEquipment.name} (${maintenanceEquipment.code}) 安排检修`}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="maintenance-date" className="text-sm font-medium">
                  检修日期 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="maintenance-date"
                  type="date"
                  value={maintenanceDate}
                  onChange={(e) => setMaintenanceDate(e.target.value)}
                  className="mt-1 transition-all border-blue-100 focus-visible:ring-blue-500"
                  required
                />
              </div>
              <div>
                <Label htmlFor="maintenance-responsible" className="text-sm font-medium">
                  负责人
                </Label>
                <Input
                  id="maintenance-responsible"
                  value={maintenanceEquipment?.responsible || ""}
                  className="mt-1 transition-all border-blue-100 focus-visible:ring-blue-500"
                  disabled
                />
              </div>
            </div>

            <div>
              <Label htmlFor="maintenance-note" className="text-sm font-medium">
                检修说明
              </Label>
              <Textarea
                id="maintenance-note"
                value={maintenanceNote}
                onChange={(e) => setMaintenanceNote(e.target.value)}
                className="mt-1 transition-all border-blue-100 focus-visible:ring-blue-500"
                placeholder="请输入检修内容、注意事项等信息"
                rows={3}
              />
            </div>

            <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-700 flex items-start gap-2">
              <Info className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <p>安排检修后，系统将自动更新设备的：</p>
                <ul className="list-disc list-inside mt-1 ml-1 space-y-1">
                  <li>上次检修日期（设为选择的检修日期）</li>
                  <li>下次检修日期（自动计算为检修日期后3个月）</li>
                  <li>添加一条新的维护记录到设备的维护历史中</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMaintenanceDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleSubmitMaintenance}
              className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
            >
              <FileCheck className="h-4 w-4" />
              确认安排
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {equipmentToDelete && (
                <>
                  您确定要删除设备 <span className="font-semibold">{equipmentToDelete.name}</span> (编号：{equipmentToDelete.code}) 吗？
                  <br />
                  此操作不可撤销。
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量删除确认对话框 */}
      <AlertDialog open={showDeleteMultipleDialog} onOpenChange={setShowDeleteMultipleDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>批量删除设备</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除选中的 {selectedItems.length} 台设备吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBatchDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 设备编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[900px]">
          <DialogHeader>
            <DialogTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              编辑设备信息
            </DialogTitle>
            <DialogDescription>
              修改设备的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">设备名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-name"
                  placeholder="例如：钻机XD-500"
                  value={equipmentForm.name}
                  onChange={(e) => setEquipmentForm({...equipmentForm, name: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-code">设备编号 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-code"
                  placeholder="例如：SB2023001"
                  value={equipmentForm.code}
                  onChange={(e) => setEquipmentForm({...equipmentForm, code: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">设备类别</Label>
                <Select
                  value={equipmentForm.category}
                  onValueChange={(value) => setEquipmentForm({...equipmentForm, category: value})}
                >
                  <SelectTrigger id="edit-category" className="transition-all border-blue-100 focus:ring-blue-500">
                    <SelectValue placeholder="选择设备类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="钻探设备">钻探设备</SelectItem>
                    <SelectItem value="装载设备">装载设备</SelectItem>
                    <SelectItem value="运输设备">运输设备</SelectItem>
                    <SelectItem value="破碎设备">破碎设备</SelectItem>
                    <SelectItem value="筛分设备">筛分设备</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-model">型号</Label>
                <Input
                  id="edit-model"
                  placeholder="例如：XD-500"
                  value={equipmentForm.model}
                  onChange={(e) => setEquipmentForm({...equipmentForm, model: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-manufacturer">制造商</Label>
                <Input
                  id="edit-manufacturer"
                  placeholder="输入制造商名称"
                  value={equipmentForm.manufacturer}
                  onChange={(e) => setEquipmentForm({...equipmentForm, manufacturer: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-purchaseDate">购置日期</Label>
                <Input
                  id="edit-purchaseDate"
                  type="date"
                  value={equipmentForm.purchaseDate}
                  onChange={(e) => setEquipmentForm({...equipmentForm, purchaseDate: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-price">价格</Label>
                <Input
                  id="edit-price"
                  placeholder="例如：850000"
                  value={equipmentForm.price}
                  onChange={(e) => setEquipmentForm({...equipmentForm, price: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">存放地点</Label>
                <Input
                  id="edit-location"
                  placeholder="例如：A矿区"
                  value={equipmentForm.location}
                  onChange={(e) => setEquipmentForm({...equipmentForm, location: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select
                  value={equipmentForm.status}
                  onValueChange={(value) => setEquipmentForm({...equipmentForm, status: value})}
                >
                  <SelectTrigger id="edit-status" className="transition-all border-blue-100 focus:ring-blue-500">
                    <SelectValue placeholder="选择设备状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="正常使用">正常使用</SelectItem>
                    <SelectItem value="维修中">维修中</SelectItem>
                    <SelectItem value="闲置">闲置</SelectItem>
                    <SelectItem value="报废">报废</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-responsible">责任人</Label>
                <Input
                  id="edit-responsible"
                  placeholder="输入责任人姓名"
                  value={equipmentForm.responsible}
                  onChange={(e) => setEquipmentForm({...equipmentForm, responsible: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-maintenanceDate">上次检修日期</Label>
                <Input
                  id="edit-maintenanceDate"
                  type="date"
                  value={equipmentForm.maintenanceDate}
                  onChange={(e) => setEquipmentForm({...equipmentForm, maintenanceDate: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-nextMaintenanceDate">下次检修日期</Label>
                <Input
                  id="edit-nextMaintenanceDate"
                  type="date"
                  value={equipmentForm.nextMaintenanceDate}
                  onChange={(e) => setEquipmentForm({...equipmentForm, nextMaintenanceDate: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-description">设备描述</Label>
                <Textarea
                  id="edit-description"
                  placeholder="输入设备的详细描述信息"
                  value={equipmentForm.description}
                  onChange={(e) => setEquipmentForm({...equipmentForm, description: e.target.value})}
                  className="min-h-[120px] transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <h3 className="text-sm font-medium text-blue-700 mb-2 flex items-center gap-2">
                  <FileCheck className="h-4 w-4" />
                  维护记录
                </h3>
                {equipmentForm.maintenanceHistory && equipmentForm.maintenanceHistory.length > 0 ? (
                  <div className="space-y-2 max-h-[200px] overflow-y-auto pr-1">
                    {equipmentForm.maintenanceHistory.map((record, index) => (
                      <div key={record.id} className="bg-white rounded border border-blue-100 p-2 text-sm">
                        <div className="flex justify-between items-start">
                          <div className="font-medium">{record.type}</div>
                          <div className="text-xs text-muted-foreground">
                            {record.date}
                          </div>
                        </div>
                        <div className="text-xs">{record.notes}</div>
                        <div className="text-xs text-muted-foreground mt-1">{record.responsible}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground text-center py-6">
                    暂无维护记录
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleSaveEditedEquipment}
              className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
            >
              <FileCheck className="h-4 w-4" />
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 维护记录查看抽屉 */}
      <Sheet open={isMaintenanceHistoryDrawerOpen} onOpenChange={setIsMaintenanceHistoryDrawerOpen}>
        <SheetContent className="w-[600px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              设备维护记录
            </SheetTitle>
            <SheetDescription>
              {selectedMaintenanceEquipment && `查看 ${selectedMaintenanceEquipment.name} (${selectedMaintenanceEquipment.code}) 的维护历史记录`}
            </SheetDescription>
          </SheetHeader>

          {selectedMaintenanceEquipment && (
            <div className="mt-6 space-y-6">
              <div className="p-4 bg-gradient-to-r from-blue-50 to-white rounded-lg border border-blue-100">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs text-muted-foreground">设备名称</Label>
                    <p className="font-medium">{selectedMaintenanceEquipment.name}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">设备编号</Label>
                    <p className="font-medium">{selectedMaintenanceEquipment.code}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">上次检修日期</Label>
                    <p className="font-medium">{selectedMaintenanceEquipment.maintenanceDate || "暂无记录"}</p>
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">下次检修日期</Label>
                    <p className="font-medium">{selectedMaintenanceEquipment.nextMaintenanceDate || "暂未安排"}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-base font-medium mb-3 flex items-center gap-2">
                  <FileCheck className="h-4 w-4 text-blue-600" />
                  维护历史记录
                </h3>

                {selectedMaintenanceEquipment.maintenanceHistory && selectedMaintenanceEquipment.maintenanceHistory.length > 0 ? (
                  <div className="space-y-3 mt-4">
                    {selectedMaintenanceEquipment.maintenanceHistory.map((record, index) => (
                      <Card key={record.id} className="border border-blue-100 shadow-sm">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-200">
                                {record.type}
                              </Badge>
                              <span className="text-sm font-medium">#{selectedMaintenanceEquipment.maintenanceHistory.length - index}</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {record.date}
                            </div>
                          </div>
                          <p className="text-sm mt-2">{record.notes}</p>
                          <div className="flex items-center mt-3 text-xs text-muted-foreground">
                            <Users className="h-3.5 w-3.5 mr-1" />
                            {record.responsible}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 text-center text-muted-foreground border border-dashed rounded-lg">
                    <FileCheck className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                    <p>暂无维护记录</p>
                  </div>
                )}
              </div>

              <SheetFooter>
                <Button
                  onClick={() => {
                    setIsMaintenanceHistoryDrawerOpen(false);
                    if (selectedMaintenanceEquipment) {
                      handleScheduleMaintenance(selectedMaintenanceEquipment);
                    }
                  }}
                  className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                >
                  <Wrench className="h-4 w-4" />
                  安排新的检修
                </Button>
              </SheetFooter>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </div>
  )
}


