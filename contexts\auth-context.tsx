"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"

interface User {
  username: string;
  role?: string;
  email?: string;
  avatar?: string;
}

interface AuthContextType {
  isAuthenticated: boolean
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  user: User | null
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  login: async () => false,
  logout: () => {},
  user: null,
})

export const useAuth = () => useContext(AuthContext)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // 检查用户是否已登录
  useEffect(() => {
    const checkAuth = () => {
      const isLoggedIn = localStorage.getItem("isLoggedIn") === "true"
      setIsAuthenticated(isLoggedIn)
      
      if (isLoggedIn) {
        const username = localStorage.getItem("username") || "用户"
        const role = localStorage.getItem("userRole") || "管理员"
        const email = localStorage.getItem("userEmail") || "<EMAIL>"
        setUser({ 
          username,
          role,
          email,
          avatar: "" // 默认为空字符串
        })
      } else {
        setUser(null)
      }
      
      setIsLoading(false)
    }
    
    checkAuth()
  }, [])

  // 路由保护 - 确保在组件挂载后执行
  useEffect(() => {
    if (!isLoading) {
      // 如果用户未登录且不在登录页面，则重定向到登录页面
      if (!isAuthenticated && pathname !== "/login") {
        console.log("未登录，重定向到登录页面")
        router.replace("/login")
      }
      
      // 如果用户已登录且在登录页面，则重定向到首页
      if (isAuthenticated && pathname === "/login") {
        console.log("已登录，重定向到首页")
        router.replace("/")
      }
    }
  }, [isAuthenticated, isLoading, pathname, router])

  // 登录函数
  const login = async (username: string, password: string): Promise<boolean> => {
    // 简单的登录验证，实际应用中应该调用API进行验证
    if (username === "admin" && password === "admin123") {
      localStorage.setItem("isLoggedIn", "true")
      localStorage.setItem("username", username)
      localStorage.setItem("userRole", "管理员")
      localStorage.setItem("userEmail", "<EMAIL>")
      
      setIsAuthenticated(true)
      setUser({ 
        username,
        role: "管理员",
        email: "<EMAIL>",
        avatar: ""
      })
      
      // 确保状态更新后再进行路由跳转
      setTimeout(() => {
        router.replace("/")
      }, 300)
      
      return true
    }
    return false
  }

  // 登出函数
  const logout = () => {
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    localStorage.removeItem("userRole")
    localStorage.removeItem("userEmail")
    setIsAuthenticated(false)
    setUser(null)
    router.replace("/login")
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, user }}>
      {children}
    </AuthContext.Provider>
  )
} 