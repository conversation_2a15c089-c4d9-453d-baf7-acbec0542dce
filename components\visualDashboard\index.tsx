"use client"

import { useEffect, useRef, useState } from 'react';
import { Card, Row, Col } from 'antd';
import * as echarts from 'echarts/core';
import {
  <PERSON><PERSON><PERSON>, <PERSON>Chart, PieChart,
  BarSeriesOption, LineSeriesOption, PieSeriesOption
} from 'echarts/charts';
import {
  TitleComponent, TooltipComponent, GridComponent,
  LegendComponent, VisualMapComponent, ToolboxComponent,
  DataZoomComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { UniversalTransition } from 'echarts/features';
import { SafetyOutlined, StarOutlined, BuildOutlined, AndroidOutlined, HomeOutlined, BulbOutlined, AlertOutlined, LineChartOutlined, RiseOutlined, BarChartOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import StatCards from './StatCards';

// 注册必要的组件
echarts.use([
  TitleComponent, TooltipComponent, GridComponent,
  <PERSON>Component, VisualMapComponent, <PERSON>lbox<PERSON>omponent,
  <PERSON>ZoomComponent, CanvasRenderer, UniversalTransition,
  BarChart, LineChart, Pie<PERSON>hart
]);

import styles from './visualDashboard.module.css';

// 引入我们的Material接口
interface Material {
  id: string;
  name: string;
  type: string;
  status: string;
  quantity: number;
  unit: string;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
  minQuantity: number;
  maxQuantity: number;
  location: string;
  supplier: string;
  turnoverRate: number;
}

// 项目接口
interface Project {
  id: string;
  name: string;
  progress: number;
  status: string;
  startDate: string;
  endDate: string;
  manager?: string;
  teamSize?: string;
  budget: string;
}

// 安全记录接口
interface SafetyRecord {
  id: string;
  type: string;
  count: number;
  status: string;
  date: string;
}

// 人员接口
interface Person {
  id: string;
  name: string;
  department: string;
  position: string;
  status: string;
  count: number;
}

// 定义通知类型
interface Notification {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  time: Date;
  id: number;
}

// 智能洞察接口
interface Insight {
  id: number;
  title: string;
  description: string;
  type: 'trend' | 'anomaly' | 'recommendation' | 'alert';
  timestamp: Date;
  module: string;
  importance: 'low' | 'medium' | 'high';
  icon?: React.ReactNode;
  color?: string;
}

export function VisualDashboard() {
  const router = useRouter();

  // 引用物资数据（与原MaterialMaintenance组件相同）
  const [materials, setMaterials] = useState<Material[]>([
    {
      id: '1',
      name: '螺丝',
      type: '标准件',
      status: '正常',
      quantity: 2500,
      unit: '个',
      lastMaintenanceDate: '2025-02-14',
      nextMaintenanceDate: '2025-04-14',
      minQuantity: 500,
      maxQuantity: 3000,
      location: 'A区-01-01',
      supplier: '某某五金有限公司',
      turnoverRate: 92,
    },
    {
      id: '2',
      name: '轴承',
      type: '机械件',
      status: '待维护',
      quantity: 35,
      unit: '个',
      lastMaintenanceDate: '2025-01-14',
      nextMaintenanceDate: '2025-04-14',
      minQuantity: 100,
      maxQuantity: 300,
      location: 'B区-02-03',
      supplier: '某某轴承厂',
      turnoverRate: 45,
    },
    {
      id: '3',
      name: '电机',
      type: '电气件',
      status: '维修中',
      quantity: 8,
      unit: '台',
      lastMaintenanceDate: '2025-03-01',
      nextMaintenanceDate: '2025-04-01',
      minQuantity: 20,
      maxQuantity: 100,
      location: 'C区-03-02',
      supplier: '某某电机厂',
      turnoverRate: 15,
    },
    {
      id: '4',
      name: '传感器',
      type: '电气件',
      status: '正常',
      quantity: 450,
      unit: '个',
      lastMaintenanceDate: '2025-01-20',
      nextMaintenanceDate: '2025-04-10',
      minQuantity: 100,
      maxQuantity: 500,
      location: 'D区-01-05',
      supplier: '某某电子有限公司',
      turnoverRate: 78,
    },
    {
      id: '5',
      name: '液压泵',
      type: '机械件',
      status: '正常',
      quantity: 25,
      unit: '台',
      lastMaintenanceDate: '2025-02-20',
      nextMaintenanceDate: '2025-05-20',
      minQuantity: 15,
      maxQuantity: 40,
      location: 'B区-03-01',
      supplier: '某某机械制造厂',
      turnoverRate: 65,
    },
  ]);

  // 添加人员数据
  const [personnel, setPersonnel] = useState<Person[]>([
    {
      id: "1",
      name: "生产部",
      department: "生产部",
      position: "生产人员",
      status: "在岗",
      count: 1250
    },
    {
      id: "2",
      name: "安全部",
      department: "安全部",
      position: "安全管理",
      status: "在岗",
      count: 380
    },
    {
      id: "3",
      name: "技术部",
      department: "技术部",
      position: "技术人员",
      status: "在岗",
      count: 420
    },
    {
      id: "4",
      name: "机电部",
      department: "机电部",
      position: "机电维护",
      status: "在岗",
      count: 560
    },
    {
      id: "5",
      name: "运输部",
      department: "运输部",
      position: "运输人员",
      status: "在岗",
      count: 480
    },
    {
      id: "6",
      name: "后勤部",
      department: "后勤部",
      position: "后勤人员",
      status: "在岗",
      count: 320
    },
    {
      id: "7",
      name: "行政部",
      department: "行政部",
      position: "行政人员",
      status: "在岗",
      count: 180
    }
  ]);

  // 添加项目数据
  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      name: "主井深部开拓工程",
      progress: 35,
      status: "进行中",
      startDate: "2025-01-01",
      endDate: "2025-08-15",
      manager: "李工",
      teamSize: "145人",
      budget: "8.5亿"
    },
    {
      id: "2",
      name: "选矿厂技改项目",
      progress: 85,
      status: "进行中",
      startDate: "2024-11-01",
      endDate: "2025-04-30",
      manager: "王工",
      teamSize: "98人",
      budget: "3.2亿"
    },
    {
      id: "3",
      name: "尾矿库扩容工程",
      progress: 15,
      status: "进行中",
      startDate: "2025-02-15",
      endDate: "2025-12-31",
      manager: "张工",
      teamSize: "160人",
      budget: "5.8亿"
    },
    {
      id: "4",
      name: "矿区智能化改造",
      progress: 92,
      status: "进行中",
      startDate: "2024-09-01",
      endDate: "2025-03-31",
      manager: "赵工",
      teamSize: "82人",
      budget: "2.8亿"
    },
    {
      id: "5",
      name: "安全监控系统升级",
      progress: 68,
      status: "进行中",
      startDate: "2024-12-15",
      endDate: "2025-05-30",
      manager: "钱工",
      teamSize: "45人",
      budget: "1.5亿"
    },
    {
      id: "6",
      name: "矿井通风系统改造",
      progress: 25,
      status: "进行中",
      startDate: "2025-02-01",
      endDate: "2025-09-30",
      manager: "孙工",
      teamSize: "75人",
      budget: "2.3亿"
    },
    {
      id: "7",
      name: "地面生产系统优化",
      progress: 55,
      status: "进行中",
      startDate: "2024-10-15",
      endDate: "2025-06-30",
      manager: "周工",
      teamSize: "120人",
      budget: "4.2亿"
    }
  ]);

  // 添加安全记录数据
  const [safetyRecords, setSafetyRecords] = useState<SafetyRecord[]>([
    {
      id: "1",
      type: "安全检查",
      count: 156,
      status: "已完成",
      date: "2025-03-14",
    },
    {
      id: "2",
      type: "安全培训",
      count: 42,
      status: "已完成",
      date: "2025-03-13",
    },
    {
      id: "3",
      type: "隐患排查",
      count: 89,
      status: "进行中",
      date: "2025-03-14",
    },
    {
      id: "4",
      type: "应急演练",
      count: 12,
      status: "已完成",
      date: "2025-03-10",
    },
    {
      id: "5",
      type: "设备巡检",
      count: 235,
      status: "进行中",
      date: "2025-03-15",
    },
  ]);

  // 时间显示
  const [currentTime, setCurrentTime] = useState("");

  // 事件通知状态
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // 状态用于控制logo点击动画
  const [logoClicked, setLogoClicked] = useState(false);
  const [showRings, setShowRings] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  // 新增公司信息数组
  const companyInfo = [
    '国家特级资质矿业集团，成立于1985年',
    '年产煤炭3000万吨，营业额超过150亿元',
    '拥有现代化矿井15座，员工总数2.8万人',
    '智能开采技术领先，安全生产记录1200天',
    '国家能源安全战略合作伙伴，绿色矿山示范基地'
  ];

  // Logo点击处理函数增强版
  const handleLogoClick = () => {
    // 增加点击计数
    setClickCount(prev => prev + 1);

    // 设置logo被点击的动画状态
    setLogoClicked(true);

    // 显示扩散环动画
    setShowRings(true);
    setTimeout(() => setShowRings(false), 2000);

    // 创建粒子爆发效果
    if (particlesRef.current) {
      const burstParticles: HTMLDivElement[] = [];

      // 创建30个爆发粒子
      for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.className = styles.burstParticle;

        // 随机大小和不透明度
        const size = Math.random() * 8 + 2;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // 随机颜色
        const hue = 180 + Math.random() * 60; // 青色到蓝色范围
        const saturation = 70 + Math.random() * 30;
        const lightness = 50 + Math.random() * 20;
        particle.style.backgroundColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

        // 从中心向外随机方向发射
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 150 + 50;
        const x = Math.cos(angle) * distance;
        const y = Math.sin(angle) * distance;

        // 设置随机动画时长 (1-2秒)
        const duration = 1 + Math.random();
        particle.style.animation = `burstOut ${duration}s ease-out forwards`;

        // 设置初始位置和目标位置
        particle.style.left = '50%';
        particle.style.top = '50%';
        particle.style.transform = `translate(-50%, -50%) scale(0.2)`;

        // 使用CSS变量存储目标位置，供keyframe动画使用
        particle.style.setProperty('--target-x', `${x}px`);
        particle.style.setProperty('--target-y', `${y}px`);

        if (particlesRef.current) {
          particlesRef.current.appendChild(particle);
          burstParticles.push(particle);
        }

        // 动画结束后移除粒子
        setTimeout(() => {
          try {
            if (particle.parentNode) {
              particle.parentNode.removeChild(particle);
            }
          } catch (e) {
            console.log('移除爆发粒子时发生错误，可能已被移除', e);
          }
        }, duration * 1000);
      }
    }

    // 触发通知
    triggerCustomNotification({
      title: "企业信息",
      message: companyInfo[clickCount % companyInfo.length],
      type: 'info'
    });

    // 5秒后重置状态
    setTimeout(() => {
      setLogoClicked(false);
    }, 5000);
  };

  // 粒子效果的引用
  const particlesRef = useRef<HTMLDivElement>(null);

  // 添加粒子效果
  useEffect(() => {
    if (!particlesRef.current) return;

    // 清除旧粒子
    if (particlesRef.current) {
      while(particlesRef.current.firstChild) {
        particlesRef.current.removeChild(particlesRef.current.firstChild);
      }
    }

    // 创建粒子
    const particleCount = 100;
    const particles: HTMLDivElement[] = []; // 跟踪已创建的粒子元素

    for (let i = 0; i < particleCount; i++) {
      // 如果在创建过程中组件已卸载，则停止创建
      if (!particlesRef.current) break;

      const particle = document.createElement('div');
      particle.className = styles.particle;

      // 随机大小和不透明度
      const size = Math.random() * 5 + 1;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // 随机位置在整个地图区域，不再仅限于Logo周围
      let x, y;

      // 更广泛的分布
      x = Math.random() * 100 - 50; // -50% 到 50%
      y = Math.random() * 100 - 50; // -50% 到 50%

      // 创建粒子群分布效果
      const angle = Math.random() * Math.PI * 2;
      const distance = 100 + Math.random() * 300;

      // 15%的粒子在Logo附近
      if (Math.random() < 0.15) {
        x = Math.cos(angle) * (50 + Math.random() * 50);
        y = Math.sin(angle) * (50 + Math.random() * 50);
      }
      // 其余的随机分布在地图各处
      else {
        x = Math.cos(angle) * distance;
        y = Math.sin(angle) * distance;
      }

      particle.style.left = `calc(50% + ${x}px)`;
      particle.style.top = `calc(50% + ${y}px)`;

      // 随机动画延迟和持续时间，创建更自然的效果
      particle.style.animationDelay = `${Math.random() * 10}s`;
      particle.style.animationDuration = `${10 + Math.random() * 15}s`;

      // 随机不透明度
      particle.style.opacity = (0.1 + Math.random() * 0.6).toString();

      if (particlesRef.current) {
        particlesRef.current.appendChild(particle);
        particles.push(particle); // 添加到跟踪数组
      }
    }

    // 清理函数
    return () => {
      // 安全地移除所有粒子
      particles.forEach(particle => {
        try {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        } catch (e) {
          console.log('清理粒子时发生错误，可能已被移除', e);
        }
      });
    };
  }, []);

  // 进度条动画初始化
  useEffect(() => {
    // 使用setTimeout序列化动画，形成流水效果
    const initialDelay = setTimeout(() => {
      const progressBars = document.querySelectorAll(`.${styles.progressBar}`);

      // 确保在组件存在时才进行操作
      if (progressBars.length > 0) {
        progressBars.forEach((bar: any) => {
          bar.style.width = '0%';
        });

        progressBars.forEach((bar: any, index) => {
          const timer = setTimeout(() => {
            try {
              if (bar && bar.parentElement) { // 确保元素仍然存在
                const targetWidth = bar.getAttribute('data-progress')
                  || (bar.parentElement.nextElementSibling
                      && bar.parentElement.nextElementSibling.querySelector(`.${styles.progressPercent}`)
                      ? bar.parentElement.nextElementSibling.querySelector(`.${styles.progressPercent}`).textContent.replace('%', '')
                      : '0');
                bar.style.width = `${targetWidth}%`;
              }
            } catch (e) {
              console.log('设置进度条时发生错误', e);
            }
          }, index * 300); // 每个进度条间隔300ms
        });
      }
    }, 500);

    // 清理函数
    return () => {
      clearTimeout(initialDelay);
    };
  }, []);

  // 随机触发事件通知
  useEffect(() => {
    // 存储所有清理函数以便清理
    const cleanupFunctions: (() => void)[] = [];

    const interval = setInterval(() => {
      if (Math.random() > 0.7) {
        const possibleEvents = [
          "系统检测到新的物资入库请求",
          "北京仓库物资调拨已完成",
          "3号仓库温湿度超出预警值",
          "月度物资盘点将于明日开始",
          "系统更新维护计划已发布"
        ];
        const randomEvent = possibleEvents[Math.floor(Math.random() * possibleEvents.length)];
        const cleanupFn = triggerCustomNotification({
          title: "系统通知",
          message: randomEvent,
          type: 'info' as const
        });

        // 将清理函数存储起来
        if (cleanupFn) {
          cleanupFunctions.push(cleanupFn);
        }
      }
    }, 15000);

    // 返回清理函数
    return () => {
      clearInterval(interval);
      // 清理所有通知计时器
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (e) {
          console.log('清理通知计时器时发生错误', e);
        }
      });
    };
  }, []);

  // 自定义通知触发函数
  const triggerCustomNotification = (notification: { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' }) => {
    const newNotification = {
      ...notification,
      time: new Date(),
      id: Date.now() // 添加唯一ID以便安全地追踪和移除通知
    };

    // 使用函数式更新，确保使用最新的状态
    setNotifications(prev => [...prev, newNotification]);

    // 修改为3秒后自动移除通知
    const timerId = setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== newNotification.id));
    }, 3000);

    // 返回清理函数
    return () => {
      try {
        clearTimeout(timerId);
      } catch (e) {
        console.log('清理通知定时器时发生错误', e);
      }
    };
  };

  // 手动触发通知函数
  const triggerNotification = () => {
    if (typeof (window as any).triggerDashboardNotification === 'function') {
      (window as any).triggerDashboardNotification();
    }
  };

  // 定期模拟数据更新功能
  useEffect(() => {
    // 模拟数据更新函数
    const updateData = () => {
      // 随机更新项目进度
      setProjects(prev => prev.map(project => {
        // 有75%概率不更新，25%概率更新进度
        if (Math.random() > 0.25) return project;

        // 计算新进度，允许进度在合理范围内小幅变化
        let newProgress = project.progress + Math.floor(Math.random() * 6) - 2; // -2到+3之间的变化
        newProgress = Math.max(0, Math.min(100, newProgress)); // 确保进度在0-100范围内

        return {
          ...project,
          progress: newProgress,
          // 如果已完成则修改状态
          status: newProgress >= 100 ? "已完成" : newProgress > 0 ? "进行中" : "待开始"
        };
      }));

      // 随机更新安全记录
      setSafetyRecords(prev => prev.map(record => {
        // 有80%概率不更新，20%概率更新检查数量
        if (Math.random() > 0.2) return record;

        // 随机增加1-3的检查数量
        const countIncrease = Math.floor(Math.random() * 3) + 1;
        return {
          ...record,
          count: record.count + countIncrease
        };
      }));
    };

    // 设置5分钟更新一次数据
    const dataUpdateInterval = 5 * 60 * 1000; // 5分钟
    const dataTimer = setInterval(updateData, dataUpdateInterval);

    // 清理函数
    return () => {
      clearInterval(dataTimer);
    };
  }, []);

  // 更新时间，只在组件加载和每分钟更新一次，而不是每秒
  useEffect(() => {
    // 更新当前时间的函数
    const updateTime = () => {
      const time = new Date();
      const year = time.getFullYear();
      const month = time.getMonth() + 1;
      const date = time.getDate();
      const hours = time.getHours();
      const minutes = time.getMinutes();

      setCurrentTime(
        `${year}年${month}月${date}日 ${hours < 10 ? '0' + hours : hours}:${
          minutes < 10 ? '0' + minutes : minutes
        }`
      );
    };

    // 立即更新一次时间
    updateTime();

    // 计算到下一分钟的毫秒数
    const now = new Date();
    const delay = (60 - now.getSeconds()) * 1000 - now.getMilliseconds();

    // 先设置一个延迟，确保在下一分钟的整点开始定时器
    const initialTimeout = setTimeout(() => {
      updateTime();

      // 然后每分钟更新一次
      const interval = setInterval(updateTime, 60000);

      // 返回清理函数
      return () => {
        clearInterval(interval);
      };
    }, delay);

    // 清理初始延迟
    return () => {
      clearTimeout(initialTimeout);
    };
  }, []);

  // 计算统计数据
  const stats = {
    total: materials.length,
    normal: materials.filter(item => item.status === '正常').length,
    pending: materials.filter(item => item.status === '待维护').length,
    repairing: materials.filter(item => item.status === '维修中').length,
    // 新增综合统计数据
    totalProjects: projects.length,
    ongoingProjects: projects.filter(p => p.status === "进行中").length,
    totalSafetyChecks: safetyRecords.reduce((acc, item) => acc + item.count, 0),
    totalPersonnel: personnel.reduce((acc, dept) => acc + dept.count, 0),
    onDutyPersonnel: personnel.reduce((acc, dept) => acc + (dept.status === "在岗" ? dept.count : 0), 0)
  };

  // 图表引用
  const barChartRef = useRef<HTMLDivElement>(null);
  const lineChartRef = useRef<HTMLDivElement>(null);
  const pieChartRef = useRef<HTMLDivElement>(null);
  const mapChartRef = useRef<HTMLDivElement>(null);
  const line2ChartRef = useRef<HTMLDivElement>(null);
  const pie2ChartRef = useRef<HTMLDivElement>(null);

  // 图表初始化
  useEffect(() => {
    if (!barChartRef.current || !lineChartRef.current ||
        !pieChartRef.current || !mapChartRef.current ||
        !line2ChartRef.current || !pie2ChartRef.current) {
      return; // 如果任何一个图表容器不存在，则退出
    }

    // 初始化echarts实例
    let barChart: echarts.ECharts | null = null;
    let lineChart: echarts.ECharts | null = null;
    let pieChart: echarts.ECharts | null = null;
    let mapChart: echarts.ECharts | null = null;
    let line2Chart: echarts.ECharts | null = null;
    let pie2Chart: echarts.ECharts | null = null;

    try {
      barChart = echarts.init(barChartRef.current);
      lineChart = echarts.init(lineChartRef.current);
      pieChart = echarts.init(pieChartRef.current);
      mapChart = echarts.init(mapChartRef.current);
      line2Chart = echarts.init(line2ChartRef.current);
      pie2Chart = echarts.init(pie2ChartRef.current);
    } catch (e) {
      console.log('初始化图表时发生错误', e);
      return; // 如果初始化失败，退出
    }

    // 物资类型统计
    const materialTypes = Array.from(new Set(materials.map(item => item.type)));
    const typeQuantities = materialTypes.map(type => {
      return materials.filter(item => item.type === type)
        .reduce((sum, item) => sum + item.quantity, 0);
    });

    // 项目名称和部门统计
    const projectNames = projects.map(project => project.name);

    // 部门人员统计
    const departments = Array.from(new Set(personnel.map(p => p.department)));
    const departmentCounts = departments.map(dept => {
      const deptPersonnel = personnel.find(p => p.department === dept);
      return deptPersonnel ? deptPersonnel.count : 0;
    });

    // 左侧柱状图配置
    const barOption = {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: '#20dbff'
      }, {
        offset: 1,
        color: '#0064c8'
      }]),
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(32,219,255,0.1)'
          }
        },
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '0%',
        top: '10px',
        right: '0%',
        bottom: '4%',
        containLabel: true
      },
      xAxis: [{
        type: 'category',
        data: departments,
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.6)",
          fontSize: 12,
          rotate: 30
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        }
      }],
      yAxis: [{
        type: 'value',
        axisLabel: {
          color: "rgba(255,255,255,.6)",
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: "rgba(255,255,255,.1)"
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255,255,255,.1)"
          }
        }
      }],
      series: [{
        name: '部门人数',
        type: 'bar',
        barWidth: '35%',
        data: departmentCounts,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(32,219,255,0.2)',
          shadowBlur: 10
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: '#5eead4'
              }, {
                offset: 1,
                color: '#0ea5e9'
              }]
            }
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut'
      }]
    };

    // 左侧折线图配置
    const lineOption = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        textStyle: {
          color: 'rgba(255,255,255,.5)',
          fontSize: '12',
        }
      },
      grid: {
        left: '10',
        top: '30',
        right: '10',
        bottom: '10',
        containLabel: true
      },
      xAxis: [{
        type: 'category',
        boundaryGap: false,
        data: safetyRecords.map(record => record.date),
        axisLabel: {
          textStyle: {
            color: "rgba(255,255,255,.6)",
            fontSize: 12
          }
        },
        axisLine: {
          lineStyle: {
            color: "rgba(255,255,255,.2)"
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
            type: 'dashed'
          }
        }
      }],
      yAxis: [{
        type: 'value',
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: "rgba(255,255,255,.1)"
          }
        },
        axisLabel: {
          textStyle: {
            color: "rgba(255,255,255,.6)",
            fontSize: 12
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255,255,255,.1)"
          }
        }
      }],
      series: [
        {
          name: '安全检查',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          showSymbol: false,
          symbolSize: 8,
          lineStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: '#20dbff'
              }, {
                offset: 1,
                color: '#0064c8'
              }]
            },
            width: 3,
            shadowColor: 'rgba(32,219,255,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(32,219,255,0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(32,219,255,0.1)"
                }
              ],
              false
            ),
            shadowColor: "rgba(32,219,255,0.1)",
            shadowBlur: 20
          },
          emphasis: {
            scale: true,
            focus: 'series',
            itemStyle: {
              color: '#fff',
              borderColor: '#20dbff',
              borderWidth: 2,
              shadowColor: 'rgba(32,219,255,0.8)',
              shadowBlur: 10
            }
          },
          data: safetyRecords.map(record => record.count)
        }
      ]
    };

    // 饼图配置
    const pieOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: {
          color: "rgba(255,255,255,.6)",
        },
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 12
      },
      series: [
        {
          name: '物资分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 2,
            shadowColor: 'rgba(32,219,255,0.2)',
            shadowBlur: 10
          },
          label: {
            show: false,
            position: 'center',
            color: '#fff',
            fontSize: 14
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#fff'
            },
            itemStyle: {
              shadowColor: 'rgba(32,219,255,0.5)',
              shadowBlur: 20
            }
          },
          labelLine: {
            show: false
          },
          data: materialTypes.map((type, index) => ({
            value: typeQuantities[index],
            name: type,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: ['#20dbff', '#0064c8', '#5eead4', '#0ea5e9'][index % 4]
              }, {
                offset: 1,
                color: ['#0064c8', '#20dbff', '#0ea5e9', '#5eead4'][index % 4]
              }])
            }
          }))
        }
      ]
    };

    // 替代中心地图配置（使用环形图）
    const alternativeMapOption = {
      tooltip: {
        trigger: "item",
        formatter: function(params: any) {
          return `<div style="padding: 8px;">
            <div style="font-weight:bold;margin-bottom:8px;color:#20dbff;">${params.name}</div>
            <div style="margin-bottom:4px;">进度: ${params.value}%</div>
            <div style="margin-bottom:4px;">状态: ${params.data.d}</div>
            <div style="margin-bottom:4px;">开始: ${params.data.startDate || '-'}</div>
            <div>预计完成: ${params.data.endDate || '-'}</div>
          </div>`;
        },
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        show: false  // 设置为false以隐藏图例
      },
      color: [
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#20dbff' },
          { offset: 1, color: '#0064c8' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#5eead4' },
          { offset: 1, color: '#0ea5e9' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#fcd34d' },
          { offset: 1, color: '#f59e0b' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#a78bfa' },
          { offset: 1, color: '#7c3aed' }
        ])
      ],
      series: [
        {
          name: "项目进度",
          type: "pie",
          radius: ["40%", "60%"],
          center: ["50%", "50%"],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 6,
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 2,
            shadowColor: 'rgba(32,219,255,0.2)',
            shadowBlur: 10
          },
          label: {
            show: true,
            position: "outside",
            formatter: function(params: any) {
              const name = params.name.length > 5 ? params.name.substring(0, 5) + '...' : params.name;
              return name + ": " + params.value + "%";
            },
            color: "rgba(255,255,255,.8)",
            fontSize: 12,
            backgroundColor: 'rgba(0,0,0,0.3)',
            borderRadius: 4,
            padding: [3, 5]
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 20,
            smooth: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.4)',
              width: 1
            }
          },
          data: projects.map(project => ({
            name: project.name,
            value: project.progress,
            d: project.status,
            startDate: project.startDate,
            endDate: project.endDate
          }))
        }
      ]
    };

    // 右侧柱状图配置
    const bar2Option = {
      grid: {
        top: "10%",
        left: "22%",
        bottom: "10%"
      },
      xAxis: {
        show: false
      },
      yAxis: [
        {
          type: "category",
          inverse: true,
          data: projectNames,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: "#fff"
          }
        },
        {
          data: projects.map(p => p.progress),
          inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: "#999"
          }
        }
      ],
      series: [
        {
          name: "项目进度",
          type: "bar",
          data: projects.map(p => p.progress),
          yAxisIndex: 0,
          itemStyle: {
            barBorderRadius: 20,
            color: function(params: any) {
              return {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#00c9e0"
                  },
                  {
                    offset: 1,
                    color: "#005fc1"
                  }
                ]
              };
            }
          },
          barWidth: 15,
          label: {
            show: true,
            position: "right",
            formatter: "{c}%"
          }
        }
      ]
    };

    // 右侧折线图配置
    const line2Option = {
      tooltip: {
        trigger: "axis",
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        top: "0%",
        data: ["营业收入", "净利润"],
        textStyle: {
          color: "rgba(255,255,255,.5)",
          fontSize: "12"
        }
      },
      grid: {
        left: "10",
        top: "30",
        right: "10",
        bottom: "10",
        containLabel: true
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
          axisLabel: {
            textStyle: {
              color: "rgba(255,255,255,.6)",
              fontSize: 12
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,.2)"
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.1)',
              type: 'dashed'
            }
          }
        }
      ],
      yAxis: [
        {
          type: "value",
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,.1)"
            }
          },
          axisLabel: {
            textStyle: {
              color: "rgba(255,255,255,.6)",
              fontSize: 12
            },
            formatter: '{value} 万'
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,.1)"
            }
          }
        }
      ],
      series: [
        {
          name: "营业收入",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: '#20dbff'
              }, {
                offset: 1,
                color: '#0064c8'
              }]
            },
            width: 3,
            shadowColor: 'rgba(32,219,255,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(32,219,255,0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(32,219,255,0.1)"
                }
              ],
              false
            ),
            shadowColor: "rgba(32,219,255,0.1)",
            shadowBlur: 20
          },
          emphasis: {
            scale: true,
            focus: 'series',
            itemStyle: {
              color: '#fff',
              borderColor: '#20dbff',
              borderWidth: 2,
              shadowColor: 'rgba(32,219,255,0.8)',
              shadowBlur: 10
            }
          },
          data: [820, 932, 901, 1234, 1290, 1330, 1520, 1864, 2030, 2100, 2432, 2690]
        },
        {
          name: "净利润",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: '#5eead4'
              }, {
                offset: 1,
                color: '#0ea5e9'
              }]
            },
            width: 3,
            shadowColor: 'rgba(94,234,212,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(94,234,212,0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(94,234,212,0.1)"
                }
              ],
              false
            ),
            shadowColor: "rgba(94,234,212,0.1)",
            shadowBlur: 20
          },
          emphasis: {
            scale: true,
            focus: 'series',
            itemStyle: {
              color: '#fff',
              borderColor: '#5eead4',
              borderWidth: 2,
              shadowColor: 'rgba(94,234,212,0.8)',
              shadowBlur: 10
            }
          },
          data: [120, 182, 191, 264, 290, 330, 410, 464, 530, 610, 680, 810]
        }
      ]
    };

    // 右侧饼图配置
    const pie2Option = {
      color: [
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#20dbff' },
          { offset: 1, color: '#0064c8' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#5eead4' },
          { offset: 1, color: '#0ea5e9' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#fcd34d' },
          { offset: 1, color: '#f59e0b' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#a78bfa' },
          { offset: 1, color: '#7c3aed' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#f472b6' },
          { offset: 1, color: '#db2777' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#4ade80' },
          { offset: 1, color: '#16a34a' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#fb923c' },
          { offset: 1, color: '#ea580c' }
        ])
      ],
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c}人 ({d}%)",
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: '#20dbff',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        bottom: "0%",
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: "rgba(255,255,255,.5)",
          fontSize: "12"
        }
      },
      series: [
        {
          name: "部门人员",
          type: "pie",
          radius: ["10%", "70%"],
          center: ["50%", "40%"],
          roseType: "radius",
          itemStyle: {
            borderRadius: 5,
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 2,
            shadowColor: 'rgba(32,219,255,0.2)',
            shadowBlur: 10
          },
          label: {
            formatter: '{b}: {c}人\n{d}%',
            color: "rgba(255,255,255,.6)",
            fontSize: 12
          },
          labelLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)"
            },
            smooth: 0.2,
            length: 10,
            length2: 20
          },
          emphasis: {
            scale: true,
            scaleSize: 10,
            itemStyle: {
              shadowColor: 'rgba(32,219,255,0.8)',
              shadowBlur: 20
            }
          },
          data: departments.map((dept, index) => ({
            value: departmentCounts[index],
            name: dept
          }))
        }
      ]
    };

    // 设置图表配置
    try {
      if (barChart) {
        barChart.setOption(barOption);
      }

      if (lineChart) {
        lineChart.setOption(lineOption);
      }

      if (pieChart) {
        pieChart.setOption(pieOption);
      }

      if (mapChart) {
        (alternativeMapOption as any).animation = true;
        (alternativeMapOption as any).animationDuration = 2000;
        (alternativeMapOption as any).animationEasing = 'elasticOut';
        mapChart.setOption(alternativeMapOption);
      }

      if (line2Chart) {
        (line2Option as any).animation = true;
        (line2Option as any).animationDuration = 2000;
        (line2Option as any).animationEasing = 'cubicInOut';
        line2Chart.setOption(line2Option);
      }

      if (pie2Chart) {
        (pie2Option as any).animation = true;
        (pie2Option as any).animationDuration = 2000;
        (pie2Option as any).animationEasing = 'quadraticOut';
        pie2Chart.setOption(pie2Option);
      }
    } catch (e) {
      console.log('设置图表配置时发生错误', e);
    }

    // 窗口大小变化时，图表自适应
    const resizeHandler = () => {
      try {
        // 检查图表和DOM元素是否还存在
        if (barChartRef.current && barChart) barChart.resize();
        if (lineChartRef.current && lineChart) lineChart.resize();
        if (pieChartRef.current && pieChart) pieChart.resize();
        if (mapChartRef.current && mapChart) mapChart.resize();
        if (line2ChartRef.current && line2Chart) line2Chart.resize();
        if (pie2ChartRef.current && pie2Chart) pie2Chart.resize();
      } catch (e) {
        console.log('调整图表大小时发生错误', e);
      }
    };

    window.addEventListener('resize', resizeHandler);

    // 添加自动轮播效果，仅当mapChart存在时
    let rotationTimer: NodeJS.Timeout | null = null;
    if (mapChartRef.current && mapChart) {
      let currentIndex = -1;
      const highlightPie = () => {
        try {
          if (!mapChartRef.current) return;

          const chart = echarts.getInstanceByDom(mapChartRef.current);
          if (!chart) return;

          currentIndex = (currentIndex + 1) % projects.length;
          chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            name: projects[currentIndex].name
          });

          // 2秒后取消高亮并高亮下一个
          setTimeout(() => {
            try {
              if (mapChartRef.current) {
                const chart = echarts.getInstanceByDom(mapChartRef.current);
                if (chart) {
                  chart.dispatchAction({
                    type: 'downplay',
                    seriesIndex: 0,
                    name: projects[currentIndex].name
                  });
                }
              }
            } catch (e) {
              console.log('取消高亮项目时发生错误', e);
            }
          }, 1800);
        } catch (e) {
          console.log('高亮项目时发生错误', e);
        }
      };

      // 开始自动轮播
      rotationTimer = setInterval(highlightPie, 2500);
    }

    // 组件卸载时的清理函数
    return () => {
      try {
        // 清理自动轮播定时器
        if (rotationTimer) {
          clearInterval(rotationTimer);
        }

        // 移除窗口调整事件监听
        window.removeEventListener('resize', resizeHandler);

        // 安全地释放图表资源
        const disposeChart = (chart: echarts.ECharts | null) => {
          if (chart) {
            try {
              chart.dispose();
            } catch (e) {
              console.log('释放图表资源时发生错误', e);
            }
          }
        };

        disposeChart(barChart);
        disposeChart(lineChart);
        disposeChart(pieChart);
        disposeChart(mapChart);
        disposeChart(line2Chart);
        disposeChart(pie2Chart);
      } catch (e) {
        console.log('清理图表资源时发生错误', e);
      }
    };
  }, [materials, projects, safetyRecords, personnel]); // 只在这些数据变化时重新渲染图表

  // 添加进度条脉冲效果动画
  useEffect(() => {
    const pulseElements = document.querySelectorAll(`.${styles.pulseBar}`);
    const intervals: NodeJS.Timeout[] = [];

    // 为每个进度条添加脉冲动画
    pulseElements.forEach(element => {
      const pulse = () => {
        try {
          if (element && document.body.contains(element)) {
            element.classList.add(styles.pulsing);
            setTimeout(() => {
              if (element && document.body.contains(element)) {
                element.classList.remove(styles.pulsing);
              }
            }, 1500);
          }
        } catch (e) {
          console.log('进度条脉冲效果时发生错误', e);
        }
      };

      // 初始运行一次
      pulse();

      // 每5秒运行一次脉冲效果
      const intervalId = setInterval(pulse, 5000);
      intervals.push(intervalId);
    });

    return () => {
      // 清理所有间隔计时器
      intervals.forEach(interval => {
        try {
          clearInterval(interval);
        } catch (e) {
          console.log('清理脉冲效果计时器时发生错误', e);
        }
      });
    };
  }, []);

  // 添加项目详情状态
  const [activeProject, setActiveProject] = useState<string | null>(null);

  // 处理项目卡片点击
  const handleProjectCardClick = (id: string) => {
    setActiveProject(activeProject === id ? null : id);
    const project = projects.find(p => p.id === id);
    if (project) {
      // 将环形图高亮对应的项目
      if (mapChartRef.current) {
        const chart = echarts.getInstanceByDom(mapChartRef.current);
        if (chart) {
          chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            name: project.name
          });
        }
      }

      triggerCustomNotification({
        title: `项目信息`,
        message: `${project.name}，进度${project.progress}%，${project.status}`,
        type: 'info'
      });
    }
  };

  // 处理返回首页
  const handleGoHome = () => {
    router.push('/');
  };

  // 添加智能洞察数据
  const [insights, setInsights] = useState<Insight[]>([
    {
      id: 1,
      title: '能源使用异常检测',
      description: '检测到B区用电量较上周同期增加了28%，可能存在设备异常耗电情况',
      type: 'anomaly',
      timestamp: new Date(),
      module: '能源管理',
      importance: 'high',
      icon: <AlertOutlined style={{ color: '#ff4d4f' }} />,
      color: '#ff4d4f',
    },
    {
      id: 2,
      title: '财务趋势预测',
      description: '根据当前数据分析，Q2营收预计将增长12.5%，超过预期目标',
      type: 'trend',
      timestamp: new Date(),
      module: '财务管理',
      importance: 'medium',
      icon: <RiseOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a',
    },
    {
      id: 3,
      title: '设备维护建议',
      description: '3号生产线设备预计在14天内需要进行预防性维护，建议提前安排',
      type: 'recommendation',
      timestamp: new Date(),
      module: '设备管理',
      importance: 'medium',
      icon: <BulbOutlined style={{ color: '#faad14' }} />,
      color: '#faad14',
    },
    {
      id: 4,
      title: '项目风险预警',
      description: '主厂房建设项目进度已落后计划7.2%，建议增加资源投入',
      type: 'alert',
      timestamp: new Date(),
      module: '项目管理',
      importance: 'high',
      icon: <AlertOutlined style={{ color: '#ff4d4f' }} />,
      color: '#ff4d4f',
    },
    {
      id: 5,
      title: '物资库存优化',
      description: '分析发现可优化5种主要物资的库存水平，预计可节省12%库存成本',
      type: 'recommendation',
      timestamp: new Date(),
      module: '物资管理',
      importance: 'low',
      icon: <BulbOutlined style={{ color: '#1890ff' }} />,
      color: '#1890ff',
    },
  ]);

  // 添加智能分析功能状态
  const [analysisActiveTab, setAnalysisActiveTab] = useState('today');
  const [showAnalyticsSidebar, setShowAnalyticsSidebar] = useState(false);

  return (
    <div className={styles.dashboard}>
      {/* 头部 */}
      <header className={styles.header}>
        <div className={styles.goHomeButton} onClick={handleGoHome}>
          <HomeOutlined />
          <span>返回首页</span>
        </div>
        <h1>综合可视化大屏</h1>
        <div className={styles.showTime}>{currentTime}</div>
      </header>

      {/* 页面主体 */}
      <section className={styles.mainbox}>
        {/* 左侧 */}
        <div className={styles.column}>
          <div className={`${styles.panel} ${styles.bar}`}>
            <h2>部门人员分布</h2>
            <div className={styles.chart} ref={barChartRef}></div>
            <div className={styles.panelFooter}></div>
          </div>
          <div className={`${styles.panel} ${styles.line}`}>
            <h2>近期安全记录</h2>
            <div className={styles.chart} ref={lineChartRef}></div>
            <div className={styles.panelFooter}></div>
          </div>
          <div className={`${styles.panel} ${styles.pie}`}>
            <h2>物资类型分布</h2>
            <div className={styles.chart} ref={pieChartRef}></div>
            <div className={styles.panelFooter}></div>
          </div>
        </div>

        {/* 中间 */}
        <div className={styles.column}>
          <div className={styles.no}>
            {/* 替换原有的统计卡片区域 */}
            <StatCards stats={stats} />
            
            {/* 中心地图区域 - 保持在中央位置 */}
            <div className={styles.map}>
              <div className={styles.map1}></div>
              <div className={styles.map2}></div>
              <div className={styles.map3}></div>

              <div className={styles.particlesContainer} ref={particlesRef}></div>

              {/* 中心LOGO - 保持在页面正中央 */}
              <div
                className={`${styles.centerLogo} ${logoClicked ? styles.logoClicked : ''}`}
                onClick={handleLogoClick}
              >
                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="currentColor">
                  <path d="M822.8 335.4L727.9 254l-2 3-183 395.7 27.8-255.2 121.6-***********.2-121.7 85.3-112.5 274.6-49.4-79.4-129.5 159.8-95.5 234.8h895.8zM175.6 959.8h673.9l41.9-67.9H133.6zM107.7 850.1h809.7l41.9-67.9H65.7z" />
                </svg>
                <div className={styles.logoText}>矿业管理系统</div>

                {logoClicked && (
                  <div className={styles.companyInfo}>
                    {companyInfo[clickCount]}
                  </div>
                )}
              </div>

              {/* 添加点击时的扩散环效果 */}
              {showRings && (
                <>
                  <div className={styles.ring1}></div>
                  <div className={styles.ring2}></div>
                  <div className={styles.ring3}></div>
                </>
              )}

              <div className={styles.chart} ref={mapChartRef}></div>

              {/* 添加地图说明文字 */}
              <div className={styles.mapDescription}>
                点击环形图查看各项目详情 · 点击中央LOGO了解更多企业信息
              </div>
            </div>

            {/* 统计卡片区域 - 增加项目状态摘要 */}
            <div className={styles.statusSummary}>
              <div className={styles.summaryHeader}>
                <h3>项目状态摘要</h3>
                <div className={styles.summaryInfo}>共 {projects.length} 个项目，{stats.ongoingProjects} 个进行中</div>
              </div>
              <div className={styles.summaryCards}>
                {['进行中', '待开始', '已完成'].map(status => {
                  const count = projects.filter(p => p.status === status).length;
                  const percent = Math.round((count / projects.length) * 100);
                  return (
                    <div key={status} className={styles.summaryCard}>
                      <div className={styles.summaryTitle}>{status}</div>
                      <div className={styles.summaryCount}>{count}</div>
                      <div className={styles.summaryPercent}>{percent}%</div>
                      <div
                        className={styles.summaryBar}
                        style={{
                          width: `${percent}%`,
                          backgroundColor: status === '进行中' ? '#1890ff' :
                                          status === '待开始' ? '#faad14' : '#52c41a'
                        }}
                      ></div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧 */}
        <div className={styles.column}>
          <div className={`${styles.panel} ${styles.line2}`}>
            <h2>财务分析趋势</h2>
            <div className={styles.chart} ref={line2ChartRef}></div>
            <div className={styles.panelFooter}></div>
          </div>

          <div className={`${styles.panel} ${styles.pie2}`}>
            <h2>部门人员分布</h2>
            <div className={styles.chart} ref={pie2ChartRef}></div>
            <div className={styles.panelFooter}></div>
          </div>

          {/* 添加物资周转率面板 */}
          <div className={`${styles.panel} ${styles.materialPanel}`}>
            <h2>物资周转统计</h2>
            <div className={styles.materialTurnoverContainer}>
              {materials.map((material, index) => (
                <div key={index} className={styles.turnoverItem}>
                  <div className={styles.turnoverTitle}>{material.name}</div>
                  <div className={styles.turnoverBarContainer}>
                    <div
                      className={styles.turnoverBar}
                      style={{
                        width: `${material.turnoverRate}%`,
                        backgroundColor: `hsl(${material.turnoverRate * 1.2}, 70%, 45%)`
                      }}
                    />
                  </div>
                  <div className={styles.turnoverValue}>{material.turnoverRate}%</div>
                </div>
              ))}
            </div>
            <div className={styles.panelFooter}></div>
          </div>
        </div>
      </section>

      {/* 测试按钮 - 仅在开发环境显示 */}
      {process.env.NODE_ENV !== 'production' && (
        <button
          onClick={triggerNotification}
          style={{
            position: 'fixed',
            bottom: '10px',
            right: '10px',
            padding: '8px 16px',
            background: 'rgba(0, 0, 0, 0.6)',
            color: '#fff',
            border: '1px solid #1890ff',
            borderRadius: '4px',
            cursor: 'pointer',
            zIndex: 1000,
            fontSize: '12px'
          }}
        >
          测试通知
        </button>
      )}

      {/* 通知区域 */}
      <div className={styles.notificationArea}>
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`${styles.notification} ${styles[`notification-${notification.type}`]}`}
          >
            <div className={styles.notificationTitle}>{notification.title}</div>
            <div className={styles.notificationMessage}>{notification.message}</div>
            <div className={styles.notificationTime}>
              {notification.time.toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}