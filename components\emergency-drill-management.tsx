"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  CheckCircle,
  AlertTriangle,
  FileCheck,
  Eye,
  Camera,
  Video,
  BarChart2,
  Calendar,
  RefreshCcw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet"

interface EmergencyDrill {
  id: string
  name: string
  type: string
  plan: string
  location: string
  scheduledDate: string
  scheduledTime: string
  status: string
  participants: number
  responsible: string
  progress: number
  description?: string
  objectives?: string
  requirements?: string
  attachments?: string[]
  evaluation?: DrillEvaluation
  disabled?: boolean
}

interface DrillEvaluation {
  id: string
  drillId: string
  date: string
  score: number
  strengths: string
  weaknesses: string
  improvements: string
  evaluator: string
  attachments?: string[]
}

interface Statistics {
  total: number
  planned: number
  inProgress: number
  completed: number
  thisMonth: number
  thisYear: number
}

export function EmergencyDrillManagement() {
  const { toast } = useToast()
  const [isAddDrillOpen, setIsAddDrillOpen] = useState(false)
  const [isEditDrillOpen, setIsEditDrillOpen] = useState(false)
  const [isViewDrillOpen, setIsViewDrillOpen] = useState(false)
  const [isEvaluationOpen, setIsEvaluationOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [selectedDrill, setSelectedDrill] = useState<EmergencyDrill | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<string>("")
  const [formData, setFormData] = useState<Partial<EmergencyDrill>>({})
  const [evaluationData, setEvaluationData] = useState<Partial<DrillEvaluation>>({})
  const [emergencyDrills, setEmergencyDrills] = useState<EmergencyDrill[]>([
    {
      id: "1",
      name: "矿区综合应急演练",
      type: "综合演练",
      plan: "矿区应急预案-2025",
      location: "主矿区",
      scheduledDate: "2025-03-20",
      scheduledTime: "09:00",
      status: "计划中",
      participants: 50,
      responsible: "张三",
      progress: 0,
      description: "针对矿区可能发生的各类突发事件进行综合演练",
      objectives: "提高应急反应能力，检验应急预案的可行性",
      requirements: "各部门需提前准备，确保人员到位",
      disabled: false
    },
    {
      id: "2",
      name: "消防疏散演练",
      type: "专项演练",
      plan: "消防应急预案-2025",
      location: "办公楼",
      scheduledDate: "2025-02-15",
      scheduledTime: "14:30",
      status: "已完成",
      participants: 30,
      responsible: "李四",
      progress: 100,
      description: "办公区火灾应急疏散演练",
      objectives: "熟悉疏散路线，提高人员疏散效率",
      requirements: "全体办公人员参与",
      disabled: false
    }
  ])

  // 统计数据
  const statistics: Statistics = {
    total: emergencyDrills.length,
    planned: emergencyDrills.filter(d => d.status === "计划中").length,
    inProgress: emergencyDrills.filter(d => d.status === "进行中").length,
    completed: emergencyDrills.filter(d => d.status === "已完成").length,
    thisMonth: emergencyDrills.filter(d => {
      const date = new Date(d.scheduledDate)
      const now = new Date()
      return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear()
    }).length,
    thisYear: emergencyDrills.filter(d => {
      const date = new Date(d.scheduledDate)
      const now = new Date()
      return date.getFullYear() === now.getFullYear()
    }).length,
  }

  // 消息提示
  const showMessage = (type: "success" | "error", content: string) => {
    toast({
      variant: type === "success" ? "default" : "destructive",
      title: content,
    })
  }

  // 导出Excel
  const handleExportExcel = () => {
    showMessage("success", "导出成功")
  }

  // 导入Excel
  const handleImportExcel = () => {
    showMessage("success", "导入成功")
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      showMessage("success", "数据已刷新")
    }, 1000)
  }

  // 处理查看详情
  const handleView = (drill: EmergencyDrill) => {
    setSelectedDrill(drill)
    setIsViewDrillOpen(true)
  }

  // 处理编辑
  const handleEdit = (drill: EmergencyDrill) => {
    setSelectedDrill(drill)
    setFormData(drill)
    setIsEditDrillOpen(true)
  }

  // 处理保存编辑
  const handleSaveEdit = () => {
    if (!selectedDrill || !formData) return

    const updatedDrills = emergencyDrills.map(drill =>
      drill.id === selectedDrill.id ? { ...drill, ...formData } : drill
    )
    setEmergencyDrills(updatedDrills)
    setIsEditDrillOpen(false)
    setSelectedDrill(null)
    setFormData({})
    showMessage("success", "修改成功")
  }

  // 处理删除
  const handleDelete = (id: string) => {
    setDeleteId(id)
    setIsDeleteDialogOpen(true)
  }

  // 确认删除
  const confirmDelete = () => {
    const newDrills = emergencyDrills.filter(drill => drill.id !== deleteId)
    setEmergencyDrills(newDrills)
    setIsDeleteDialogOpen(false)
    showMessage("success", "演练已删除")
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setIsDeleteDialogOpen(true)
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    setEmergencyDrills(emergencyDrills.filter(drill => !selectedRowKeys.includes(drill.id)))
    setSelectedRowKeys([])
    showMessage("success", "批量删除成功")
    setIsDeleteDialogOpen(false)
  }

  // 处理禁用/启用
  const handleToggleDisable = (id: string) => {
    setEmergencyDrills(emergencyDrills.map(drill =>
      drill.id === id ? { ...drill, disabled: !drill.disabled } : drill
    ))
    showMessage("success", `${emergencyDrills.find(d => d.id === id)?.disabled ? '启用' : '禁用'}成功`)
  }

  // 处理添加演练
  const handleAddDrill = () => {
    if (!formData.name || !formData.type || !formData.plan || !formData.location) {
      showMessage("error", "请填写必填项")
      return
    }

    const newDrill = {
      id: Date.now().toString(),
      name: formData.name || "",
      type: formData.type || "",
      plan: formData.plan || "",
      location: formData.location || "",
      scheduledDate: formData.scheduledDate || "",
      scheduledTime: formData.scheduledTime || "",
      status: formData.status || "计划中",
      participants: formData.participants || 0,
      responsible: formData.responsible || "",
      progress: formData.progress || 0,
      description: formData.description,
      objectives: formData.objectives,
      requirements: formData.requirements,
      disabled: false
    } as EmergencyDrill;

    setEmergencyDrills([...emergencyDrills, newDrill])
    setIsAddDrillOpen(false)
    setFormData({})
    showMessage("success", "添加成功")
  }

  // 处理评估
  const handleEvaluation = (drill: EmergencyDrill) => {
    setSelectedDrill(drill)
    setEvaluationData({
      drillId: drill.id,
      date: new Date().toISOString().split('T')[0],
      evaluator: "",
    })
    setIsEvaluationOpen(true)
  }

  // 保存评估
  const handleSaveEvaluation = () => {
    if (!selectedDrill || !evaluationData.score || !evaluationData.evaluator) {
      showMessage("error", "请填写完整的评估信息")
      return
    }

    const evaluationId = Date.now().toString();
    const updatedDrills = emergencyDrills.map(drill =>
      drill.id === selectedDrill.id
        ? {
            ...drill,
            evaluation: {
              id: evaluationId,
              drillId: drill.id,
              date: evaluationData.date || new Date().toISOString().split('T')[0],
              score: evaluationData.score || 0,
              strengths: evaluationData.strengths || "",
              weaknesses: evaluationData.weaknesses || "",
              improvements: evaluationData.improvements || "",
              evaluator: evaluationData.evaluator || ""
            },
      status: "已完成",
      progress: 100,
          }
        : drill
    )

    setEmergencyDrills(updatedDrills)
    setIsEvaluationOpen(false)
    showMessage("success", "评估已保存")
  }

  // 搜索和筛选
  const filteredDrills = emergencyDrills.filter(drill => {
    const matchSearch = drill.name.toLowerCase().includes(searchText.toLowerCase()) ||
      drill.location.toLowerCase().includes(searchText.toLowerCase()) ||
      drill.responsible.toLowerCase().includes(searchText.toLowerCase())

    const matchType = selectedTypes.length === 0 || selectedTypes.includes(drill.type)
    const matchStatus = selectedStatus.length === 0 || selectedStatus.includes(drill.status)

    return matchSearch && matchType && matchStatus
  })

  // 处理类型选择
  const handleTypeChange = (value: string) => {
    if (value === 'all') {
      setSelectedTypes([])
    } else {
      setSelectedTypes([value])
    }
  }

  // 处理状态选择
  const handleStatusChange = (value: string) => {
    if (value === 'all') {
      setSelectedStatus([])
    } else {
      setSelectedStatus([value])
    }
  }

  // 演练类型统计
  const drillTypeStats = [
    { type: "消防演练", count: 8, icon: <AlertTriangle className="h-6 w-6 text-red-600" /> },
    { type: "事故演练", count: 12, icon: <AlertTriangle className="h-6 w-6 text-amber-600" /> },
    { type: "综合演练", count: 5, icon: <AlertTriangle className="h-6 w-6 text-blue-600" /> },
    { type: "桌面演练", count: 7, icon: <AlertTriangle className="h-6 w-6 text-green-600" /> },
  ]

  // 演练评估
  const drillEvaluations = [
    {
      id: "1",
      drillName: "井下透水事故演练",
      date: "2025-03-20",
      score: 92,
      strengths: "响应迅速，团队协作良好",
      weaknesses: "通信设备使用不熟练",
      improvements: "加强通信设备使用培训",
    },
    {
      id: "2",
      drillName: "瓦斯超限应急演练",
      date: "2025-02-25",
      score: 85,
      strengths: "疏散程序执行规范",
      weaknesses: "个别人员防护装备使用不当",
      improvements: "强化个人防护装备使用培训",
    },
    {
      id: "3",
      drillName: "触电事故应急演练",
      date: "2025-03-10",
      score: 88,
      strengths: "急救措施实施及时",
      weaknesses: "现场指挥不够明确",
      improvements: "明确现场指挥权责，加强指挥培训",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">应急演练管理</h2>
          <p className="text-muted-foreground">管理和维护应急演练计划，提高应急响应能力</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportExcel}>
            <Download className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">应急演练总数</h3>
              <div className="rounded-full bg-blue-100 p-2">
                <FileCheck className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.total}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
            </CardContent>
          </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">计划中演练</h3>
              <div className="rounded-full bg-yellow-100 p-2">
                <Calendar className="h-4 w-4 text-yellow-600" />
      </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.planned}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">进行中演练</h3>
              <div className="rounded-full bg-green-100 p-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.inProgress}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">已完成演练</h3>
              <div className="rounded-full bg-blue-100 p-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.completed}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">本月演练</h3>
              <div className="rounded-full bg-purple-100 p-2">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.thisMonth}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-y-1">
              <h3 className="tracking-tight text-sm font-medium">年度演练</h3>
              <div className="rounded-full bg-orange-100 p-2">
                <Calendar className="h-4 w-4 text-orange-600" />
              </div>
            </div>
            <div className="flex items-baseline space-x-2">
              <h1 className="text-2xl font-semibold">{statistics.thisYear}</h1>
              <span className="text-xs text-muted-foreground">次</span>
            </div>
            </CardContent>
          </Card>
      </div>

      {/* 主要内容区域 */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
              <CardTitle>演练列表</CardTitle>
              <CardDescription>管理和维护应急演练计划</CardDescription>
                </div>
            <Button onClick={() => setIsAddDrillOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加演练
            </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索演练..."
                  className="pl-8 w-[250px]"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
                    </div>
              <Select
                value={selectedTypes.length > 0 ? selectedTypes[0] : 'all'}
                onValueChange={handleTypeChange}
              >
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="演练类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="消防演练">消防演练</SelectItem>
                  <SelectItem value="事故演练">事故演练</SelectItem>
                  <SelectItem value="综合演练">综合演练</SelectItem>
                  <SelectItem value="桌面演练">桌面演练</SelectItem>
                      </SelectContent>
                    </Select>
              <Select
                value={selectedStatus.length > 0 ? selectedStatus[0] : 'all'}
                onValueChange={handleStatusChange}
              >
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="演练状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="计划中">计划中</SelectItem>
                  <SelectItem value="进行中">进行中</SelectItem>
                  <SelectItem value="已完成">已完成</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                              </div>

          {/* 批量操作 */}
          {selectedRowKeys.length > 0 && (
            <div className="bg-muted/50 p-2 rounded-lg">
                  <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  已选择 {selectedRowKeys.length} 项
                </span>
                <Button variant="destructive" size="sm" onClick={handleBatchDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                        </Button>
                              </div>
                              </div>
          )}

          {/* 演练表格 */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>演练名称</TableHead>
                        <TableHead>类型</TableHead>
                  <TableHead>应急预案</TableHead>
                        <TableHead>地点</TableHead>
                  <TableHead>日期</TableHead>
                  <TableHead>时间</TableHead>
                        <TableHead>参与人数</TableHead>
                        <TableHead>负责人</TableHead>
                  <TableHead>状态</TableHead>
                        <TableHead>进度</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                {filteredDrills.map((drill) => (
                        <TableRow key={drill.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                        <FileCheck className="h-4 w-4 mr-2 text-blue-500" />
                        {drill.name}
                            </div>
                          </TableCell>
                          <TableCell>{drill.type}</TableCell>
                          <TableCell>{drill.plan}</TableCell>
                          <TableCell>{drill.location}</TableCell>
                          <TableCell>{drill.scheduledDate}</TableCell>
                          <TableCell>{drill.scheduledTime}</TableCell>
                    <TableCell>{drill.participants}</TableCell>
                    <TableCell>{drill.responsible}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                          drill.status === "计划中"
                                    ? "secondary"
                            : drill.status === "进行中"
                                  ? "default"
                                    : "outline"
                              }
                            >
                              {drill.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                        <Progress value={drill.progress} className="w-[60px]" />
                        <span className="text-xs text-muted-foreground">{drill.progress}%</span>
                            </div>
                          </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button variant="ghost" size="icon" onClick={() => handleView(drill)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleEdit(drill)}>
                          <Edit className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEvaluation(drill)}>
                                    <FileText className="h-4 w-4 mr-2" />
                              评估
                                  </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(drill.id)}>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除
                                  </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleDisable(drill.id)}>
                              {drill.disabled ? (
                                <>
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  启用
                                </>
                              ) : (
                                <>
                                  <AlertTriangle className="h-4 w-4 mr-2" />
                                  禁用
                                </>
                              )}
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
            </CardContent>
      </Card>

      {/* 删除对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>删除确认</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedRowKeys.length > 0
                ? `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`
                : "确定要删除该记录吗？"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={selectedRowKeys.length > 0 ? confirmBatchDelete : confirmDelete}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 统计分析抽屉 */}
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-[500px] sm:w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>应急演练统计分析</SheetTitle>
            <SheetDescription>查看应急演练统计数据和图表分析</SheetDescription>
          </SheetHeader>

          <div className="py-6 space-y-6">
            {/* 统计概览 */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 flex flex-col items-center">
                  <div className="rounded-full bg-blue-100 p-2">
                    <FileText className="h-4 w-4 text-blue-600" />
              </div>
                  <h3 className="text-xl font-bold mt-2">{statistics.total}</h3>
                  <p className="text-xs text-muted-foreground">演练总数</p>
            </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 flex flex-col items-center">
                  <div className="rounded-full bg-yellow-100 p-2">
                    <Calendar className="h-4 w-4 text-yellow-600" />
              </div>
                  <h3 className="text-xl font-bold mt-2">{statistics.planned}</h3>
                  <p className="text-xs text-muted-foreground">计划中</p>
                </CardContent>
          </Card>

          <Card>
                <CardContent className="p-4 flex flex-col items-center">
                  <div className="rounded-full bg-green-100 p-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                  <h3 className="text-xl font-bold mt-2">{statistics.completed}</h3>
                  <p className="text-xs text-muted-foreground">已完成</p>
                </CardContent>
              </Card>
              </div>

            {/* 演练类型统计 */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>演练类型分布</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                  {drillTypeStats.map((stat) => (
                    <div key={stat.type} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {stat.icon}
                        <span>{stat.type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">{stat.count}</span>
                        <span className="bg-muted text-xs px-1.5 py-0.5 rounded-full">
                          {Math.round((stat.count / statistics.total) * 100)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 演练评估统计 */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>演练评估情况</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                {drillEvaluations.map((evaluation) => (
                    <div key={evaluation.id} className="space-y-2 p-3 border rounded-md">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{evaluation.drillName}</span>
                        <Badge>{evaluation.score} 分</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p><span className="text-foreground font-medium">优点：</span>{evaluation.strengths}</p>
                        <p><span className="text-foreground font-medium">不足：</span>{evaluation.weaknesses}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <SheetFooter className="pt-4">
            <Button onClick={() => setIsStatsDrawerOpen(false)}>关闭</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 查看详情抽屉 */}
      <Sheet open={isViewDrillOpen} onOpenChange={setIsViewDrillOpen}>
        <SheetContent className="w-[600px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                演练详情
              </div>
            </SheetTitle>
            <SheetDescription>查看应急演练的详细信息</SheetDescription>
          </SheetHeader>
          {selectedDrill && (
            <div className="mt-6 space-y-6">
              <div className="grid grid-cols-2 gap-4">
                      <div>
                  <Label>演练名称</Label>
                  <p className="mt-1">{selectedDrill.name}</p>
                      </div>
                <div>
                  <Label>演练类型</Label>
                  <p className="mt-1">{selectedDrill.type}</p>
                </div>
                <div>
                  <Label>应急预案</Label>
                  <p className="mt-1">{selectedDrill.plan}</p>
                </div>
                <div>
                  <Label>演练地点</Label>
                  <p className="mt-1">{selectedDrill.location}</p>
                </div>
                <div>
                  <Label>演练日期</Label>
                  <p className="mt-1">{selectedDrill.scheduledDate}</p>
                </div>
                <div>
                  <Label>演练时间</Label>
                  <p className="mt-1">{selectedDrill.scheduledTime}</p>
                </div>
                <div>
                  <Label>参与人数</Label>
                  <p className="mt-1">{selectedDrill.participants} 人</p>
              </div>
                      <div>
                  <Label>负责人</Label>
                  <p className="mt-1">{selectedDrill.responsible}</p>
                      </div>
                <div>
                  <Label>状态</Label>
                  <p className="mt-1">
                        <Badge
                          variant={
                        selectedDrill.status === "计划中"
                          ? "secondary"
                          : selectedDrill.status === "进行中"
                            ? "default"
                            : "outline"
                      }
                    >
                      {selectedDrill.status}
                        </Badge>
                  </p>
                      </div>
                <div>
                  <Label>进度</Label>
                  <div className="mt-2 flex items-center gap-2">
                    <Progress value={selectedDrill.progress} className="w-[100px]" />
                    <span className="text-sm text-muted-foreground">{selectedDrill.progress}%</span>
                    </div>
                      </div>
                    </div>

              {selectedDrill.description && (
                      <div>
                  <Label>演练描述</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.description}</p>
                      </div>
              )}

              {selectedDrill.objectives && (
                      <div>
                  <Label>演练目标</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.objectives}</p>
                      </div>
              )}

              {selectedDrill.requirements && (
                      <div>
                  <Label>演练要求</Label>
                  <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.requirements}</p>
                      </div>
              )}

              {selectedDrill.evaluation && (
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">演练评估</h4>
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">评估日期</span>
                          <span className="text-sm">{selectedDrill.evaluation.date}</span>
                    </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">评估得分</span>
                          <span className="text-sm">{selectedDrill.evaluation.score} 分</span>
                    </div>
                      <div>
                          <span className="text-sm font-medium">优点</span>
                          <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.evaluation.strengths}</p>
                  </div>
                      <div>
                          <span className="text-sm font-medium">不足</span>
                          <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.evaluation.weaknesses}</p>
                      </div>
                      <div>
                          <span className="text-sm font-medium">改进建议</span>
                          <p className="mt-1 text-sm text-muted-foreground">{selectedDrill.evaluation.improvements}</p>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">评估人</span>
                          <span className="text-sm">{selectedDrill.evaluation.evaluator}</span>
                        </div>
              </div>
            </CardContent>
          </Card>
                </div>
              )}
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 编辑抽屉 */}
      <Sheet open={isEditDrillOpen} onOpenChange={setIsEditDrillOpen}>
        <SheetContent className="w-[600px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>
              <div className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                编辑演练
          </div>
            </SheetTitle>
            <SheetDescription>修改应急演练信息</SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">演练名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">演练类型 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择演练类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="消防演练">消防演练</SelectItem>
                    <SelectItem value="事故演练">事故演练</SelectItem>
                    <SelectItem value="综合演练">综合演练</SelectItem>
                    <SelectItem value="桌面演练">桌面演练</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-plan">应急预案 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-plan"
                  value={formData.plan}
                  onChange={(e) => setFormData({ ...formData, plan: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">演练地点 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-date">演练日期 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={formData.scheduledDate}
                  onChange={(e) => setFormData({ ...formData, scheduledDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-time">演练时间 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-time"
                  type="time"
                  value={formData.scheduledTime}
                  onChange={(e) => setFormData({ ...formData, scheduledTime: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-participants">参与人数 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-participants"
                  type="number"
                  value={formData.participants}
                  onChange={(e) => setFormData({ ...formData, participants: parseInt(e.target.value) })}
                />
                    </div>
              <div className="space-y-2">
                <Label htmlFor="edit-responsible">负责人 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-responsible"
                  value={formData.responsible}
                  onChange={(e) => setFormData({ ...formData, responsible: e.target.value })}
                />
                    </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择演练状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="计划中">计划中</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
                  </div>
              <div className="space-y-2">
                <Label htmlFor="edit-progress">进度 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-progress"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.progress}
                  onChange={(e) => setFormData({ ...formData, progress: parseInt(e.target.value) })}
                />
              </div>
            </div>

            <div className="mt-6 space-y-2">
              <Label htmlFor="description">演练描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
              />
            </div>

            <div className="mt-6 space-y-2">
              <Label htmlFor="objectives">演练目标</Label>
              <Textarea
                id="objectives"
                value={formData.objectives}
                onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
                rows={3}
              />
            </div>

            <div className="mt-6 space-y-2">
              <Label htmlFor="requirements">演练要求</Label>
              <Textarea
                id="requirements"
                value={formData.requirements}
                onChange={(e) => setFormData({ ...formData, requirements: e.target.value })}
                rows={3}
              />
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-4">
            <Button variant="outline" onClick={() => setIsEditDrillOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>保存</Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* 添加演练对话框 */}
      <Dialog open={isAddDrillOpen} onOpenChange={setIsAddDrillOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加应急演练</DialogTitle>
            <DialogDescription>
              请填写新演练的相关信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="add-name">演练名称 <span className="text-red-500">*</span></Label>
              <Input
                id="add-name"
                value={formData.name || ""}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="请输入演练名称"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-type">演练类型 <span className="text-red-500">*</span></Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择演练类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="消防演练">消防演练</SelectItem>
                  <SelectItem value="事故演练">事故演练</SelectItem>
                  <SelectItem value="综合演练">综合演练</SelectItem>
                  <SelectItem value="桌面演练">桌面演练</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-plan">应急预案 <span className="text-red-500">*</span></Label>
              <Input
                id="add-plan"
                value={formData.plan || ""}
                onChange={(e) => setFormData({ ...formData, plan: e.target.value })}
                placeholder="请输入应急预案名称"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-location">演练地点 <span className="text-red-500">*</span></Label>
              <Input
                id="add-location"
                value={formData.location || ""}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="请输入演练地点"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-date">演练日期 <span className="text-red-500">*</span></Label>
              <Input
                id="add-date"
                type="date"
                value={formData.scheduledDate || ""}
                onChange={(e) => setFormData({ ...formData, scheduledDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-time">演练时间 <span className="text-red-500">*</span></Label>
              <Input
                id="add-time"
                type="time"
                value={formData.scheduledTime || ""}
                onChange={(e) => setFormData({ ...formData, scheduledTime: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-participants">参与人数 <span className="text-red-500">*</span></Label>
              <Input
                id="add-participants"
                type="number"
                min="1"
                value={formData.participants || ""}
                onChange={(e) => setFormData({ ...formData, participants: parseInt(e.target.value) })}
                placeholder="请输入参与人数"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="add-responsible">负责人 <span className="text-red-500">*</span></Label>
              <Input
                id="add-responsible"
                value={formData.responsible || ""}
                onChange={(e) => setFormData({ ...formData, responsible: e.target.value })}
                placeholder="请输入负责人姓名"
              />
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="add-description">演练描述</Label>
            <Textarea
              id="add-description"
              value={formData.description || ""}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="请输入演练描述"
              rows={3}
            />
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="add-objectives">演练目标</Label>
            <Textarea
              id="add-objectives"
              value={formData.objectives || ""}
              onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
              placeholder="请输入演练目标"
              rows={3}
            />
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="add-requirements">演练要求</Label>
            <Textarea
              id="add-requirements"
              value={formData.requirements || ""}
              onChange={(e) => setFormData({ ...formData, requirements: e.target.value })}
              placeholder="请输入演练要求"
              rows={3}
            />
          </div>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => {
              setIsAddDrillOpen(false)
              setFormData({})
            }}>
              取消
            </Button>
            <Button onClick={handleAddDrill}>
              确认添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

