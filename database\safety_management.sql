-- 安全管理模块数据库初始化脚本
-- 创建于2025年1月1日
-- 包含安全检查、隐患管理、应急预案等安全管理相关数据

-- 创建安全检查表
CREATE TABLE IF NOT EXISTS `safety_checks` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `department` VARCHAR(50) NOT NULL,
  `inspector_id` INT,
  `inspector` VARCHAR(50) NOT NULL,
  `check_date` DATE NOT NULL,
  `status` ENUM('计划中', '进行中', '已完成', '已取消') DEFAULT '计划中',
  `issues_count` INT DEFAULT 0,
  `description` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建安全隐患表
CREATE TABLE IF NOT EXISTS `safety_hazards` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `check_id` INT,
  `title` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `location` VARCHAR(100) NOT NULL,
  `level` ENUM('低', '中', '高', '严重') NOT NULL,
  `status` ENUM('待处理', '处理中', '已解决', '已验收', '已关闭') DEFAULT '待处理',
  `reporter_id` INT,
  `reporter` VARCHAR(50) NOT NULL,
  `report_date` DATE NOT NULL,
  `deadline` DATE,
  `responsible_id` INT,
  `responsible` VARCHAR(50),
  `solution` TEXT,
  `solve_date` DATE,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建应急预案表
CREATE TABLE IF NOT EXISTS `emergency_plans` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `version` VARCHAR(20) NOT NULL,
  `department` VARCHAR(50) NOT NULL,
  `responsible_id` INT,
  `responsible` VARCHAR(50) NOT NULL,
  `approval_date` DATE NOT NULL,
  `effective_date` DATE NOT NULL,
  `expiry_date` DATE,
  `status` ENUM('有效', '失效', '修订中') DEFAULT '有效',
  `content` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建安全培训表
CREATE TABLE IF NOT EXISTS `safety_trainings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `trainer_id` INT,
  `trainer` VARCHAR(50) NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NOT NULL,
  `location` VARCHAR(100),
  `participants_count` INT DEFAULT 0,
  `status` ENUM('计划中', '进行中', '已完成', '已取消') DEFAULT '计划中',
  `description` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建安全培训参与人员表
CREATE TABLE IF NOT EXISTS `safety_training_participants` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `training_id` INT NOT NULL,
  `employee_id` INT NOT NULL,
  `employee_name` VARCHAR(50) NOT NULL,
  `department` VARCHAR(50),
  `attendance` ENUM('未签到', '已签到', '缺席') DEFAULT '未签到',
  `score` INT,
  `certificate` VARCHAR(50),
  `feedback` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建安全事故表
CREATE TABLE IF NOT EXISTS `safety_incidents` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `title` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `level` ENUM('轻微', '一般', '较大', '重大', '特别重大') NOT NULL,
  `location` VARCHAR(100) NOT NULL,
  `incident_date` DATETIME NOT NULL,
  `reporter_id` INT,
  `reporter` VARCHAR(50) NOT NULL,
  `report_date` DATE NOT NULL,
  `description` TEXT,
  `cause` TEXT,
  `casualties` INT DEFAULT 0,
  `injuries` INT DEFAULT 0,
  `property_loss` DECIMAL(12,2) DEFAULT 0,
  `status` ENUM('调查中', '处理中', '已结案') DEFAULT '调查中',
  `solution` TEXT,
  `prevention` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建安全设备表
CREATE TABLE IF NOT EXISTS `safety_equipment` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `model` VARCHAR(50),
  `serial_number` VARCHAR(50),
  `location` VARCHAR(100) NOT NULL,
  `status` ENUM('正常', '维修中', '报废') DEFAULT '正常',
  `purchase_date` DATE,
  `expiry_date` DATE,
  `last_check_date` DATE,
  `next_check_date` DATE,
  `responsible_id` INT,
  `responsible` VARCHAR(50),
  `description` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入安全检查数据
INSERT INTO `safety_checks` (`name`, `type`, `department`, `inspector_id`, `inspector`, `check_date`, `status`, `issues_count`, `description`) VALUES
('矿区A3安全例行检查', '例行检查', '安全管理部', 2, '张三', '2025-03-01', '已完成', 0, '矿区A3区域的安全例行检查，包括设备、环境和人员安全状况检查'),
('设备安全检查', '专项检查', '设备管理部', 3, '李四', '2025-02-28', '已完成', 2, '对主要生产设备进行安全专项检查，重点检查设备运行状态和安全防护措施'),
('爆破区域安全检查', '专项检查', '安全管理部', 2, '王五', '2025-02-25', '已完成', 1, '对爆破区域进行安全专项检查，包括爆破物品管理、操作规程执行情况等'),
('矿区B2安全例行检查', '例行检查', '安全管理部', 4, '赵六', '2025-03-02', '进行中', 0, '矿区B2区域的安全例行检查，包括设备、环境和人员安全状况检查'),
('消防设备检查', '专项检查', '安全管理部', 2, '张三', '2025-03-05', '计划中', 0, '对全公司消防设备进行专项检查，包括灭火器、消防栓、烟感器等'),
('高危作业区域检查', '专项检查', '安全管理部', 2, '张三', '2025-03-10', '计划中', 0, '对高危作业区域进行安全专项检查，重点检查安全操作规程执行情况'),
('运输车辆安全检查', '例行检查', '运输部', 5, '钱七', '2025-03-08', '计划中', 0, '对运输车辆进行安全例行检查，包括车况、驾驶员资质等'),
('井下通风系统检查', '专项检查', '安全管理部', 2, '张三', '2025-03-15', '计划中', 0, '对井下通风系统进行专项检查，确保通风系统正常运行'),
('安全警示标志检查', '例行检查', '安全管理部', 4, '赵六', '2025-03-12', '计划中', 0, '对全公司安全警示标志进行例行检查，确保标志清晰可见且位置正确');

-- 插入安全隐患数据
INSERT INTO `safety_hazards` (`check_id`, `title`, `description`, `location`, `level`, `status`, `reporter_id`, `reporter`, `report_date`, `deadline`, `responsible_id`, `responsible`, `solution`) VALUES
(2, '输送带防护罩损坏', '3号输送带的防护罩出现破损，存在卷入风险', 'A区生产线', '高', '已解决', 3, '李四', '2025-02-28', '2025-03-05', 3, '李四', '更换了新的防护罩，并加固了安装方式'),
(2, '电气线路老化', '配电箱附近的电气线路存在老化现象，绝缘层有破损', 'B区配电室', '中', '处理中', 3, '李四', '2025-02-28', '2025-03-10', 6, '孙八', '计划更换老化线路，临时已用绝缘胶带包扎'),
(3, '爆破物品管理不规范', '爆破物品出入库登记不完善，存在管理漏洞', 'C区爆破物品库', '严重', '已解决', 2, '王五', '2025-02-25', '2025-03-01', 2, '王五', '完善了爆破物品管理制度，加强了出入库登记管理'),
(1, '安全通道堆放杂物', '安全通道内堆放了部分工具和材料，影响紧急疏散', 'A3区安全通道', '中', '已解决', 2, '张三', '2025-03-01', '2025-03-03', 5, '钱七', '清理了通道内的杂物，并加强了日常巡查'),
(1, '安全帽佩戴不规范', '部分工人未正确佩戴安全帽，存在安全隐患', 'A3区作业现场', '低', '已解决', 2, '张三', '2025-03-01', '2025-03-02', 2, '张三', '对相关人员进行了安全教育，强调了正确佩戴安全帽的重要性');

-- 插入应急预案数据
INSERT INTO `emergency_plans` (`name`, `type`, `version`, `department`, `responsible_id`, `responsible`, `approval_date`, `effective_date`, `expiry_date`, `status`, `content`) VALUES
('矿井突水应急预案', '自然灾害', 'V2.0', '安全管理部', 2, '张三', '2025-01-15', '2025-02-01', '2026-01-31', '有效', '本预案针对矿井突水事件制定，包括预警机制、应急响应、人员疏散、救援措施等内容...'),
('火灾应急处理预案', '火灾事故', 'V3.1', '安全管理部', 2, '张三', '2025-01-20', '2025-02-01', '2026-01-31', '有效', '本预案针对矿区可能发生的火灾事故制定，包括火灾报警、初期扑救、人员疏散、消防队伍调动等内容...'),
('瓦斯爆炸应急预案', '瓦斯事故', 'V2.5', '安全管理部', 2, '张三', '2025-01-25', '2025-02-01', '2026-01-31', '有效', '本预案针对瓦斯爆炸事故制定，包括预警监测、应急响应、人员救援、事故调查等内容...'),
('坍塌事故应急预案', '坍塌事故', 'V1.8', '安全管理部', 2, '张三', '2025-01-10', '2025-02-01', '2026-01-31', '有效', '本预案针对矿井坍塌事故制定，包括预警信号、应急响应、人员搜救、医疗救护等内容...'),
('触电事故应急预案', '电气事故', 'V2.2', '安全管理部', 2, '张三', '2025-01-05', '2025-02-01', '2026-01-31', '有效', '本预案针对电气设备导致的触电事故制定，包括断电措施、紧急救护、人员培训等内容...'),
('有毒气体泄漏应急预案', '中毒窒息', 'V1.5', '安全管理部', 2, '张三', '2025-01-30', '2025-02-01', '2026-01-31', '有效', '本预案针对有毒气体泄漏事故制定，包括气体检测、应急处置、人员疏散、医疗救护等内容...'),
('机械伤害应急预案', '机械事故', 'V1.3', '安全管理部', 2, '张三', '2025-01-12', '2025-02-01', '2026-01-31', '有效', '本预案针对机械设备导致的伤害事故制定，包括设备停机、伤员救护、事故调查等内容...');

-- 插入安全培训数据
INSERT INTO `safety_trainings` (`name`, `type`, `trainer_id`, `trainer`, `start_date`, `end_date`, `location`, `participants_count`, `status`, `description`) VALUES
('新员工安全培训', '入职培训', 2, '张三', '2025-03-05', '2025-03-07', '培训中心A室', 25, '已完成', '针对新入职员工的安全基础知识培训，包括安全规章制度、安全操作规程、应急处置等内容'),
('特种作业人员培训', '专业培训', 2, '张三', '2025-03-10', '2025-03-15', '培训中心B室', 15, '进行中', '针对特种作业人员的专业安全培训，包括理论学习和实操训练'),
('安全管理人员培训', '管理培训', 7, '周九', '2025-03-20', '2025-03-22', '会议室A', 12, '计划中', '针对各部门安全管理人员的培训，提升安全管理能力和事故防范意识'),
('消防安全培训', '专项培训', 2, '张三', '2025-02-25', '2025-02-26', '操场', 50, '已完成', '全员消防安全知识培训和灭火器使用实操训练'),
('应急救援培训', '专项培训', 8, '吴十', '2025-04-01', '2025-04-03', '培训中心C室', 20, '计划中', '应急救援队伍的专业培训，包括救援技能和急救知识'),
('安全生产法规培训', '法规培训', 2, '张三', '2025-03-25', '2025-03-26', '会议室B', 30, '计划中', '安全生产相关法律法规的学习和解读');

-- 插入安全培训参与人员数据
INSERT INTO `safety_training_participants` (`training_id`, `employee_id`, `employee_name`, `department`, `attendance`, `score`, `certificate`) VALUES
(1, 10, '张小明', '生产部', '已签到', 85, 'CERT202503001'),
(1, 11, '李小红', '生产部', '已签到', 90, 'CERT202503002'),
(1, 12, '王小军', '生产部', '已签到', 78, 'CERT202503003'),
(1, 13, '赵小梅', '质量部', '已签到', 92, 'CERT202503004'),
(1, 14, '钱小伟', '设备部', '已签到', 88, 'CERT202503005'),
(2, 15, '孙小刚', '生产部', '已签到', NULL, NULL),
(2, 16, '周小强', '生产部', '已签到', NULL, NULL),
(2, 17, '吴小勇', '设备部', '已签到', NULL, NULL),
(4, 10, '张小明', '生产部', '已签到', 80, 'CERT202502001'),
(4, 11, '李小红', '生产部', '已签到', 85, 'CERT202502002'),
(4, 15, '孙小刚', '生产部', '缺席', NULL, NULL);

-- 插入安全事故数据
INSERT INTO `safety_incidents` (`title`, `type`, `level`, `location`, `incident_date`, `reporter_id`, `reporter`, `report_date`, `description`, `cause`, `casualties`, `injuries`, `property_loss`, `status`) VALUES
('B区输送带故障导致轻微伤害', '机械伤害', '轻微', 'B区生产线', '2025-02-10 14:30:00', 3, '李四', '2025-02-10', '输送带突然停止运行，操作工尝试手动调整时手部受到轻微擦伤', '设备维护不及时，操作不规范', 0, 1, 500.00, '已结案'),
('配电室电气短路引发小型火灾', '火灾事故', '一般', 'C区配电室', '2025-01-25 09:15:00', 6, '孙八', '2025-01-25', '配电室内电气线路短路引发小型火灾，被及时发现并扑灭，无人员伤亡', '线路老化，绝缘层破损', 0, 0, 5000.00, '已结案'),
('运输车辆侧翻事故', '交通事故', '较大', '矿区外运输道路', '2025-02-15 16:45:00', 5, '钱七', '2025-02-15', '一辆运输车辆在转弯处侧翻，驾驶员受轻伤，车辆损坏严重', '车速过快，路面湿滑', 0, 1, 150000.00, '处理中');

-- 插入安全设备数据
INSERT INTO `safety_equipment` (`name`, `type`, `model`, `serial_number`, `location`, `status`, `purchase_date`, `expiry_date`, `last_check_date`, `next_check_date`, `responsible_id`, `responsible`) VALUES
('灭火器', '消防设备', 'MFX-50', 'FE20250001', 'A区生产线', '正常', '2025-01-10', '2026-01-09', '2025-02-15', '2025-05-15', 2, '张三'),
('灭火器', '消防设备', 'MFX-50', 'FE20250002', 'B区生产线', '正常', '2025-01-10', '2026-01-09', '2025-02-15', '2025-05-15', 2, '张三'),
('灭火器', '消防设备', 'MFX-50', 'FE20250003', 'C区生产线', '正常', '2025-01-10', '2026-01-09', '2025-02-15', '2025-05-15', 2, '张三'),
('消防栓', '消防设备', 'HS-100', 'HS20250001', 'A区走廊', '正常', '2024-05-20', '2034-05-19', '2025-02-15', '2025-08-15', 2, '张三'),
('消防栓', '消防设备', 'HS-100', 'HS20250002', 'B区走廊', '正常', '2024-05-20', '2034-05-19', '2025-02-15', '2025-08-15', 2, '张三'),
('烟感报警器', '报警设备', 'YG-200', 'YG20250001', 'A区办公室', '正常', '2025-01-15', '2030-01-14', '2025-02-15', '2025-08-15', 2, '张三'),
('烟感报警器', '报警设备', 'YG-200', 'YG20250002', 'B区办公室', '正常', '2025-01-15', '2030-01-14', '2025-02-15', '2025-08-15', 2, '张三'),
('安全帽', '个人防护', 'AQM-01', 'AQM20250050', '安全装备室', '正常', '2025-01-20', '2027-01-19', '2025-02-20', '2025-08-20', 2, '张三'),
('防毒面具', '个人防护', 'FDMJ-02', 'FDMJ20250010', '安全装备室', '正常', '2025-01-25', '2026-01-24', '2025-02-25', '2025-08-25', 2, '张三'),
('氧气呼吸器', '救援设备', 'YQHX-03', 'YQHX20250005', '救援装备室', '正常', '2025-02-01', '2030-01-31', '2025-02-28', '2025-08-28', 2, '张三'),
('担架', '救援设备', 'DJ-01', 'DJ20250003', '救援装备室', '正常', '2025-02-05', '2030-02-04', '2025-02-28', '2025-08-28', 2, '张三'),
('急救箱', '医疗设备', 'JJX-01', 'JJX20250008', '医务室', '正常', '2025-02-10', '2026-02-09', '2025-02-28', '2025-08-28', 9, '郑医生');
