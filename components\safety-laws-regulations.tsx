"use client"

import { useState, useEffect } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  Calendar,
  BookOpen,
  ExternalLink,
  Eye,
  BookMarked,
  Scale,
  GraduationCap,
  Upload,
  RefreshCcw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"

interface Regulation {
  id: string
  title: string
  type: string
  issueAuthority: string
  issueDate: string
  effectiveDate: string
  category: string
  level: string
  status: string
  description?: string
  keywords?: string[]
  attachments?: string[]
  remarks?: string
  createdAt: string
  updatedAt: string
  disabled?: boolean
}

export function SafetyLawsRegulations() {
  const [isAddRegulationOpen, setIsAddRegulationOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [selectedRegulation, setSelectedRegulation] = useState<Regulation | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedLevel, setSelectedLevel] = useState("all")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [loading, setLoading] = useState(false)
  const [regulations, setRegulations] = useState<Regulation[]>([
    {
      id: "1",
      title: "中华人民共和国安全生产法",
      type: "法律",
      issueAuthority: "全国人大常委会",
      issueDate: "2021-06-10",
      effectiveDate: "2021-09-01",
      category: "综合安全",
      level: "国家级",
      status: "现行有效",
      description: "这是一部关于安全生产的基本法律...",
      keywords: ["安全生产", "责任制", "事故预防"],
      createdAt: "2021-06-10",
      updatedAt: "2021-06-10",
    },
    {
      id: "2",
      title: "矿山安全法实施条例",
      type: "条例",
      issueAuthority: "国务院",
      issueDate: "1996-10-30",
      effectiveDate: "1996-10-30",
      category: "矿山安全",
      level: "国家级",
      status: "现行有效",
      createdAt: "1996-10-30",
      updatedAt: "1996-10-30",
    },
    {
      id: "3",
      title: "金属非金属矿山安全规程",
      type: "规程",
      issueAuthority: "应急管理部",
      issueDate: "2018-05-15",
      effectiveDate: "2018-10-01",
      category: "矿山安全",
      level: "部门规章",
      status: "现行有效",
      createdAt: "2018-05-15",
      updatedAt: "2018-05-15",
    },
    {
      id: "4",
      title: "爆破安全规程",
      type: "规程",
      issueAuthority: "国家安全生产监督管理总局",
      issueDate: "2014-09-29",
      effectiveDate: "2015-01-01",
      category: "爆破安全",
      level: "部门规章",
      status: "现行有效",
      createdAt: "2014-09-29",
      updatedAt: "2014-09-29",
    },
    {
      id: "5",
      title: "公司安全生产管理制度",
      type: "制度",
      issueAuthority: "公司安全管理部",
      issueDate: "2022-03-15",
      effectiveDate: "2022-04-01",
      category: "企业制度",
      level: "企业级",
      status: "现行有效",
      createdAt: "2022-03-15",
      updatedAt: "2022-03-15",
    },
  ])

  // 统计数据
  const statistics = {
    total: regulations.length,
    active: regulations.filter(r => r.status === "现行有效" && !r.disabled).length,
    revised: regulations.filter(r => r.status === "已修订").length,
    abolished: regulations.filter(r => r.status === "已废止").length,
    byType: {
      law: regulations.filter(r => r.type === "法律").length,
      regulation: regulations.filter(r => r.type === "条例").length,
      rule: regulations.filter(r => r.type === "规程").length,
      standard: regulations.filter(r => r.type === "标准").length,
    }
  }

  // 处理搜索和筛选
  const filteredRegulations = regulations.filter(regulation => {
    const matchesSearch = regulation.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         regulation.description?.toLowerCase().includes(searchText.toLowerCase()) ||
                         regulation.issueAuthority.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || regulation.type === selectedType
    const matchesLevel = selectedLevel === "all" || regulation.level === selectedLevel
    const matchesCategory = selectedCategory === "all" || regulation.category === selectedCategory
    const matchesStatus = selectedStatus === "all" || regulation.status === selectedStatus
    return matchesSearch && matchesType && matchesLevel && matchesCategory && matchesStatus
  })

  // 处理添加法规
  const handleAddRegulation = (formData: any) => {
    const newRegulation: Regulation = {
      id: Date.now().toString(),
      ...formData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    setRegulations([...regulations, newRegulation])
    setIsAddRegulationOpen(false)
    message.success("法规添加成功")
  }

  // 处理删除法规
  const handleDeleteRegulation = (id: string) => {
    const updatedRegulations = regulations.filter(r => r.id !== id)
    setRegulations(updatedRegulations)
    message.success("法规删除成功")
  }

  // 处理禁用法规
  const handleDisableRegulation = (id: string) => {
    const updatedRegulations = regulations.map(r =>
      r.id === id ? { ...r, disabled: !r.disabled } : r
    )
    setRegulations(updatedRegulations)
    message.success(`法规${regulations.find(r => r.id === id)?.disabled ? "启用" : "禁用"}成功`)
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      const exportData = regulations.map(regulation => ({
        '法规标题': regulation.title,
        '类型': regulation.type,
        '发布机构': regulation.issueAuthority,
        '发布日期': regulation.issueDate,
        '生效日期': regulation.effectiveDate,
        '所属类别': regulation.category,
        '级别': regulation.level,
        '状态': regulation.status,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 40 }, // 法规标题
        { wch: 15 }, // 类型
        { wch: 20 }, // 发布机构
        { wch: 15 }, // 发布日期
        { wch: 15 }, // 生效日期
        { wch: 15 }, // 所属类别
        { wch: 15 }, // 级别
        { wch: 15 }, // 状态
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '安全法规列表')
      XLSX.writeFile(wb, `安全法规列表_${new Date().toISOString().split('T')[0]}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 导入Excel
  const handleImportExcel = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        // 转换导入的数据格式
        const importedRegulations: Regulation[] = jsonData.map((item: any) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          title: item['法规标题'],
          type: item['类型'],
          issueAuthority: item['发布机构'],
          issueDate: item['发布日期'],
          effectiveDate: item['生效日期'],
          category: item['所属类别'],
          level: item['级别'],
          status: item['状态'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))

        setRegulations([...regulations, ...importedRegulations])
        message.success('导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">安全法律法规</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-muted-foreground">法律法规</p>
                <h3 className="text-2xl font-bold">{statistics.byType.law}</h3>
              </div>
              <div className="rounded-full bg-blue-100 p-3">
              <Scale className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                占比 {((statistics.byType.law / statistics.total) * 100).toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-muted-foreground">规章制度</p>
                <h3 className="text-2xl font-bold">{statistics.byType.regulation}</h3>
              </div>
              <div className="rounded-full bg-green-100 p-3">
              <BookOpen className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                占比 {((statistics.byType.regulation / statistics.total) * 100).toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-muted-foreground">操作规程</p>
                <h3 className="text-2xl font-bold">{statistics.byType.rule}</h3>
              </div>
              <div className="rounded-full bg-amber-100 p-3">
              <BookMarked className="h-6 w-6 text-amber-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                占比 {((statistics.byType.rule / statistics.total) * 100).toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-muted-foreground">安全标准</p>
                <h3 className="text-2xl font-bold">{statistics.byType.standard}</h3>
              </div>
              <div className="rounded-full bg-purple-100 p-3">
              <GraduationCap className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                占比 {((statistics.byType.standard / statistics.total) * 100).toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>法律法规列表</CardTitle>
              <CardDescription>管理安全生产相关法律法规和规章制度</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索法规..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="法律">法律</SelectItem>
                    <SelectItem value="条例">条例</SelectItem>
                    <SelectItem value="规程">规程</SelectItem>
                    <SelectItem value="标准">标准</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="级别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有级别</SelectItem>
                    <SelectItem value="国家级">国家级</SelectItem>
                    <SelectItem value="部门规章">部门规章</SelectItem>
                    <SelectItem value="地方法规">地方法规</SelectItem>
                    <SelectItem value="企业级">企业级</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="现行有效">现行有效</SelectItem>
                    <SelectItem value="已修订">已修订</SelectItem>
                    <SelectItem value="已废止">已废止</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={handleRefresh}>
                  <RefreshCcw className="h-4 w-4" />
                </Button>
                <Button variant="outline" onClick={handleExportExcel}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Input
                  type="file"
                  accept=".xlsx,.xls"
                  className="hidden"
                  id="import-excel"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleImportExcel(file)
                  }}
                />
                <Button variant="outline" onClick={() => document.getElementById('import-excel')?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  导入
                </Button>
                <Button onClick={() => setIsAddRegulationOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      添加法规
                    </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>法规标题</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>发布机构</TableHead>
                    <TableHead>发布日期</TableHead>
                    <TableHead>生效日期</TableHead>
                    <TableHead>所属类别</TableHead>
                    <TableHead>级别</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRegulations.map((regulation) => (
                    <TableRow key={regulation.id} className={regulation.disabled ? "opacity-50" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{regulation.title}</span>
                        </div>
                      </TableCell>
                      <TableCell>{regulation.type}</TableCell>
                      <TableCell>{regulation.issueAuthority}</TableCell>
                      <TableCell>{regulation.issueDate}</TableCell>
                      <TableCell>{regulation.effectiveDate}</TableCell>
                      <TableCell>{regulation.category}</TableCell>
                      <TableCell>{regulation.level}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            regulation.status === "现行有效"
                              ? "default"
                              : regulation.status === "已修订"
                                ? "secondary"
                                : "outline"
                          }
                        >
                          {regulation.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedRegulation(regulation)
                              setIsViewModalOpen(true)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedRegulation(regulation)
                                setIsViewModalOpen(true)
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDisableRegulation(regulation.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                {regulation.disabled ? "启用" : "禁用"}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteRegulation(regulation.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredRegulations.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>法规解读</CardTitle>
          <CardDescription>最新安全生产法律法规解读</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">《中华人民共和国安全生产法》修订要点解读</h3>
                <Badge>最新</Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                本文对2021年修订的《中华人民共和国安全生产法》主要变化进行了详细解读，包括加大对生产经营单位主要负责人的处罚力度等重要变化。
              </p>
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>发布日期: 2021-09-15</span>
                </div>
                <Button variant="link" size="sm" className="h-auto p-0">
                  查看详情
                </Button>
              </div>
            </div>

            <div className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">《金属非金属矿山安全规程》重点条款解析</h3>
                <Badge variant="outline">热门</Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                针对矿山企业安全管理人员，对《金属非金属矿山安全规程》中的重点条款进行详细解析，帮助企业更好地理解和执行安全规程。
              </p>
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>发布日期: 2021-06-20</span>
                </div>
                <Button variant="link" size="sm" className="h-auto p-0">
                  查看详情
                </Button>
              </div>
            </div>

            <div className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">《爆破安全规程》实施指南</h3>
                <Badge variant="secondary">推荐</Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                本指南针对《爆破安全规程》的实施提供了详细的操作指导，包括爆破作业的安全距离计算、爆破方案设计等关键内容。
              </p>
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>发布日期: 2021-04-10</span>
                </div>
                <Button variant="link" size="sm" className="h-auto p-0">
                  查看详情
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>法规详情</DialogTitle>
          </DialogHeader>
          {selectedRegulation && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>法规标题</Label>
                <p className="text-sm">{selectedRegulation.title}</p>
              </div>
              <div className="space-y-2">
                <Label>类型</Label>
                <p className="text-sm">{selectedRegulation.type}</p>
              </div>
              <div className="space-y-2">
                <Label>发布机构</Label>
                <p className="text-sm">{selectedRegulation.issueAuthority}</p>
              </div>
              <div className="space-y-2">
                <Label>发布日期</Label>
                <p className="text-sm">{selectedRegulation.issueDate}</p>
              </div>
              <div className="space-y-2">
                <Label>生效日期</Label>
                <p className="text-sm">{selectedRegulation.effectiveDate}</p>
              </div>
              <div className="space-y-2">
                <Label>所属类别</Label>
                <p className="text-sm">{selectedRegulation.category}</p>
              </div>
              <div className="space-y-2">
                <Label>级别</Label>
                <p className="text-sm">{selectedRegulation.level}</p>
              </div>
              <div className="space-y-2">
                <Label>状态</Label>
                <p className="text-sm">{selectedRegulation.status}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>描述</Label>
                <p className="text-sm">{selectedRegulation.description}</p>
              </div>
              {selectedRegulation.keywords && (
                <div className="col-span-2 space-y-2">
                  <Label>关键词</Label>
                  <div className="flex gap-2">
                    {selectedRegulation.keywords.map((keyword, index) => (
                      <Badge key={index} variant="secondary">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isAddRegulationOpen} onOpenChange={setIsAddRegulationOpen}>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>添加法律法规</DialogTitle>
                      <DialogDescription>添加新的安全生产法律法规或规章制度</DialogDescription>
                    </DialogHeader>
                    <Tabs defaultValue="basic" className="mt-4">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="basic">基本信息</TabsTrigger>
                        <TabsTrigger value="content">内容摘要</TabsTrigger>
                      </TabsList>
                      <TabsContent value="basic" className="space-y-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="regulation-title">法规标题</Label>
                            <Input id="regulation-title" placeholder="请输入法规标题" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-type">法规类型</Label>
                            <Select>
                              <SelectTrigger id="regulation-type">
                                <SelectValue placeholder="选择法规类型" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="law">法律</SelectItem>
                                <SelectItem value="regulation">条例</SelectItem>
                                <SelectItem value="rule">规程</SelectItem>
                                <SelectItem value="standard">标准</SelectItem>
                                <SelectItem value="system">制度</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-authority">发布机构</Label>
                            <Input id="regulation-authority" placeholder="请输入发布机构" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-level">法规级别</Label>
                            <Select>
                              <SelectTrigger id="regulation-level">
                                <SelectValue placeholder="选择法规级别" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="national">国家级</SelectItem>
                                <SelectItem value="department">部门规章</SelectItem>
                                <SelectItem value="local">地方法规</SelectItem>
                                <SelectItem value="enterprise">企业级</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-issue-date">发布日期</Label>
                            <Input id="regulation-issue-date" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-effective-date">生效日期</Label>
                            <Input id="regulation-effective-date" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-category">所属类别</Label>
                            <Select>
                              <SelectTrigger id="regulation-category">
                                <SelectValue placeholder="选择所属类别" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="general">综合安全</SelectItem>
                                <SelectItem value="mine">矿山安全</SelectItem>
                                <SelectItem value="blasting">爆破安全</SelectItem>
                                <SelectItem value="fire">消防安全</SelectItem>
                                <SelectItem value="enterprise">企业制度</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-status">状态</Label>
                            <Select>
                              <SelectTrigger id="regulation-status">
                                <SelectValue placeholder="选择状态" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">现行有效</SelectItem>
                                <SelectItem value="revised">已修订</SelectItem>
                                <SelectItem value="abolished">已废止</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </TabsContent>
                      <TabsContent value="content" className="space-y-4 mt-4">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="regulation-summary">内容摘要</Label>
                            <Textarea id="regulation-summary" placeholder="请输入法规内容摘要" rows={4} />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-keywords">关键词</Label>
                            <Input id="regulation-keywords" placeholder="请输入关键词，用逗号分隔" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-file">上传文件</Label>
                            <Input id="regulation-file" type="file" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-link">外部链接</Label>
                            <Input id="regulation-link" placeholder="请输入外部链接" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="regulation-remarks">备注</Label>
                            <Textarea id="regulation-remarks" placeholder="请输入备注信息" rows={3} />
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                    <DialogFooter className="mt-6">
                      <Button variant="outline" onClick={() => setIsAddRegulationOpen(false)}>
                        取消
                      </Button>
                      <Button onClick={() => setIsAddRegulationOpen(false)}>保存</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
    </div>
  )
}

