"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  Package,
  User,
  Building2,
  ArrowRight,
  ArrowLeft,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Boxes,
  Warehouse,
  ClipboardList,
  DollarSign,
  BarChart,
  FileCheck,
  FileX,
  FileClock,
  FilePlus,
  PackagePlus,
  PackageMinus,
  PackageSearch
} from "lucide-react"

interface WarehouseRecord {
  id: string
  type: string
  itemName: string
  specification: string
  unit: string
  category: string
  location: string
  currentStock: number
  minStock: number
  maxStock: number
  unitPrice: number
  supplier: string
  lastInboundDate?: string
  lastOutboundDate?: string
  status: string
  description: string
  inboundRecords: {
    id: string
    date: string
    quantity: number
    operator: string
    batchNo: string
    supplier: string
    remarks: string
  }[]
  outboundRecords: {
    id: string
    date: string
    quantity: number
    operator: string
    department: string
    purpose: string
    remarks: string
  }[]
}

export function WarehouseManagement() {
  // 初始仓库记录数据
  const initialRecords: WarehouseRecord[] = [
    {
      id: "1",
      type: "物资",
      itemName: "安全帽",
      specification: "标准型",
      unit: "个",
      category: "安全防护",
      location: "A区-01-01",
      currentStock: 150,
      minStock: 100,
      maxStock: 500,
      unitPrice: 50,
      supplier: "安全防护用品有限公司",
      lastInboundDate: "2025-03-01",
      lastOutboundDate: "2025-03-15",
      status: "正常",
      description: "用于日常安全防护",
      inboundRecords: [
        {
          id: "IN001",
          date: "2025-03-01",
          quantity: 200,
          operator: "张三",
          batchNo: "B20240301001",
          supplier: "安全防护用品有限公司",
          remarks: "常规采购入库"
        }
      ],
      outboundRecords: [
        {
          id: "OUT001",
          date: "2025-03-15",
          quantity: 50,
          operator: "李四",
          department: "安全部",
          purpose: "日常领用",
          remarks: "安全部月度领用"
        }
      ]
    },
    {
      id: "2",
      type: "物资",
      itemName: "矿灯",
      specification: "LED型",
      unit: "个",
      category: "照明设备",
      location: "B区-02-03",
      currentStock: 80,
      minStock: 100,
      maxStock: 200,
      unitPrice: 200,
      supplier: "矿山设备有限公司",
      lastInboundDate: "2025-02-20",
      lastOutboundDate: "2025-03-10",
      status: "库存不足",
      description: "用于井下照明",
      inboundRecords: [
        {
          id: "IN002",
          date: "2025-02-20",
          quantity: 100,
          operator: "王五",
          batchNo: "B20240220001",
          supplier: "矿山设备有限公司",
          remarks: "设备更新采购"
        }
      ],
      outboundRecords: [
        {
          id: "OUT002",
          date: "2025-03-10",
          quantity: 20,
          operator: "赵六",
          department: "设备部",
          purpose: "设备更换",
          remarks: "设备定期更换"
        }
      ]
    }
  ]

  // 状态管理
  const [records, setRecords] = useState<WarehouseRecord[]>(initialRecords)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isInboundDialogOpen, setIsInboundDialogOpen] = useState(false)
  const [isOutboundDialogOpen, setIsOutboundDialogOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<WarehouseRecord>({
    id: "",
    type: "",
    itemName: "",
    specification: "",
    unit: "",
    category: "",
    location: "",
    currentStock: 0,
    minStock: 0,
    maxStock: 0,
    unitPrice: 0,
    supplier: "",
    status: "",
    description: "",
    inboundRecords: [],
    outboundRecords: []
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤记录
  const filteredRecords = records.filter(
    (record) => {
      const matchesSearch =
        record.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.location.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "normal") return matchesSearch && record.status === "正常";
      if (activeTab === "low") return matchesSearch && record.status === "库存不足";
      if (activeTab === "over") return matchesSearch && record.status === "库存积压";

      return matchesSearch;
    }
  )

  // 添加记录
  const handleAddRecord = () => {
    const newRecord = {
      ...currentRecord,
      id: (records.length + 1).toString(),
      currentStock: 0,
      inboundRecords: [],
      outboundRecords: [],
      status: "正常"
    }
    setRecords([...records, newRecord])
    setCurrentRecord({
      id: "",
      type: "",
      itemName: "",
      specification: "",
      unit: "",
      category: "",
      location: "",
      currentStock: 0,
      minStock: 0,
      maxStock: 0,
      unitPrice: 0,
      supplier: "",
      status: "",
      description: "",
      inboundRecords: [],
      outboundRecords: []
    })
    setIsAddDialogOpen(false)
  }

  // 编辑记录
  const handleEditRecord = () => {
    const updatedRecords = records.map((record) =>
      record.id === currentRecord.id ? currentRecord : record
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      type: "",
      itemName: "",
      specification: "",
      unit: "",
      category: "",
      location: "",
      currentStock: 0,
      minStock: 0,
      maxStock: 0,
      unitPrice: 0,
      supplier: "",
      status: "",
      description: "",
      inboundRecords: [],
      outboundRecords: []
    })
    setIsEditDialogOpen(false)
  }

  // 删除记录
  const handleDeleteRecord = () => {
    const updatedRecords = records.filter(
      (record) => record.id !== currentRecord.id
    )
    setRecords(updatedRecords)
    setCurrentRecord({
      id: "",
      type: "",
      itemName: "",
      specification: "",
      unit: "",
      category: "",
      location: "",
      currentStock: 0,
      minStock: 0,
      maxStock: 0,
      unitPrice: 0,
      supplier: "",
      status: "",
      description: "",
      inboundRecords: [],
      outboundRecords: []
    })
    setIsDeleteDialogOpen(false)
  }

  // 入库操作
  const handleInbound = (record: WarehouseRecord, quantity: number, operator: string, batchNo: string, supplier: string, remarks: string) => {
    const updatedRecords = records.map((r) =>
      r.id === record.id ? {
        ...r,
        currentStock: r.currentStock + quantity,
        lastInboundDate: new Date().toISOString().split('T')[0],
        status: r.currentStock + quantity > r.maxStock ? "库存积压" :
                r.currentStock + quantity < r.minStock ? "库存不足" : "正常",
        inboundRecords: [
          ...r.inboundRecords,
          {
            id: `IN${r.inboundRecords.length + 1}`,
            date: new Date().toISOString().split('T')[0],
            quantity,
            operator,
            batchNo,
            supplier,
            remarks
          }
        ]
      } : r
    )
    setRecords(updatedRecords)
    setIsInboundDialogOpen(false)
  }

  // 出库操作
  const handleOutbound = (record: WarehouseRecord, quantity: number, operator: string, department: string, purpose: string, remarks: string) => {
    if (record.currentStock < quantity) {
      alert("库存不足")
      return
    }
    const updatedRecords = records.map((r) =>
      r.id === record.id ? {
        ...r,
        currentStock: r.currentStock - quantity,
        lastOutboundDate: new Date().toISOString().split('T')[0],
        status: r.currentStock - quantity < r.minStock ? "库存不足" : "正常",
        outboundRecords: [
          ...r.outboundRecords,
          {
            id: `OUT${r.outboundRecords.length + 1}`,
            date: new Date().toISOString().split('T')[0],
            quantity,
            operator,
            department,
            purpose,
            remarks
          }
        ]
      } : r
    )
    setRecords(updatedRecords)
    setIsOutboundDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (record: WarehouseRecord) => {
    setCurrentRecord(record)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (record: WarehouseRecord) => {
    setCurrentRecord(record)
    setIsDeleteDialogOpen(true)
  }

  // 打开入库对话框
  const openInboundDialog = (record: WarehouseRecord) => {
    setCurrentRecord(record)
    setIsInboundDialogOpen(true)
  }

  // 打开出库对话框
  const openOutboundDialog = (record: WarehouseRecord) => {
    setCurrentRecord(record)
    setIsOutboundDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "库存不足":
        return <Badge className="bg-red-500">库存不足</Badge>
      case "库存积压":
        return <Badge className="bg-yellow-500">库存积压</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">仓库管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增物资
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>新增物资</DialogTitle>
              <DialogDescription>
                请填写物资的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">物资类型</Label>
                  <Select
                    value={currentRecord.type}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择物资类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="物资">物资</SelectItem>
                      <SelectItem value="设备">设备</SelectItem>
                      <SelectItem value="工具">工具</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="itemName">物资名称</Label>
                  <Input
                    id="itemName"
                    value={currentRecord.itemName}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, itemName: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="specification">规格型号</Label>
                  <Input
                    id="specification"
                    value={currentRecord.specification}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, specification: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unit">单位</Label>
                  <Input
                    id="unit"
                    value={currentRecord.unit}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, unit: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">物资类别</Label>
                  <Select
                    value={currentRecord.category}
                    onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="选择物资类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全防护">安全防护</SelectItem>
                      <SelectItem value="照明设备">照明设备</SelectItem>
                      <SelectItem value="工具设备">工具设备</SelectItem>
                      <SelectItem value="办公用品">办公用品</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">库位</Label>
                  <Input
                    id="location"
                    value={currentRecord.location}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minStock">最低库存</Label>
                  <Input
                    id="minStock"
                    type="number"
                    value={currentRecord.minStock}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, minStock: Number(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxStock">最高库存</Label>
                  <Input
                    id="maxStock"
                    type="number"
                    value={currentRecord.maxStock}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, maxStock: Number(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unitPrice">单价</Label>
                  <Input
                    id="unitPrice"
                    type="number"
                    value={currentRecord.unitPrice}
                    onChange={(e) => setCurrentRecord({ ...currentRecord, unitPrice: Number(e.target.value) })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="supplier">供应商</Label>
                <Input
                  id="supplier"
                  value={currentRecord.supplier}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, supplier: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">物资说明</Label>
                <Textarea
                  id="description"
                  value={currentRecord.description}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddRecord}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索物资..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="normal">正常</TabsTrigger>
            <TabsTrigger value="low">库存不足</TabsTrigger>
            <TabsTrigger value="over">库存积压</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                <Package className="h-5 w-5 mr-2 text-blue-500" />
                <CardTitle className="text-sm font-medium">{record.itemName}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(record.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <Package className="h-4 w-4 mr-2 text-gray-500" />
                    {record.specification}
                  </div>
                  <div className="flex items-center">
                    <Warehouse className="h-4 w-4 mr-2 text-gray-500" />
                    {record.location}
                  </div>
                </div>
                <div className="flex items-center text-sm">
                  <Boxes className="h-4 w-4 mr-2 text-gray-500" />
                  当前库存: {record.currentStock} {record.unit}
                </div>
                <div className="flex items-center text-sm">
                  <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                  单价: {record.unitPrice}元
                </div>
                <div className="flex items-center text-sm">
                  <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                  供应商: {record.supplier}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">物资说明:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{record.description}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openInboundDialog(record)}>
                    <PackagePlus className="h-4 w-4 mr-2" />
                    入库
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openOutboundDialog(record)}>
                    <PackageMinus className="h-4 w-4 mr-2" />
                    出库
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openEditDialog(record)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(record)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>库存记录列表</CardTitle>
          <CardDescription>管理所有物资库存</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>物资名称</TableHead>
                <TableHead>规格型号</TableHead>
                <TableHead>类别</TableHead>
                <TableHead>库位</TableHead>
                <TableHead>当前库存</TableHead>
                <TableHead>单价</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.itemName}</TableCell>
                  <TableCell>{record.specification}</TableCell>
                  <TableCell>{record.category}</TableCell>
                  <TableCell>{record.location}</TableCell>
                  <TableCell>{record.currentStock} {record.unit}</TableCell>
                  <TableCell>{record.unitPrice}元</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openInboundDialog(record)}>
                      <PackagePlus className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openOutboundDialog(record)}>
                      <PackageMinus className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(record)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 入库对话框 */}
      <Dialog open={isInboundDialogOpen} onOpenChange={setIsInboundDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>物资入库</DialogTitle>
            <DialogDescription>
              请填写入库信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>物资信息</Label>
              <div className="text-sm text-gray-500">
                <div>名称: {currentRecord.itemName}</div>
                <div>规格: {currentRecord.specification}</div>
                <div>当前库存: {currentRecord.currentStock} {currentRecord.unit}</div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="inbound-quantity">入库数量</Label>
              <Input
                id="inbound-quantity"
                type="number"
                placeholder="请输入入库数量"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="inbound-operator">操作人</Label>
              <Input
                id="inbound-operator"
                placeholder="请输入操作人姓名"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="inbound-batch">批次号</Label>
              <Input
                id="inbound-batch"
                placeholder="请输入批次号"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="inbound-supplier">供应商</Label>
              <Input
                id="inbound-supplier"
                value={currentRecord.supplier}
                onChange={(e) => setCurrentRecord({ ...currentRecord, supplier: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="inbound-remarks">备注</Label>
              <Textarea
                id="inbound-remarks"
                placeholder="请输入备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsInboundDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              const quantity = Number((document.getElementById("inbound-quantity") as HTMLInputElement).value)
              const operator = (document.getElementById("inbound-operator") as HTMLInputElement).value
              const batchNo = (document.getElementById("inbound-batch") as HTMLInputElement).value
              const supplier = (document.getElementById("inbound-supplier") as HTMLInputElement).value
              const remarks = (document.getElementById("inbound-remarks") as HTMLTextAreaElement).value
              handleInbound(currentRecord, quantity, operator, batchNo, supplier, remarks)
            }}>
              确认入库
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 出库对话框 */}
      <Dialog open={isOutboundDialogOpen} onOpenChange={setIsOutboundDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>物资出库</DialogTitle>
            <DialogDescription>
              请填写出库信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>物资信息</Label>
              <div className="text-sm text-gray-500">
                <div>名称: {currentRecord.itemName}</div>
                <div>规格: {currentRecord.specification}</div>
                <div>当前库存: {currentRecord.currentStock} {currentRecord.unit}</div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="outbound-quantity">出库数量</Label>
              <Input
                id="outbound-quantity"
                type="number"
                placeholder="请输入出库数量"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="outbound-operator">操作人</Label>
              <Input
                id="outbound-operator"
                placeholder="请输入操作人姓名"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="outbound-department">领用部门</Label>
              <Input
                id="outbound-department"
                placeholder="请输入领用部门"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="outbound-purpose">用途</Label>
              <Input
                id="outbound-purpose"
                placeholder="请输入用途"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="outbound-remarks">备注</Label>
              <Textarea
                id="outbound-remarks"
                placeholder="请输入备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOutboundDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              const quantity = Number((document.getElementById("outbound-quantity") as HTMLInputElement).value)
              const operator = (document.getElementById("outbound-operator") as HTMLInputElement).value
              const department = (document.getElementById("outbound-department") as HTMLInputElement).value
              const purpose = (document.getElementById("outbound-purpose") as HTMLInputElement).value
              const remarks = (document.getElementById("outbound-remarks") as HTMLTextAreaElement).value
              handleOutbound(currentRecord, quantity, operator, department, purpose, remarks)
            }}>
              确认出库
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>编辑物资信息</DialogTitle>
            <DialogDescription>
              修改物资的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">物资类型</Label>
                <Select
                  value={currentRecord.type}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择物资类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="物资">物资</SelectItem>
                    <SelectItem value="设备">设备</SelectItem>
                    <SelectItem value="工具">工具</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-itemName">物资名称</Label>
                <Input
                  id="edit-itemName"
                  value={currentRecord.itemName}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, itemName: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-specification">规格型号</Label>
                <Input
                  id="edit-specification"
                  value={currentRecord.specification}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, specification: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-unit">单位</Label>
                <Input
                  id="edit-unit"
                  value={currentRecord.unit}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, unit: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-category">物资类别</Label>
                <Select
                  value={currentRecord.category}
                  onValueChange={(value) => setCurrentRecord({ ...currentRecord, category: value })}
                >
                  <SelectTrigger id="edit-category">
                    <SelectValue placeholder="选择物资类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="安全防护">安全防护</SelectItem>
                    <SelectItem value="照明设备">照明设备</SelectItem>
                    <SelectItem value="工具设备">工具设备</SelectItem>
                    <SelectItem value="办公用品">办公用品</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-location">库位</Label>
                <Input
                  id="edit-location"
                  value={currentRecord.location}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, location: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-minStock">最低库存</Label>
                <Input
                  id="edit-minStock"
                  type="number"
                  value={currentRecord.minStock}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, minStock: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-maxStock">最高库存</Label>
                <Input
                  id="edit-maxStock"
                  type="number"
                  value={currentRecord.maxStock}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, maxStock: Number(e.target.value) })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-unitPrice">单价</Label>
                <Input
                  id="edit-unitPrice"
                  type="number"
                  value={currentRecord.unitPrice}
                  onChange={(e) => setCurrentRecord({ ...currentRecord, unitPrice: Number(e.target.value) })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-supplier">供应商</Label>
              <Input
                id="edit-supplier"
                value={currentRecord.supplier}
                onChange={(e) => setCurrentRecord({ ...currentRecord, supplier: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">物资说明</Label>
              <Textarea
                id="edit-description"
                value={currentRecord.description}
                onChange={(e) => setCurrentRecord({ ...currentRecord, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRecord}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentRecord.itemName}" 的库存记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
