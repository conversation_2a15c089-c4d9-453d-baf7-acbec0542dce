"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  FileText,
  Heart,
  Users,
  Calendar,
  DollarSign,
  Clock,
  HelpCircle,
  Target,
  Award,
  ChevronUp,
  ChevronDown,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  MapPin,
  Phone,
  User,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import * as XLSX from 'xlsx-js-style'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart, Pie, Cell } from 'recharts'
import { cn } from "@/lib/utils"

interface UnionSupportRecord {
  id: string
  employeeName: string
  employeeId: string
  department: string
  supportType: string
  supportAmount: number
  supportDate: string
  supportReason: string
  status: string
  createdAt: string
  updatedAt: string
  approver?: string
  contactPhone?: string
  familySize?: number
  previousSupport?: string
}

interface UnionActivityRecord {
  id: string
  activityName: string
  organizer: string
  startDate: string
  endDate: string
  location: string
  participants: number
  budget: number
  status: string
  description: string
  requirements?: string
  materials?: string[]
  registrationDeadline?: string
  contactPerson?: string
  contactPhone?: string
  createdAt: string
  updatedAt: string
}

export function UnionSupportEnhanced() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("support")
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(false)
  const [isAddSupportOpen, setIsAddSupportOpen] = useState(false)
  const [isEditSupportOpen, setIsEditSupportOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedSupport, setSelectedSupport] = useState<UnionSupportRecord | null>(null)
  const [selectedActivity, setSelectedActivity] = useState<UnionActivityRecord | null>(null)
  const [isViewActivityOpen, setIsViewActivityOpen] = useState(false)
  const [isAddActivityOpen, setIsAddActivityOpen] = useState(false)
  const [isEditActivityOpen, setIsEditActivityOpen] = useState(false)

  // 帮扶类型统计数据
  const supportTypeStats = [
    { name: '困难帮扶', value: 35 },
    { name: '医疗帮扶', value: 25 },
    { name: '子女教育', value: 20 },
    { name: '灾害帮扶', value: 15 },
    { name: '其他帮扶', value: 5 },
  ]

  // 帮扶金额趋势数据
  const supportAmountTrend = [
    { month: '1月', amount: 45000 },
    { month: '2月', amount: 52000 },
    { month: '3月', amount: 48000 },
    { month: '4月', amount: 60000 },
    { month: '5月', amount: 55000 },
    { month: '6月', amount: 65000 },
  ]

  const COLORS = ['#4f46e5', '#22c55e', '#eab308', '#ef4444', '#6b7280']

  const [supportRecords, setSupportRecords] = useState<UnionSupportRecord[]>([
    {
      id: "SP001",
      employeeName: "张三",
      employeeId: "EMP001",
      department: "采矿部",
      supportType: "困难帮扶",
      supportAmount: 5000,
      supportDate: "2024-03-15",
      supportReason: "家庭成员重病住院",
      status: "已发放",
      approver: "李经理",
      contactPhone: "13800138000",
      familySize: 4,
      previousSupport: "2023年医疗帮扶",
      createdAt: "2024-03-01",
      updatedAt: "2024-03-15"
    },
    {
      id: "SP002",
      employeeName: "李四",
      employeeId: "EMP045",
      department: "安全部",
      supportType: "子女教育",
      supportAmount: 3000,
      supportDate: "2024-03-20",
      supportReason: "子女大学入学资助",
      status: "审核中",
      approver: "王主管",
      contactPhone: "13900139000",
      familySize: 3,
      createdAt: "2024-03-05",
      updatedAt: "2024-03-05"
    },
    {
      id: "SP003",
      employeeName: "王五",
      employeeId: "EMP078",
      department: "机电部",
      supportType: "医疗帮扶",
      supportAmount: 8000,
      supportDate: "2024-03-25",
      supportReason: "重大疾病治疗",
      status: "已发放",
      approver: "赵经理",
      contactPhone: "13700137000",
      familySize: 5,
      previousSupport: "2023年困难帮扶",
      createdAt: "2024-03-10",
      updatedAt: "2024-03-10"
    },
    {
      id: "SP004",
      employeeName: "赵六",
      employeeId: "EMP102",
      department: "运输部",
      supportType: "灾害帮扶",
      supportAmount: 10000,
      supportDate: "2024-04-05",
      supportReason: "家庭遭遇自然灾害",
      status: "已发放",
      approver: "钱总监",
      contactPhone: "13600136000",
      familySize: 4,
      createdAt: "2024-03-15",
      updatedAt: "2024-03-15"
    },
    {
      id: "SP005",
      employeeName: "钱七",
      employeeId: "EMP156",
      department: "通风部",
      supportType: "困难帮扶",
      supportAmount: 4000,
      supportDate: "2024-04-10",
      supportReason: "家庭经济困难",
      status: "审核中",
      approver: "孙经理",
      contactPhone: "13500135000",
      familySize: 3,
      createdAt: "2024-03-20",
      updatedAt: "2024-03-20"
    }
  ])

  const [unionActivities, setUnionActivities] = useState<UnionActivityRecord[]>([
    {
      id: "UA001",
      activityName: "职工文化节",
      organizer: "矿山工会",
      startDate: "2024-04-01",
      endDate: "2024-04-07",
      location: "矿区文化中心",
      participants: 350,
      budget: 50000,
      status: "未开始",
      description: "丰富职工文化生活，展示职工才艺",
      requirements: "各部门至少组织20人参加",
      materials: ["舞台设备", "展板", "奖品"],
      registrationDeadline: "2024-03-25",
      contactPerson: "张组长",
      contactPhone: "13800138001",
      createdAt: "2024-03-01",
      updatedAt: "2024-03-01"
    },
    {
      id: "UA002",
      activityName: "职工运动会",
      organizer: "工会体育部",
      startDate: "2024-05-01",
      endDate: "2024-05-03",
      location: "矿区体育场",
      participants: 500,
      budget: 80000,
      status: "筹备中",
      description: "增强职工体质，促进部门交流",
      requirements: "身体健康，适合运动",
      materials: ["体育器材", "医疗用品", "饮用水"],
      registrationDeadline: "2024-04-20",
      contactPerson: "李主管",
      contactPhone: "13900139001",
      createdAt: "2024-03-05",
      updatedAt: "2024-03-05"
    }
  ])

  // 筛选帮扶记录
  const filteredSupportRecords = supportRecords.filter(record => 
    (typeFilter === "all" || record.supportType === typeFilter) &&
    (statusFilter === "all" || record.status === statusFilter) &&
    (searchTerm === "" || 
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.department.toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  // 筛选工会活动
  const filteredActivities = unionActivities.filter(activity => 
    (statusFilter === "all" || activity.status === statusFilter) &&
    (searchTerm === "" || 
      activity.activityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.organizer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.location.toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  // 处理查看详情
  const handleViewDetail = (record: UnionSupportRecord) => {
    setSelectedSupport(record)
    setIsViewDetailOpen(true)
  }

  // 处理编辑帮扶记录
  const handleEditSupport = (record: UnionSupportRecord) => {
    setSelectedSupport(record)
    setIsEditSupportOpen(true)
  }

  // 处理删除帮扶记录
  const handleDeleteSupport = (record: UnionSupportRecord) => {
    setSelectedSupport(record)
    setIsDeleteDialogOpen(true)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "刷新成功",
        description: "数据已更新",
      })
    }, 1000)
  }

  // 导出数据
  const handleExport = () => {
    try {
      const exportData = supportRecords.map(record => ({
        '员工姓名': record.employeeName,
        '员工编号': record.employeeId,
        '部门': record.department,
        '帮扶类型': record.supportType,
        '帮扶金额': record.supportAmount,
        '帮扶日期': record.supportDate,
        '帮扶原因': record.supportReason,
        '状态': record.status,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 15 }, // 员工姓名
        { wch: 15 }, // 员工编号
        { wch: 15 }, // 部门
        { wch: 15 }, // 帮扶类型
        { wch: 15 }, // 帮扶金额
        { wch: 15 }, // 帮扶日期
        { wch: 40 }, // 帮扶原因
        { wch: 10 }, // 状态
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '工会帮扶记录')
      XLSX.writeFile(wb, `工会帮扶记录_${new Date().toLocaleDateString()}.xlsx`)
      
      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理添加帮扶记录
  const handleAddSupport = (formData: Partial<UnionSupportRecord>) => {
    const newRecord: UnionSupportRecord = {
      id: `SP${(supportRecords.length + 1).toString().padStart(3, '0')}`,
      employeeName: formData.employeeName || "",
      employeeId: formData.employeeId || "",
      department: formData.department || "",
      supportType: formData.supportType || "其他帮扶",
      supportAmount: formData.supportAmount || 0,
      supportDate: formData.supportDate || new Date().toISOString().split('T')[0],
      supportReason: formData.supportReason || "",
      status: "审核中",
      approver: formData.approver,
      contactPhone: formData.contactPhone,
      familySize: formData.familySize,
      previousSupport: formData.previousSupport,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setSupportRecords(prev => [...prev, newRecord])
    toast({
      title: "添加成功",
      description: "已成功添加新的帮扶记录",
    })
    setIsAddSupportOpen(false)
  }

  // 处理更新帮扶记录
  const handleUpdateSupport = (formData: Partial<UnionSupportRecord>) => {
    if (!selectedSupport) return

    setSupportRecords(prev => prev.map(record => 
      record.id === selectedSupport.id 
        ? { 
            ...record, 
            ...formData,
            updatedAt: new Date().toISOString()
          }
        : record
    ))

    toast({
      title: "更新成功",
      description: "已成功更新帮扶记录",
    })
    setIsEditSupportOpen(false)
    setSelectedSupport(null)
  }

  // 处理删除帮扶记录
  const handleConfirmDelete = () => {
    if (!selectedSupport) return

    setSupportRecords(prev => prev.filter(record => record.id !== selectedSupport.id))
    toast({
      title: "删除成功",
      description: "已成功删除帮扶记录",
    })
    setIsDeleteDialogOpen(false)
    setSelectedSupport(null)
  }

  // 处理查看活动详情
  const handleViewActivity = (activity: UnionActivityRecord) => {
    setSelectedActivity(activity)
    setIsViewActivityOpen(true)
  }

  // 处理编辑活动
  const handleEditActivity = (activity: UnionActivityRecord) => {
    setSelectedActivity(activity)
    setIsEditActivityOpen(true)
  }

  // 处理删除活动
  const handleDeleteActivity = (activity: UnionActivityRecord) => {
    setSelectedActivity(activity)
    setIsDeleteDialogOpen(true)
  }

  // 处理添加活动
  const handleAddActivity = (formData: Partial<UnionActivityRecord>) => {
    const newActivity: UnionActivityRecord = {
      id: `UA${(unionActivities.length + 1).toString().padStart(3, '0')}`,
      activityName: formData.activityName || "",
      organizer: formData.organizer || "",
      startDate: formData.startDate || new Date().toISOString().split('T')[0],
      endDate: formData.endDate || new Date().toISOString().split('T')[0],
      location: formData.location || "",
      participants: formData.participants || 0,
      budget: formData.budget || 0,
      status: "未开始",
      description: formData.description || "",
      requirements: formData.requirements,
      materials: formData.materials,
      registrationDeadline: formData.registrationDeadline,
      contactPerson: formData.contactPerson,
      contactPhone: formData.contactPhone,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setUnionActivities(prev => [...prev, newActivity])
    toast({
      title: "添加成功",
      description: "已成功添加新的工会活动",
    })
    setIsAddActivityOpen(false)
  }

  // 处理更新活动
  const handleUpdateActivity = (formData: Partial<UnionActivityRecord>) => {
    if (!selectedActivity) return

    setUnionActivities(prev => prev.map(activity => 
      activity.id === selectedActivity.id 
        ? { 
            ...activity, 
            ...formData,
            updatedAt: new Date().toISOString()
          }
        : activity
    ))

    toast({
      title: "更新成功",
      description: "已成功更新工会活动",
    })
    setIsEditActivityOpen(false)
    setSelectedActivity(null)
  }

  // 处理确认删除活动
  const handleConfirmDeleteActivity = () => {
    if (!selectedActivity) return

    setUnionActivities(prev => prev.filter(activity => activity.id !== selectedActivity.id))
    toast({
      title: "删除成功",
      description: "已成功删除工会活动",
    })
    setIsDeleteDialogOpen(false)
    setSelectedActivity(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">工会帮扶管理</h2>
          <p className="text-muted-foreground">管理工会帮扶记录和活动信息</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
          <Button onClick={handleRefresh}>
            <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
            刷新数据
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">帮扶总数</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supportRecords.length}</div>
            <p className="text-xs text-muted-foreground">
              +{supportRecords.filter(r => new Date(r.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length} 条新增记录
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">帮扶总金额</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {supportRecords.reduce((sum, r) => sum + r.supportAmount, 0).toLocaleString('zh-CN', {
                style: 'currency',
                currency: 'CNY'
              })}
            </div>
            <p className="text-xs text-muted-foreground">
              本月支出：
              {supportRecords
                .filter(r => new Date(r.supportDate).getMonth() === new Date().getMonth())
                .reduce((sum, r) => sum + r.supportAmount, 0)
                .toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' })}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supportRecords.filter(r => r.status === "审核中").length}</div>
            <p className="text-xs text-muted-foreground">
              审核通过率：
              {((supportRecords.filter(r => r.status === "已发放").length / supportRecords.length) * 100).toFixed(1)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">工会活动</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unionActivities.length}</div>
            <p className="text-xs text-muted-foreground">
              参与总人数：{unionActivities.reduce((sum, a) => sum + a.participants, 0)} 人
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>帮扶类型分布</CardTitle>
            <CardDescription>各类型帮扶数量统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <PieChart width={300} height={300}>
                <Pie
                  data={supportTypeStats}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {supportTypeStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>帮扶金额趋势</CardTitle>
            <CardDescription>每月帮扶金额变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <BarChart
                width={500}
                height={300}
                data={supportAmountTrend}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="amount" fill="#4f46e5" name="帮扶金额" />
              </BarChart>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="support" className="space-y-4">
        <TabsList>
          <TabsTrigger value="support">帮扶管理</TabsTrigger>
          <TabsTrigger value="activity">活动管理</TabsTrigger>
        </TabsList>
        <TabsContent value="support" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>帮扶记录</CardTitle>
                  <CardDescription>管理帮扶申请和发放记录</CardDescription>
                </div>
                <Button onClick={() => setIsAddSupportOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  新增帮扶
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="搜索员工姓名、工号或部门..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="帮扶类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="困难帮扶">困难帮扶</SelectItem>
                      <SelectItem value="医疗帮扶">医疗帮扶</SelectItem>
                      <SelectItem value="子女教育">子女教育</SelectItem>
                      <SelectItem value="灾害帮扶">灾害帮扶</SelectItem>
                      <SelectItem value="其他帮扶">其他帮扶</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="审核中">审核中</SelectItem>
                      <SelectItem value="已发放">已发放</SelectItem>
                      <SelectItem value="已驳回">已驳回</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>帮扶编号</TableHead>
                        <TableHead>员工姓名</TableHead>
                        <TableHead>部门</TableHead>
                        <TableHead>帮扶类型</TableHead>
                        <TableHead>帮扶金额</TableHead>
                        <TableHead>帮扶日期</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSupportRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>{record.employeeName}</span>
                            </div>
                          </TableCell>
                          <TableCell>{record.department}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{record.supportType}</Badge>
                          </TableCell>
                          <TableCell>
                            {record.supportAmount.toLocaleString("zh-CN", {
                              style: "currency",
                              currency: "CNY"
                            })}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span>{record.supportDate}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                record.status === "已发放"
                                  ? "default"
                                  : record.status === "审核中"
                                    ? "outline"
                                    : "secondary"
                              }
                              className={
                                record.status === "已发放"
                                  ? "bg-green-100 text-green-700"
                                  : record.status === "审核中"
                                    ? "bg-blue-50 text-blue-700"
                                    : "bg-slate-100 text-slate-700"
                              }
                            >
                              {record.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleViewDetail(record)}
                              >
                                <FileText className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditSupport(record)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteSupport(record)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>工会活动</CardTitle>
                  <CardDescription>管理工会活动信息</CardDescription>
                </div>
                <Button onClick={() => setIsAddActivityOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  新增活动
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="搜索活动名称、组织者或地点..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="活动状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="未开始">未开始</SelectItem>
                      <SelectItem value="筹备中">筹备中</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已结束">已结束</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>活动编号</TableHead>
                      <TableHead>活动名称</TableHead>
                      <TableHead>组织者</TableHead>
                      <TableHead>开始日期</TableHead>
                      <TableHead>地点</TableHead>
                      <TableHead>参与人数</TableHead>
                      <TableHead>预算</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredActivities.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>{activity.id}</TableCell>
                        <TableCell>{activity.activityName}</TableCell>
                        <TableCell>{activity.organizer}</TableCell>
                        <TableCell>{activity.startDate}</TableCell>
                        <TableCell>{activity.location}</TableCell>
                        <TableCell>{activity.participants}</TableCell>
                        <TableCell>
                          {activity.budget.toLocaleString('zh-CN', {
                            style: 'currency',
                            currency: 'CNY'
                          })}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              activity.status === "已结束"
                                ? "secondary"
                                : activity.status === "进行中"
                                ? "default"
                                : activity.status === "未开始"
                                ? "outline"
                                : "default"
                            }
                          >
                            {activity.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleViewActivity(activity)}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditActivity(activity)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteActivity(activity)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 查看帮扶记录详情对话框 */}
      <Dialog open={isViewDetailOpen && selectedSupport !== null} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>帮扶记录详情</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[500px] w-full rounded-md border p-4">
            {selectedSupport && (
              <div className="space-y-6">
                <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <Heart className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedSupport.employeeName}</h3>
                    <p className="text-sm text-muted-foreground">{selectedSupport.employeeId}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>部门</Label>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedSupport.department}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>帮扶类型</Label>
                    <Badge variant="outline">{selectedSupport.supportType}</Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>帮扶金额</Label>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {selectedSupport.supportAmount.toLocaleString('zh-CN', {
                          style: 'currency',
                          currency: 'CNY'
                        })}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>帮扶日期</Label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedSupport.supportDate}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>联系电话</Label>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedSupport.contactPhone}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>家庭人数</Label>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedSupport.familySize} 人</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>帮扶原因</Label>
                  <div className="p-4 bg-slate-50 rounded-lg">
                    <p className="text-sm">{selectedSupport.supportReason}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>历史帮扶记录</Label>
                  <div className="p-4 bg-slate-50 rounded-lg">
                    <p className="text-sm">{selectedSupport.previousSupport || "无"}</p>
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* 添加/编辑帮扶记录对话框 */}
      <Dialog open={isAddSupportOpen || isEditSupportOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddSupportOpen(false)
          setIsEditSupportOpen(false)
          setSelectedSupport(null)
        }
      }}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isEditSupportOpen ? "编辑帮扶记录" : "新增帮扶记录"}</DialogTitle>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const data: Partial<UnionSupportRecord> = {
              employeeName: formData.get("employeeName") as string,
              employeeId: formData.get("employeeId") as string,
              department: formData.get("department") as string,
              supportType: formData.get("supportType") as string,
              supportAmount: Number(formData.get("supportAmount")),
              supportDate: formData.get("supportDate") as string,
              supportReason: formData.get("supportReason") as string,
              contactPhone: formData.get("contactPhone") as string,
              familySize: Number(formData.get("familySize")),
              previousSupport: formData.get("previousSupport") as string,
            }
            if (isEditSupportOpen && selectedSupport) {
              handleUpdateSupport(data)
            } else {
              handleAddSupport(data)
            }
          }}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employeeName">员工姓名</Label>
                  <Input
                    id="employeeName"
                    name="employeeName"
                    defaultValue={selectedSupport?.employeeName}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employeeId">员工编号</Label>
                  <Input
                    id="employeeId"
                    name="employeeId"
                    defaultValue={selectedSupport?.employeeId}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="department">所属部门</Label>
                  <Input
                    id="department"
                    name="department"
                    defaultValue={selectedSupport?.department}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="supportType">帮扶类型</Label>
                  <Select
                    name="supportType"
                    defaultValue={selectedSupport?.supportType}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择帮扶类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="困难帮扶">困难帮扶</SelectItem>
                      <SelectItem value="医疗帮扶">医疗帮扶</SelectItem>
                      <SelectItem value="子女教育">子女教育</SelectItem>
                      <SelectItem value="灾害帮扶">灾害帮扶</SelectItem>
                      <SelectItem value="其他帮扶">其他帮扶</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="supportAmount">帮扶金额</Label>
                  <Input
                    id="supportAmount"
                    name="supportAmount"
                    type="number"
                    defaultValue={selectedSupport?.supportAmount}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="supportDate">帮扶日期</Label>
                  <Input
                    id="supportDate"
                    name="supportDate"
                    type="date"
                    defaultValue={selectedSupport?.supportDate}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <Input
                    id="contactPhone"
                    name="contactPhone"
                    defaultValue={selectedSupport?.contactPhone}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="familySize">家庭人数</Label>
                  <Input
                    id="familySize"
                    name="familySize"
                    type="number"
                    defaultValue={selectedSupport?.familySize}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="supportReason">帮扶原因</Label>
                <Textarea
                  id="supportReason"
                  name="supportReason"
                  defaultValue={selectedSupport?.supportReason}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="previousSupport">历史帮扶记录</Label>
                <Textarea
                  id="previousSupport"
                  name="previousSupport"
                  defaultValue={selectedSupport?.previousSupport}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">
                {isEditSupportOpen ? "保存修改" : "提交申请"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 添加/编辑活动对话框 */}
      <Dialog open={isAddActivityOpen || isEditActivityOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddActivityOpen(false)
          setIsEditActivityOpen(false)
          setSelectedActivity(null)
        }
      }}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isEditActivityOpen ? "编辑活动" : "新增活动"}</DialogTitle>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const data: Partial<UnionActivityRecord> = {
              activityName: formData.get("activityName") as string,
              organizer: formData.get("organizer") as string,
              startDate: formData.get("startDate") as string,
              endDate: formData.get("endDate") as string,
              location: formData.get("location") as string,
              participants: Number(formData.get("participants")),
              budget: Number(formData.get("budget")),
              description: formData.get("description") as string,
              requirements: formData.get("requirements") as string,
              materials: (formData.get("materials") as string).split(",").map(item => item.trim()),
              registrationDeadline: formData.get("registrationDeadline") as string,
              contactPerson: formData.get("contactPerson") as string,
              contactPhone: formData.get("contactPhone") as string,
            }
            if (isEditActivityOpen && selectedActivity) {
              handleUpdateActivity(data)
            } else {
              handleAddActivity(data)
            }
          }}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="activityName">活动名称</Label>
                  <Input
                    id="activityName"
                    name="activityName"
                    defaultValue={selectedActivity?.activityName}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organizer">组织者</Label>
                  <Input
                    id="organizer"
                    name="organizer"
                    defaultValue={selectedActivity?.organizer}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">开始日期</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    defaultValue={selectedActivity?.startDate}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">结束日期</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    defaultValue={selectedActivity?.endDate}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">活动地点</Label>
                  <Input
                    id="location"
                    name="location"
                    defaultValue={selectedActivity?.location}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="participants">参与人数</Label>
                  <Input
                    id="participants"
                    name="participants"
                    type="number"
                    defaultValue={selectedActivity?.participants}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">活动预算</Label>
                  <Input
                    id="budget"
                    name="budget"
                    type="number"
                    defaultValue={selectedActivity?.budget}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="registrationDeadline">报名截止日期</Label>
                  <Input
                    id="registrationDeadline"
                    name="registrationDeadline"
                    type="date"
                    defaultValue={selectedActivity?.registrationDeadline}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">活动描述</Label>
                <Textarea
                  id="description"
                  name="description"
                  defaultValue={selectedActivity?.description}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="requirements">参与要求</Label>
                <Textarea
                  id="requirements"
                  name="requirements"
                  defaultValue={selectedActivity?.requirements}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="materials">所需物资（用逗号分隔）</Label>
                <Input
                  id="materials"
                  name="materials"
                  defaultValue={selectedActivity?.materials?.join(", ")}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactPerson">联系人</Label>
                  <Input
                    id="contactPerson"
                    name="contactPerson"
                    defaultValue={selectedActivity?.contactPerson}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <Input
                    id="contactPhone"
                    name="contactPhone"
                    defaultValue={selectedActivity?.contactPhone}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">
                {isEditActivityOpen ? "保存修改" : "创建活动"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              此操作无法撤销，请确认是否要删除该记录？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => {
                if (selectedSupport) {
                  handleConfirmDelete()
                } else if (selectedActivity) {
                  handleConfirmDeleteActivity()
                }
              }}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 