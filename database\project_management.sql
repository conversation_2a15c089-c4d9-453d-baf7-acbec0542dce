-- 工程管理模块数据库初始化脚本
-- 创建于2025年1月1日
-- 包含工程项目、进度、质量等工程管理相关数据

-- 创建工程项目表
CREATE TABLE IF NOT EXISTS `projects` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(50) NOT NULL UNIQUE,
  `type` VARCHAR(50) NOT NULL,
  `location` VARCHAR(100),
  `start_date` DATE NOT NULL,
  `planned_end_date` DATE NOT NULL,
  `actual_end_date` DATE,
  `budget` DECIMAL(15,2) NOT NULL,
  `actual_cost` DECIMAL(15,2),
  `manager_id` INT,
  `manager` VARCHAR(50) NOT NULL,
  `status` ENUM('未开始', '进行中', '已完成', '已暂停', '已取消') DEFAULT '未开始',
  `progress` INT DEFAULT 0,
  `priority` ENUM('低', '中', '高', '紧急') DEFAULT '中',
  `description` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程阶段表
CREATE TABLE IF NOT EXISTS `project_phases` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `start_date` DATE NOT NULL,
  `planned_end_date` DATE NOT NULL,
  `actual_end_date` DATE,
  `weight` INT DEFAULT 0,
  `status` ENUM('未开始', '进行中', '已完成', '已暂停') DEFAULT '未开始',
  `progress` INT DEFAULT 0,
  `description` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程任务表
CREATE TABLE IF NOT EXISTS `project_tasks` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `phase_id` INT,
  `name` VARCHAR(100) NOT NULL,
  `start_date` DATE NOT NULL,
  `planned_end_date` DATE NOT NULL,
  `actual_end_date` DATE,
  `assignee_id` INT,
  `assignee` VARCHAR(50),
  `status` ENUM('未开始', '进行中', '已完成', '已暂停', '已取消') DEFAULT '未开始',
  `progress` INT DEFAULT 0,
  `priority` ENUM('低', '中', '高', '紧急') DEFAULT '中',
  `description` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程进度记录表
CREATE TABLE IF NOT EXISTS `project_progress` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `record_date` DATE NOT NULL,
  `progress` INT NOT NULL,
  `description` TEXT,
  `recorder_id` INT,
  `recorder` VARCHAR(50) NOT NULL,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建工程质量检查表
CREATE TABLE IF NOT EXISTS `project_quality_checks` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `phase_id` INT,
  `check_date` DATE NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `location` VARCHAR(100),
  `inspector_id` INT,
  `inspector` VARCHAR(50) NOT NULL,
  `status` ENUM('合格', '不合格', '待整改') NOT NULL,
  `issues_count` INT DEFAULT 0,
  `description` TEXT,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程质量问题表
CREATE TABLE IF NOT EXISTS `project_quality_issues` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `check_id` INT NOT NULL,
  `project_id` INT NOT NULL,
  `title` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `location` VARCHAR(100),
  `level` ENUM('轻微', '一般', '严重') NOT NULL,
  `status` ENUM('待处理', '处理中', '已解决', '已验收') DEFAULT '待处理',
  `responsible_id` INT,
  `responsible` VARCHAR(50),
  `deadline` DATE,
  `solution` TEXT,
  `solve_date` DATE,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程变更表
CREATE TABLE IF NOT EXISTS `project_changes` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `title` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `description` TEXT,
  `reason` TEXT,
  `impact` TEXT,
  `cost_change` DECIMAL(15,2) DEFAULT 0,
  `schedule_change` INT DEFAULT 0,
  `proposer_id` INT,
  `proposer` VARCHAR(50) NOT NULL,
  `propose_date` DATE NOT NULL,
  `status` ENUM('待审批', '已批准', '已拒绝', '已实施') DEFAULT '待审批',
  `approver_id` INT,
  `approver` VARCHAR(50),
  `approval_date` DATE,
  `implementation_date` DATE,
  `attachments` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程风险表
CREATE TABLE IF NOT EXISTS `project_risks` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `title` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `description` TEXT,
  `probability` ENUM('低', '中', '高') NOT NULL,
  `impact` ENUM('低', '中', '高') NOT NULL,
  `level` ENUM('低', '中', '高', '极高') NOT NULL,
  `status` ENUM('监控中', '已发生', '已解决', '已关闭') DEFAULT '监控中',
  `response_plan` TEXT,
  `responsible_id` INT,
  `responsible` VARCHAR(50),
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建工程资源分配表
CREATE TABLE IF NOT EXISTS `project_resources` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `project_id` INT NOT NULL,
  `resource_type` ENUM('人力', '设备', '材料', '资金') NOT NULL,
  `resource_name` VARCHAR(100) NOT NULL,
  `quantity` DECIMAL(10,2) NOT NULL,
  `unit` VARCHAR(20),
  `allocation_date` DATE NOT NULL,
  `release_date` DATE,
  `status` ENUM('已分配', '使用中', '已释放') DEFAULT '已分配',
  `description` TEXT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入工程项目数据
INSERT INTO `projects` (`name`, `code`, `type`, `location`, `start_date`, `planned_end_date`, `budget`, `manager_id`, `manager`, `status`, `progress`, `priority`, `description`) VALUES
('A区矿井开拓工程', 'PRJ-2025-001', '开拓工程', 'A区', '2025-02-01', '2025-08-31', 5000000.00, 3, '李四', '进行中', 25, '高', 'A区新矿井开拓工程，包括井筒开挖、支护、运输系统安装等工作'),
('B区安全改造工程', 'PRJ-2025-002', '改造工程', 'B区', '2025-03-01', '2025-06-30', 2000000.00, 3, '李四', '进行中', 15, '高', 'B区矿井安全改造工程，包括通风系统改造、安全监测系统升级等'),
('C区选矿厂扩建工程', 'PRJ-2025-003', '扩建工程', 'C区', '2025-04-01', '2025-12-31', 8000000.00, 3, '李四', '未开始', 0, '中', 'C区选矿厂扩建工程，提高选矿能力和效率'),
('矿区道路维修工程', 'PRJ-2025-004', '维修工程', '全矿区', '2025-02-15', '2025-04-15', 1000000.00, 8, '周九', '进行中', 60, '中', '矿区主要道路的维修和改造工程，提高运输效率和安全性'),
('办公楼节能改造工程', 'PRJ-2025-005', '改造工程', 'D区', '2025-03-15', '2025-05-15', 800000.00, 8, '周九', '未开始', 0, '低', '办公楼节能改造工程，包括照明系统、空调系统、门窗改造等'),
('尾矿库扩容工程', 'PRJ-2025-006', '扩建工程', 'E区', '2025-01-10', '2025-07-31', 3000000.00, 3, '李四', '进行中', 40, '高', '尾矿库扩容工程，提高尾矿存储能力，确保生产安全');

-- 插入工程阶段数据
INSERT INTO `project_phases` (`project_id`, `name`, `start_date`, `planned_end_date`, `weight`, `status`, `progress`, `description`) VALUES
(1, '前期准备', '2025-02-01', '2025-02-28', 10, '已完成', 100, '项目前期准备工作，包括设计审查、施工队伍组建、设备材料准备等'),
(1, '井筒开挖', '2025-03-01', '2025-05-31', 40, '进行中', 30, '井筒开挖工作，包括竖井和平巷的开挖'),
(1, '支护施工', '2025-04-15', '2025-06-30', 30, '未开始', 0, '井筒支护施工，确保井筒稳定和安全'),
(1, '设备安装', '2025-07-01', '2025-08-15', 15, '未开始', 0, '井下设备安装，包括运输系统、通风系统、排水系统等'),
(1, '验收交付', '2025-08-16', '2025-08-31', 5, '未开始', 0, '工程验收和交付使用'),
(2, '前期准备', '2025-03-01', '2025-03-15', 10, '已完成', 100, '项目前期准备工作，包括设计审查、施工队伍组建、设备材料准备等'),
(2, '通风系统改造', '2025-03-16', '2025-05-15', 50, '进行中', 20, '通风系统改造工作，包括风机更换、风道优化等'),
(2, '监测系统升级', '2025-05-16', '2025-06-15', 30, '未开始', 0, '安全监测系统升级，包括气体监测、视频监控等'),
(2, '验收交付', '2025-06-16', '2025-06-30', 10, '未开始', 0, '工程验收和交付使用');

-- 插入工程任务数据
INSERT INTO `project_tasks` (`project_id`, `phase_id`, `name`, `start_date`, `planned_end_date`, `assignee_id`, `assignee`, `status`, `progress`, `priority`, `description`) VALUES
(1, 1, '设计审查', '2025-02-01', '2025-02-10', 3, '李四', '已完成', 100, '高', '审查工程设计文件，确保设计合理可行'),
(1, 1, '施工队伍组建', '2025-02-11', '2025-02-20', 3, '李四', '已完成', 100, '高', '组建施工队伍，明确职责分工'),
(1, 1, '设备材料准备', '2025-02-21', '2025-02-28', 8, '周九', '已完成', 100, '高', '准备工程所需的设备和材料'),
(1, 2, '竖井开挖', '2025-03-01', '2025-04-30', 10, '张小明', '进行中', 40, '高', '进行竖井开挖工作'),
(1, 2, '平巷开挖', '2025-03-15', '2025-05-31', 11, '李小红', '进行中', 20, '高', '进行平巷开挖工作'),
(2, 1, '设计审查', '2025-03-01', '2025-03-05', 3, '李四', '已完成', 100, '高', '审查改造工程设计文件'),
(2, 1, '施工队伍组建', '2025-03-06', '2025-03-10', 3, '李四', '已完成', 100, '高', '组建改造工程施工队伍'),
(2, 1, '设备材料准备', '2025-03-11', '2025-03-15', 8, '周九', '已完成', 100, '高', '准备改造工程所需的设备和材料'),
(2, 2, '主风机更换', '2025-03-16', '2025-04-15', 12, '王小军', '进行中', 30, '高', '更换主风机设备'),
(2, 2, '风道优化改造', '2025-04-16', '2025-05-15', 12, '王小军', '未开始', 0, '高', '优化风道布局和结构');

-- 插入工程进度记录数据
INSERT INTO `project_progress` (`project_id`, `record_date`, `progress`, `description`, `recorder_id`, `recorder`) VALUES
(1, '2025-02-28', 10, '完成前期准备工作，项目正式进入施工阶段', 3, '李四'),
(1, '2025-03-15', 15, '竖井开挖进展顺利，已完成计划的30%', 3, '李四'),
(1, '2025-03-31', 20, '竖井开挖继续推进，平巷开挖已启动', 3, '李四'),
(1, '2025-04-15', 25, '竖井开挖已完成40%，平巷开挖完成20%', 3, '李四'),
(2, '2025-03-15', 10, '完成前期准备工作，改造工程正式启动', 3, '李四'),
(2, '2025-03-31', 15, '主风机更换工作进展顺利', 3, '李四'),
(4, '2025-02-28', 20, '完成道路勘测和设计，开始路基处理', 8, '周九'),
(4, '2025-03-15', 40, '路基处理基本完成，开始路面铺设', 8, '周九'),
(4, '2025-03-31', 60, '主要路段路面铺设完成，开始附属设施安装', 8, '周九'),
(6, '2025-01-31', 15, '完成前期准备和场地清理工作', 3, '李四'),
(6, '2025-02-28', 30, '挡土坝加高工程进展顺利', 3, '李四'),
(6, '2025-03-31', 40, '挡土坝加高工程基本完成，开始排水系统改造', 3, '李四');

-- 插入工程质量检查数据
INSERT INTO `project_quality_checks` (`project_id`, `phase_id`, `check_date`, `type`, `location`, `inspector_id`, `inspector`, `status`, `issues_count`, `description`) VALUES
(1, 1, '2025-02-25', '阶段验收', 'A区项目部', 7, '孙八', '合格', 0, '前期准备工作质量检查，文件齐全，人员到位，设备材料符合要求'),
(1, 2, '2025-03-20', '过程检查', 'A区竖井工作面', 7, '孙八', '不合格', 2, '竖井开挖过程质量检查，发现支护不及时和测量偏差问题'),
(1, 2, '2025-04-10', '过程检查', 'A区竖井工作面', 7, '孙八', '合格', 0, '竖井开挖过程复查，之前问题已整改完成'),
(2, 1, '2025-03-14', '阶段验收', 'B区项目部', 7, '孙八', '合格', 0, '前期准备工作质量检查，文件齐全，人员到位，设备材料符合要求'),
(2, 2, '2025-04-05', '过程检查', 'B区风机房', 7, '孙八', '待整改', 1, '主风机安装过程检查，发现基础固定不牢固问题'),
(4, NULL, '2025-03-10', '过程检查', '矿区主干道', 7, '孙八', '合格', 0, '道路路基施工质量检查，压实度符合要求'),
(4, NULL, '2025-03-25', '过程检查', '矿区主干道', 7, '孙八', '待整改', 1, '道路路面铺设质量检查，发现部分路段厚度不足'),
(6, NULL, '2025-02-20', '过程检查', 'E区尾矿库', 7, '孙八', '合格', 0, '挡土坝施工质量检查，材料和施工方法符合要求');

-- 插入工程质量问题数据
INSERT INTO `project_quality_issues` (`check_id`, `project_id`, `title`, `description`, `location`, `level`, `status`, `responsible_id`, `responsible`, `deadline`, `solution`, `solve_date`) VALUES
(2, 1, '支护不及时', '竖井开挖后支护不及时，存在安全隐患', 'A区竖井3号工作面', '严重', '已解决', 10, '张小明', '2025-03-25', '立即进行支护施工，并调整施工计划，确保开挖与支护同步进行', '2025-03-24'),
(2, 1, '测量偏差', '竖井开挖测量存在偏差，影响工程质量', 'A区竖井工作面', '一般', '已解决', 10, '张小明', '2025-03-25', '重新进行测量，调整开挖方向，并加强测量复核', '2025-03-23'),
(5, 2, '基础固定不牢固', '主风机基础固定不牢固，影响设备稳定性和使用寿命', 'B区风机房', '一般', '处理中', 12, '王小军', '2025-04-10', '加固基础，增加固定点，重新安装固定装置', NULL),
(7, 4, '路面厚度不足', '部分路段路面铺设厚度不足，不符合设计要求', '矿区主干道K2+500至K3+200段', '一般', '已解决', 8, '周九', '2025-03-31', '重新铺设不合格路段，确保厚度符合设计要求', '2025-03-30');

-- 插入工程变更数据
INSERT INTO `project_changes` (`project_id`, `title`, `type`, `description`, `reason`, `impact`, `cost_change`, `schedule_change`, `proposer_id`, `proposer`, `propose_date`, `status`, `approver_id`, `approver`, `approval_date`, `implementation_date`) VALUES
(1, '井筒直径增加', '设计变更', '将井筒直径从5米增加到5.5米', '考虑到未来产能扩展需求，增加井筒直径以提高通风和运输能力', '增加工程量和成本，但提高了矿井未来扩产能力', 500000.00, 15, 3, '李四', '2025-03-01', '已批准', 1, '系统管理员', '2025-03-05', '2025-03-10'),
(2, '更换监测系统供应商', '采购变更', '将安全监测系统供应商从A公司更换为B公司', '原供应商交货期延迟，影响工程进度；新供应商可以提供更先进的设备并保证及时交付', '设备性能提升，但成本略有增加', 50000.00, -5, 3, '李四', '2025-03-20', '已批准', 1, '系统管理员', '2025-03-25', '2025-03-30'),
(4, '路面材料升级', '材料变更', '将原沥青混凝土路面升级为改性沥青混凝土', '提高道路耐久性和承载能力，减少后期维护成本', '提高工程质量，但增加材料成本', 100000.00, 5, 8, '周九', '2025-03-15', '待审批', NULL, NULL, NULL, NULL),
(6, '排水系统优化', '设计变更', '增加排水沟断面尺寸并优化布局', '根据最新水文地质资料，优化排水系统设计，提高排水能力', '提高工程安全性，略微增加工程量', 80000.00, 0, 3, '李四', '2025-03-25', '已批准', 1, '系统管理员', '2025-03-28', '2025-04-01');

-- 插入工程风险数据
INSERT INTO `project_risks` (`project_id`, `title`, `type`, `description`, `probability`, `impact`, `level`, `status`, `response_plan`, `responsible_id`, `responsible`) VALUES
(1, '地质条件复杂', '技术风险', '井筒开挖过程中可能遇到复杂地质条件，如断层、涌水等', '中', '高', '高', '监控中', '加强地质勘探和超前探测，准备应急处理方案和设备', 3, '李四'),
(1, '安全事故', '安全风险', '井下作业存在安全风险，如冒顶、瓦斯等', '低', '高', '中', '监控中', '严格执行安全操作规程，加强安全监测和培训，配备必要的安全设备', 2, '张三'),
(1, '工期延误', '进度风险', '由于各种原因导致工期延误', '中', '中', '中', '监控中', '合理安排施工计划，加强进度管理，准备应急资源', 3, '李四'),
(2, '设备交付延迟', '供应风险', '关键设备交付延迟影响工程进度', '中', '高', '高', '已解决', '更换供应商，确保设备及时交付', 3, '李四'),
(4, '恶劣天气影响', '自然风险', '雨季施工可能受到天气影响', '高', '中', '高', '监控中', '合理安排施工计划，准备防雨措施，加强排水设施', 8, '周九'),
(6, '环保要求变化', '政策风险', '环保政策变化可能影响尾矿库扩容工程', '低', '高', '中', '监控中', '密切关注政策变化，做好环保措施，保持与环保部门的沟通', 3, '李四');

-- 插入工程资源分配数据
INSERT INTO `project_resources` (`project_id`, `resource_type`, `resource_name`, `quantity`, `unit`, `allocation_date`, `status`, `description`) VALUES
(1, '人力', '采掘工', 20, '人', '2025-03-01', '使用中', 'A区矿井开拓工程采掘工人力资源'),
(1, '人力', '支护工', 15, '人', '2025-03-01', '使用中', 'A区矿井开拓工程支护工人力资源'),
(1, '设备', '掘进机', 2, '台', '2025-03-01', '使用中', 'A区矿井开拓工程掘进设备'),
(1, '设备', '装载机', 3, '台', '2025-03-01', '使用中', 'A区矿井开拓工程装载设备'),
(1, '材料', '钢材', 500, '吨', '2025-03-01', '使用中', 'A区矿井开拓工程支护用钢材'),
(1, '材料', '水泥', 1000, '吨', '2025-03-01', '使用中', 'A区矿井开拓工程支护用水泥'),
(2, '人力', '安装工', 10, '人', '2025-03-16', '使用中', 'B区安全改造工程安装工人力资源'),
(2, '设备', '风机', 2, '台', '2025-03-16', '使用中', 'B区安全改造工程通风设备'),
(2, '材料', '风筒', 500, '米', '2025-03-16', '使用中', 'B区安全改造工程通风管道'),
(4, '人力', '道路工', 15, '人', '2025-02-15', '使用中', '矿区道路维修工程人力资源'),
(4, '设备', '压路机', 2, '台', '2025-02-15', '使用中', '矿区道路维修工程压实设备'),
(4, '设备', '摊铺机', 1, '台', '2025-02-15', '使用中', '矿区道路维修工程摊铺设备'),
(4, '材料', '沥青混凝土', 2000, '吨', '2025-03-15', '使用中', '矿区道路维修工程路面材料'),
(6, '人力', '土建工', 25, '人', '2025-01-10', '使用中', '尾矿库扩容工程人力资源'),
(6, '设备', '挖掘机', 3, '台', '2025-01-10', '使用中', '尾矿库扩容工程挖掘设备'),
(6, '设备', '推土机', 2, '台', '2025-01-10', '使用中', '尾矿库扩容工程推土设备'),
(6, '材料', '土工膜', 10000, '平方米', '2025-01-10', '使用中', '尾矿库扩容工程防渗材料');
