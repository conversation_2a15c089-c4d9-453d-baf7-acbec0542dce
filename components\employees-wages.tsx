"use client"

import React, { useState, useEffect, useMemo, useRef, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  BarChart,
  FileText,
  Plus,
  Search,
  Trash2,
  Edit,
  Download,
  Upload,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle2,
  Clock,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  RefreshCw,
  FileUp,
  Eye,
  PieChart,
  Activity,
  BarChart2,
  LineChart,
  ArrowUpIcon,
  ArrowDownIcon,
  Edit2,
  Trash
} from "lucide-react"
import { cn } from "@/lib/utils"
// 导入 echarts
import ReactECharts from "echarts-for-react"
import * as echarts from "echarts/core"
// echarts 主题
import { theme } from "./wages-echarts-theme"
// 导入 toast
import { toast } from "sonner"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { ScrollArea } from "@/components/ui/scroll-area"

// 注册 echarts 主题
try {
  echarts.registerTheme('wages-theme', theme);
} catch (e) {
  console.log('已注册 echarts 主题或注册失败', e);
}

// 定义工资状态类型
type PaymentStatus = "待发放" | "发放中" | "已发放" | "发放异常";
type ApprovalStatus = "未提交" | "审批中" | "已通过" | "已拒绝";

// 扩展的工资数据接口
interface EmployeeWage {
  id: number;
  department: string;
  year: string;
  month: string;
  employeeCount: number;
  totalWages: string;
  averageWage: string;
  status: string;

  // 工资发放相关
  paymentStatus: PaymentStatus;
  plannedPaymentDate?: string;
  actualPaymentDate?: string;
  paymentDelay?: number; // 发放延迟天数

  // 工资结构相关
  basicSalary: number;
  performanceBonus: number;
  allowance: number;
  overtimePay: number;
  otherBonus: number;

  // 社保公积金相关
  socialInsurance: number;
  housingFund: number;
  taxAmount: number;

  // 审批相关
  approvalStatus: ApprovalStatus;
  approver?: string;
  approvalDate?: string;

  // 预算相关
  budgetAmount: number;
  budgetDifference: number;
  budgetExecutionRate: number;

  // 分析相关
  yearOnYearGrowth: number; // 同比增长
  monthOnMonthGrowth: number; // 环比增长

  // 计算值
  totalWagesValue: number; // 工资总额数值
  avgWageValue: number; // 平均工资数值

  // 新增字段
  notes?: string;
}

// 部门工资异常数据接口
interface DepartmentAnomaly {
  department: string;
  anomalyType: "超预算" | "增长异常" | "发放延迟" | "核算错误";
  description: string;
  severity: "低" | "中" | "高";
  detectedAt: string;
  status: "未处理" | "处理中" | "已解决";
}

// 工资发放进度接口
interface PaymentProgressData {
  department: string;
  employeeCount: number;
  completedCount: number;
  progressRate: number;
  startTime?: string;
  estimatedCompletionTime?: string;
}

// 模拟工资数据
const wagesDataExtended: EmployeeWage[] = [
  {
    id: 1,
    department: "采矿部",
    year: "2025",
    month: "01",
    employeeCount: 45,
    totalWages: "¥675,000",
    averageWage: "¥15,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-24",
    paymentDelay: -1,
    basicSalary: 450000,
    performanceBonus: 112500,
    allowance: 67500,
    overtimePay: 22500,
    otherBonus: 22500,
    socialInsurance: 135000,
    housingFund: 67500,
    taxAmount: 33750,
    approvalStatus: "已通过",
    approver: "张经理",
    approvalDate: "2025-01-20",
    budgetAmount: 700000,
    budgetDifference: 25000,
    budgetExecutionRate: 96.4,
    yearOnYearGrowth: 5.2,
    monthOnMonthGrowth: 2.3,
    totalWagesValue: 675000,
    avgWageValue: 15000
  },
  {
    id: 2,
    department: "安全部",
    year: "2025",
    month: "01",
    employeeCount: 28,
    totalWages: "¥448,000",
    averageWage: "¥16,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-24",
    paymentDelay: -1,
    basicSalary: 280000,
    performanceBonus: 112000,
    allowance: 28000,
    overtimePay: 14000,
    otherBonus: 14000,
    socialInsurance: 89600,
    housingFund: 44800,
    taxAmount: 22400,
    approvalStatus: "已通过",
    approver: "李主管",
    approvalDate: "2025-01-20",
    budgetAmount: 450000,
    budgetDifference: 2000,
    budgetExecutionRate: 99.6,
    yearOnYearGrowth: 6.5,
    monthOnMonthGrowth: 1.8,
    totalWagesValue: 448000,
    avgWageValue: 16000
  },
  {
    id: 3,
    department: "工程部",
    year: "2025",
    month: "01",
    employeeCount: 36,
    totalWages: "¥612,000",
    averageWage: "¥17,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-24",
    paymentDelay: -1,
    basicSalary: 360000,
    performanceBonus: 180000,
    allowance: 36000,
    overtimePay: 18000,
    otherBonus: 18000,
    socialInsurance: 122400,
    housingFund: 61200,
    taxAmount: 30600,
    approvalStatus: "已通过",
    approver: "王总监",
    approvalDate: "2025-01-19",
    budgetAmount: 600000,
    budgetDifference: -12000,
    budgetExecutionRate: 102.0,
    yearOnYearGrowth: 8.2,
    monthOnMonthGrowth: 3.1,
    totalWagesValue: 612000,
    avgWageValue: 17000
  },
  {
    id: 4,
    department: "行政部",
    year: "2025",
    month: "01",
    employeeCount: 15,
    totalWages: "¥225,000",
    averageWage: "¥15,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-25",
    paymentDelay: 0,
    basicSalary: 150000,
    performanceBonus: 37500,
    allowance: 22500,
    overtimePay: 7500,
    otherBonus: 7500,
    socialInsurance: 45000,
    housingFund: 22500,
    taxAmount: 11250,
    approvalStatus: "已通过",
    approver: "赵主任",
    approvalDate: "2025-01-20",
    budgetAmount: 230000,
    budgetDifference: 5000,
    budgetExecutionRate: 97.8,
    yearOnYearGrowth: 3.5,
    monthOnMonthGrowth: 0.0,
    totalWagesValue: 225000,
    avgWageValue: 15000
  },
  {
    id: 5,
    department: "财务部",
    year: "2025",
    month: "01",
    employeeCount: 12,
    totalWages: "¥204,000",
    averageWage: "¥17,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-23",
    paymentDelay: -2,
    basicSalary: 120000,
    performanceBonus: 60000,
    allowance: 12000,
    overtimePay: 6000,
    otherBonus: 6000,
    socialInsurance: 40800,
    housingFund: 20400,
    taxAmount: 10200,
    approvalStatus: "已通过",
    approver: "钱经理",
    approvalDate: "2025-01-18",
    budgetAmount: 200000,
    budgetDifference: -4000,
    budgetExecutionRate: 102.0,
    yearOnYearGrowth: 7.2,
    monthOnMonthGrowth: 2.5,
    totalWagesValue: 204000,
    avgWageValue: 17000
  },
  {
    id: 6,
    department: "信息部",
    year: "2025",
    month: "01",
    employeeCount: 18,
    totalWages: "¥324,000",
    averageWage: "¥18,000",
    status: "已发放",
    paymentStatus: "已发放",
    plannedPaymentDate: "2025-01-25",
    actualPaymentDate: "2025-01-24",
    paymentDelay: -1,
    basicSalary: 180000,
    performanceBonus: 108000,
    allowance: 18000,
    overtimePay: 9000,
    otherBonus: 9000,
    socialInsurance: 64800,
    housingFund: 32400,
    taxAmount: 16200,
    approvalStatus: "已通过",
    approver: "孙总监",
    approvalDate: "2025-01-19",
    budgetAmount: 330000,
    budgetDifference: 6000,
    budgetExecutionRate: 98.2,
    yearOnYearGrowth: 9.5,
    monthOnMonthGrowth: 3.8,
    totalWagesValue: 324000,
    avgWageValue: 18000
  },
  {
    id: 7,
    department: "销售部",
    year: "2025",
    month: "02",
    employeeCount: 25,
    totalWages: "¥450,000",
    averageWage: "¥18,000",
    status: "发放中",
    paymentStatus: "发放中",
    plannedPaymentDate: "2025-02-25",
    actualPaymentDate: "",
    paymentDelay: 0,
    basicSalary: 250000,
    performanceBonus: 150000,
    allowance: 25000,
    overtimePay: 12500,
    otherBonus: 12500,
    socialInsurance: 90000,
    housingFund: 45000,
    taxAmount: 22500,
    approvalStatus: "已通过",
    approver: "周经理",
    approvalDate: "2025-02-20",
    budgetAmount: 460000,
    budgetDifference: 10000,
    budgetExecutionRate: 97.8,
    yearOnYearGrowth: 8.7,
    monthOnMonthGrowth: 4.2,
    totalWagesValue: 450000,
    avgWageValue: 18000
  },
  {
    id: 8,
    department: "人力资源部",
    year: "2025",
    month: "02",
    employeeCount: 10,
    totalWages: "¥170,000",
    averageWage: "¥17,000",
    status: "待发放",
    paymentStatus: "待发放",
    plannedPaymentDate: "2025-02-25",
    actualPaymentDate: "",
    paymentDelay: 0,
    basicSalary: 100000,
    performanceBonus: 50000,
    allowance: 10000,
    overtimePay: 5000,
    otherBonus: 5000,
    socialInsurance: 34000,
    housingFund: 17000,
    taxAmount: 8500,
    approvalStatus: "审批中",
    approver: "吴总监",
    approvalDate: "",
    budgetAmount: 180000,
    budgetDifference: 10000,
    budgetExecutionRate: 94.4,
    yearOnYearGrowth: 6.3,
    monthOnMonthGrowth: 2.1,
    totalWagesValue: 170000,
    avgWageValue: 17000
  },
  {
    id: 9,
    department: "研发部",
    year: "2025",
    month: "02",
    employeeCount: 20,
    totalWages: "¥400,000",
    averageWage: "¥20,000",
    status: "审核中",
    paymentStatus: "待发放",
    plannedPaymentDate: "2025-02-25",
    actualPaymentDate: "",
    paymentDelay: 0,
    basicSalary: 200000,
    performanceBonus: 160000,
    allowance: 20000,
    overtimePay: 10000,
    otherBonus: 10000,
    socialInsurance: 80000,
    housingFund: 40000,
    taxAmount: 20000,
    approvalStatus: "审批中",
    approver: "郑主管",
    approvalDate: "",
    budgetAmount: 390000,
    budgetDifference: -10000,
    budgetExecutionRate: 102.6,
    yearOnYearGrowth: 10.5,
    monthOnMonthGrowth: 5.3,
    totalWagesValue: 400000,
    avgWageValue: 20000
  },
  {
    id: 10,
    department: "生产部",
    year: "2025",
    month: "02",
    employeeCount: 50,
    totalWages: "¥800,000",
    averageWage: "¥16,000",
    status: "发放异常",
    paymentStatus: "发放异常",
    plannedPaymentDate: "2025-02-25",
    actualPaymentDate: "",
    paymentDelay: 2,
    basicSalary: 500000,
    performanceBonus: 200000,
    allowance: 50000,
    overtimePay: 25000,
    otherBonus: 25000,
    socialInsurance: 160000,
    housingFund: 80000,
    taxAmount: 40000,
    approvalStatus: "已通过",
    approver: "冯经理",
    approvalDate: "2024-01-19",
    budgetAmount: 750000,
    budgetDifference: -50000,
    budgetExecutionRate: 106.7,
    yearOnYearGrowth: 11.2,
    monthOnMonthGrowth: 6.5,
    totalWagesValue: 800000,
    avgWageValue: 16000
  }
];

// 工资异常数据
const wagesAnomalies: DepartmentAnomaly[] = [
  {
    department: "工程部",
    anomalyType: "超预算",
    description: "工程部本月工资支出超出预算2%",
    severity: "低",
    detectedAt: "2023-12-24",
    status: "已解决"
  },
  {
    department: "财务部",
    anomalyType: "超预算",
    description: "财务部本月工资支出超出预算2%",
    severity: "低",
    detectedAt: "2023-12-23",
    status: "已解决"
  },
  {
    department: "生产部",
    anomalyType: "超预算",
    description: "生产部本月工资支出超出预算6.7%",
    severity: "中",
    detectedAt: "2024-01-23",
    status: "处理中"
  },
  {
    department: "研发部",
    anomalyType: "超预算",
    description: "研发部本月工资支出超出预算2.6%",
    severity: "低",
    detectedAt: "2024-01-23",
    status: "未处理"
  },
  {
    department: "生产部",
    anomalyType: "发放延迟",
    description: "生产部工资发放延迟2天",
    severity: "高",
    detectedAt: "2024-01-27",
    status: "处理中"
  }
];

// 工资发放进度数据
const paymentProgressData: PaymentProgressData[] = [
  {
    department: "销售部",
    employeeCount: 25,
    completedCount: 15,
    progressRate: 60,
    startTime: "2024-01-25 09:00:00",
    estimatedCompletionTime: "2024-01-25 17:00:00"
  },
  {
    department: "研发部",
    employeeCount: 20,
    completedCount: 0,
    progressRate: 0,
    startTime: "",
    estimatedCompletionTime: "2024-01-25 17:00:00"
  },
  {
    department: "人力资源部",
    employeeCount: 10,
    completedCount: 0,
    progressRate: 0,
    startTime: "",
    estimatedCompletionTime: "2024-01-25 15:00:00"
  },
  {
    department: "生产部",
    employeeCount: 50,
    completedCount: 10,
    progressRate: 20,
    startTime: "2024-01-25 09:00:00",
    estimatedCompletionTime: "2024-01-27 17:00:00"
  }
];

// 生成部门工资对比图表
const generateDepartmentWagesChart = (data: EmployeeWage[]) => {
  // 按部门分组计算
  const departments = Array.from(new Set(data.map(item => item.department)));
  const series = [
    {
      name: '员工数量',
      type: 'bar',
      barWidth: '15%',
      yAxisIndex: 1,
      data: departments.map(dept =>
        data.find(item => item.department === dept)?.employeeCount || 0
      )
    },
    {
      name: '工资总额(万元)',
      type: 'bar',
      barWidth: '15%',
      data: departments.map(dept => {
        const record = data.find(item => item.department === dept);
        return record ? (record.totalWagesValue / 10000).toFixed(2) : 0;
      })
    },
    {
      name: '平均工资(元)',
      type: 'line',
      yAxisIndex: 2,
      symbol: 'circle',
      symbolSize: 8,
      data: departments.map(dept => {
        const record = data.find(item => item.department === dept);
        return record ? record.avgWageValue : 0;
      })
    }
  ];

  return {
    title: {
      text: '部门工资对比分析',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '各部门员工数量、工资总额与平均工资对比',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          let value = param.value;
          if (param.seriesName === '工资总额(万元)') {
            value = value + ' 万元';
          } else if (param.seriesName === '平均工资(元)') {
            value = value + ' 元';
          }
          result += param.marker + ' ' + param.seriesName + ': ' + value + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['员工数量', '工资总额(万元)', '平均工资(元)']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departments
    },
    yAxis: [
      {
        type: 'value',
        name: '工资总额(万元)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#3b82f6'
          }
        },
        axisLabel: {
          formatter: '{value} 万元'
        }
      },
      {
        type: 'value',
        name: '员工数量',
        position: 'right',
        offset: 80,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#22c55e'
          }
        },
        axisLabel: {
          formatter: '{value} 人'
        }
      },
      {
        type: 'value',
        name: '平均工资(元)',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#7c3aed'
          }
        },
        axisLabel: {
          formatter: '{value} 元'
        }
      }
    ],
    series: series
  };
};

// 生成工资结构分析图表
const generateWageStructureChart = (data: EmployeeWage[]) => {
  // 计算所有部门的工资结构
  const totalBasicSalary = data.reduce((acc, item) => acc + item.basicSalary, 0);
  const totalPerformanceBonus = data.reduce((acc, item) => acc + item.performanceBonus, 0);
  const totalAllowance = data.reduce((acc, item) => acc + item.allowance, 0);
  const totalOvertimePay = data.reduce((acc, item) => acc + item.overtimePay, 0);
  const totalOtherBonus = data.reduce((acc, item) => acc + item.otherBonus, 0);

  const pieData = [
    { value: totalBasicSalary, name: '基本工资' },
    { value: totalPerformanceBonus, name: '绩效奖金' },
    { value: totalAllowance, name: '津贴补贴' },
    { value: totalOvertimePay, name: '加班工资' },
    { value: totalOtherBonus, name: '其他奖金' }
  ];

  return {
    title: {
      text: '工资结构分析',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '各类工资项目占比',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '工资结构',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        data: pieData
      }
    ]
  };
};

// 生成工资发放状态图表
const generatePaymentStatusChart = (data: EmployeeWage[]) => {
  // 计算不同支付状态的记录数量
  const statusCounts = {
    "已发放": 0,
    "待发放": 0,
    "发放中": 0,
    "发放异常": 0
  };

  data.forEach(item => {
    if (statusCounts.hasOwnProperty(item.paymentStatus)) {
      statusCounts[item.paymentStatus as keyof typeof statusCounts]++;
    }
  });

  const pieData = Object.entries(statusCounts).map(([key, value]) => ({
    name: key,
    value: value
  }));

  return {
    title: {
      text: '工资发放状态分布',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '各状态记录数量',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '发放状态',
        type: 'pie',
        radius: '60%',
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          formatter: '{b}: {c} ({d}%)'
        }
      }
    ]
  };
};

// 修改工资趋势图表生成函数
const generateWageTrendsChart = (data: EmployeeWage[]) => {
  // 按月份对数据排序
  const sortedData = [...data].sort((a, b) => {
    const yearA = parseInt(a.year);
    const yearB = parseInt(b.year);
    if (yearA !== yearB) return yearA - yearB;
    return parseInt(a.month) - parseInt(b.month);
  });

  // 取最近12个月的数据
  const recentData = sortedData.slice(-12);

  // 准备图表数据
  const chartData = recentData.map(item => ({
    month: `${item.year}-${item.month}`,
    totalWages: item.totalWagesValue,
    averageWage: item.avgWageValue
  }));

  const option = {
    title: {
      text: '工资趋势分析',
      subtext: '近12个月工资总额和平均工资变化',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params: any) {
        const totalWages = params[0].value;
        const avgWage = params[1].value;
        return `${params[0].name}<br/>
                工资总额: ${formatCurrency(totalWages)}<br/>
                平均工资: ${formatCurrency(avgWage)}`;
      }
    },
    legend: {
      data: ['工资总额', '平均工资'],
      bottom: 10
    },
    grid: {
      left: '5%',
      right: '5%',
      top: 80,
      bottom: 80,
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: chartData.map(item => item.month),
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 11,
          margin: 15
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '工资总额',
        min: 0,
        nameTextStyle: {
          padding: [0, 0, 0, 20] // 避免标签重叠
        },
        axisLabel: {
          formatter: function(value: number) {
            return (value / 10000).toFixed(0) + '万';
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '平均工资',
        min: 0,
        nameTextStyle: {
          padding: [0, 20, 0, 0] // 避免标签重叠
        },
        axisLabel: {
          formatter: function(value: number) {
            return value.toFixed(0);
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '工资总额',
        type: 'bar',
        barWidth: '20%', // 减小柱状宽度
        barMaxWidth: 30, // 设置最大宽度限制
        data: chartData.map(item => item.totalWages),
        itemStyle: {
          borderRadius: [3, 3, 0, 0] // 柱状图圆角
        }
      },
      {
        name: '平均工资',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.map(item => item.averageWage),
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3
        }
      }
    ]
  };

  return option;
};

// 生成工资发放进度条形图
const generatePaymentProgressChart = (data: PaymentProgressData[]) => {
  const departments = data.map(item => item.department);

  return {
    title: {
      text: '工资发放进度',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '当前月份各部门工资发放进度',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const dept = departments[dataIndex];
        const item = data.find(d => d.department === dept);
        if (!item) return '';

        return `${dept}<br/>
                进度: ${item.progressRate}%<br/>
                已完成: ${item.completedCount}/${item.employeeCount}人<br/>
                ${item.startTime ? `开始时间: ${item.startTime}<br/>` : ''}
                ${item.estimatedCompletionTime ? `预计完成: ${item.estimatedCompletionTime}` : ''}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: departments
    },
    series: [
      {
        name: '发放进度',
        type: 'bar',
        data: data.map(item => item.progressRate),
        itemStyle: {
          color: function(params: any) {
            const progress = data[params.dataIndex].progressRate;
            if (progress < 20) return '#ef4444';
            if (progress < 60) return '#f59e0b';
            return '#22c55e';
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  };
};

// 修改预算执行情况图表生成函数
const generateBudgetExecutionChart = (data: EmployeeWage[]) => {
  // 按预算执行率对数据排序
  const sortedData = [...data]
    .filter((item, index, self) =>
      index === self.findIndex(t => t.department === item.department)
    )
    .sort((a, b) => b.budgetExecutionRate - a.budgetExecutionRate);

  // 取前10个部门
  const topDepartments = sortedData.slice(0, 7);

  const option = {
    title: {
      text: '预算执行情况',
      subtext: '各部门工资预算执行率与差异',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const executionRate = params[0].value;
        const budgetDiff = params[1].value;
        return `${params[0].name}<br/>
                预算执行率: ${executionRate}%<br/>
                预算差异: ${formatCurrency(budgetDiff)}`;
      }
    },
    legend: {
      data: ['预算执行率', '预算差异'],
      bottom: 10
    },
    grid: {
      left: '5%',
      right: '5%',
      top: 80,
      bottom: 80,
      containLabel: true
    },
    xAxis: [
      {
        type: 'value',
        name: '预算执行率(%)',
        max: 120,
        axisLabel: {
          formatter: '{value}%'
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'category',
        data: topDepartments.map(item => item.department),
        axisLabel: {
          interval: 0,
          fontSize: 11,
          margin: 16,
          width: 80, // 固定宽度
          overflow: 'truncate' // 过长时截断
        }
      }
    ],
    series: [
      {
        name: '预算执行率',
        type: 'bar',
        barWidth: '40%', // 适当减小柱状宽度
        barMaxWidth: 20, // 设置最大宽度
        data: topDepartments.map(item => item.budgetExecutionRate),
        itemStyle: {
          borderRadius: [0, 3, 3, 0] // 柱状图圆角
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%',
          fontSize: 11
        },
        markLine: {
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: {
                color: '#888',
                type: 'dashed'
              },
              label: {
                position: 'end'
              }
            },
            {
              yAxis: 100,
              name: '预算线',
              lineStyle: {
                color: '#ff9800',
                type: 'dashed'
              },
              label: {
                formatter: '预算线',
                position: 'end'
              }
            }
          ]
        }
      },
      {
        name: '预算差异',
        type: 'scatter',
        symbolSize: function(value: number) {
          return Math.abs(value) / 5000 + 5; // 根据差异大小调整点的大小
        },
        data: topDepartments.map(item => item.budgetDifference),
        itemStyle: {
          color: function(params: any) {
            const value = params.data;
            return value > 0 ? '#f5222d' : '#52c41a';
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: function(params: any) {
            return formatCurrency(params.value).replace('¥', '');
          },
          fontSize: 11
        }
      }
    ]
  };

  return option;
};

// 生成工资异常变动监控图表
const generateWageAnomalyChart = (data: EmployeeWage[]) => {
  // 按部门分组并计算同比和环比增长
  const departments = Array.from(new Set(data.map(item => item.department)));

  const yoyGrowthData = departments.map(dept => {
    const record = data.find(item => item.department === dept);
    return record ? record.yearOnYearGrowth : 0;
  });

  const momGrowthData = departments.map(dept => {
    const record = data.find(item => item.department === dept);
    return record ? record.monthOnMonthGrowth : 0;
  });

  return {
    title: {
      text: '工资异常变动监控',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '各部门工资同比/环比增长率',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['同比增长率(%)', '环比增长率(%)'],
      top: 'bottom'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departments
    },
    yAxis: {
      type: 'value',
      name: '增长率(%)',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '同比增长率(%)',
        type: 'bar',
        data: yoyGrowthData.map(v => parseFloat(v.toFixed(1))),
        itemStyle: {
          color: function(params: any) {
            const value = yoyGrowthData[params.dataIndex];
            if (value >= 10) return '#ef4444'; // 同比增长超过10%，红色警示
            if (value >= 5) return '#f59e0b'; // 同比增长5-10%，橙色警示
            return '#22c55e'; // 正常增长，绿色
          }
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' },
            { yAxis: 10, name: '异常阈值', lineStyle: { color: '#ef4444' } }
          ]
        }
      },
      {
        name: '环比增长率(%)',
        type: 'bar',
        data: momGrowthData.map(v => parseFloat(v.toFixed(1))),
        itemStyle: {
          color: function(params: any) {
            const value = momGrowthData[params.dataIndex];
            if (value >= 6) return '#ef4444'; // 环比增长超过6%，红色警示
            if (value >= 3) return '#f59e0b'; // 环比增长3-6%，橙色警示
            return '#22c55e'; // 正常增长，绿色
          }
        },
        markLine: {
          data: [
            { yAxis: 6, name: '异常阈值', lineStyle: { color: '#ef4444' } }
          ]
        }
      }
    ]
  };
};

// 生成工资发放延迟监控图表
const generatePaymentDelayChart = (data: EmployeeWage[]) => {
  // 筛选当月数据
  const currentMonthData = data.filter(item =>
    (item.year === "2024" && item.month === "01") ||
    (item.paymentStatus === "发放异常" || item.paymentDelay !== undefined)
  );

  const departments = currentMonthData.map(item => item.department);

  const delayDays = currentMonthData.map(item => item.paymentDelay || 0);

  // 工资及时率数据
  const onTimeRate = departments.map((_, index) => {
    const delay = delayDays[index];
    // 设置一个简单的计算方式：延迟天数 => 及时率
    if (delay <= 0) return 100; // 提前或按时发放
    if (delay === 1) return 90; // 延迟1天
    if (delay === 2) return 70; // 延迟2天
    return 50; // 延迟3天及以上
  });

  return {
    title: {
      text: '工资发放延迟监控',
      left: 'center',
      top: 10,
      padding: [10, 0],
      textStyle: {
        fontSize: 16
      },
      subtext: '各部门工资发放及时率与延迟天数',
      subtextStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const departmentIndex = params[0].dataIndex;
        const department = departments[departmentIndex];
        const delay = delayDays[departmentIndex];
        const rate = onTimeRate[departmentIndex];

        return `${department}<br/>
                延迟天数: ${delay >= 0 ? delay + '天' : '提前' + Math.abs(delay) + '天'}<br/>
                及时率: ${rate}%<br/>
                状态: ${delay > 0 ? '异常' : '正常'}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departments
    },
    yAxis: [
      {
        type: 'value',
        name: '及时率(%)',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '延迟天数',
        position: 'right',
        axisLabel: {
          formatter: '{value}天'
        }
      }
    ],
    series: [
      {
        name: '及时率',
        type: 'bar',
        data: onTimeRate,
        itemStyle: {
          color: function(params: any) {
            const rate = onTimeRate[params.dataIndex];
            if (rate < 70) return '#ef4444';
            if (rate < 90) return '#f59e0b';
            return '#22c55e';
          }
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' },
            { yAxis: 90, name: '目标及时率', lineStyle: { color: '#22c55e' } }
          ]
        }
      },
      {
        name: '延迟天数',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        data: delayDays,
        itemStyle: {
          color: function(params: any) {
            const delay = delayDays[params.dataIndex];
            if (delay > 0) return '#ef4444';
            return '#22c55e';
          }
        },
        markLine: {
          data: [
            { yAxis: 0, name: '零延迟线', lineStyle: { color: '#22c55e' } }
          ]
        }
      }
    ]
  };
};

// 获取工资状态徽章
const getPaymentStatusBadge = (status: PaymentStatus) => {
  switch (status) {
    case "已发放":
      return <Badge className="bg-green-100 text-green-800">已发放</Badge>
    case "发放中":
      return <Badge variant="outline" className="text-blue-600 border-blue-600">发放中</Badge>
    case "待发放":
      return <Badge variant="secondary">待发放</Badge>
    case "发放异常":
      return <Badge variant="destructive">发放异常</Badge>
    default:
      return <Badge>{status}</Badge>
  }
};

// 获取审批状态徽章
const getApprovalStatusBadge = (status: ApprovalStatus) => {
  switch (status) {
    case "已通过":
      return <Badge className="bg-green-100 text-green-800">已通过</Badge>
    case "审批中":
      return <Badge variant="outline" className="text-blue-600 border-blue-600">审批中</Badge>
    case "未提交":
      return <Badge variant="secondary">未提交</Badge>
    case "已拒绝":
      return <Badge variant="destructive">已拒绝</Badge>
    default:
      return <Badge>{status}</Badge>
  }
};

// 获取异常严重性徽章
const getAnomalySeverityBadge = (severity: "低" | "中" | "高") => {
  switch (severity) {
    case "低":
      return <Badge className="bg-yellow-100 text-yellow-800">低</Badge>
    case "中":
      return <Badge className="bg-orange-100 text-orange-800">中</Badge>
    case "高":
      return <Badge variant="destructive">高</Badge>
    default:
      return <Badge>{severity}</Badge>
  }
};

// 添加格式化货币的函数
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value);
};

export function EmployeesWages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState<string>("全部");
  const [selectedStatus, setSelectedStatus] = useState<string>("全部");
  const [activeTab, setActiveTab] = useState<string>("表格视图");

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedWage, setSelectedWage] = useState<EmployeeWage | null>(null);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    department: "",
    year: "",
    month: "",
    employeeCount: 0,
    basicSalary: 0,
    performanceBonus: 0,
    allowance: 0,
    overtimePay: 0,
    otherBonus: 0,
    socialInsurance: 0,
    housingFund: 0,
    taxAmount: 0,
    paymentStatus: "待发放" as PaymentStatus,
    approvalStatus: "未提交" as ApprovalStatus,
    plannedPaymentDate: "",
    actualPaymentDate: "",
    approver: "",
    approvalDate: "",
    budgetAmount: 0,
    notes: ""
  });

  const [filteredData, setFilteredData] = useState(wagesDataExtended);

  // 所有可用年份
  const availableYears = useMemo(() => {
    const years = Array.from(new Set(wagesDataExtended.map(item => item.year)));
    return ["全部", ...years.sort((a, b) => parseInt(b) - parseInt(a))];
  }, []);

  // 所有可用状态
  const availableStatuses = ["全部", "已发放", "发放中", "待发放", "发放异常"];

  // 应用所有筛选条件
  const applyFilters = useCallback(() => {
    let result = [...wagesDataExtended];

    // 应用搜索词筛选
    if (searchTerm) {
      result = result.filter(
        item =>
          item.department.includes(searchTerm) ||
          item.year.includes(searchTerm) ||
          item.month.includes(searchTerm) ||
          item.status.includes(searchTerm) ||
          (item.notes && item.notes.includes(searchTerm))
      );
    }

    // 应用年份筛选
    if (selectedYear !== "全部") {
      result = result.filter(item => item.year === selectedYear);
    }

    // 应用状态筛选
    if (selectedStatus !== "全部") {
      result = result.filter(item => item.paymentStatus === selectedStatus);
    }

    setFilteredData(result);
  }, [searchTerm, selectedYear, selectedStatus]);

  // 当筛选条件变化时重新应用筛选
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // 搜索功能
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // 年份筛选变化
  const handleYearFilterChange = (value: string) => {
    setSelectedYear(value);
  };

  // 状态筛选变化
  const handleStatusFilterChange = (value: string) => {
    setSelectedStatus(value);
  };

  // 查看详情
  const handleViewDetail = (wage: EmployeeWage) => {
    setSelectedWage(wage);
    setIsViewDialogOpen(true);
  };

  // 编辑记录
  const handleEditRecord = (wage: EmployeeWage) => {
    setSelectedWage(wage);
    setFormData({
      department: wage.department,
      year: wage.year,
      month: wage.month,
      employeeCount: wage.employeeCount,
      basicSalary: wage.basicSalary,
      performanceBonus: wage.performanceBonus,
      allowance: wage.allowance,
      overtimePay: wage.overtimePay,
      otherBonus: wage.otherBonus,
      socialInsurance: wage.socialInsurance || 0,
      housingFund: wage.housingFund || 0,
      taxAmount: wage.taxAmount || 0,
      paymentStatus: wage.paymentStatus,
      approvalStatus: wage.approvalStatus,
      plannedPaymentDate: wage.plannedPaymentDate || "",
      actualPaymentDate: wage.actualPaymentDate || "",
      approver: wage.approver || "",
      approvalDate: wage.approvalDate || "",
      budgetAmount: wage.budgetAmount,
      notes: wage.notes || ""
    });
    setIsEditDialogOpen(true);
  };

  // 删除记录
  const handleDeleteRecord = (wage: EmployeeWage) => {
    setSelectedWage(wage);
    setIsDeleteDialogOpen(true);
  };

  // 计算分析指标
  const totals = useMemo(() => {
    const totalEmployees = filteredData.reduce((acc, item) => acc + item.employeeCount, 0);
    const totalWages = filteredData.reduce((acc, item) => acc + item.totalWagesValue, 0);
    const avgWage = totalEmployees > 0 ? totalWages / totalEmployees : 0;

    return {
      totalEmployees,
      totalWages,
      avgWage,
      budgetTotal: filteredData.reduce((acc, item) => acc + item.budgetAmount, 0),
      budgetDiff: filteredData.reduce((acc, item) => acc + item.budgetDifference, 0),
      budgetExecution: filteredData.reduce((acc, item) => acc + item.budgetExecutionRate, 0) /
                      (filteredData.length || 1)
    };
  }, [filteredData]);

  // 准备ECharts选项
  const departmentWagesOptions = useMemo(() =>
    generateDepartmentWagesChart(filteredData), [filteredData]);

  const wageStructureOptions = useMemo(() =>
    generateWageStructureChart(filteredData), [filteredData]);

  const paymentStatusOptions = useMemo(() =>
    generatePaymentStatusChart(filteredData), [filteredData]);

  const wageTrendsOptions = useMemo(() =>
    generateWageTrendsChart(filteredData), [filteredData]);

  const paymentProgressOptions = useMemo(() =>
    generatePaymentProgressChart(paymentProgressData), [paymentProgressData]);

  const budgetExecutionOptions = useMemo(() =>
    generateBudgetExecutionChart(filteredData), [filteredData]);

  // 新增图表配置
  const wageAnomalyOptions = useMemo(() =>
    generateWageAnomalyChart(filteredData), [filteredData]);

  const paymentDelayOptions = useMemo(() =>
    generatePaymentDelayChart(filteredData), [filteredData]);

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value, name } = e.target;
    const fieldName = name || id;

    setFormData({
      ...formData,
      [fieldName]: fieldName === "notes" ||
                  fieldName === "department" ||
                  fieldName === "year" ||
                  fieldName === "month" ||
                  fieldName === "paymentStatus" ||
                  fieldName === "approvalStatus" ||
                  fieldName === "plannedPaymentDate" ||
                  fieldName === "actualPaymentDate" ||
                  fieldName === "approver" ||
                  fieldName === "approvalDate"
                  ? value : Number(value)
    });
  };

  // 添加记录
  const handleAddRecord = () => {
    // 表单验证
    if (!formData.department || !formData.year || !formData.month || formData.employeeCount <= 0) {
      toast.error("请填写必填字段", {
        description: "部门、年份、月份和员工数量为必填项"
      });
      return;
    }

    setLoading(true);

    // 模拟添加记录的API请求
    setTimeout(() => {
      // 计算总额
      const totalWages = formData.basicSalary + formData.performanceBonus +
                         formData.allowance + formData.overtimePay + formData.otherBonus;

      toast.success("添加成功", {
        description: `已添加${formData.department} ${formData.year}年${formData.month}月的工资记录`
      });

      // 重置表单
      setFormData({
        department: "",
        year: "",
        month: "",
        employeeCount: 0,
        basicSalary: 0,
        performanceBonus: 0,
        allowance: 0,
        overtimePay: 0,
        otherBonus: 0,
        socialInsurance: 0,
        housingFund: 0,
        taxAmount: 0,
        paymentStatus: "待发放" as PaymentStatus,
        approvalStatus: "未提交" as ApprovalStatus,
        plannedPaymentDate: "",
        actualPaymentDate: "",
        approver: "",
        approvalDate: "",
        budgetAmount: 0,
        notes: ""
      });

      setIsAddDialogOpen(false);
      setLoading(false);

      // 重新获取数据
      applyFilters();
    }, 1000);
  };

  // 更新记录
  const handleUpdateRecord = () => {
    if (!selectedWage) return;

    // 表单验证
    if (!formData.department || !formData.year || !formData.month || formData.employeeCount <= 0) {
      toast.error("请填写必填字段", {
        description: "部门、年份、月份和员工数量为必填项"
      });
      return;
    }

    setLoading(true);

    // 模拟更新记录的API请求
    setTimeout(() => {
      toast.success("更新成功", {
        description: `已更新${formData.department} ${formData.year}年${formData.month}月的工资记录`
      });

      setIsEditDialogOpen(false);
      setLoading(false);

      // 重新获取数据
      applyFilters();
    }, 1000);
  };

  // 确认删除记录
  const confirmDeleteRecord = () => {
    if (!selectedWage) return;

    setLoading(true);

    // 模拟删除记录的API请求
    setTimeout(() => {
      toast.success("删除成功", {
        description: `已删除${selectedWage.department} ${selectedWage.year}年${selectedWage.month}月的工资记录`
      });

      setIsDeleteDialogOpen(false);
      setLoading(false);

      // 重新获取数据
      applyFilters();
    }, 1000);
  };

  // 导出数据
  const handleExport = () => {
    setLoading(true);

    // 模拟导出数据的处理
    setTimeout(() => {
      // 准备CSV数据
      const headers = [
        "部门", "年份", "月份", "员工数量", "工资总额(元)", "人均工资(元)",
        "基本工资(元)", "绩效奖金(元)", "津贴补贴(元)", "加班工资(元)", "其他奖金(元)",
        "支付状态", "审批状态", "预算执行率(%)"
      ].join(",");

      const rows = filteredData.map(wage => [
        wage.department,
        wage.year,
        wage.month,
        wage.employeeCount,
        wage.totalWagesValue.toFixed(2),
        wage.avgWageValue.toFixed(2),
        wage.basicSalary.toFixed(2),
        wage.performanceBonus.toFixed(2),
        wage.allowance.toFixed(2),
        wage.overtimePay.toFixed(2),
        wage.otherBonus.toFixed(2),
        wage.paymentStatus,
        wage.approvalStatus,
        wage.budgetExecutionRate.toFixed(1)
      ].join(","));

      const csvContent = [headers, ...rows].join("\n");

      // 创建下载链接
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", `工资数据_${new Date().toISOString().split("T")[0]}.csv`);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("导出成功", {
        description: "工资数据已导出为CSV文件"
      });

      setLoading(false);
    }, 1500);
  };

  // 添加用于响应式图表的useEffect
  useEffect(() => {
    // 初始化图表时添加基本响应式支持
    const handleResize = () => {
      const charts = document.querySelectorAll('.echarts-for-react');
      charts.forEach((chart: any) => {
        if (chart.__echarts__) {
          chart.__echarts__.resize();
        }
      });
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">从业人员及工资总额</h1>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索..."
              className="w-64 pl-8"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>

          {/* 年份筛选 */}
          <Select value={selectedYear} onValueChange={handleYearFilterChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="年份筛选" />
            </SelectTrigger>
            <SelectContent>
              {availableYears.map(year => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* 状态筛选 */}
          <Select value={selectedStatus} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              {availableStatuses.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" onClick={handleExport} disabled={loading}>
            <Download className="mr-2 h-4 w-4" />
            {loading ? "导出中..." : "导出"}
          </Button>

          {/* 添加对话框 */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                添加工资记录
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>添加工资记录</DialogTitle>
                <DialogDescription>请填写部门工资记录的详细信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="department">部门</Label>
                    <Input id="department" value={formData.department} onChange={handleInputChange} placeholder="例如：采矿部" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="employeeCount">员工数量</Label>
                    <Input id="employeeCount" type="number" value={formData.employeeCount} onChange={handleInputChange} placeholder="0" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="year">年份</Label>
                    <Input id="year" value={formData.year} onChange={handleInputChange} placeholder="例如：2024" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="month">月份</Label>
                    <Input id="month" value={formData.month} onChange={handleInputChange} placeholder="例如：01" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="basicSalary">基本工资 (元)</Label>
                    <Input id="basicSalary" type="number" value={formData.basicSalary} onChange={handleInputChange} placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="performanceBonus">绩效奖金 (元)</Label>
                    <Input id="performanceBonus" type="number" value={formData.performanceBonus} onChange={handleInputChange} placeholder="0" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="allowance">津贴补贴 (元)</Label>
                    <Input id="allowance" type="number" value={formData.allowance} onChange={handleInputChange} placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="overtimePay">加班工资 (元)</Label>
                    <Input id="overtimePay" type="number" value={formData.overtimePay} onChange={handleInputChange} placeholder="0" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="otherBonus">其他奖金 (元)</Label>
                  <Input id="otherBonus" type="number" value={formData.otherBonus} onChange={handleInputChange} placeholder="0" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">备注</Label>
                  <Textarea id="notes" value={formData.notes} onChange={handleInputChange} placeholder="输入备注信息..." />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} disabled={loading}>取消</Button>
                <Button type="submit" onClick={handleAddRecord} disabled={loading}>
                  {loading ? "添加中..." : "添加"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">总员工数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.totalEmployees.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <ArrowUpIcon className="h-4 w-4 text-green-500 inline mr-1" />
              较上月增长 5%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">工资总额</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(totals.totalWages / 10000).toFixed(2)} 万元</div>
            <p className="text-xs text-muted-foreground">
              <ArrowUpIcon className="h-4 w-4 text-green-500 inline mr-1" />
              较上月增长 3.5%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">人均工资</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.avgWage.toFixed(2)} 元</div>
            <p className="text-xs text-muted-foreground">
              <ArrowDownIcon className="h-4 w-4 text-red-500 inline mr-1" />
              较上月下降 1.2%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">预算执行率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold">{totals.budgetExecution.toFixed(1)}%</div>
              <Progress value={totals.budgetExecution} className="h-2" />
            </div>
            <p className="text-xs text-muted-foreground">
              预算总额: {(totals.budgetTotal / 10000).toFixed(2)} 万元
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">预算差异</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totals.budgetDiff >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totals.budgetDiff >= 0 ? '+' : ''}{(totals.budgetDiff / 10000).toFixed(2)} 万元
            </div>
            <p className="text-xs text-muted-foreground">
              {totals.budgetDiff >= 0 ? '结余' : '超支'}
              {(Math.abs(totals.budgetDiff) / totals.budgetTotal * 100).toFixed(1)}%
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="表格视图">表格视图</TabsTrigger>
          <TabsTrigger value="图表视图">图表视图</TabsTrigger>
          <TabsTrigger value="异常监控">异常监控</TabsTrigger>
        </TabsList>
        <TabsContent value="表格视图" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>部门</TableHead>
                    <TableHead>年月</TableHead>
                    <TableHead>员工数量</TableHead>
                    <TableHead>工资总额</TableHead>
                    <TableHead>人均工资</TableHead>
                    <TableHead className="text-center">支付状态</TableHead>
                    <TableHead>审批状态</TableHead>
                    <TableHead className="text-right">预算执行率</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((wage) => (
                    <TableRow key={wage.id}>
                      <TableCell className="font-medium">{wage.department}</TableCell>
                      <TableCell>{wage.year}-{wage.month}</TableCell>
                      <TableCell>{wage.employeeCount}</TableCell>
                      <TableCell>{(wage.totalWagesValue / 10000).toFixed(2)} 万元</TableCell>
                      <TableCell>{wage.avgWageValue.toFixed(0)} 元</TableCell>
                      <TableCell className="text-center">{getPaymentStatusBadge(wage.paymentStatus)}</TableCell>
                      <TableCell>{getApprovalStatusBadge(wage.approvalStatus)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span className={wage.budgetExecutionRate > 100 ? "text-red-500" : "text-green-500"}>
                            {wage.budgetExecutionRate}%
                          </span>
                          <Progress
                            value={wage.budgetExecutionRate}
                            max={120}
                            className={cn(
                              "h-2 w-16",
                              wage.budgetExecutionRate > 100 ? "text-red-500" : "text-green-500"
                            )}
                          />
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleViewDetail(wage)}>
                            <Eye className="h-4 w-4" />
                        </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleEditRecord(wage)}>
                            <Edit2 className="h-4 w-4" />
                        </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteRecord(wage)}>
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="图表视图" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* 部门工资对比图 */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>部门工资对比</CardTitle>
                <CardDescription>查看各部门员工数量、工资总额与平均工资</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] md:h-[500px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={departmentWagesOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 工资结构分析 */}
          <Card>
            <CardHeader>
                <CardTitle>工资结构分析</CardTitle>
                <CardDescription>各类工资项目占比</CardDescription>
            </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={wageStructureOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
              </div>
            </CardContent>
          </Card>

            {/* 工资发放状态 */}
            <Card>
              <CardHeader>
                <CardTitle>工资发放状态</CardTitle>
                <CardDescription>各状态记录数量</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={paymentStatusOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 工资趋势分析 */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>工资趋势分析</CardTitle>
                <CardDescription>各月份工资总额和平均工资变化</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] md:h-[500px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={wageTrendsOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 工资发放进度 */}
            <Card>
              <CardHeader>
                <CardTitle>工资发放进度</CardTitle>
                <CardDescription>当前月份各部门工资发放进度</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={paymentProgressOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 预算执行情况 */}
            <Card>
              <CardHeader>
                <CardTitle>预算执行情况</CardTitle>
                <CardDescription>各部门工资预算执行率与差异</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={budgetExecutionOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="异常监控" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 异常记录表 */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>异常记录</CardTitle>
                <CardDescription>检测到的潜在工资异常情况</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>部门</TableHead>
                      <TableHead>异常类型</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>严重性</TableHead>
                      <TableHead>检测时间</TableHead>
                      <TableHead className="text-right">状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {wagesAnomalies.map((anomaly) => (
                      <TableRow key={`${anomaly.department}-${anomaly.anomalyType}`}>
                        <TableCell className="font-medium">{anomaly.department}</TableCell>
                        <TableCell>{anomaly.anomalyType}</TableCell>
                        <TableCell>{anomaly.description}</TableCell>
                        <TableCell>{getAnomalySeverityBadge(anomaly.severity)}</TableCell>
                        <TableCell>{anomaly.detectedAt}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant={
                            anomaly.status === "已解决" ? "outline" :
                            anomaly.status === "处理中" ? "secondary" : "destructive"
                          }>
                            {anomaly.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* 工资发放延迟监控 */}
            <Card>
              <CardHeader>
                <CardTitle>工资发放延迟监控</CardTitle>
                <CardDescription>各部门工资发放及时率</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] md:h-[500px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={paymentDelayOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 工资异常变动监控 */}
            <Card>
              <CardHeader>
                <CardTitle>工资异常变动监控</CardTitle>
                <CardDescription>检测同比/环比异常变动</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] md:h-[500px] w-full" style={{ position: 'relative' }}>
                  <ReactECharts
                    option={wageAnomalyOptions}
                    style={{ height: '100%', width: '100%' }}
                    theme="wages-theme"
                    opts={{ renderer: 'canvas', devicePixelRatio: window.devicePixelRatio }}
                    className="echarts-for-react"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>工资记录详情</DialogTitle>
            <DialogDescription>
              {selectedWage?.department} - {selectedWage?.year}年{selectedWage?.month}月
            </DialogDescription>
          </DialogHeader>
          {selectedWage && (
            <div className="grid gap-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">基本信息</h3>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">部门:</dt>
                      <dd className="text-sm font-medium">{selectedWage.department}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">年月:</dt>
                      <dd className="text-sm font-medium">{selectedWage.year}-{selectedWage.month}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">员工数量:</dt>
                      <dd className="text-sm font-medium">{selectedWage.employeeCount} 人</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">工资总额:</dt>
                      <dd className="text-sm font-medium">{(selectedWage.totalWagesValue / 10000).toFixed(2)} 万元</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">人均工资:</dt>
                      <dd className="text-sm font-medium">{selectedWage.avgWageValue.toFixed(0)} 元</dd>
                    </div>
                  </dl>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">状态信息</h3>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">支付状态:</dt>
                      <dd className="text-sm font-medium">{getPaymentStatusBadge(selectedWage.paymentStatus)}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">审批状态:</dt>
                      <dd className="text-sm font-medium">{getApprovalStatusBadge(selectedWage.approvalStatus)}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">审批人:</dt>
                      <dd className="text-sm font-medium">{selectedWage.approver || "-"}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">审批日期:</dt>
                      <dd className="text-sm font-medium">{selectedWage.approvalDate || "-"}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-muted-foreground">计划发放日期:</dt>
                      <dd className="text-sm font-medium">{selectedWage.plannedPaymentDate || "-"}</dd>
                    </div>
                  </dl>
                </div>
              </div>

              {/* 工资结构 */}
              <div>
                <h3 className="text-sm font-medium mb-2">工资结构</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">基本工资:</span>
                      <span className="text-sm font-medium">{selectedWage.basicSalary.toLocaleString()} 元</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">绩效奖金:</span>
                      <span className="text-sm font-medium">{selectedWage.performanceBonus.toLocaleString()} 元</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">津贴补贴:</span>
                      <span className="text-sm font-medium">{selectedWage.allowance.toLocaleString()} 元</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">加班工资:</span>
                      <span className="text-sm font-medium">{selectedWage.overtimePay.toLocaleString()} 元</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">其他奖金:</span>
                      <span className="text-sm font-medium">{selectedWage.otherBonus.toLocaleString()} 元</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">社保公积金:</span>
                      <span className="text-sm font-medium">{selectedWage.socialInsurance.toLocaleString()} 元</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 预算情况 */}
              <div>
                <h3 className="text-sm font-medium mb-2">预算情况</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">预算金额:</span>
                    <span className="text-sm font-medium">{(selectedWage.budgetAmount / 10000).toFixed(2)} 万元</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">预算差异:</span>
                    <span className={`text-sm font-medium ${selectedWage.budgetDifference >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {selectedWage.budgetDifference >= 0 ? '+' : ''}{(selectedWage.budgetDifference / 10000).toFixed(2)} 万元
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">预算执行率:</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-medium ${selectedWage.budgetExecutionRate > 100 ? 'text-red-500' : 'text-green-500'}`}>
                        {selectedWage.budgetExecutionRate}%
                      </span>
                      <Progress
                        value={selectedWage.budgetExecutionRate}
                        max={120}
                        className={cn(
                          "h-2 w-16",
                          selectedWage.budgetExecutionRate > 100 ? "text-red-500" : "text-green-500"
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* 分析指标 */}
              <div>
                <h3 className="text-sm font-medium mb-2">同比环比分析</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">同比增长率:</span>
                    <span className={`text-sm font-medium ${selectedWage.yearOnYearGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {selectedWage.yearOnYearGrowth >= 0 ? '+' : ''}{selectedWage.yearOnYearGrowth}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">环比增长率:</span>
                    <span className={`text-sm font-medium ${selectedWage.monthOnMonthGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {selectedWage.monthOnMonthGrowth >= 0 ? '+' : ''}{selectedWage.monthOnMonthGrowth}%
                    </span>
                  </div>
                </div>
              </div>

              {/* 备注信息 */}
              {selectedWage.notes && (
                <div>
                  <h3 className="text-sm font-medium mb-2">备注信息</h3>
                  <p className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                    {selectedWage.notes}
                  </p>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>关闭</Button>
            <Button onClick={() => {
              setIsViewDialogOpen(false);
              if (selectedWage) handleEditRecord(selectedWage);
            }}>
              编辑
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>编辑工资记录</DialogTitle>
            <DialogDescription>
              修改工资记录信息
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="flex-1 max-h-[calc(90vh-120px)]">
            <div className="p-4 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-department">部门</Label>
                    <Input
                      id="edit-department"
                      value={formData.department}
                      name="department"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-year">年份</Label>
                    <Input
                      id="edit-year"
                      value={formData.year}
                      name="year"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-month">月份</Label>
                    <Input
                      id="edit-month"
                      value={formData.month}
                      name="month"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-employeeCount">员工人数</Label>
                    <Input
                      id="edit-employeeCount"
                      type="number"
                      value={formData.employeeCount}
                      name="employeeCount"
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-basicSalary">基本工资</Label>
                    <Input
                      id="edit-basicSalary"
                      type="number"
                      value={formData.basicSalary}
                      name="basicSalary"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-performanceBonus">绩效奖金</Label>
                    <Input
                      id="edit-performanceBonus"
                      type="number"
                      value={formData.performanceBonus}
                      name="performanceBonus"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-allowance">津贴</Label>
                    <Input
                      id="edit-allowance"
                      type="number"
                      value={formData.allowance}
                      name="allowance"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-overtimePay">加班费</Label>
                    <Input
                      id="edit-overtimePay"
                      type="number"
                      value={formData.overtimePay}
                      name="overtimePay"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-otherBonus">其他奖金</Label>
                    <Input
                      id="edit-otherBonus"
                      type="number"
                      value={formData.otherBonus}
                      name="otherBonus"
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-socialInsurance">社保</Label>
                    <Input
                      id="edit-socialInsurance"
                      type="number"
                      value={formData.socialInsurance}
                      name="socialInsurance"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-housingFund">公积金</Label>
                    <Input
                      id="edit-housingFund"
                      type="number"
                      value={formData.housingFund}
                      name="housingFund"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-taxAmount">个税</Label>
                    <Input
                      id="edit-taxAmount"
                      type="number"
                      value={formData.taxAmount}
                      name="taxAmount"
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-budgetAmount">预算金额</Label>
                    <Input
                      id="edit-budgetAmount"
                      type="number"
                      value={formData.budgetAmount}
                      name="budgetAmount"
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-paymentStatus">支付状态</Label>
                  <Select
                    value={formData.paymentStatus}
                    onValueChange={(value) =>
                      setFormData({...formData, paymentStatus: value as PaymentStatus})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择支付状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="待发放">待发放</SelectItem>
                      <SelectItem value="发放中">发放中</SelectItem>
                      <SelectItem value="已发放">已发放</SelectItem>
                      <SelectItem value="发放异常">发放异常</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-approvalStatus">审批状态</Label>
                  <Select
                    value={formData.approvalStatus}
                    onValueChange={(value) =>
                      setFormData({...formData, approvalStatus: value as ApprovalStatus})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择审批状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="未提交">未提交</SelectItem>
                      <SelectItem value="审批中">审批中</SelectItem>
                      <SelectItem value="已通过">已通过</SelectItem>
                      <SelectItem value="已拒绝">已拒绝</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-plannedPaymentDate">计划支付日期</Label>
                  <Input
                    id="edit-plannedPaymentDate"
                    type="date"
                    value={formData.plannedPaymentDate}
                    name="plannedPaymentDate"
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-actualPaymentDate">实际支付日期</Label>
                  <Input
                    id="edit-actualPaymentDate"
                    type="date"
                    value={formData.actualPaymentDate}
                    name="actualPaymentDate"
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-approver">审批人</Label>
                  <Input
                    id="edit-approver"
                    value={formData.approver}
                    name="approver"
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-approvalDate">审批日期</Label>
                  <Input
                    id="edit-approvalDate"
                    type="date"
                    value={formData.approvalDate}
                    name="approvalDate"
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-notes">备注</Label>
                <Textarea
                  id="edit-notes"
                  value={formData.notes}
                  name="notes"
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className="px-6 py-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleUpdateRecord}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除?</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 {selectedWage?.department} {selectedWage?.year}年{selectedWage?.month}月 的工资记录吗？
              此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteRecord}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loading ? "删除中..." : "删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 查看详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>工资详情</DialogTitle>
            <DialogDescription>
              查看工资详细信息及相关数据
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="flex-1 max-h-[calc(90vh-120px)]">
            <div className="p-4 space-y-6">
              {selectedWage && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* 基本信息 */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">基本信息</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">部门</div>
                          <div className="text-sm">{selectedWage.department}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">年份</div>
                          <div className="text-sm">{selectedWage.year}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">月份</div>
                          <div className="text-sm">{selectedWage.month}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">员工人数</div>
                          <div className="text-sm">{selectedWage.employeeCount}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">状态</div>
                          <div className="text-sm">{getPaymentStatusBadge(selectedWage.paymentStatus)}</div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* 工资信息 */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">工资信息</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">工资总额</div>
                          <div className="text-sm font-bold text-primary">{selectedWage.totalWages}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">平均工资</div>
                          <div className="text-sm">{selectedWage.averageWage}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">同比增长</div>
                          <div className={`text-sm ${selectedWage.yearOnYearGrowth > 10 ? 'text-red-500' : 'text-green-500'}`}>
                            {selectedWage.yearOnYearGrowth}%
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">环比增长</div>
                          <div className={`text-sm ${selectedWage.monthOnMonthGrowth > 6 ? 'text-red-500' : 'text-blue-500'}`}>
                            {selectedWage.monthOnMonthGrowth}%
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* 预算信息 */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">预算信息</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">预算金额</div>
                          <div className="text-sm">{formatCurrency(selectedWage.budgetAmount)}</div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">差异金额</div>
                          <div className={`text-sm ${selectedWage.budgetDifference > 0 ? 'text-red-500' : 'text-green-500'}`}>
                            {formatCurrency(selectedWage.budgetDifference)}
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-muted-foreground">执行率</div>
                          <div className="text-sm">
                            <Progress value={selectedWage.budgetExecutionRate} className="h-2" />
                            <span className="text-xs">{selectedWage.budgetExecutionRate}%</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 工资结构 */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">工资结构</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">基本工资</div>
                            <div className="text-sm">{formatCurrency(selectedWage.basicSalary)}</div>
                          </div>
                          <Progress value={(selectedWage.basicSalary / selectedWage.totalWagesValue) * 100} className="h-2" />

                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">绩效奖金</div>
                            <div className="text-sm">{formatCurrency(selectedWage.performanceBonus)}</div>
                          </div>
                          <Progress value={(selectedWage.performanceBonus / selectedWage.totalWagesValue) * 100} className="h-2" />

                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">津贴</div>
                            <div className="text-sm">{formatCurrency(selectedWage.allowance)}</div>
                          </div>
                          <Progress value={(selectedWage.allowance / selectedWage.totalWagesValue) * 100} className="h-2" />
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">加班费</div>
                            <div className="text-sm">{formatCurrency(selectedWage.overtimePay)}</div>
                          </div>
                          <Progress value={(selectedWage.overtimePay / selectedWage.totalWagesValue) * 100} className="h-2" />

                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">其他奖金</div>
                            <div className="text-sm">{formatCurrency(selectedWage.otherBonus)}</div>
                          </div>
                          <Progress value={(selectedWage.otherBonus / selectedWage.totalWagesValue) * 100} className="h-2" />

                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium text-muted-foreground">扣除项</div>
                            <div className="text-sm">{formatCurrency(selectedWage.socialInsurance + selectedWage.housingFund + selectedWage.taxAmount)}</div>
                          </div>
                          <Progress value={((selectedWage.socialInsurance + selectedWage.housingFund + selectedWage.taxAmount) / selectedWage.totalWagesValue) * 100} className="h-2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 支付信息 */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">支付信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">支付状态</div>
                          <div>{getPaymentStatusBadge(selectedWage.paymentStatus)}</div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">计划支付日期</div>
                          <div className="text-sm">{selectedWage.plannedPaymentDate || '未设置'}</div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">实际支付日期</div>
                          <div className="text-sm">{selectedWage.actualPaymentDate || '未支付'}</div>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">审批状态</div>
                          <div>{getApprovalStatusBadge(selectedWage.approvalStatus)}</div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">审批人</div>
                          <div className="text-sm">{selectedWage.approver || '未审批'}</div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm font-medium text-muted-foreground">审批日期</div>
                          <div className="text-sm">{selectedWage.approvalDate || '未审批'}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 备注信息 */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">备注信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm border rounded-md p-3 bg-muted/50">
                        {selectedWage.notes || '暂无备注信息'}
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </ScrollArea>
          <DialogFooter className="px-6 py-4">
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>关闭</Button>
            <Button onClick={() => {
              setIsDetailDialogOpen(false);
              handleEditRecord(selectedWage!);
            }}>编辑</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

