"use client"

import { use<PERSON>tate, use<PERSON>ffect, use<PERSON><PERSON><PERSON> } from "react"
import {
  Bar<PERSON>hart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  Clock,
  CheckCircle2,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCcw,
  Edit,
  Eye,
  Trash2,
  MoreHorizontal,
  FileText,
  CalendarDays,
  PieChart,
  CheckCircle,
  XCircle,
  ArrowRight,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { message } from "@/components/ui/use-message"
import { useToast } from "@/components/ui/use-toast"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import * as XLSX from 'xlsx'
import * as echarts from 'echarts/core'
import ReactECharts from 'echarts-for-react'
import dayjs from "dayjs"

// 定义工程进度接口
interface ProjectProgress {
  id: string
  name: string
  type: string
  manager: string
  department?: string
  startDate: string
  endDate: string
  plannedProgress: number
  actualProgress: number
  status: string
  trend: string
  milestone: string
  lastUpdate: string
  description?: string
  budget?: string
  spent?: string
  riskLevel?: string
  phase?: string
  milestones?: Milestone[]
  progressHistory?: ProgressRecord[]
  attachments?: string[]
  notes?: string
  disabled?: boolean
}

// 定义里程碑接口
interface Milestone {
  id: string
  name: string
  dueDate: string
  completed: boolean
  completionDate?: string
  description?: string
}

// 定义进度记录接口
interface ProgressRecord {
  id: string
  date: string
  progress: number
  milestone: string
  status: string
  updatedBy: string
  notes?: string
}

// 定义统计数据接口
interface Statistics {
  total: number
  inProgress: number
  normal: number
  delayed: number
  ahead: number
  paused: number
  completionRate: number
  averageDelay: number
  projectsByType: Record<string, number>
  projectsByDepartment: Record<string, number>
  projectsByPhase: Record<string, number>
  projectsByRisk: Record<string, number>
}

export function ProjectProgress() {
  const { toast } = useToast()
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isAddProgressRecordOpen, setIsAddProgressRecordOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [isAddProjectOpen, setIsAddProjectOpen] = useState(false)
  const [isEditProjectOpen, setIsEditProjectOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isGanttViewOpen, setIsGanttViewOpen] = useState(false)
  const [isAdvancedFilterOpen, setIsAdvancedFilterOpen] = useState(false)

  const [selectedProject, setSelectedProject] = useState<ProjectProgress | null>(null)
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [loading, setLoading] = useState(false)
  const [currentTab, setCurrentTab] = useState("all")

  // 表单数据
  const [projectForm, setProjectForm] = useState<Partial<ProjectProgress>>({})
  const [progressForm, setProgressForm] = useState<Partial<ProgressRecord>>({})
  const [milestoneForm, setMilestoneForm] = useState<Partial<Milestone>>({})

  // 示例数据
  const [projects, setProjects] = useState<ProjectProgress[]>([
    {
      id: "1",
      name: "矿区A3开发项目",
      type: "采矿工程",
      manager: "张三",
      department: "开发部",
      startDate: "2025-01-15",
      endDate: "2025-03-30",
      plannedProgress: 85,
      actualProgress: 80,
      status: "正常",
      lastUpdate: "2025-03-15",
      trend: "up",
      milestone: "设备安装阶段",
      phase: "实施阶段",
      riskLevel: "中",
      budget: "500万",
      spent: "420万",
      description: "矿区A3区域的开发项目，包含新采矿设备的安装和调试。",
      milestones: [
        { id: "1-1", name: "完成设计规划", dueDate: "2025-01-30", completed: true, completionDate: "2025-01-28" },
        { id: "1-2", name: "设备采购", dueDate: "2025-02-15", completed: true, completionDate: "2025-02-20" },
        { id: "1-3", name: "设备安装", dueDate: "2025-03-15", completed: false },
        { id: "1-4", name: "调试验收", dueDate: "2025-03-30", completed: false },
      ],
      progressHistory: [
        { id: "h1-1", date: "2025-01-15", progress: 0, milestone: "开始", status: "正常", updatedBy: "张三" },
        { id: "h1-2", date: "2025-01-30", progress: 20, milestone: "完成设计规划", status: "正常", updatedBy: "张三" },
        { id: "h1-3", date: "2025-02-20", progress: 40, milestone: "设备采购", status: "延期", updatedBy: "李四", notes: "供应商延迟交付" },
        { id: "h1-4", date: "2025-03-05", progress: 60, milestone: "开始安装", status: "正常", updatedBy: "张三" },
        { id: "h1-5", date: "2025-03-15", progress: 80, milestone: "设备安装阶段", status: "正常", updatedBy: "张三" },
      ],
    },
    {
      id: "2",
      name: "设备更新计划",
      type: "设备工程",
      manager: "李四",
      department: "设备部",
      startDate: "2025-02-01",
      endDate: "2025-04-15",
      plannedProgress: 50,
      actualProgress: 45,
      status: "正常",
      lastUpdate: "2025-03-14",
      trend: "down",
      milestone: "设备采购阶段",
      phase: "采购阶段",
      riskLevel: "低",
      budget: "300万",
      spent: "140万",
      description: "更新老旧设备，提高生产效率和安全性。",
    },
    {
      id: "3",
      name: "安全系统升级",
      type: "安全工程",
      manager: "王五",
      department: "安全部",
      startDate: "2025-01-15",
      endDate: "2025-03-10",
      plannedProgress: 100,
      actualProgress: 90,
      status: "延期",
      lastUpdate: "2025-03-10",
      trend: "down",
      milestone: "系统测试阶段",
      phase: "收尾阶段",
      riskLevel: "高",
      budget: "200万",
      spent: "190万",
      description: "升级矿区安全监测系统，提高事故预警能力。",
    },
    {
      id: "4",
      name: "新矿区勘探",
      type: "勘探工程",
      manager: "赵六",
      department: "勘探部",
      startDate: "2025-02-01",
      endDate: "2025-04-01",
      plannedProgress: 25,
      actualProgress: 20,
      status: "正常",
      lastUpdate: "2025-03-12",
      trend: "down",
      milestone: "初步勘探阶段",
      phase: "初始阶段",
      riskLevel: "高",
      budget: "800万",
      spent: "180万",
      description: "对B区域进行资源勘探，评估开采价值。",
    },
    {
      id: "5",
      name: "环保设施改造",
      type: "环保工程",
      manager: "钱七",
      department: "环保部",
      startDate: "2025-01-05",
      endDate: "2025-03-15",
      plannedProgress: 15,
      actualProgress: 20,
      status: "超前",
      lastUpdate: "2025-03-15",
      trend: "up",
      milestone: "设计规划阶段",
      phase: "初始阶段",
      riskLevel: "中",
      budget: "250万",
      spent: "40万",
      description: "升级环保设施，满足新环保标准要求。",
    },
  ])

  // 使用useMemo计算统计数据
  const statistics = useMemo(() => {
    const stats: Statistics = {
      total: projects.length,
      inProgress: projects.filter(p => p.actualProgress > 0 && p.actualProgress < 100).length,
      normal: projects.filter(p => p.status === "正常").length,
      delayed: projects.filter(p => p.status === "延期").length,
      ahead: projects.filter(p => p.status === "超前").length,
      paused: projects.filter(p => p.status === "暂停").length,
      completionRate: projects.length > 0
        ? Math.round((projects.filter(p => p.actualProgress === 100).length / projects.length) * 100)
        : 0,
      averageDelay: 0,
      projectsByType: {},
      projectsByDepartment: {},
      projectsByPhase: {},
      projectsByRisk: {},
    }

    // 计算平均延迟天数
    const delayedProjects = projects.filter(p => p.status === "延期")
    if (delayedProjects.length > 0) {
      // 计算逻辑这里简化，实际应该计算预计完成时间与现在日期的差
      stats.averageDelay = 7
    }

    // 按类型统计
    projects.forEach(p => {
      if (p.type) {
        stats.projectsByType[p.type] = (stats.projectsByType[p.type] || 0) + 1
      }
      if (p.department) {
        stats.projectsByDepartment[p.department] = (stats.projectsByDepartment[p.department] || 0) + 1
      }
      if (p.phase) {
        stats.projectsByPhase[p.phase] = (stats.projectsByPhase[p.phase] || 0) + 1
      }
      if (p.riskLevel) {
        stats.projectsByRisk[p.riskLevel] = (stats.projectsByRisk[p.riskLevel] || 0) + 1
      }
    })

    return stats
  }, [projects])

  // 过滤和排序项目
  const filteredProjects = useMemo(() => {
    let result = [...projects]

    // 按状态Tab过滤
    if (currentTab !== "all") {
      const statusMap: Record<string, string> = {
        "normal": "正常",
        "delayed": "延期",
        "ahead": "超前",
        "paused": "暂停"
      }
      result = result.filter(p => p.status === statusMap[currentTab])
    }

    // 按搜索关键词过滤
    if (searchText) {
      const keyword = searchText.toLowerCase()
      result = result.filter(p =>
        p.name.toLowerCase().includes(keyword) ||
        p.manager.toLowerCase().includes(keyword) ||
        p.milestone.toLowerCase().includes(keyword)
      )
    }

    // 按类型过滤
    if (selectedType !== "all") {
      result = result.filter(p => p.type === selectedType)
    }

    // 按部门过滤
    if (selectedDepartment !== "all") {
      result = result.filter(p => p.department === selectedDepartment)
    }

    // 按排序字段排序
    if (sortColumn) {
      result.sort((a, b) => {
        const valueA = a[sortColumn as keyof ProjectProgress]
        const valueB = b[sortColumn as keyof ProjectProgress]

        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return sortDirection === "asc"
            ? valueA.toLowerCase().localeCompare(valueB.toLowerCase())
            : valueB.toLowerCase().localeCompare(valueA.toLowerCase())
        }

        if (valueA === undefined || valueB === undefined) return 0

        if (valueA < valueB) return sortDirection === "asc" ? -1 : 1
        if (valueA > valueB) return sortDirection === "asc" ? 1 : -1
        return 0
      })
    }

    return result
  }, [projects, currentTab, searchText, selectedType, selectedDepartment, sortColumn, sortDirection])

  // 处理排序
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(column)
      setSortDirection("asc")
    }
  }

  // 消息提示函数
  const showMessage = (type: 'success' | 'error', content: string) => {
    if (type === 'success') {
      message.success(content)
    } else {
      message.error(content)
    }
  }

  // 处理添加新工程
  const handleAddProject = () => {
    if (!projectForm.name || !projectForm.type || !projectForm.manager || !projectForm.startDate || !projectForm.endDate) {
      showMessage('error', '请填写必填项')
      return
    }

    const newProject: ProjectProgress = {
      id: Date.now().toString(),
      name: projectForm.name!,
      type: projectForm.type!,
      manager: projectForm.manager!,
      department: projectForm.department || '',
      startDate: projectForm.startDate!,
      endDate: projectForm.endDate!,
      plannedProgress: projectForm.plannedProgress || 0,
      actualProgress: projectForm.actualProgress || 0,
      status: projectForm.status || '正常',
      trend: 'up',
      milestone: projectForm.milestone || '启动阶段',
      lastUpdate: new Date().toISOString().split('T')[0],
      description: projectForm.description,
      budget: projectForm.budget,
      spent: projectForm.spent,
      riskLevel: projectForm.riskLevel,
      phase: projectForm.phase,
      milestones: [],
    }

    setProjects([...projects, newProject])
    setIsAddProjectOpen(false)
    setProjectForm({})
    showMessage('success', '工程添加成功')
  }

  // 处理编辑工程
  const handleEditProject = () => {
    if (!selectedProject) return
    if (!projectForm.name || !projectForm.type || !projectForm.manager || !projectForm.startDate || !projectForm.endDate) {
      showMessage('error', '请填写必填项')
      return
    }

    const updatedProjects = projects.map(p =>
      p.id === selectedProject.id
        ? {
            ...p,
            ...projectForm,
            lastUpdate: new Date().toISOString().split('T')[0]
          }
        : p
    )

    setProjects(updatedProjects)
    setIsEditProjectOpen(false)
    setProjectForm({})
    setSelectedProject(null)
    showMessage('success', '工程更新成功')
  }

  // 处理删除工程
  const handleDeleteProject = () => {
    if (!projectToDelete) return

    setProjects(projects.filter(p => p.id !== projectToDelete))
    setIsDeleteDialogOpen(false)
    setProjectToDelete(null)
    showMessage('success', '工程已删除')
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRows.length === 0) return

    setProjects(projects.filter(p => !selectedRows.includes(p.id)))
    setSelectedRows([])
    setIsDeleteDialogOpen(false)
    showMessage('success', `已删除${selectedRows.length}个工程`)
  }

  // 处理添加进度记录
  const handleAddProgressRecord = () => {
    if (!selectedProject) return
    if (!progressForm.progress || !progressForm.milestone || !progressForm.status) {
      showMessage('error', '请填写必填项')
      return
    }

    const newProgressRecord: ProgressRecord = {
      id: Date.now().toString(),
      date: new Date().toISOString().split('T')[0],
      progress: progressForm.progress as number,
      milestone: progressForm.milestone!,
      status: progressForm.status!,
      updatedBy: progressForm.updatedBy || '当前用户',
      notes: progressForm.notes,
    }

    const updatedProjects = projects.map(p => {
      if (p.id === selectedProject.id) {
        // 计算进度变化趋势
        const trend = (progressForm.progress || 0) > p.actualProgress ? 'up' :
                     (progressForm.progress || 0) < p.actualProgress ? 'down' : p.trend

        return {
          ...p,
          actualProgress: progressForm.progress as number,
          milestone: progressForm.milestone!,
          status: progressForm.status!,
          lastUpdate: new Date().toISOString().split('T')[0],
          trend,
          progressHistory: [...(p.progressHistory || []), newProgressRecord]
        }
      }
      return p
    })

    setProjects(updatedProjects)
    setIsAddProgressRecordOpen(false)
    setProgressForm({})
    showMessage('success', '进度更新成功')
  }

  // 导出数据
  const handleExportData = (format: string) => {
    try {
      if (format === 'excel') {
        const worksheet = XLSX.utils.json_to_sheet(projects.map(p => ({
          工程名称: p.name,
          工程类型: p.type,
          负责人: p.manager,
          部门: p.department,
          开始日期: p.startDate,
          结束日期: p.endDate,
          计划进度: `${p.plannedProgress}%`,
          实际进度: `${p.actualProgress}%`,
          状态: p.status,
          当前里程碑: p.milestone,
          最后更新: p.lastUpdate,
          预算: p.budget,
          已用资金: p.spent,
          风险等级: p.riskLevel,
          阶段: p.phase,
          描述: p.description,
        })))

        // 设置列宽
        const columnWidths = [
          { wch: 20 }, // 工程名称
          { wch: 10 }, // 工程类型
          { wch: 8 }, // 负责人
          { wch: 10 }, // 部门
          { wch: 10 }, // 开始日期
          { wch: 10 }, // 结束日期
          { wch: 10 }, // 计划进度
          { wch: 10 }, // 实际进度
          { wch: 10 }, // 状态
          { wch: 15 }, // 当前里程碑
          { wch: 10 }, // 最后更新
          { wch: 10 }, // 预算
          { wch: 10 }, // 已用资金
          { wch: 10 }, // 风险等级
          { wch: 10 }, // 阶段
          { wch: 30 }, // 描述
        ]
        worksheet['!cols'] = columnWidths

        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, '工程进度')

        XLSX.writeFile(workbook, '工程进度报告.xlsx')
        showMessage('success', 'Excel导出成功')
      } else {
        // 导出为CSV
        const csvContent = projects.map(p =>
          `${p.name},${p.type},${p.manager},${p.department},${p.startDate},${p.endDate},${p.plannedProgress}%,${p.actualProgress}%,${p.status},${p.milestone},${p.lastUpdate}`
        ).join('\n')

        const blob = new Blob([`工程名称,工程类型,负责人,部门,开始日期,结束日期,计划进度,实际进度,状态,当前里程碑,最后更新\n${csvContent}`], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', '工程进度报告.csv')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        showMessage('success', 'CSV导出成功')
      }

      setIsExportDialogOpen(false)
    } catch (error) {
      showMessage('error', '导出失败，请重试')
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    // 模拟数据加载
    setTimeout(() => {
      setLoading(false)
      showMessage('success', '数据已刷新')
    }, 1000)
  }

  // 处理点击行选择
  const handleRowClick = (project: ProjectProgress) => {
    setSelectedProject(project)
    setIsViewDetailsOpen(true)
  }

  // 初始化编辑表单数据
  const prepareEditForm = (project: ProjectProgress) => {
    setProjectForm({
      name: project.name,
      type: project.type,
      manager: project.manager,
      department: project.department,
      startDate: project.startDate,
      endDate: project.endDate,
      plannedProgress: project.plannedProgress,
      milestone: project.milestone,
      riskLevel: project.riskLevel,
      phase: project.phase,
      budget: project.budget,
      spent: project.spent,
      description: project.description,
    })
    setSelectedProject(project)
    setIsEditProjectOpen(true)
  }

  // 初始化进度更新表单数据
  const prepareProgressForm = (project: ProjectProgress) => {
    setProgressForm({
      progress: project.actualProgress,
      milestone: project.milestone,
      status: project.status,
      updatedBy: '当前用户',
    })
    setSelectedProject(project)
    setIsAddProgressRecordOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "延期":
        return <Badge variant="destructive">延期</Badge>
      case "超前":
        return <Badge className="bg-blue-500">超前</Badge>
      case "暂停":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            暂停
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取进度趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <ArrowUpRight className="h-4 w-4 text-green-500" />
      case "down":
        return <ArrowDownRight className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  // 生成进度趋势图数据
  const generateTrendChartData = () => {
    // 假设有一些进度历史数据用于呈现趋势图
    const timeData = []
    const plannedData = []
    const actualData = []

    // 使用当前项目数据生成图表数据点
    const project = projects[0] // 使用第一个项目作为示例
    if (project && project.progressHistory) {
      project.progressHistory.forEach(record => {
        timeData.push(record.date)
        actualData.push(record.progress)
        // 假设计划进度是线性的
        const startDate = new Date(project.startDate).getTime()
        const endDate = new Date(project.endDate).getTime()
        const recordDate = new Date(record.date).getTime()
        const totalDuration = endDate - startDate
        const elapsedDuration = recordDate - startDate
        const plannedProgress = Math.min(100, Math.max(0, Math.round((elapsedDuration / totalDuration) * 100)))
        plannedData.push(plannedProgress)
      })
    } else {
      // 如果没有项目数据，生成模拟数据
      const now = new Date()
      for (let i = 30; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        timeData.push(date.toISOString().split('T')[0])
        plannedData.push(Math.min(100, Math.round((30 - i) * 3.3)))
        actualData.push(Math.min(100, Math.round((30 - i) * 3.3) + (Math.random() > 0.5 ? 1 : -1) * Math.round(Math.random() * 5)))
      }
    }

    return { timeData, plannedData, actualData }
  }

  // 生成里程碑完成情况数据
  const generateMilestoneChartData = () => {
    const categories: string[] = []
    const completed: number[] = []
    const pending: number[] = []

    // 统计所有项目的里程碑完成情况
    projects.forEach(project => {
      if (project.milestones && project.milestones.length > 0) {
        const completedCount = project.milestones.filter(m => m.completed).length
        const pendingCount = project.milestones.length - completedCount
        categories.push(project.name)
        completed.push(completedCount)
        pending.push(pendingCount)
      }
    })

    // 如果没有足够的数据，生成模拟数据
    if (categories.length === 0) {
      categories.push('项目A', '项目B', '项目C', '项目D', '项目E')
      completed.push(4, 3, 2, 1, 3)
      pending.push(1, 2, 2, 3, 2)
    }

    return { categories, completed, pending }
  }

  // 生成按类型分布的饼图数据
  const generateTypeDistributionData = () => {
    const typeData: Array<{name: string, value: number}> = []

    // 转换统计数据为饼图所需格式
    Object.entries(statistics.projectsByType).forEach(([type, count]) => {
      typeData.push({
        name: type,
        value: count
      })
    })

    return typeData
  }

  // 进度趋势图配置
  const getTrendChartOption = () => {
    const { timeData, plannedData, actualData } = generateTrendChartData()

    return {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['计划进度', '实际进度'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeData,
        axisLabel: {
          rotate: 45,
          formatter: function (value: string) {
            return value.substring(5); // 只显示月-日
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '进度(%)',
        min: 0,
        max: 100
      },
      series: [
        {
          name: '计划进度',
          type: 'line',
          data: plannedData,
          smooth: true,
          lineStyle: {
            width: 3,
            type: 'dashed'
          },
          itemStyle: {
            color: '#8884d8'
          }
        },
        {
          name: '实际进度',
          type: 'line',
          data: actualData,
          smooth: true,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: '#82ca9d'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(130, 202, 157, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(130, 202, 157, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }
  }

  // 里程碑完成情况图表配置
  const getMilestoneChartOption = () => {
    const { categories, completed, pending } = generateMilestoneChartData()

    return {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['已完成', '未完成'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          formatter: function (value: string) {
            if (value.length > 10) {
              return value.substring(0, 10) + '...';
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '已完成',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            formatter: '{c}'
          },
          emphasis: {
            focus: 'series'
          },
          data: completed,
          itemStyle: {
            color: '#52c41a'
          }
        },
        {
          name: '未完成',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            formatter: '{c}'
          },
          emphasis: {
            focus: 'series'
          },
          data: pending,
          itemStyle: {
            color: '#d9d9d9'
          }
        }
      ]
    }
  }

  // 按类型分布饼图配置
  const getTypeDistributionChartOption = () => {
    const typeData = generateTypeDistributionData()

    return {
      title: {
        text: '', // 移除标题，只保留外部的标题
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        data: typeData.map(item => item.name)
      },
      series: [
        {
          name: '工程类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: typeData
        }
      ]
    }
  }

  // 进度完成率环形图配置
  const getCompletionRateChartOption = () => {
    return {
      series: [
        {
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#1890ff'
                  },
                  {
                    offset: 1,
                    color: '#52c41a'
                  }
                ]
              }
            }
          },
          axisLine: {
            lineStyle: {
              width: 20,
              color: [
                [1, '#e6e6e6']
              ]
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          title: {
            fontSize: 14
          },
          detail: {
            width: '60%',
            lineHeight: 40,
            height: 40,
            borderRadius: 8,
            fontSize: 30,
            fontWeight: 'bold',
            color: 'inherit',
            formatter: '{value}%'
          },
          data: [
            {
              value: statistics.completionRate,
              name: '完成率',
              title: {
                offsetCenter: ['0%', '-25%']
              },
              detail: {
                offsetCenter: ['0%', '0%']
              }
            }
          ]
        }
      ]
    }
  }

  // 甘特图配置
  const getGanttChartOption = () => {
    // 准备甘特图数据
    const categories: string[] = []
    const ganttData: Array<{
      name: string,
      value: [number, number, number, number],
      itemStyle: { color: string }
    }> = []

    // 计算日期范围
    let minDate = new Date()
    let maxDate = new Date()

    if (projects.length > 0) {
      const startDates = projects.map(p => new Date(p.startDate))
      const endDates = projects.map(p => new Date(p.endDate))

      minDate = new Date(Math.min(...startDates.map(d => d.getTime())))
      maxDate = new Date(Math.max(...endDates.map(d => d.getTime())))

      // 确保日期范围至少有7天
      const diffDays = Math.ceil((maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24))
      if (diffDays < 7) {
        maxDate = new Date(minDate.getTime() + 7 * 24 * 60 * 60 * 1000)
      }

      // 添加项目到甘特图数据
      projects.forEach((project, index) => {
        categories.push(project.name)

        // 获取项目的颜色
        let itemColor = '#1890ff' // 默认蓝色
        if (project.status === '延期') {
          itemColor = '#ff4d4f' // 红色
        } else if (project.status === '超前') {
          itemColor = '#52c41a' // 绿色
        }

        ganttData.push({
          name: project.name,
          value: [
            index,
            new Date(project.startDate).getTime(),
            new Date(project.endDate).getTime(),
            project.actualProgress
          ],
          itemStyle: { color: itemColor }
        })
      })
    } else {
      // 如果没有项目数据，则创建样例数据
      const today = new Date()
      minDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 30)
      maxDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 60)

      const sampleProjects = [
        { name: '示例项目1', startDate: new Date(minDate.getTime() + 5 * 24 * 60 * 60 * 1000), endDate: new Date(minDate.getTime() + 35 * 24 * 60 * 60 * 1000), progress: 60, status: '正常' },
        { name: '示例项目2', startDate: new Date(minDate.getTime() + 10 * 24 * 60 * 60 * 1000), endDate: new Date(minDate.getTime() + 50 * 24 * 60 * 60 * 1000), progress: 30, status: '延期' },
        { name: '示例项目3', startDate: new Date(minDate.getTime() + 20 * 24 * 60 * 60 * 1000), endDate: new Date(minDate.getTime() + 40 * 24 * 60 * 60 * 1000), progress: 80, status: '超前' }
      ]

      sampleProjects.forEach((project, index) => {
        categories.push(project.name)

        let itemColor = '#1890ff' // 默认蓝色
        if (project.status === '延期') {
          itemColor = '#ff4d4f' // 红色
        } else if (project.status === '超前') {
          itemColor = '#52c41a' // 绿色
        }

        ganttData.push({
          name: project.name,
          value: [
            index,
            project.startDate.getTime(),
            project.endDate.getTime(),
            project.progress
          ],
          itemStyle: { color: itemColor }
        })
      })
    }

    return {
      title: {
        text: '工程甘特图',
        left: 'center'
      },
      tooltip: {
        formatter: function (params: any) {
          const startDate = new Date(params.value[1]).toLocaleDateString('zh-CN')
          const endDate = new Date(params.value[2]).toLocaleDateString('zh-CN')
          const duration = Math.ceil((params.value[2] - params.value[1]) / (1000 * 60 * 60 * 24))
          return `${params.name}<br/>
                  开始日期: ${startDate}<br/>
                  结束日期: ${endDate}<br/>
                  工期: ${duration}天<br/>
                  进度: ${params.value[3]}%`
        }
      },
      legend: {
        show: false
      },
      grid: {
        top: 70,
        bottom: 20,
        left: 120,
        right: 20
      },
      xAxis: {
        type: 'time',
        min: minDate.getTime(),
        max: maxDate.getTime(),
        axisLabel: {
          formatter: '{MM}-{dd}',
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          fontWeight: 'bold'
        },
        inverse: true
      },
      series: [{
        type: 'custom',
        renderItem: function (params: any, api: any) {
          const categoryIndex = api.value(0)
          const start = api.coord([api.value(1), categoryIndex])
          const end = api.coord([api.value(2), categoryIndex])
          const height = api.size([0, 1])[1] * 0.6
          const progress = api.value(3)

          const rectShape = {
            x: start[0],
            y: start[1] - height / 2,
            width: end[0] - start[0],
            height: height,
          }

          const progressWidth = rectShape.width * (progress / 100)

          return {
            type: 'group',
            children: [
              {
                // 背景条
                type: 'rect',
                shape: rectShape,
                style: {
                  fill: 'rgba(0,0,0,0.1)',
                  stroke: api.visual('color'),
                  lineWidth: 1
                }
              },
              {
                // 进度条
                type: 'rect',
                shape: {
                  x: rectShape.x,
                  y: rectShape.y,
                  width: progressWidth,
                  height: rectShape.height
                },
                style: {
                  fill: api.visual('color')
                }
              },
              {
                // 显示进度百分比
                type: 'text',
                style: {
                  x: rectShape.x + rectShape.width / 2,
                  y: rectShape.y + rectShape.height / 2,
                  text: progress + '%',
                  textAlign: 'center',
                  textVerticalAlign: 'middle',
                  fill: '#fff',
                  fontWeight: 'bold',
                  stroke: '#000',
                  lineWidth: 0.5
                }
              }
            ]
          }
        },
        data: ganttData
      }]
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">工程进度管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsGanttViewOpen(true)}>
            <Calendar className="h-4 w-4 mr-2" />
            甘特图
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsAddProjectOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加工程
          </Button>
          <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                更新进度
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>更新工程进度</DialogTitle>
                <DialogDescription>更新工程的实际进度和当前里程碑</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="project">选择工程</Label>
                  <Select>
                    <SelectTrigger id="project">
                      <SelectValue placeholder="选择工程" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="actual-progress">实际进度 (%)</Label>
                  <Input id="actual-progress" type="number" min="0" max="100" placeholder="输入实际进度百分比" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="milestone">当前里程碑</Label>
                  <Select>
                    <SelectTrigger id="milestone">
                      <SelectValue placeholder="选择当前里程碑" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="planning">规划设计阶段</SelectItem>
                      <SelectItem value="procurement">设备采购阶段</SelectItem>
                      <SelectItem value="installation">设备安装阶段</SelectItem>
                      <SelectItem value="testing">系统测试阶段</SelectItem>
                      <SelectItem value="completion">完工验收阶段</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">当前状态</Label>
                  <Select>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择当前状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="delayed">延期</SelectItem>
                      <SelectItem value="ahead">超前</SelectItem>
                      <SelectItem value="paused">暂停</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="update-notes">更新说明</Label>
                  <Textarea id="update-notes" placeholder="输入进度更新说明" rows={4} />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsUpdateDialogOpen(false)}>保存</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <BarChart2 className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">进行中工程</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.normal}</h3>
            <p className="text-sm text-muted-foreground">正常进度</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.delayed}</h3>
            <p className="text-sm text-muted-foreground">延期工程</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.paused}</h3>
            <p className="text-sm text-muted-foreground">暂停工程</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>工程进度跟踪</CardTitle>
              <CardDescription>监控所有工程项目的进度状态</CardDescription>
            </div>
            <Tabs defaultValue="all" value={currentTab} onValueChange={setCurrentTab}>
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="normal">正常</TabsTrigger>
                <TabsTrigger value="delayed">延期</TabsTrigger>
                <TabsTrigger value="ahead">超前</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索工程..."
                  className="pl-8 w-[250px]"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </div>
              <Select
                defaultValue="all"
                value={selectedType}
                onValueChange={setSelectedType}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="工程类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="采矿工程">采矿工程</SelectItem>
                  <SelectItem value="设备工程">设备工程</SelectItem>
                  <SelectItem value="安全工程">安全工程</SelectItem>
                  <SelectItem value="勘探工程">勘探工程</SelectItem>
                  <SelectItem value="环保工程">环保工程</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={() => setIsAdvancedFilterOpen(true)}>
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[30px]">
                    <Checkbox
                      checked={selectedRows.length === projects.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRows(projects.map(p => p.id))
                        } else {
                          setSelectedRows([])
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>工程名称</TableHead>
                  <TableHead>负责人</TableHead>
                  <TableHead>开始日期</TableHead>
                  <TableHead>结束日期</TableHead>
                  <TableHead>计划进度</TableHead>
                  <TableHead>实际进度</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>趋势</TableHead>
                  <TableHead>当前里程碑</TableHead>
                  <TableHead>最后更新</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProjects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRows.includes(project.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRows([...selectedRows, project.id])
                          } else {
                            setSelectedRows(selectedRows.filter(id => id !== project.id))
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{project.name}</TableCell>
                    <TableCell>{project.manager}</TableCell>
                    <TableCell>{project.startDate}</TableCell>
                    <TableCell>{project.endDate}</TableCell>
                    <TableCell>{project.plannedProgress}%</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={project.actualProgress} className="h-2 w-[60px]" />
                        <span>{project.actualProgress}%</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(project.status)}</TableCell>
                    <TableCell>{getTrendIcon(project.trend)}</TableCell>
                    <TableCell>{project.milestone}</TableCell>
                    <TableCell>{project.lastUpdate}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end">
                        <Button variant="ghost" size="sm" onClick={() => {
                          setSelectedProject(project)
                          setIsViewDetailsOpen(true)
                        }}>
                          <Eye className="h-4 w-4" />
                      </Button>
                        <Button variant="ghost" size="sm" onClick={() => prepareProgressForm(project)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                      </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => prepareProgressForm(project)}>
                              更新进度
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => prepareEditForm(project)}>
                              编辑工程
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setProjectToDelete(project.id)
                              setIsDeleteDialogOpen(true)
                            }}>
                              删除工程
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {projects.length} 个工程项目</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>工程进度趋势</CardTitle>
              <Select defaultValue="month">
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">近一周</SelectItem>
                  <SelectItem value="month">近一月</SelectItem>
                  <SelectItem value="quarter">近一季</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent className="h-80">
            <ReactECharts
              option={getTrendChartOption()}
              style={{ height: '100%', width: '100%' }}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>里程碑完成情况</CardTitle>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加里程碑
              </Button>
            </div>
          </CardHeader>
          <CardContent className="h-72"> {/* 将高度从h-80调整为h-72 */}
            <ReactECharts
              option={getMilestoneChartOption()}
              style={{ height: '100%', width: '100%' }}
            />
          </CardContent>
        </Card>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedRows.length > 0
                ? `确定要删除选中的 ${selectedRows.length} 个工程项目吗？此操作不可恢复。`
                : "确定要删除此工程项目吗？此操作不可恢复。"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={selectedRows.length > 0 ? handleBatchDelete : handleDeleteProject}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 工程详情抽屉 */}
      <Sheet open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <SheetContent className="w-[600px] sm:w-[540px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>工程详情</SheetTitle>
            <SheetDescription>查看工程的详细信息和进度记录</SheetDescription>
          </SheetHeader>

          {selectedProject && (
            <div className="py-6 space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">{selectedProject.name}</h3>
                <div className="flex items-center gap-2">
                  {getStatusBadge(selectedProject.status)}
                  <span className="text-sm text-muted-foreground">
                    最后更新: {selectedProject.lastUpdate}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">工程类型</Label>
                  <p className="text-sm">{selectedProject.type}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">风险等级</Label>
                  <p className="text-sm">{selectedProject.riskLevel || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">负责人</Label>
                  <p className="text-sm">{selectedProject.manager}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">部门</Label>
                  <p className="text-sm">{selectedProject.department || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">开始日期</Label>
                  <p className="text-sm">{selectedProject.startDate}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">计划结束日期</Label>
                  <p className="text-sm">{selectedProject.endDate}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">当前里程碑</Label>
                  <p className="text-sm">{selectedProject.milestone}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">项目阶段</Label>
                  <p className="text-sm">{selectedProject.phase || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">预算</Label>
                  <p className="text-sm">{selectedProject.budget || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">已用资金</Label>
                  <p className="text-sm">{selectedProject.spent || "未设置"}</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">进度</Label>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>计划进度: {selectedProject.plannedProgress}%</span>
                    <span>实际进度: {selectedProject.actualProgress}%</span>
                  </div>
                  <Progress value={selectedProject.actualProgress} className="h-2" />
                </div>
              </div>

              {selectedProject.description && (
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">工程描述</Label>
                  <p className="text-sm">{selectedProject.description}</p>
                </div>
              )}

              {/* 里程碑列表 */}
              {selectedProject.milestones && selectedProject.milestones.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">里程碑</h4>
                  <div className="space-y-2">
                    {selectedProject.milestones.map((milestone) => (
                      <div
                        key={milestone.id}
                        className="flex items-center justify-between border rounded-md p-3"
                      >
                        <div className="flex items-center gap-2">
                          {milestone.completed ? (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          ) : (
                            <Clock className="h-4 w-4 text-yellow-500" />
                          )}
                          <div>
                            <p className="text-sm font-medium">{milestone.name}</p>
                            <p className="text-xs text-muted-foreground">
                              计划日期: {milestone.dueDate}
                              {milestone.completionDate && ` | 完成日期: ${milestone.completionDate}`}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant={milestone.completed ? "outline" : "secondary"}
                          className={milestone.completed ? "bg-green-100" : ""}
                        >
                          {milestone.completed ? "已完成" : "进行中"}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 进度历史记录 */}
              {selectedProject.progressHistory && selectedProject.progressHistory.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">进度历史记录</h4>
                  <div className="space-y-2">
                    {selectedProject.progressHistory.map((record) => (
                      <div
                        key={record.id}
                        className="border rounded-md p-3 space-y-2"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <CalendarDays className="h-4 w-4 text-muted-foreground" />
                            <p className="text-sm font-medium">{record.date}</p>
                          </div>
                          {getStatusBadge(record.status)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={record.progress} className="h-2 flex-1" />
                          <span className="text-sm">{record.progress}%</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-xs text-muted-foreground">里程碑: </span>
                            <span>{record.milestone}</span>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground">更新人: </span>
                            <span>{record.updatedBy}</span>
                          </div>
                        </div>
                        {record.notes && (
                          <p className="text-xs text-muted-foreground mt-1">{record.notes}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <SheetFooter>
            <div className="flex mt-4 justify-between w-full">
              <Button variant="outline" onClick={() => {
                if (selectedProject) {
                  prepareProgressForm(selectedProject)
                }
                setIsViewDetailsOpen(false)
              }}>
                更新进度
              </Button>
              <Button variant="outline" onClick={() => {
                if (selectedProject) {
                  prepareEditForm(selectedProject)
                }
                setIsViewDetailsOpen(false)
              }}>
                编辑工程
              </Button>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 添加/更新进度记录对话框 */}
      <Dialog open={isAddProgressRecordOpen} onOpenChange={setIsAddProgressRecordOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>更新工程进度</DialogTitle>
            <DialogDescription>
              {selectedProject && `为工程 "${selectedProject.name}" 更新最新进度数据`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="current-progress">实际进度 (%)<span className="text-red-500">*</span></Label>
              <Input
                id="current-progress"
                type="number"
                min="0"
                max="100"
                value={progressForm.progress || 0}
                onChange={(e) => setProgressForm({ ...progressForm, progress: Number(e.target.value) })}
                placeholder="输入实际进度百分比"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="milestone">当前里程碑<span className="text-red-500">*</span></Label>
              <Input
                id="milestone"
                value={progressForm.milestone || ""}
                onChange={(e) => setProgressForm({ ...progressForm, milestone: e.target.value })}
                placeholder="输入当前里程碑"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">状态<span className="text-red-500">*</span></Label>
              <Select
                value={progressForm.status}
                onValueChange={(value) => setProgressForm({ ...progressForm, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="选择当前状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="正常">正常</SelectItem>
                  <SelectItem value="延期">延期</SelectItem>
                  <SelectItem value="超前">超前</SelectItem>
                  <SelectItem value="暂停">暂停</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="updatedBy">更新人<span className="text-red-500">*</span></Label>
              <Input
                id="updatedBy"
                value={progressForm.updatedBy || ""}
                onChange={(e) => setProgressForm({ ...progressForm, updatedBy: e.target.value })}
                placeholder="输入更新人姓名"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">更新说明</Label>
              <Textarea
                id="notes"
                value={progressForm.notes || ""}
                onChange={(e) => setProgressForm({ ...progressForm, notes: e.target.value })}
                placeholder="输入进度更新说明"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddProgressRecordOpen(false)
                setProgressForm({})
              }}
            >
              取消
            </Button>
            <Button onClick={handleAddProgressRecord}>保存进度</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加工程对话框 */}
      <Dialog open={isAddProjectOpen} onOpenChange={setIsAddProjectOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加新工程</DialogTitle>
            <DialogDescription>
              添加新工程项目并设置基本信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project-name">工程名称<span className="text-red-500">*</span></Label>
                <Input
                  id="project-name"
                  value={projectForm.name || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, name: e.target.value })}
                  placeholder="输入工程名称"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-type">工程类型<span className="text-red-500">*</span></Label>
                <Select
                  value={projectForm.type}
                  onValueChange={(value) => setProjectForm({ ...projectForm, type: value })}
                >
                  <SelectTrigger id="project-type">
                    <SelectValue placeholder="选择工程类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程">采矿工程</SelectItem>
                    <SelectItem value="设备工程">设备工程</SelectItem>
                    <SelectItem value="安全工程">安全工程</SelectItem>
                    <SelectItem value="勘探工程">勘探工程</SelectItem>
                    <SelectItem value="环保工程">环保工程</SelectItem>
                    <SelectItem value="基建工程">基建工程</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-manager">负责人<span className="text-red-500">*</span></Label>
                <Input
                  id="project-manager"
                  value={projectForm.manager || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, manager: e.target.value })}
                  placeholder="输入负责人姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-department">所属部门</Label>
                <Input
                  id="project-department"
                  value={projectForm.department || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, department: e.target.value })}
                  placeholder="输入所属部门"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-start-date">开始日期<span className="text-red-500">*</span></Label>
                <Input
                  id="project-start-date"
                  type="date"
                  value={projectForm.startDate || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, startDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-end-date">计划结束日期<span className="text-red-500">*</span></Label>
                <Input
                  id="project-end-date"
                  type="date"
                  value={projectForm.endDate || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, endDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-milestone">当前里程碑</Label>
                <Input
                  id="project-milestone"
                  value={projectForm.milestone || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, milestone: e.target.value })}
                  placeholder="输入当前里程碑"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-planned-progress">计划进度 (%)</Label>
                <Input
                  id="project-planned-progress"
                  type="number"
                  min="0"
                  max="100"
                  value={projectForm.plannedProgress || 0}
                  onChange={(e) => setProjectForm({ ...projectForm, plannedProgress: Number(e.target.value) })}
                  placeholder="输入计划进度百分比"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-budget">预算</Label>
                <Input
                  id="project-budget"
                  value={projectForm.budget || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, budget: e.target.value })}
                  placeholder="输入工程预算"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-risk">风险等级</Label>
                <Select
                  value={projectForm.riskLevel}
                  onValueChange={(value) => setProjectForm({ ...projectForm, riskLevel: value })}
                >
                  <SelectTrigger id="project-risk">
                    <SelectValue placeholder="选择风险等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-phase">项目阶段</Label>
                <Select
                  value={projectForm.phase}
                  onValueChange={(value) => setProjectForm({ ...projectForm, phase: value })}
                >
                  <SelectTrigger id="project-phase">
                    <SelectValue placeholder="选择项目阶段" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="初始阶段">初始阶段</SelectItem>
                    <SelectItem value="规划阶段">规划阶段</SelectItem>
                    <SelectItem value="采购阶段">采购阶段</SelectItem>
                    <SelectItem value="实施阶段">实施阶段</SelectItem>
                    <SelectItem value="验收阶段">验收阶段</SelectItem>
                    <SelectItem value="收尾阶段">收尾阶段</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="project-description">工程描述</Label>
              <Textarea
                id="project-description"
                value={projectForm.description || ""}
                onChange={(e) => setProjectForm({ ...projectForm, description: e.target.value })}
                placeholder="输入工程描述"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddProjectOpen(false)
                setProjectForm({})
              }}
            >
              取消
            </Button>
            <Button onClick={handleAddProject}>添加工程</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑工程对话框 */}
      <Dialog open={isEditProjectOpen} onOpenChange={setIsEditProjectOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑工程</DialogTitle>
            <DialogDescription>
              {selectedProject && `编辑 "${selectedProject.name}" 的工程信息`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-project-name">工程名称<span className="text-red-500">*</span></Label>
                <Input
                  id="edit-project-name"
                  value={projectForm.name || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, name: e.target.value })}
                  placeholder="输入工程名称"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-type">工程类型<span className="text-red-500">*</span></Label>
                <Select
                  value={projectForm.type}
                  onValueChange={(value) => setProjectForm({ ...projectForm, type: value })}
                >
                  <SelectTrigger id="edit-project-type">
                    <SelectValue placeholder="选择工程类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="采矿工程">采矿工程</SelectItem>
                    <SelectItem value="设备工程">设备工程</SelectItem>
                    <SelectItem value="安全工程">安全工程</SelectItem>
                    <SelectItem value="勘探工程">勘探工程</SelectItem>
                    <SelectItem value="环保工程">环保工程</SelectItem>
                    <SelectItem value="基建工程">基建工程</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-manager">负责人<span className="text-red-500">*</span></Label>
                <Input
                  id="edit-project-manager"
                  value={projectForm.manager || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, manager: e.target.value })}
                  placeholder="输入负责人姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-department">所属部门</Label>
                <Input
                  id="edit-project-department"
                  value={projectForm.department || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, department: e.target.value })}
                  placeholder="输入所属部门"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-start-date">开始日期<span className="text-red-500">*</span></Label>
                <Input
                  id="edit-project-start-date"
                  type="date"
                  value={projectForm.startDate || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, startDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-end-date">计划结束日期<span className="text-red-500">*</span></Label>
                <Input
                  id="edit-project-end-date"
                  type="date"
                  value={projectForm.endDate || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, endDate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-milestone">当前里程碑</Label>
                <Input
                  id="edit-project-milestone"
                  value={projectForm.milestone || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, milestone: e.target.value })}
                  placeholder="输入当前里程碑"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-planned-progress">计划进度 (%)</Label>
                <Input
                  id="edit-project-planned-progress"
                  type="number"
                  min="0"
                  max="100"
                  value={projectForm.plannedProgress || 0}
                  onChange={(e) => setProjectForm({ ...projectForm, plannedProgress: Number(e.target.value) })}
                  placeholder="输入计划进度百分比"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-budget">预算</Label>
                <Input
                  id="edit-project-budget"
                  value={projectForm.budget || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, budget: e.target.value })}
                  placeholder="输入工程预算"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-spent">已用资金</Label>
                <Input
                  id="edit-project-spent"
                  value={projectForm.spent || ""}
                  onChange={(e) => setProjectForm({ ...projectForm, spent: e.target.value })}
                  placeholder="输入已用资金"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-risk">风险等级</Label>
                <Select
                  value={projectForm.riskLevel}
                  onValueChange={(value) => setProjectForm({ ...projectForm, riskLevel: value })}
                >
                  <SelectTrigger id="edit-project-risk">
                    <SelectValue placeholder="选择风险等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="低">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-project-phase">项目阶段</Label>
                <Select
                  value={projectForm.phase}
                  onValueChange={(value) => setProjectForm({ ...projectForm, phase: value })}
                >
                  <SelectTrigger id="edit-project-phase">
                    <SelectValue placeholder="选择项目阶段" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="初始阶段">初始阶段</SelectItem>
                    <SelectItem value="规划阶段">规划阶段</SelectItem>
                    <SelectItem value="采购阶段">采购阶段</SelectItem>
                    <SelectItem value="实施阶段">实施阶段</SelectItem>
                    <SelectItem value="验收阶段">验收阶段</SelectItem>
                    <SelectItem value="收尾阶段">收尾阶段</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-project-description">工程描述</Label>
              <Textarea
                id="edit-project-description"
                value={projectForm.description || ""}
                onChange={(e) => setProjectForm({ ...projectForm, description: e.target.value })}
                placeholder="输入工程描述"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsEditProjectOpen(false)
                setProjectForm({})
              }}
            >
              取消
            </Button>
            <Button onClick={handleEditProject}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出数据对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>导出工程进度数据</DialogTitle>
            <DialogDescription>
              选择导出格式并下载工程进度数据
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>选择导出格式</Label>
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  className="flex-1 justify-start"
                  onClick={() => handleExportData('excel')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Excel格式
                </Button>
                <Button
                  variant="outline"
                  className="flex-1 justify-start"
                  onClick={() => handleExportData('csv')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  CSV格式
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>导出范围</Label>
              <Select defaultValue="all">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有工程</SelectItem>
                  <SelectItem value="filtered">筛选后的工程</SelectItem>
                  <SelectItem value="selected">选中的工程</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 高级筛选对话框 */}
      <Dialog open={isAdvancedFilterOpen} onOpenChange={setIsAdvancedFilterOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>高级筛选</DialogTitle>
            <DialogDescription>
              设置多个条件筛选工程项目
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="filter-department">所属部门</Label>
              <Select
                value={selectedDepartment}
                onValueChange={setSelectedDepartment}
              >
                <SelectTrigger id="filter-department">
                  <SelectValue placeholder="选择部门" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有部门</SelectItem>
                  <SelectItem value="开发部">开发部</SelectItem>
                  <SelectItem value="设备部">设备部</SelectItem>
                  <SelectItem value="安全部">安全部</SelectItem>
                  <SelectItem value="勘探部">勘探部</SelectItem>
                  <SelectItem value="环保部">环保部</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="filter-risk">风险等级</Label>
              <Select defaultValue="all">
                <SelectTrigger id="filter-risk">
                  <SelectValue placeholder="选择风险等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有风险等级</SelectItem>
                  <SelectItem value="高">高</SelectItem>
                  <SelectItem value="中">中</SelectItem>
                  <SelectItem value="低">低</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="filter-phase">项目阶段</Label>
              <Select defaultValue="all">
                <SelectTrigger id="filter-phase">
                  <SelectValue placeholder="选择项目阶段" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有阶段</SelectItem>
                  <SelectItem value="初始阶段">初始阶段</SelectItem>
                  <SelectItem value="规划阶段">规划阶段</SelectItem>
                  <SelectItem value="采购阶段">采购阶段</SelectItem>
                  <SelectItem value="实施阶段">实施阶段</SelectItem>
                  <SelectItem value="验收阶段">验收阶段</SelectItem>
                  <SelectItem value="收尾阶段">收尾阶段</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date-range">时间范围</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="start-date" className="text-xs">开始日期</Label>
                  <Input
                    id="start-date"
                    type="date"
                  />
                </div>
                <div>
                  <Label htmlFor="end-date" className="text-xs">结束日期</Label>
                  <Input
                    id="end-date"
                    type="date"
                  />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="filter-progress">进度范围</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="min-progress" className="text-xs">最小进度 (%)</Label>
                  <Input
                    id="min-progress"
                    type="number"
                    min="0"
                    max="100"
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="max-progress" className="text-xs">最大进度 (%)</Label>
                  <Input
                    id="max-progress"
                    type="number"
                    min="0"
                    max="100"
                    placeholder="100"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAdvancedFilterOpen(false)}>
              重置
            </Button>
            <Button onClick={() => setIsAdvancedFilterOpen(false)}>
              应用筛选
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计分析抽屉 */}
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-[1200vw] max-w-[1000px] overflow-y-auto"> {/* 修改宽度为 1200vw */}
          <SheetHeader>
            <SheetTitle>工程进度统计分析</SheetTitle>
            <SheetDescription>查看工程项目的统计分析数据</SheetDescription>
          </SheetHeader>

          <div className="py-6 space-y-8">
            {/* 完成率展示 */}
            <div className="w-full flex flex-col items-center justify-center">
              <h3 className="text-lg font-medium mb-4">总体完成率</h3>
              <div className="w-[300px] h-[300px]">
                <ReactECharts
                  option={getCompletionRateChartOption()}
                  style={{ height: '100%', width: '100%' }}
                />
              </div>
            </div>

            {/* 基本统计 */}
            <div className="space-y-3">
              <h3 className="text-lg font-medium">基本统计</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                <Card className="bg-gradient-to-br from-blue-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">总工程数</p>
                        <p className="text-2xl font-bold mt-1">{statistics.total}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <BarChart2 className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">正常进度</p>
                        <p className="text-2xl font-bold mt-1">{statistics.normal}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle2 className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-red-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">延期工程</p>
                        <p className="text-2xl font-bold mt-1">{statistics.delayed}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                        <AlertTriangle className="h-6 w-6 text-red-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-blue-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">超前工程</p>
                        <p className="text-2xl font-bold mt-1">{statistics.ahead}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <ArrowUpRight className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-yellow-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">进行中工程</p>
                        <p className="text-2xl font-bold mt-1">{statistics.inProgress}</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                        <Clock className="h-6 w-6 text-yellow-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-50 to-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">平均延期</p>
                        <p className="text-2xl font-bold mt-1">{statistics.averageDelay}天</p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center">
                        <Calendar className="h-6 w-6 text-orange-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* 工程类型分布 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-3">
                <h3 className="text-lg font-medium">按工程类型分布</h3>
                <div className="w-full h-[250px]"> {/* 将高度从300px调整为250px */}
                  <ReactECharts
                    option={getTypeDistributionChartOption()}
                    style={{ height: '100%', width: '100%' }}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-medium">各部门工程进度</h3>
                <div className="border rounded-md p-4 space-y-4">
                  {Object.entries(statistics.projectsByDepartment).map(([dept, count]) => (
                    <div key={dept} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{dept}</span>
                        <span className="text-sm">{count}个工程</span>
                      </div>
                      <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"
                          style={{ width: `${Math.min(100, Math.random() * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 里程碑完成情况 */}
            <div className="space-y-3">
              <h3 className="text-lg font-medium">里程碑完成情况</h3>
              <div className="w-full h-[250px]">
                <ReactECharts
                  option={getMilestoneChartOption()}
                  style={{ height: '100%', width: '100%' }}
                />
              </div>
            </div>

            {/* 工程进度状态分布 */}
            <div className="space-y-3">
              <h3 className="text-lg font-medium">工程进度状态分布</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center gap-4 border rounded-md p-4 bg-gradient-to-r from-green-50 to-white">
                  <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">已完成</p>
                    <p className="text-xl font-bold">
                      {statistics.total > 0
                        ? Math.round((projects.filter(p => p.actualProgress === 100).length / statistics.total) * 100)
                        : 0}%
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 border rounded-md p-4 bg-gradient-to-r from-yellow-50 to-white">
                  <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">进行中</p>
                    <p className="text-xl font-bold">
                      {statistics.total > 0
                        ? Math.round((statistics.inProgress / statistics.total) * 100)
                        : 0}%
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 border rounded-md p-4 bg-gradient-to-r from-red-50 to-white">
                  <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                    <XCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">延期</p>
                    <p className="text-xl font-bold">
                      {statistics.total > 0
                        ? Math.round((statistics.delayed / statistics.total) * 100)
                        : 0}%
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4 border rounded-md p-4 bg-gradient-to-r from-blue-50 to-white">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <ArrowRight className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">超前</p>
                    <p className="text-xl font-bold">
                      {statistics.total > 0
                        ? Math.round((statistics.ahead / statistics.total) * 100)
                        : 0}%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <SheetFooter>
            <Button variant="outline" onClick={() => handleExportData('excel')}>
              <Download className="h-4 w-4 mr-2" />
              导出分析报告
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 甘特图视图抽屉 */}
      <Sheet open={isGanttViewOpen} onOpenChange={setIsGanttViewOpen}>
        <SheetContent className="w-[95vw] max-w-[1200px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>工程甘特图视图</SheetTitle>
            <SheetDescription>查看工程项目的甘特图时间线，可查看工期和进度情况</SheetDescription>
          </SheetHeader>

          <div className="py-6 h-[80vh]">
            <ReactECharts
              option={getGanttChartOption()}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
            />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}

