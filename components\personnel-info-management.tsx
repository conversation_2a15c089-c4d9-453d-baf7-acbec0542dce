"use client"

import { useState, useEffect } from "react"
import { Search, Plus, Edit, Trash2, Download, MoreHorizontal, Users, FileText, Upload, UserPlus, Building2, Briefcase, Mail, Filter, Settings2, List, Grid, Phone, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Personnel, usePersonnelData } from "@/hooks/use-personnel-data"
import { PersonnelDetail } from "@/components/personnel-detail"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Checkbox } from "@/components/ui/checkbox"
import { PersonnelStatistics } from "@/components/personnel-statistics"

// 添加SavedFilter接口
interface SavedFilter {
  id: string;
  name: string;
  conditions: {
    field: string;
    operator: string;
    value: any;
  }[];
}

// 添加列配置接口
interface ColumnConfig {
  id: string;
  label: string;
  visible: boolean;
}

// 扩展Personnel接口
interface ExtendedPersonnel extends Personnel {
  education?: string;
  address?: string;
}

export function PersonnelInfoManagement() {
  const { toast } = useToast()
  const { 
    personnel, 
    loading, 
    addPersonnel, 
    updatePersonnel, 
    deletePersonnel, 
    filterPersonnel 
  } = usePersonnelData()
  
  // 基本状态
  const [isAddPersonnelOpen, setIsAddPersonnelOpen] = useState(false)
  const [isEditPersonnelOpen, setIsEditPersonnelOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedPersonnel, setSelectedPersonnel] = useState<Personnel | null>(null)
  const [formData, setFormData] = useState<Partial<Personnel>>({})
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"list" | "detail">("list")
  const [selectedPersonnelId, setSelectedPersonnelId] = useState<string | null>(null)

  // 新增状态
  const [viewType, setViewType] = useState<"list" | "card">("list")
  const [advancedFilterVisible, setAdvancedFilterVisible] = useState(false)
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])
  const [educationFilter, setEducationFilter] = useState<string[]>([])
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([])
  const [columnSettingsVisible, setColumnSettingsVisible] = useState(false)
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "name", "department", "position", "hireDate", "status", "email", "phone"
  ])

  // 列配置
  const columnConfigs: ColumnConfig[] = [
    { id: 'name', label: '姓名', visible: true },
    { id: 'department', label: '部门', visible: true },
    { id: 'position', label: '职位', visible: true },
    { id: 'hireDate', label: '入职日期', visible: true },
    { id: 'status', label: '状态', visible: true },
    { id: 'email', label: '邮箱', visible: true },
    { id: 'phone', label: '电话', visible: true },
    { id: 'education', label: '学历', visible: true }
  ]

  // 高级筛选处理函数
  const handleAdvancedFilter = () => {
    const filteredData = filterPersonnel({
      search: searchTerm,
      department: departmentFilter,
      status: statusFilter,
      dateRange,
      education: educationFilter[0]
    })
    setAdvancedFilterVisible(false)
    // 这里可以添加筛选后的处理逻辑
  }

  // 保存筛选条件
  const handleSaveFilter = (name: string) => {
    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name,
      conditions: [
        { field: 'dateRange', operator: 'between', value: dateRange },
        { field: 'education', operator: 'equals', value: educationFilter[0] },
        { field: 'department', operator: 'equals', value: departmentFilter },
        { field: 'status', operator: 'equals', value: statusFilter }
      ]
    }
    setSavedFilters([...savedFilters, newFilter])
  }

  // 应用已保存的筛选条件
  const applyFilter = (filter: SavedFilter) => {
    filter.conditions.forEach(condition => {
      switch (condition.field) {
        case 'dateRange':
          setDateRange(condition.value)
          break
        case 'education':
          setEducationFilter([condition.value])
          break
        case 'department':
          setDepartmentFilter(condition.value)
          break
        case 'status':
          setStatusFilter(condition.value)
          break
      }
    })
    handleAdvancedFilter()
  }

  // 处理列设置变更
  const handleColumnChange = (columnId: string, checked: boolean) => {
    if (checked) {
      setVisibleColumns([...visibleColumns, columnId])
    } else {
      setVisibleColumns(visibleColumns.filter(id => id !== columnId))
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    try {
      const text = await file.text()
      const rows = text.split('\n').map(row => row.split(','))
      const headers = rows[0]
      
      const importedPersonnel = rows.slice(1).map(row => ({
        name: row[0],
        department: row[1],
        position: row[2],
        hireDate: row[3],
        status: row[4] || "在职",
        email: row[5],
        phone: row[6],
        address: row[7],
        education: row[8]
      }))
      
      // 这里应该调用API来保存导入的数据
      importedPersonnel.forEach(person => {
        addPersonnel(person)
      })
      
      toast({
        title: "导入成功",
        description: `成功导入 ${importedPersonnel.length} 条记录`,
      })
      
      // 重置文件输入
      event.target.value = ''
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    }
  }

  // 筛选人员数据
  const filteredPersonnel = filterPersonnel({
    search: searchTerm,
    department: departmentFilter,
    status: statusFilter
  })

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id.replace('personnel-', '')]: value
    }))
  }

  // 处理选择框变化
  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 重置表单数据
  const resetFormData = () => {
    setFormData({
      name: "",
      department: "",
      position: "",
      hireDate: new Date().toISOString().split('T')[0],
      status: "在职",
      email: "",
      phone: "",
      address: "",
      education: "本科"
    })
  }

  // 统计数据计算
  const statistics = {
    total: personnel.length,
    departments: new Set(personnel.map(p => p.department)).size,
    active: personnel.filter(p => p.status === "在职").length,
    withEmail: personnel.filter(p => p.email).length,
    education: {
      undergraduate: personnel.filter(p => p.education === "本科").length,
      master: personnel.filter(p => p.education === "硕士").length,
      doctor: personnel.filter(p => p.education === "博士").length
    },
    departmentDistribution: Object.entries(
      personnel.reduce((acc, p) => {
        acc[p.department] = (acc[p.department] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    ).sort((a, b) => b[1] - a[1])
  }

  // 打开编辑对话框
  const openEditDialog = (person: Personnel) => {
    setSelectedPersonnel(person)
    setFormData(person)
    setIsEditPersonnelOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (person: Personnel) => {
    setSelectedPersonnel(person)
    setIsDeleteDialogOpen(true)
  }

  // 查看详情
  const viewPersonnelDetail = (id: string) => {
    setSelectedPersonnelId(id)
    setViewMode("detail")
  }

  // 返回列表
  const backToList = () => {
    setViewMode("list")
    setSelectedPersonnelId(null)
  }

  // 添加人员
  const handleAddPersonnel = () => {
    if (!formData.name || !formData.department || !formData.position) {
      toast({
        title: "请填写必要信息",
        description: "姓名、部门和职位为必填项",
        variant: "destructive"
      })
      return
    }

    addPersonnel({
      name: formData.name || "",
      department: formData.department || "",
      position: formData.position || "",
      hireDate: formData.hireDate || new Date().toISOString().split('T')[0],
      status: formData.status || "在职",
      email: formData.email || "",
      phone: formData.phone || "",
      address: formData.address,
      education: formData.education
    })

    toast({
      title: "添加成功",
      description: `已成功添加人员：${formData.name}`
    })

    resetFormData()
    setIsAddPersonnelOpen(false)
  }

  // 更新人员
  const handleUpdatePersonnel = () => {
    if (!selectedPersonnel) return

    if (!formData.name || !formData.department || !formData.position) {
      toast({
        title: "请填写必要信息",
        description: "姓名、部门和职位为必填项",
        variant: "destructive"
      })
      return
    }

    updatePersonnel(selectedPersonnel.id, formData)

    toast({
      title: "更新成功",
      description: `已成功更新人员：${formData.name}`
    })

    setIsEditPersonnelOpen(false)
  }

  // 删除人员
  const handleDeletePersonnel = () => {
    if (!selectedPersonnel) return

    deletePersonnel(selectedPersonnel.id)

    toast({
      title: "删除成功",
      description: `已成功删除人员：${selectedPersonnel.name}`
    })

    setIsDeleteDialogOpen(false)
  }

  // 导出数据
  const handleExportData = () => {
    const dataStr = JSON.stringify(personnel, null, 2)
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`
    
    const exportFileDefaultName = `personnel-data-${new Date().toISOString().slice(0, 10)}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  // 初始化表单数据
  useEffect(() => {
    resetFormData()
  }, [])

  // 如果正在加载数据，显示加载状态
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>加载中...</p>
      </div>
    )
  }

  // 如果是详情视图，显示人员详情
  if (viewMode === "detail" && selectedPersonnelId) {
    return <PersonnelDetail id={selectedPersonnelId} onBack={backToList} />
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">人员信息管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExportData} className="hover:bg-blue-50 hover:text-blue-600">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".csv"
              onChange={handleImport}
              className="hidden"
              id="import-file"
            />
            <Button variant="outline" size="sm" onClick={() => document.getElementById('import-file')?.click()} className="hover:bg-green-50 hover:text-green-600">
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-blue-100 p-3">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">总人数</p>
                <p className="text-2xl font-bold text-blue-600">{statistics.total}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">本科</span>
                <span className="font-medium">{statistics.education.undergraduate}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">硕士</span>
                <span className="font-medium">{statistics.education.master}</span>
              </div>
              <div className="flex justify-between text-sm mt-1">
                <span className="text-muted-foreground">博士</span>
                <span className="font-medium">{statistics.education.doctor}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-green-100 p-3">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">部门数量</p>
                <p className="text-2xl font-bold text-green-600">{statistics.departments}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              {statistics.departmentDistribution.slice(0, 3).map(([dept, count]) => (
                <div key={dept} className="flex justify-between text-sm mt-1">
                  <span className="text-muted-foreground truncate">{dept}</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-amber-100 p-3">
                <Briefcase className="h-6 w-6 text-amber-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">在职人员</p>
                <p className="text-2xl font-bold text-amber-600">{statistics.active}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">在职率</span>
                <span className="font-medium">
                  {((statistics.active / statistics.total) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="rounded-full bg-purple-100 p-3">
                <Mail className="h-6 w-6 text-purple-600" />
              </div>
              <div className="space-y-1 text-right">
                <p className="text-sm text-muted-foreground">已关联邮箱</p>
                <p className="text-2xl font-bold text-purple-600">{statistics.withEmail}</p>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">邮箱覆盖率</span>
                <span className="font-medium">
                  {((statistics.withEmail / statistics.total) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {viewMode === "list" ? (
        <Tabs defaultValue="list" className="space-y-4">
          <TabsList>
            <TabsTrigger value="list">列表视图</TabsTrigger>
            <TabsTrigger value="statistics">数据统计</TabsTrigger>
          </TabsList>
          
          <TabsContent value="list">
            <Card className="border-none shadow-md">
              <CardHeader>
                <CardTitle>人员列表</CardTitle>
                <CardDescription>管理公司所有人员信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input 
                          type="search" 
                          placeholder="搜索人员..." 
                          className="pl-8 w-[250px] border-slate-200 focus-visible:ring-blue-500"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                      <Select 
                        value={departmentFilter}
                        onValueChange={setDepartmentFilter}
                      >
                        <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                          <SelectValue placeholder="部门" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">所有部门</SelectItem>
                          <SelectItem value="安全管理部">安全管理部</SelectItem>
                          <SelectItem value="工程管理部">工程管理部</SelectItem>
                          <SelectItem value="人事管理部">人事管理部</SelectItem>
                          <SelectItem value="财务管理部">财务管理部</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select 
                        value={statusFilter}
                        onValueChange={setStatusFilter}
                      >
                        <SelectTrigger className="w-[150px] border-slate-200 focus:ring-blue-500">
                          <SelectValue placeholder="状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">所有状态</SelectItem>
                          <SelectItem value="active">在职</SelectItem>
                          <SelectItem value="inactive">离职</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setAdvancedFilterVisible(true)}
                        className="flex items-center gap-1"
                      >
                        <Filter className="h-4 w-4" />
                        高级筛选
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setColumnSettingsVisible(true)}
                        className="flex items-center gap-1"
                      >
                        <Settings2 className="h-4 w-4" />
                        列设置
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <ToggleGroup type="single" value={viewType} onValueChange={(value) => value && setViewType(value as "list" | "card")}>
                        <ToggleGroupItem value="list" aria-label="列表视图">
                          <List className="h-4 w-4" />
                        </ToggleGroupItem>
                        <ToggleGroupItem value="card" aria-label="卡片视图">
                          <Grid className="h-4 w-4" />
                        </ToggleGroupItem>
                      </ToggleGroup>
                      <Button onClick={() => setIsAddPersonnelOpen(true)} className="bg-blue-500 hover:bg-blue-600">
                        <UserPlus className="h-4 w-4 mr-2" />
                        添加人员
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 根据视图类型显示不同的内容 */}
                {viewType === "list" ? (
                  <div className="rounded-md border border-slate-200">
                    <Table>
                      <TableHeader className="bg-slate-50">
                        <TableRow>
                          {columnConfigs
                            .filter(col => visibleColumns.includes(col.id))
                            .map(col => (
                              <TableHead key={col.id} className="font-semibold">
                                {col.label}
                              </TableHead>
                            ))}
                          <TableHead className="text-right">操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredPersonnel.map((person) => (
                          <TableRow key={person.id} className="hover:bg-slate-50">
                            {columnConfigs
                              .filter(col => visibleColumns.includes(col.id))
                              .map(col => (
                                <TableCell key={col.id}>
                                  {col.id === 'name' ? (
                                    <div className="flex items-center">
                                      <Users className="h-4 w-4 mr-2 text-slate-400" />
                                      <span className="font-medium">{person[col.id]}</span>
                                    </div>
                                  ) : col.id === 'status' ? (
                                    <Badge variant={person.status === "在职" ? "default" : "secondary"} className={
                                      person.status === "在职" ? "bg-green-100 text-green-700" : "bg-slate-100 text-slate-700"
                                    }>
                                      {person.status}
                                    </Badge>
                                  ) : (
                                    person[col.id]
                                  )}
                                </TableCell>
                              ))}
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => viewPersonnelDetail(person.id)}
                                  className="hover:bg-blue-50 hover:text-blue-600"
                                >
                                  <FileText className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openEditDialog(person)}
                                  className="hover:bg-amber-50 hover:text-amber-600"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openDeleteDialog(person)}
                                  className="hover:bg-red-50 hover:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredPersonnel.map((person) => (
                      <Card key={person.id} className="hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center gap-2">
                              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <Users className="h-5 w-5 text-blue-600" />
                              </div>
                              <div>
                                <CardTitle className="text-lg">{person.name}</CardTitle>
                                <CardDescription>{person.position}</CardDescription>
                              </div>
                            </div>
                            <Badge variant={person.status === "在职" ? "default" : "secondary"} className={
                              person.status === "在职" ? "bg-green-100 text-green-700" : "bg-slate-100 text-slate-700"
                            }>
                              {person.status}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                              <Building2 className="h-4 w-4 text-slate-400" />
                              <span>{person.department}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="h-4 w-4 text-slate-400" />
                              <span>{person.email || '未设置'}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Phone className="h-4 w-4 text-slate-400" />
                              <span>{person.phone || '未设置'}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="h-4 w-4 text-slate-400" />
                              <span>入职时间：{person.hireDate}</span>
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="flex justify-end gap-2 pt-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewPersonnelDetail(person.id)}
                            className="hover:bg-blue-50 hover:text-blue-600"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(person)}
                            className="hover:bg-amber-50 hover:text-amber-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDeleteDialog(person)}
                            className="hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between border-t bg-slate-50 px-6 py-3">
                <div className="text-sm text-slate-600">
                  共 {filteredPersonnel.length} 条记录
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled className="border-slate-200 hover:bg-slate-100">
                    上一页
                  </Button>
                  <Button variant="outline" size="sm" className="px-3 border-slate-200 bg-white hover:bg-slate-100">
                    1
                  </Button>
                  <Button variant="outline" size="sm" disabled className="border-slate-200 hover:bg-slate-100">
                    下一页
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="statistics">
            <Card className="border-none shadow-md">
              <CardHeader>
                <CardTitle>数据统计</CardTitle>
                <CardDescription>人员数据可视化分析</CardDescription>
              </CardHeader>
              <CardContent>
                <PersonnelStatistics personnel={personnel} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <PersonnelDetail 
          id={selectedPersonnelId!} 
          onBack={() => {
            setSelectedPersonnelId(null)
            setViewMode("list")
          }} 
        />
      )}

      {/* 添加人员对话框 */}
      <Dialog open={isAddPersonnelOpen} onOpenChange={(open) => {
        setIsAddPersonnelOpen(open)
        if (!open) resetFormData()
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              <span>添加人员</span>
            </DialogTitle>
            <DialogDescription>
              添加新的公司人员信息，带 * 为必填项
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  姓名 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder="请输入姓名"
                  value={formData.name || ''}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="border-slate-200"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">
                  部门 <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) => setFormData({ ...formData, department: value })}
                >
                  <SelectTrigger className="border-slate-200">
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="安全管理部">安全管理部</SelectItem>
                    <SelectItem value="工程管理部">工程管理部</SelectItem>
                    <SelectItem value="人事管理部">人事管理部</SelectItem>
                    <SelectItem value="财务管理部">财务管理部</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">
                  职位 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="position"
                  placeholder="请输入职位"
                  value={formData.position || ''}
                  onChange={(e) => setFormData({ ...formData, position: e.target.value })}
                  className="border-slate-200"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="hireDate">入职日期</Label>
                <Input
                  id="hireDate"
                  type="date"
                  value={formData.hireDate || new Date().toISOString().split('T')[0]}
                  onChange={(e) => setFormData({ ...formData, hireDate: e.target.value })}
                  className="border-slate-200"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入邮箱"
                  value={formData.email || ''}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="border-slate-200"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">电话</Label>
                <Input
                  id="phone"
                  placeholder="请输入电话"
                  value={formData.phone || ''}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  className="border-slate-200"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="education">学历</Label>
                <Select
                  value={formData.education}
                  onValueChange={(value) => setFormData({ ...formData, education: value })}
                >
                  <SelectTrigger className="border-slate-200">
                    <SelectValue placeholder="选择学历" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="本科">本科</SelectItem>
                    <SelectItem value="硕士">硕士</SelectItem>
                    <SelectItem value="博士">博士</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger className="border-slate-200">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="在职">在职</SelectItem>
                    <SelectItem value="离职">离职</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">地址</Label>
              <Input
                id="address"
                placeholder="请输入地址"
                value={formData.address || ''}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                className="border-slate-200"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddPersonnelOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddPersonnel} className="bg-blue-500 hover:bg-blue-600">
              确认添加
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditPersonnelOpen} onOpenChange={setIsEditPersonnelOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑人员</DialogTitle>
            <DialogDescription>修改人员信息</DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="contact">联系方式</TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="personnel-name">姓名</Label>
                  <Input 
                    id="personnel-name" 
                    placeholder="请输入姓名" 
                    value={formData.name || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-department">部门</Label>
                  <Select 
                    value={formData.department}
                    onValueChange={(value) => handleSelectChange('department', value)}
                  >
                    <SelectTrigger id="personnel-department">
                      <SelectValue placeholder="选择部门" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全管理部">安全管理部</SelectItem>
                      <SelectItem value="工程管理部">工程管理部</SelectItem>
                      <SelectItem value="人事管理部">人事管理部</SelectItem>
                      <SelectItem value="财务管理部">财务管理部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-position">职位</Label>
                  <Input 
                    id="personnel-position" 
                    placeholder="请输入职位" 
                    value={formData.position || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-hireDate">入职日期</Label>
                  <Input 
                    id="personnel-hireDate" 
                    type="date" 
                    value={formData.hireDate || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-status">状态</Label>
                  <Select 
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger id="personnel-status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="在职">在职</SelectItem>
                      <SelectItem value="离职">离职</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-education">学历</Label>
                  <Select 
                    value={formData.education}
                    onValueChange={(value) => handleSelectChange('education', value)}
                  >
                    <SelectTrigger id="personnel-education">
                      <SelectValue placeholder="选择学历" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="本科">本科</SelectItem>
                      <SelectItem value="硕士">硕士</SelectItem>
                      <SelectItem value="博士">博士</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="contact" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="personnel-email">邮箱</Label>
                  <Input 
                    id="personnel-email" 
                    type="email" 
                    placeholder="请输入邮箱" 
                    value={formData.email || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-phone">电话</Label>
                  <Input 
                    id="personnel-phone" 
                    placeholder="请输入电话" 
                    value={formData.phone || ''}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="personnel-address">地址</Label>
                  <Input 
                    id="personnel-address" 
                    placeholder="请输入地址" 
                    value={formData.address || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsEditPersonnelOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdatePersonnel}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除 {selectedPersonnel?.name} 的信息吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeletePersonnel}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 高级筛选对话框 */}
      <Dialog open={advancedFilterVisible} onOpenChange={setAdvancedFilterVisible}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>高级筛选</DialogTitle>
            <DialogDescription>
              设置多个条件进行精确筛选
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>入职时间范围</Label>
                  <div className="flex gap-2">
                    <Input
                      type="date"
                      value={dateRange[0]?.toISOString().split('T')[0] || ''}
                      onChange={(e) => setDateRange([new Date(e.target.value), dateRange[1]])}
                    />
                    <Input
                      type="date"
                      value={dateRange[1]?.toISOString().split('T')[0] || ''}
                      onChange={(e) => setDateRange([dateRange[0], new Date(e.target.value)])}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>学历</Label>
                  <Select
                    value={educationFilter[0]}
                    onValueChange={(value) => setEducationFilter([value])}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择学历" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="本科">本科</SelectItem>
                      <SelectItem value="硕士">硕士</SelectItem>
                      <SelectItem value="博士">博士</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label>已保存的筛选条件</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择已保存的筛选条件" />
                </SelectTrigger>
                <SelectContent>
                  {savedFilters.map(filter => (
                    <SelectItem key={filter.id} value={filter.id}>
                      {filter.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setAdvancedFilterVisible(false)}>
              取消
            </Button>
            <Button onClick={handleAdvancedFilter}>
              应用筛选
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 列设置对话框 */}
      <Dialog open={columnSettingsVisible} onOpenChange={setColumnSettingsVisible}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>列设置</DialogTitle>
            <DialogDescription>
              自定义显示的列
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {columnConfigs.map(column => (
              <div key={column.id} className="flex items-center space-x-2">
                <Checkbox
                  id={column.id}
                  checked={visibleColumns.includes(column.id)}
                  onCheckedChange={(checked) => {
                    handleColumnChange(column.id, checked as boolean)
                  }}
                />
                <Label htmlFor={column.id}>{column.label}</Label>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setColumnSettingsVisible(false)}>
              取消
            </Button>
            <Button onClick={() => setColumnSettingsVisible(false)}>
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

