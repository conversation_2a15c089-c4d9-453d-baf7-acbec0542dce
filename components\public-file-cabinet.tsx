"use client"

import { useState, useEffect } from "react"
import {
  Search,
  FolderPlus,
  FilePlus,
  Folder,
  File,
  FileText,
  FileImage,
  FileArchive,
  Download,
  Trash2,
  MoreHorizontal,
  Share2,
  Edit,
  Eye,
  ChevronRight,
  Filter,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select as AntSelect, Space, Tag, Modal, Form, Input as AntInput, message, Tooltip, Popconfirm, Row, Col, Statistic, Upload, Progress } from 'antd'
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge as AntBadge } from 'antd'
import { toast } from "sonner"
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  CloudUploadOutlined,
  CloudDownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  ExportOutlined,
  FileOutlined,
  FolderOutlined,
  CloudOutlined,
} from '@ant-design/icons'
import type { UploadProps } from 'antd'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx-js-style'

const { Option } = AntSelect
const { TextArea } = AntInput

interface FileItem {
  id: string
  name: string
  type: string
  size: string
  owner: string
  lastModified: string
  shared: boolean
  downloads: number
  status: string
  description?: string
  tags?: string[]
}

interface FolderItem {
  id: string
  name: string
  itemCount: number
  owner: string
  lastModified: string
}

export function PublicFileCabinet() {
  const [isAddFolderOpen, setIsAddFolderOpen] = useState(false)
  const [isUploadFileOpen, setIsUploadFileOpen] = useState(false)
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [currentPath, setCurrentPath] = useState<string[]>(["公共文件柜"])
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [searchText, setSearchText] = useState("")
  const [sortBy, setSortBy] = useState("name")
  const [currentTab, setCurrentTab] = useState("all")
  const [files, setFiles] = useState<FileItem[]>([
    {
      id: "1",
      name: "安全管理制度.docx",
      type: "document",
      size: "2.5 MB",
      owner: "张三",
      lastModified: "2023-11-20 14:30",
      shared: true,
      downloads: 0,
      status: "正常",
    },
    {
      id: "2",
      name: "员工手册.pdf",
      type: "pdf",
      size: "4.8 MB",
      owner: "李四",
      lastModified: "2023-11-15 09:45",
      shared: true,
      downloads: 0,
      status: "正常",
    },
    {
      id: "3",
      name: "公司组织架构图.png",
      type: "image",
      size: "1.2 MB",
      owner: "王五",
      lastModified: "2023-11-10 16:20",
      shared: true,
      downloads: 0,
      status: "正常",
    },
    {
      id: "4",
      name: "设备维护手册.pdf",
      type: "pdf",
      size: "8.7 MB",
      owner: "赵六",
      lastModified: "2023-11-05 11:10",
      shared: false,
      downloads: 0,
      status: "正常",
    },
    {
      id: "5",
      name: "财务报表模板.xlsx",
      type: "spreadsheet",
      size: "1.8 MB",
      owner: "张三",
      lastModified: "2023-10-28 15:40",
      shared: true,
      downloads: 0,
      status: "正常",
    },
  ])
  const [folders, setFolders] = useState<FolderItem[]>([
    {
      id: "f1",
      name: "安全管理文档",
      itemCount: 12,
      owner: "张三",
      lastModified: "2023-11-22 10:15",
    },
    {
      id: "f2",
      name: "人事管理文件",
      itemCount: 8,
      owner: "李四",
      lastModified: "2023-11-18 14:30",
    },
    {
      id: "f3",
      name: "工程项目资料",
      itemCount: 15,
      owner: "王五",
      lastModified: "2023-11-12 09:20",
    },
  ])
  const [renamingItem, setRenamingItem] = useState<{ id: string; name: string; type: 'file' | 'folder' } | null>(null)
  const [newFolderName, setNewFolderName] = useState("")
  const [newFileName, setNewFileName] = useState("")
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [editingFile, setEditingFile] = useState<FileItem | null>(null)
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [isViewModalVisible, setIsViewModalVisible] = useState(false)
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)

  const toggleSelectFile = (fileId: string) => {
    setSelectedFiles((prev) => (prev.includes(fileId) ? prev.filter((id) => id !== fileId) : [...prev, fileId]))
  }

  const toggleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([])
    } else {
      setSelectedFiles(files.map((file) => file.id))
    }
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case "document":
        return <FileText className="h-5 w-5 text-blue-500" />
      case "pdf":
        return <FileText className="h-5 w-5 text-red-500" />
      case "image":
        return <FileImage className="h-5 w-5 text-green-500" />
      case "spreadsheet":
        return <FileText className="h-5 w-5 text-emerald-500" />
      case "archive":
        return <FileArchive className="h-5 w-5 text-amber-500" />
      default:
        return <File className="h-5 w-5 text-gray-500" />
    }
  }

  const handleAddFolder = () => {
    if (!newFolderName.trim()) {
      toast.error("请输入文件夹名称")
      return
    }

    const newFolder: FolderItem = {
      id: `f${Date.now()}`,
      name: newFolderName,
      itemCount: 0,
      owner: "当前用户",
      lastModified: new Date().toLocaleString(),
    }

    setFolders([...folders, newFolder])
    setIsAddFolderOpen(false)
    setNewFolderName("")
    toast.success("文件夹创建成功")
  }

  const handleUploadFile = (file: File) => {
    const newFile: FileItem = {
      id: `file${Date.now()}`,
      name: file.name,
      type: file.type.split("/")[1] || "document",
      size: `${(file.size / (1024 * 1024)).toFixed(2)} MB`,
      owner: "当前用户",
      lastModified: new Date().toLocaleString(),
      shared: false,
      downloads: 0,
      status: "正常",
    }

    setFiles([...files, newFile])
    setIsUploadFileOpen(false)
    toast.success("文件上传成功")
  }

  const handleDelete = (id: string, type: 'file' | 'folder') => {
    if (type === 'file') {
      setFiles(files.filter(file => file.id !== id))
      toast.success("文件删除成功")
    } else {
      setFolders(folders.filter(folder => folder.id !== id))
      toast.success("文件夹删除成功")
    }
    setSelectedFiles(selectedFiles.filter(fileId => fileId !== id))
  }

  const handleBatchDelete = () => {
    setFiles(files.filter(file => !selectedFiles.includes(file.id)))
    setSelectedFiles([])
    toast.success("批量删除成功")
  }

  const handleRename = () => {
    if (!renamingItem) return
    
    if (renamingItem.type === 'file') {
      setFiles(files.map(file => 
        file.id === renamingItem.id 
          ? { ...file, name: newFileName }
          : file
      ))
    } else {
      setFolders(folders.map(folder =>
        folder.id === renamingItem.id
          ? { ...folder, name: newFileName }
          : folder
      ))
    }
    
    setIsRenameDialogOpen(false)
    setRenamingItem(null)
    setNewFileName("")
    toast.success("重命名成功")
  }

  const handleShare = (id: string) => {
    setFiles(files.map(file =>
      file.id === id
        ? { ...file, shared: !file.shared }
        : file
    ))
    toast.success("分享状态已更新")
  }

  const handleDownload = (fileIds: string[]) => {
    fileIds.forEach(id => {
      const file = files.find(f => f.id === id)
      if (file) {
        // 这里添加实际的文件下载逻辑
        toast.success(`开始下载: ${file.name}`)
      }
    })
  }

  const filterItems = () => {
    let filteredFiles = [...files]
    let filteredFolders = [...folders]

    if (searchText) {
      filteredFiles = filteredFiles.filter(file =>
        file.name.toLowerCase().includes(searchText.toLowerCase())
      )
      filteredFolders = filteredFolders.filter(folder =>
        folder.name.toLowerCase().includes(searchText.toLowerCase())
      )
    }

    if (currentTab !== 'all') {
      if (currentTab === 'documents') {
        filteredFiles = filteredFiles.filter(file =>
          ['document', 'pdf'].includes(file.type)
        )
      } else if (currentTab === 'images') {
        filteredFiles = filteredFiles.filter(file =>
          file.type === 'image'
        )
      } else if (currentTab === 'shared') {
        filteredFiles = filteredFiles.filter(file => file.shared)
      }
    }

    const sortItems = (items: any[]) => {
      return items.sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.name.localeCompare(b.name)
          case 'date':
            return new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
          case 'size':
            return parseFloat(b.size) - parseFloat(a.size)
          default:
            return 0
        }
      })
    }

    return {
      files: sortItems(filteredFiles),
      folders: sortItems(filteredFolders)
    }
  }

  const { files: displayFiles, folders: displayFolders } = filterItems()

  const handleAdd = () => {
    setEditingFile(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEdit = (file: FileItem) => {
    setEditingFile(file)
    form.setFieldsValue(file)
    setIsModalVisible(true)
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const formattedValues = {
        ...values,
        uploadTime: values.uploadTime || new Date().toLocaleString(),
        downloads: values.downloads || 0,
      }

      if (editingFile) {
        // 编辑现有文件
        const updatedFiles = files.map(file =>
          file.id === editingFile.id
            ? { ...file, ...formattedValues }
            : file
        )
        setFiles(updatedFiles)
        toast.success('文件信息已更新')
      } else {
        // 添加新文件
        const newFile: FileItem = {
          id: Date.now().toString(),
          ...formattedValues,
          downloads: 0,
          uploadTime: new Date().toLocaleString(),
          shared: false,
          status: "正常",
        }
        setFiles([...files, newFile])
        toast.success('文件已添加')
      }
      setIsModalVisible(false)
      setEditingFile(null)
      form.resetFields()
    })
  }

  // 导入Excel
  const handleImport = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        const newFiles = jsonData.map((item: any) => ({
          id: `file${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: item['文件名'] || '',
          type: item['类型'] || 'document',
          size: item['大小'] || '0 KB',
          owner: item['所有者'] || '当前用户',
          lastModified: item['上传时间'] || new Date().toLocaleString(),
          shared: false,
          downloads: 0,
          status: '正常',
          description: item['描述'] || '',
          tags: item['标签'] ? item['标签'].split(',') : [],
        }))

        setFiles([...files, ...newFiles])
        message.success(`成功导入 ${newFiles.length} 个文件`)
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败，请检查文件格式')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 导出Excel
  const handleExport = () => {
    try {
      const exportData = files.map(file => ({
        '文件名': file.name,
        '类型': file.type,
        '大小': file.size,
        '所有者': file.owner,
        '上传时间': file.lastModified,
        '下载次数': file.downloads,
        '状态': file.status,
        '是否共享': file.shared ? '是' : '否',
        '描述': file.description || '',
        '标签': file.tags ? file.tags.join(',') : '',
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 文件名
        { wch: 10 }, // 类型
        { wch: 10 }, // 大小
        { wch: 15 }, // 所有者
        { wch: 20 }, // 上传时间
        { wch: 10 }, // 下载次数
        { wch: 10 }, // 状态
        { wch: 10 }, // 是否共享
        { wch: 40 }, // 描述
        { wch: 20 }, // 标签
      ]
      ws['!cols'] = colWidths

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      }

      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1"
        if (!ws[address]) continue
        ws[address].s = headerStyle
      }

      XLSX.utils.book_append_sheet(wb, ws, '文件列表')
      XLSX.writeFile(wb, `文件列表_${dayjs().format('YYYY-MM-DD')}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 批量操作
  const handleBatchUpdateStatus = (status: string) => {
    setFiles(files.map(file => 
      selectedRowKeys.includes(file.id) ? { ...file, status } : file
    ))
    setSelectedRowKeys([])
    toast.success('批量更新状态成功')
  }

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/upload',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status === 'done') {
        toast.success(`${info.file.name} 上传成功`)
      } else if (info.file.status === 'error') {
        toast.error(`${info.file.name} 上传失败`)
      }
    },
  }

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys as string[])
    },
  }

  // 在 PublicFileCabinet 组件的开头添加统计数据
  const statistics = {
    total: files.length,
    active: files.filter(f => f.status === '正常').length,
    disabled: files.filter(f => f.status === '禁用').length,
    shared: files.filter(f => f.shared).length,
    byType: {
      document: files.filter(f => f.type === 'document').length,
      pdf: files.filter(f => f.type === 'pdf').length,
      image: files.filter(f => f.type === 'image').length,
      spreadsheet: files.filter(f => f.type === 'spreadsheet').length,
    },
    totalDownloads: files.reduce((acc, file) => acc + file.downloads, 0),
  }

  const handleViewFile = (file: FileItem) => {
    setSelectedFile(file)
    setIsViewModalVisible(true)
  }

  const handleStatusChange = (fileId: string, status: string) => {
    setFiles(files.map(file =>
      file.id === fileId
        ? { ...file, status }
        : file
    ))
    message.success(`文件状态已更新为: ${status}`)
  }

  const handleBatchStatusChange = (status: string) => {
    setFiles(files.map(file =>
      selectedRowKeys.includes(file.id)
        ? { ...file, status }
        : file
    ))
    setSelectedRowKeys([])
    message.success(`已批量更新 ${selectedRowKeys.length} 个文件的状态`)
  }

  return (
    <div className="space-y-6">
      {/* 美化后的统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="flex flex-col items-center p-6">
            <FileText className="h-10 w-10 text-blue-500 mb-3" />
            <h3 className="text-2xl font-bold text-blue-500">{statistics.byType.document + statistics.byType.pdf}</h3>
            <p className="text-sm text-muted-foreground mt-1">文档文件</p>
            <div className="mt-2 text-xs text-muted-foreground">
              文档: {statistics.byType.document} | PDF: {statistics.byType.pdf}
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="flex flex-col items-center p-6">
            <FileImage className="h-10 w-10 text-green-500 mb-3" />
            <h3 className="text-2xl font-bold text-green-500">{statistics.byType.image}</h3>
            <p className="text-sm text-muted-foreground mt-1">图片文件</p>
            <div className="mt-2 text-xs text-muted-foreground">
              总下载: {statistics.totalDownloads} 次
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="flex flex-col items-center p-6">
            <FileArchive className="h-10 w-10 text-amber-500 mb-3" />
            <h3 className="text-2xl font-bold text-amber-500">{statistics.byType.spreadsheet}</h3>
            <p className="text-sm text-muted-foreground mt-1">表格文件</p>
            <div className="mt-2 text-xs text-muted-foreground">
              共享: {statistics.shared} 个
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="flex flex-col items-center p-6">
            <File className="h-10 w-10 text-gray-500 mb-3" />
            <h3 className="text-2xl font-bold text-gray-500">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground mt-1">总文件数</p>
            <div className="mt-2 text-xs text-muted-foreground">
              活跃: {statistics.active} | 禁用: {statistics.disabled}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">公共文件柜</h2>
          <div className="flex items-center text-sm text-muted-foreground mt-1">
            {currentPath.map((path, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
                <span
                  className={`cursor-pointer hover:text-primary ${
                    index === currentPath.length - 1 ? "font-medium text-foreground" : ""
                  }`}
                >
                  {path}
                </span>
              </div>
            ))}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Dialog open={isUploadFileOpen} onOpenChange={setIsUploadFileOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <FilePlus className="h-4 w-4 mr-2" />
                上传文件
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>上传文件</DialogTitle>
                <DialogDescription>将文件上传到当前文件夹</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div
                  className="border-2 border-dashed rounded-lg p-6 text-center"
                  onDragOver={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                  }}
                  onDrop={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    const files = Array.from(e.dataTransfer.files)
                    files.forEach(file => handleUploadFile(file))
                  }}
                >
                  <div className="flex flex-col items-center">
                    <FilePlus className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm font-medium mb-1">拖放文件到此处或点击上传</p>
                    <p className="text-xs text-muted-foreground mb-4">支持的文件格式: PDF, DOCX, XLSX, JPG, PNG</p>
                    <input
                      type="file"
                      id="file-upload"
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        const files = Array.from(e.target.files || [])
                        files.forEach(file => handleUploadFile(file))
                      }}
                    />
                    <Button size="sm" onClick={() => document.getElementById('file-upload')?.click()}>
                      选择文件
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>文件权限</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="public-file" defaultChecked />
                    <Label htmlFor="public-file">公开文件（所有用户可访问）</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsUploadFileOpen(false)}>
                  取消
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddFolderOpen} onOpenChange={setIsAddFolderOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <FolderPlus className="h-4 w-4 mr-2" />
                新建文件夹
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>新建文件夹</DialogTitle>
                <DialogDescription>在当前位置创建新文件夹</DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="folder-name">文件夹名称</Label>
                  <Input id="folder-name" placeholder="请输入文件夹名称" />
                </div>
                <div className="space-y-2">
                  <Label>文件夹权限</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="public-folder" defaultChecked />
                    <Label htmlFor="public-folder">公开文件夹（所有用户可访问）</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddFolderOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddFolder}>创建</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Upload
            accept=".xlsx,.xls"
            showUploadList={false}
            beforeUpload={(file) => {
              handleImport(file)
              return false
            }}
          >
            <Button icon={<CloudUploadOutlined />}>导入</Button>
          </Upload>
          <Button icon={<CloudDownloadOutlined />} onClick={handleExport}>
            导出
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>文件列表</CardTitle>
            <Tabs defaultValue="all" className="w-[400px]">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="documents">文档</TabsTrigger>
                <TabsTrigger value="images">图片</TabsTrigger>
                <TabsTrigger value="shared">共享</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索文件..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <AntSelect value={sortBy} onChange={setSortBy} style={{ width: 150 }}>
                  <Option value="name">按名称</Option>
                  <Option value="date">按日期</Option>
                  <Option value="size">按大小</Option>
                  <Option value="type">按类型</Option>
                </AntSelect>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={selectedFiles.length === 0}
                  onClick={() => handleDownload(selectedFiles)}
                >
                  <Download className="h-4 w-4 mr-2" />
                  下载
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={selectedFiles.length === 0}
                  onClick={() => {
                    const file = files.find(f => f.id === selectedFiles[0])
                    if (file) handleShare(file.id)
                  }}
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  共享
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={selectedFiles.length === 0}
                  onClick={handleBatchDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedFiles.length === files.length && files.length > 0}
                        onCheckedChange={toggleSelectAll}
                      />
                    </TableHead>
                    <TableHead>名称</TableHead>
                    <TableHead>大小</TableHead>
                    <TableHead>所有者</TableHead>
                    <TableHead>修改时间</TableHead>
                    <TableHead>共享状态</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayFolders.map((folder) => (
                    <TableRow key={folder.id} className="bg-muted/30">
                      <TableCell>
                        <Checkbox checked={false} disabled />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Folder className="h-5 w-5 text-amber-500 mr-2" />
                          <span className="font-medium">{folder.name}</span>
                          <AntBadge variant="outline" className="ml-2 text-xs">
                            {folder.itemCount} 项
                          </AntBadge>
                        </div>
                      </TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>{folder.owner}</TableCell>
                      <TableCell>{folder.lastModified}</TableCell>
                      <TableCell>
                        <AntBadge variant="outline">文件夹</AntBadge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                重命名
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Share2 className="h-4 w-4 mr-2" />
                                共享
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {displayFiles.map((file) => (
                    <TableRow key={file.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedFiles.includes(file.id)}
                          onCheckedChange={() => toggleSelectFile(file.id)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getFileIcon(file.type)}
                          <span className="ml-2">{file.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{file.size}</TableCell>
                      <TableCell>{file.owner}</TableCell>
                      <TableCell>{file.lastModified}</TableCell>
                      <TableCell>
                        <AntBadge variant={file.shared ? "default" : "outline"}>{file.shared ? "已共享" : "私有"}</AntBadge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleViewFile(file)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDownload([file.id])}>
                            <Download className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setRenamingItem({ id: file.id, name: file.name, type: 'file' })
                                setNewFileName(file.name)
                                setIsRenameDialogOpen(true)
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                重命名
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleShare(file.id)}>
                                <Share2 className="h-4 w-4 mr-2" />
                                {file.shared ? '取消共享' : '共享'}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDelete(file.id, 'file')}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(file.id, file.status === '正常' ? '禁用' : '正常')}>
                                {file.status === '正常' ? (
                                  <>
                                    <ExclamationCircleOutlined className="h-4 w-4 mr-2" />
                                    禁用
                                  </>
                                ) : (
                                  <>
                                    <CheckCircleOutlined className="h-4 w-4 mr-2" />
                                    启用
                                  </>
                                )}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {folders.length + files.length} 项</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重命名</DialogTitle>
            <DialogDescription>请输入新的名称</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>名称</Label>
              <Input
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                placeholder="请输入新名称"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRenameDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleRename}>确定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Modal
        title={editingFile ? '编辑文件' : '上传文件'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="文件名"
            rules={[{ required: true, message: '请输入文件名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="文件类型"
            rules={[{ required: true, message: '请选择文件类型' }]}
          >
            <AntSelect>
              <Option value="document">文档</Option>
              <Option value="pdf">PDF</Option>
              <Option value="image">图片</Option>
              <Option value="spreadsheet">表格</Option>
              <Option value="archive">压缩文件</Option>
            </AntSelect>
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <AntSelect>
              <Option value="正常">正常</Option>
              <Option value="禁用">禁用</Option>
            </AntSelect>
          </Form.Item>
          <Form.Item
            name="description"
            label="文件描述"
            rules={[{ required: true, message: '请输入文件描述' }]}
          >
            <AntInput.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="tags"
            label="标签"
            rules={[{ required: true, message: '请输入标签' }]}
          >
            <AntSelect mode="tags" placeholder="请输入标签">
              <Option value="重要">重要</Option>
              <Option value="紧急">紧急</Option>
              <Option value="文档">文档</Option>
              <Option value="表格">表格</Option>
            </AntSelect>
          </Form.Item>
          {!editingFile && (
            <Form.Item
              label="上传文件"
              required
            >
              <Upload {...uploadProps}>
                <Button className="flex items-center">
                  <UploadOutlined className="mr-2" />
                  选择文件
                </Button>
              </Upload>
            </Form.Item>
          )}
        </Form>
      </Modal>

      <Modal
        title="文件详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false)
          setSelectedFile(null)
        }}
        footer={null}
        width={800}
      >
        {selectedFile && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">文件名：</span>
                {selectedFile.name}
              </div>
              <div>
                <span className="font-medium">文件类型：</span>
                <Tag color={
                  selectedFile.type === 'document' ? 'blue' :
                  selectedFile.type === 'pdf' ? 'red' :
                  selectedFile.type === 'image' ? 'green' :
                  selectedFile.type === 'spreadsheet' ? 'cyan' :
                  'default'
                }>{selectedFile.type}</Tag>
              </div>
              <div>
                <span className="font-medium">文件大小：</span>
                {selectedFile.size}
              </div>
              <div>
                <span className="font-medium">所有者：</span>
                {selectedFile.owner}
              </div>
              <div>
                <span className="font-medium">上传时间：</span>
                {selectedFile.lastModified}
              </div>
              <div>
                <span className="font-medium">下载次数：</span>
                {selectedFile.downloads}
              </div>
              <div>
                <span className="font-medium">状态：</span>
                <AntBadge status={selectedFile.status === '正常' ? 'success' : 'warning'} text={selectedFile.status} />
              </div>
              <div>
                <span className="font-medium">共享状态：</span>
                <AntBadge status={selectedFile.shared ? 'processing' : 'default'} text={selectedFile.shared ? '已共享' : '私有'} />
              </div>
            </div>
            
            {selectedFile.description && (
              <div>
                <span className="font-medium">文件描述：</span>
                <p className="mt-1">{selectedFile.description}</p>
              </div>
            )}
            
            {selectedFile.tags && selectedFile.tags.length > 0 && (
              <div>
                <span className="font-medium">标签：</span>
                <div className="mt-1">
                  {selectedFile.tags.map((tag, index) => (
                    <Tag key={index}>{tag}</Tag>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex justify-end mt-4">
              <Space>
                <Button onClick={() => handleDownload([selectedFile.id])}>
                  <Download className="h-4 w-4 mr-2" />
                  下载
                </Button>
                <Button onClick={() => handleShare(selectedFile.id)}>
                  <Share2 className="h-4 w-4 mr-2" />
                  {selectedFile.shared ? '取消共享' : '共享'}
                </Button>
                <Button onClick={() => {
                  setIsViewModalVisible(false)
                  handleEdit(selectedFile)
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  编辑
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

