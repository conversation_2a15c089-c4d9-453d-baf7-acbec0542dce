"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  AlertTriangle,
  CheckCircle,
  Eye,
  Upload,
  RefreshCcw,
  AlertOctagon,
  ShieldAlert,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import * as XLSX from 'xlsx-js-style'
import { message } from "antd"

interface HazardSource {
  id: string
  name: string
  type: string
  level: string
  location: string
  department: string
  manager: string
  checkFrequency: string
  lastCheckDate: string
  nextCheckDate: string
  status: string
  description?: string
  riskFactors?: string[]
  controlMeasures?: string[]
  emergencyMeasures?: string[]
  remarks?: string
  disabled?: boolean
}

export function HazardSourceManagement() {
  const [isAddHazardOpen, setIsAddHazardOpen] = useState(false)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [selectedHazard, setSelectedHazard] = useState<HazardSource | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedLevel, setSelectedLevel] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [loading, setLoading] = useState(false)
  const [hazards, setHazards] = useState<HazardSource[]>([
    {
      id: "1",
      name: "矿井瓦斯积聚",
      type: "瓦斯危险源",
      level: "重大危险源",
      location: "井下采煤工作面",
      department: "安全生产部",
      manager: "张三",
      checkFrequency: "每日",
      lastCheckDate: "2024-03-14",
      nextCheckDate: "2024-03-15",
      status: "正常监控",
      description: "由于煤层瓦斯含量高，易发生瓦斯积聚...",
      riskFactors: ["瓦斯浓度超标", "通风不良", "监测设备故障"],
      controlMeasures: ["加强通风", "安装瓦斯监测器", "定期巡检"],
      emergencyMeasures: ["立即撤离人员", "切断电源", "启动应急预案"],
    },
    {
      id: "2",
      name: "露天采场高边坡",
      type: "地质危险源",
      level: "重大危险源",
      location: "露天矿区东部",
      department: "采矿工程部",
      manager: "李四",
      checkFrequency: "每周",
      lastCheckDate: "2024-03-10",
      nextCheckDate: "2024-03-17",
      status: "需要处理",
      description: "露天采场东部存在高陡边坡，存在滑坡风险...",
      riskFactors: ["边坡角度过大", "降雨影响", "震动扰动"],
      controlMeasures: ["边坡加固", "排水设施", "监测预警"],
    },
    {
      id: "3",
      name: "火工品库",
      type: "爆炸危险源",
      level: "重大危险源",
      location: "地面炸药库",
      department: "爆破工程部",
      manager: "王五",
      checkFrequency: "每日",
      lastCheckDate: "2024-03-14",
      nextCheckDate: "2024-03-15",
      status: "正常监控",
      description: "储存炸药、雷管等爆炸物品...",
      controlMeasures: ["专人管理", "温湿度控制", "安防监控"],
    },
    {
      id: "4",
      name: "矿井涌水",
      type: "水文危险源",
      level: "一般危险源",
      location: "井下开采区域",
      department: "防治水部",
      manager: "赵六",
      checkFrequency: "每日",
      lastCheckDate: "2024-03-14",
      nextCheckDate: "2024-03-15",
      status: "正常监控",
      description: "矿井正常涌水量较大，存在突水风险...",
    },
    {
      id: "5",
      name: "变电所",
      type: "电气危险源",
      level: "一般危险源",
      location: "地面变电所",
      department: "机电工程部",
      manager: "孙七",
      checkFrequency: "每周",
      lastCheckDate: "2024-03-08",
      nextCheckDate: "2024-03-15",
      status: "需要处理",
      description: "主要变电设备及高压线路...",
    },
  ])

  // 统计数据
  const statistics = {
    total: hazards.length,
    normal: hazards.filter(h => h.status === "正常监控" && !h.disabled).length,
    warning: hazards.filter(h => h.status === "需要处理").length,
    major: hazards.filter(h => h.level === "重大危险源").length,
    needCheck: hazards.filter(h => {
      const nextCheck = new Date(h.nextCheckDate)
      const today = new Date()
      return nextCheck <= today
    }).length
  }

  // 处理搜索和筛选
  const filteredHazards = hazards.filter(hazard => {
    const matchesSearch = hazard.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         hazard.description?.toLowerCase().includes(searchText.toLowerCase()) ||
                         hazard.location.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedType === "all" || hazard.type === selectedType
    const matchesLevel = selectedLevel === "all" || hazard.level === selectedLevel
    const matchesStatus = selectedStatus === "all" || hazard.status === selectedStatus
    return matchesSearch && matchesType && matchesLevel && matchesStatus
  })

  // 处理添加危险源
  const handleAddHazard = (formData: any) => {
    const newHazard: HazardSource = {
      id: Date.now().toString(),
      ...formData,
    }
    setHazards([...hazards, newHazard])
    setIsAddHazardOpen(false)
    message.success("危险源添加成功")
  }

  // 处理删除危险源
  const handleDeleteHazard = (id: string) => {
    const updatedHazards = hazards.filter(h => h.id !== id)
    setHazards(updatedHazards)
    message.success("危险源删除成功")
  }

  // 处理禁用危险源
  const handleDisableHazard = (id: string) => {
    const updatedHazards = hazards.map(h =>
      h.id === id ? { ...h, disabled: !h.disabled } : h
    )
    setHazards(updatedHazards)
    message.success(`危险源${hazards.find(h => h.id === id)?.disabled ? "启用" : "禁用"}成功`)
  }

  // 导出Excel
  const handleExportExcel = () => {
    try {
      const exportData = hazards.map(hazard => ({
        '危险源名称': hazard.name,
        '类型': hazard.type,
        '危险等级': hazard.level,
        '位置': hazard.location,
        '责任部门': hazard.department,
        '负责人': hazard.manager,
        '检查频率': hazard.checkFrequency,
        '上次检查': hazard.lastCheckDate,
        '下次检查': hazard.nextCheckDate,
        '状态': hazard.status,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 40 }, // 危险源名称
        { wch: 15 }, // 类型
        { wch: 15 }, // 危险等级
        { wch: 20 }, // 位置
        { wch: 15 }, // 责任部门
        { wch: 15 }, // 负责人
        { wch: 15 }, // 检查频率
        { wch: 15 }, // 上次检查
        { wch: 15 }, // 下次检查
        { wch: 15 }, // 状态
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '危险源列表')
      XLSX.writeFile(wb, `危险源列表_${new Date().toISOString().split('T')[0]}.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 导入Excel
  const handleImportExcel = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        // 转换导入的数据格式
        const importedHazards: HazardSource[] = jsonData.map((item: any) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: item['危险源名称'],
          type: item['类型'],
          level: item['危险等级'],
          location: item['位置'],
          department: item['责任部门'],
          manager: item['负责人'],
          checkFrequency: item['检查频率'],
          lastCheckDate: item['上次检查'],
          nextCheckDate: item['下次检查'],
          status: item['状态'],
        }))

        setHazards([...hazards, ...importedHazards])
        message.success('导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      message.success('数据已刷新')
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">危险源管理</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <AlertOctagon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">危险源总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.normal}</h3>
            <p className="text-sm text-muted-foreground">正常监控</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <ShieldAlert className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.major}</h3>
            <p className="text-sm text-muted-foreground">重大危险源</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.needCheck}</h3>
            <p className="text-sm text-muted-foreground">待检查</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>危险源列表</CardTitle>
              <CardDescription>管理企业各类危险源</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索危险源..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="危险源类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="瓦斯危险源">瓦斯危险源</SelectItem>
                    <SelectItem value="地质危险源">地质危险源</SelectItem>
                    <SelectItem value="爆炸危险源">爆炸危险源</SelectItem>
                    <SelectItem value="水文危险源">水文危险源</SelectItem>
                    <SelectItem value="电气危险源">电气危险源</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="危险等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有等级</SelectItem>
                    <SelectItem value="重大危险源">重大危险源</SelectItem>
                    <SelectItem value="一般危险源">一般危险源</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="正常监控">正常监控</SelectItem>
                    <SelectItem value="需要处理">需要处理</SelectItem>
                    <SelectItem value="已停用">已停用</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={handleRefresh}>
                  <RefreshCcw className="h-4 w-4" />
                </Button>
                <Button variant="outline" onClick={handleExportExcel}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Input
                  type="file"
                  accept=".xlsx,.xls"
                  className="hidden"
                  id="import-excel"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleImportExcel(file)
                  }}
                />
                <Button variant="outline" onClick={() => document.getElementById('import-excel')?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  导入
                </Button>
                <Button onClick={() => setIsAddHazardOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加危险源
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>危险源名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>危险等级</TableHead>
                    <TableHead>位置</TableHead>
                    <TableHead>责任部门</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>检查频率</TableHead>
                    <TableHead>上次检查</TableHead>
                    <TableHead>下次检查</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHazards.map((hazard) => (
                    <TableRow key={hazard.id} className={hazard.disabled ? "opacity-50" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <AlertOctagon className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{hazard.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{hazard.type}</TableCell>
                      <TableCell>
                        <Badge variant={hazard.level === "重大危险源" ? "destructive" : "default"}>
                          {hazard.level}
                        </Badge>
                      </TableCell>
                      <TableCell>{hazard.location}</TableCell>
                      <TableCell>{hazard.department}</TableCell>
                      <TableCell>{hazard.manager}</TableCell>
                      <TableCell>{hazard.checkFrequency}</TableCell>
                      <TableCell>{hazard.lastCheckDate}</TableCell>
                      <TableCell>{hazard.nextCheckDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            hazard.status === "正常监控"
                              ? "default"
                              : hazard.status === "需要处理"
                                ? "secondary"
                                : "outline"
                          }
                        >
                          {hazard.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedHazard(hazard)
                              setIsViewModalOpen(true)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedHazard(hazard)
                                setIsViewModalOpen(true)
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDisableHazard(hazard.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                {hazard.disabled ? "启用" : "禁用"}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteHazard(hazard.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredHazards.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>危险源详情</DialogTitle>
          </DialogHeader>
          {selectedHazard && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>危险源名称</Label>
                <p className="text-sm">{selectedHazard.name}</p>
              </div>
              <div className="space-y-2">
                <Label>类型</Label>
                <p className="text-sm">{selectedHazard.type}</p>
              </div>
              <div className="space-y-2">
                <Label>危险等级</Label>
                <p className="text-sm">{selectedHazard.level}</p>
              </div>
              <div className="space-y-2">
                <Label>位置</Label>
                <p className="text-sm">{selectedHazard.location}</p>
              </div>
              <div className="space-y-2">
                <Label>责任部门</Label>
                <p className="text-sm">{selectedHazard.department}</p>
              </div>
              <div className="space-y-2">
                <Label>负责人</Label>
                <p className="text-sm">{selectedHazard.manager}</p>
              </div>
              <div className="space-y-2">
                <Label>检查频率</Label>
                <p className="text-sm">{selectedHazard.checkFrequency}</p>
              </div>
              <div className="space-y-2">
                <Label>状态</Label>
                <p className="text-sm">{selectedHazard.status}</p>
              </div>
              <div className="col-span-2 space-y-2">
                <Label>描述</Label>
                <p className="text-sm">{selectedHazard.description}</p>
              </div>
              {selectedHazard.riskFactors && (
                <div className="col-span-2 space-y-2">
                  <Label>风险因素</Label>
                  <div className="flex gap-2">
                    {selectedHazard.riskFactors.map((factor, index) => (
                      <Badge key={index} variant="secondary">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              {selectedHazard.controlMeasures && (
                <div className="col-span-2 space-y-2">
                  <Label>控制措施</Label>
                  <div className="flex gap-2">
                    {selectedHazard.controlMeasures.map((measure, index) => (
                      <Badge key={index} variant="outline">
                        {measure}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              {selectedHazard.emergencyMeasures && (
                <div className="col-span-2 space-y-2">
                  <Label>应急措施</Label>
                  <div className="flex gap-2">
                    {selectedHazard.emergencyMeasures.map((measure, index) => (
                      <Badge key={index} variant="destructive">
                        {measure}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

