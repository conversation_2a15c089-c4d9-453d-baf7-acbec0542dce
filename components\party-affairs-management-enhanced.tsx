"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  FileText,
  Flag,
  Users,
  Calendar,
  MapPin,
  Clock,
  BookOpen,
  Target,
  Award,
  ChevronUp,
  ChevronDown,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  XCircle,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import * as XLSX from 'xlsx-js-style'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart, Pie, Cell } from 'recharts'

interface PartyAffair {
  id: string
  title: string
  type: string
  date: string
  time: string
  location: string
  organizer: string
  participants: number
  status: string
  description: string
  requirements: string
  materials: string[]
  createdAt: string
  updatedAt: string
}

export function PartyAffairsManagementEnhanced() {
  const { toast } = useToast()
  const [isAddAffairOpen, setIsAddAffairOpen] = useState(false)
  const [isEditAffairOpen, setIsEditAffairOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isViewDetailOpen, setIsViewDetailOpen] = useState(false)
  const [selectedAffair, setSelectedAffair] = useState<PartyAffair | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(false)

  // 活动类型统计数据
  const typeStats = [
    { name: '理论学习', value: 35 },
    { name: '组织生活', value: 25 },
    { name: '主题党日', value: 20 },
    { name: '志愿服务', value: 15 },
    { name: '其他活动', value: 5 },
  ]

  // 活动参与度数据
  const participationData = [
    { month: '1月', count: 45 },
    { month: '2月', count: 52 },
    { month: '3月', count: 48 },
    { month: '4月', count: 60 },
    { month: '5月', count: 55 },
    { month: '6月', count: 65 },
  ]

  const COLORS = ['#4f46e5', '#22c55e', '#eab308', '#ef4444', '#6b7280']

  const [affairs, setAffairs] = useState<PartyAffair[]>([
    {
      id: "1",
      title: "学习习近平新时代中国特色社会主义思想",
      type: "理论学习",
      date: "2025-03-15",
      time: "14:00-16:00",
      location: "党建活动室",
      organizer: "党支部",
      participants: 30,
      status: "已完成",
      description: "深入学习贯彻习近平新时代中国特色社会主义思想，提高党员理论水平。",
      requirements: "全体党员必须参加",
      materials: ["学习材料", "笔记本", "党员手册"],
      createdAt: "2025-03-01",
      updatedAt: "2025-03-15"
    },
    {
      id: "2",
      title: "2024年第一季度组织生活会",
      type: "组织生活",
      date: "2025-03-20",
      time: "09:00-11:00",
      location: "会议室",
      organizer: "党支部书记",
      participants: 25,
      status: "未开始",
      description: "总结2025年第一季度工作，部署下一阶段任务。",
      requirements: "请准备个人工作总结",
      materials: ["工作总结", "计划书"],
      createdAt: "2025-03-05",
      updatedAt: "2025-03-05"
    },
    {
      id: "3",
      title: "党史学习教育主题党日活动",
      type: "主题党日",
      date: "2025-03-25",
      time: "10:00-12:00",
      location: "党建活动室",
      organizer: "组织委员",
      participants: 28,
      status: "进行中",
      description: "开展党史学习教育，缅怀革命先烈，传承红色基因。",
      requirements: "着装整齐，佩戴党徽",
      materials: ["党史教材", "观影资料"],
      createdAt: "2025-03-10",
      updatedAt: "2025-03-10"
    },
    {
      id: "4",
      title: "社区志愿服务活动",
      type: "志愿服务",
      date: "2025-04-05",
      time: "08:30-12:00",
      location: "社区服务中心",
      organizer: "党群工作部",
      participants: 20,
      status: "未开始",
      description: "开展社区帮扶、卫生清洁等志愿服务活动。",
      requirements: "穿着志愿者服装",
      materials: ["工具", "记录本"],
      createdAt: "2025-03-15",
      updatedAt: "2025-03-15"
    },
    {
      id: "5",
      title: "党员先锋岗表彰大会",
      type: "其他活动",
      date: "2025-04-10",
      time: "15:00-17:00",
      location: "大会议室",
      organizer: "党委办公室",
      participants: 50,
      status: "未开始",
      description: "表彰优秀党员，激励先进典型。",
      requirements: "准备表彰材料",
      materials: ["奖状", "表彰文件"],
      createdAt: "2025-03-20",
      updatedAt: "2025-03-20"
    }
  ])

  // 筛选活动
  const filteredAffairs = affairs.filter(affair =>
    (typeFilter === "all" || affair.type === typeFilter) &&
    (statusFilter === "all" || affair.status === statusFilter) &&
    (searchTerm === "" ||
      affair.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      affair.organizer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      affair.location.toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  // 处理查看详情
  const handleViewDetail = (affair: PartyAffair) => {
    setSelectedAffair(affair)
    setIsViewDetailOpen(true)
  }

  // 处理编辑活动
  const handleEditAffair = (affair: PartyAffair) => {
    setSelectedAffair(affair)
    setIsEditAffairOpen(true)
  }

  // 处理删除活动
  const handleDeleteAffair = (affair: PartyAffair) => {
    setSelectedAffair(affair)
    setIsDeleteDialogOpen(true)
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      toast({
        title: "刷新成功",
        description: "数据已更新",
      })
    }, 1000)
  }

  // 导出数据
  const handleExport = () => {
    try {
      const exportData = affairs.map(affair => ({
        '活动名称': affair.title,
        '活动类型': affair.type,
        '活动日期': affair.date,
        '活动时间': affair.time,
        '地点': affair.location,
        '组织者': affair.organizer,
        '参与人数': affair.participants,
        '状态': affair.status,
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 40 }, // 活动名称
        { wch: 15 }, // 活动类型
        { wch: 15 }, // 活动日期
        { wch: 15 }, // 活动时间
        { wch: 20 }, // 地点
        { wch: 15 }, // 组织者
        { wch: 10 }, // 参与人数
        { wch: 10 }, // 状态
      ]
      ws['!cols'] = colWidths

      XLSX.utils.book_append_sheet(wb, ws, '党务活动列表')
      XLSX.writeFile(wb, `党务活动列表_${new Date().toLocaleDateString()}.xlsx`)

      toast({
        title: "导出成功",
        description: "文件已下载到本地",
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理添加活动
  const handleAddAffair = (formData: Partial<PartyAffair>) => {
    const newAffair: PartyAffair = {
      id: `${affairs.length + 1}`,
      title: formData.title || "",
      type: formData.type || "其他活动",
      date: formData.date || new Date().toISOString().split('T')[0],
      time: formData.time || "09:00-11:00",
      location: formData.location || "",
      organizer: formData.organizer || "",
      participants: formData.participants || 0,
      status: "未开始",
      description: formData.description || "",
      requirements: formData.requirements || "",
      materials: formData.materials || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setAffairs(prev => [...prev, newAffair])
    toast({
      title: "添加成功",
      description: "已成功添加新的党务活动",
    })
    setIsAddAffairOpen(false)
  }

  // 处理更新活动
  const handleUpdateAffair = (formData: Partial<PartyAffair>) => {
    if (!selectedAffair) return

    setAffairs(prev => prev.map(affair =>
      affair.id === selectedAffair.id
        ? {
            ...affair,
            ...formData,
            updatedAt: new Date().toISOString()
          }
        : affair
    ))

    toast({
      title: "更新成功",
      description: "已成功更新党务活动信息",
    })
    setIsEditAffairOpen(false)
    setSelectedAffair(null)
  }

  // 处理删除活动
  const handleConfirmDelete = () => {
    if (!selectedAffair) return

    setAffairs(prev => prev.filter(affair => affair.id !== selectedAffair.id))
    toast({
      title: "删除成功",
      description: "已成功删除党务活动",
    })
    setIsDeleteDialogOpen(false)
    setSelectedAffair(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">党务管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            刷新
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-indigo-100 p-3">
                  <Flag className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总活动数</p>
                  <h3 className="text-2xl font-bold">{affairs.length}</h3>
                </div>
              </div>
              <ChevronUp className="h-4 w-4 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-green-100 p-3">
                  <CheckCircle2 className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已完成</p>
                  <h3 className="text-2xl font-bold">
                    {affairs.filter(a => a.status === "已完成").length}
                  </h3>
                </div>
              </div>
              <Progress value={75} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-yellow-100 p-3">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">进行中</p>
                  <h3 className="text-2xl font-bold">
                    {affairs.filter(a => a.status === "进行中").length}
                  </h3>
                </div>
              </div>
              <Progress value={25} className="w-[60px]" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-blue-100 p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">参与人数</p>
                  <h3 className="text-2xl font-bold">
                    {affairs.reduce((sum, a) => sum + a.participants, 0)}
                  </h3>
                </div>
              </div>
              <ChevronUp className="h-4 w-4 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>活动类型分布</CardTitle>
            <CardDescription>各类型活动占比统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full flex items-center justify-center">
              <PieChart width={300} height={300}>
                <Pie
                  data={typeStats}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  fill="#8884d8"
                  paddingAngle={5}
                  dataKey="value"
                >
                  {typeStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>活动参与度趋势</CardTitle>
            <CardDescription>近6个月活动参与人数统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <BarChart
                width={500}
                height={300}
                data={participationData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#4f46e5" name="参与人数" />
              </BarChart>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>党务活动列表</CardTitle>
          <CardDescription>管理党务活动信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索活动..."
                    className="pl-8 w-[250px]"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="活动类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="理论学习">理论学习</SelectItem>
                    <SelectItem value="组织生活">组织生活</SelectItem>
                    <SelectItem value="主题党日">主题党日</SelectItem>
                    <SelectItem value="志愿服务">志愿服务</SelectItem>
                    <SelectItem value="其他活动">其他活动</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="未开始">未开始</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button size="sm" onClick={() => setIsAddAffairOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                添加活动
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>活动名称</TableHead>
                    <TableHead>活动类型</TableHead>
                    <TableHead>活动日期</TableHead>
                    <TableHead>地点</TableHead>
                    <TableHead>组织者</TableHead>
                    <TableHead>参与人数</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAffairs.map((affair) => (
                    <TableRow key={affair.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Flag className="h-4 w-4 text-indigo-500" />
                          <span className="font-medium">{affair.title}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{affair.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{affair.date}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{affair.location}</span>
                        </div>
                      </TableCell>
                      <TableCell>{affair.organizer}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>{affair.participants}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            affair.status === "已完成"
                              ? "default"
                              : affair.status === "进行中"
                                ? "outline"
                                : "secondary"
                          }
                          className={
                            affair.status === "已完成"
                              ? "bg-green-100 text-green-700"
                              : affair.status === "进行中"
                                ? "bg-blue-50 text-blue-700"
                                : "bg-slate-100 text-slate-700"
                          }
                        >
                          {affair.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewDetail(affair)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditAffair(affair)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteAffair(affair)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            共 {filteredAffairs.length} 条记录
          </div>
        </CardFooter>
      </Card>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailOpen} onOpenChange={setIsViewDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>活动详情</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[500px] w-full rounded-md border p-4">
            {selectedAffair && (
              <div className="space-y-6">
                <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
                  <div className="rounded-full bg-indigo-100 p-3">
                    <Flag className="h-6 w-6 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{selectedAffair.title}</h3>
                    <p className="text-sm text-muted-foreground">{selectedAffair.type}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>活动日期</Label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAffair.date}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>活动时间</Label>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAffair.time}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>地点</Label>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAffair.location}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>组织者</Label>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedAffair.organizer}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>活动描述</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">{selectedAffair.description}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>活动要求</Label>
                    <div className="p-4 bg-slate-50 rounded-lg">
                      <p className="text-sm">{selectedAffair.requirements}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>所需材料</Label>
                    <div className="flex flex-wrap gap-2">
                      {selectedAffair.materials.map((material, index) => (
                        <Badge key={index} variant="outline">
                          {material}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>确认删除</span>
            </DialogTitle>
            <DialogDescription>
              您确定要删除该活动记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加/编辑活动对话框 */}
      <Dialog open={isAddAffairOpen || isEditAffairOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddAffairOpen(false)
          setIsEditAffairOpen(false)
          setSelectedAffair(null)
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isEditAffairOpen ? "编辑活动" : "添加活动"}</DialogTitle>
            <DialogDescription>
              {isEditAffairOpen ? "修改活动信息" : "添加新的党务活动"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">活动名称</Label>
                <Input
                  id="title"
                  placeholder="请输入活动名称"
                  defaultValue={selectedAffair?.title}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">活动类型</Label>
                <Select defaultValue={selectedAffair?.type || "其他活动"}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择活动类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="理论学习">理论学习</SelectItem>
                    <SelectItem value="组织生活">组织生活</SelectItem>
                    <SelectItem value="主题党日">主题党日</SelectItem>
                    <SelectItem value="志愿服务">志愿服务</SelectItem>
                    <SelectItem value="其他活动">其他活动</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">活动日期</Label>
                <Input
                  id="date"
                  type="date"
                  defaultValue={selectedAffair?.date}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="time">活动时间</Label>
                <Input
                  id="time"
                  type="time"
                  defaultValue={selectedAffair?.time?.split("-")[0]}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">活动地点</Label>
                <Input
                  id="location"
                  placeholder="请输入活动地点"
                  defaultValue={selectedAffair?.location}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="organizer">组织者</Label>
                <Input
                  id="organizer"
                  placeholder="请输入组织者"
                  defaultValue={selectedAffair?.organizer}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="participants">参与人数</Label>
                <Input
                  id="participants"
                  type="number"
                  placeholder="请输入预计参与人数"
                  defaultValue={selectedAffair?.participants}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">活动状态</Label>
                <Select defaultValue={selectedAffair?.status || "未开始"}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择活动状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="未开始">未开始</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="已取消">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">活动描述</Label>
              <Textarea
                id="description"
                placeholder="请输入活动描述"
                defaultValue={selectedAffair?.description}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="requirements">活动要求</Label>
              <Textarea
                id="requirements"
                placeholder="请输入活动要求"
                defaultValue={selectedAffair?.requirements}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="materials">所需材料</Label>
              <Input
                id="materials"
                placeholder="请输入所需材料，用逗号分隔"
                defaultValue={selectedAffair?.materials?.join(", ")}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddAffairOpen(false)
              setIsEditAffairOpen(false)
              setSelectedAffair(null)
            }}>
              取消
            </Button>
            <Button onClick={() => {
              const titleInput = document.getElementById("title") as HTMLInputElement;
              const typeSelect = document.querySelector("[data-value]") as HTMLElement;
              const dateInput = document.getElementById("date") as HTMLInputElement;
              const timeInput = document.getElementById("time") as HTMLInputElement;
              const locationInput = document.getElementById("location") as HTMLInputElement;
              const organizerInput = document.getElementById("organizer") as HTMLInputElement;
              const participantsInput = document.getElementById("participants") as HTMLInputElement;
              const statusSelect = document.querySelector("[data-value]") as HTMLElement;
              const descriptionInput = document.getElementById("description") as HTMLTextAreaElement;
              const requirementsInput = document.getElementById("requirements") as HTMLTextAreaElement;
              const materialsInput = document.getElementById("materials") as HTMLInputElement;

              const formData: Partial<PartyAffair> = {
                title: titleInput.value,
                type: typeSelect.getAttribute("data-value") || "其他活动",
                date: dateInput.value,
                time: timeInput.value,
                location: locationInput.value,
                organizer: organizerInput.value,
                participants: parseInt(participantsInput.value) || 0,
                status: statusSelect.getAttribute("data-value") || "未开始",
                description: descriptionInput.value,
                requirements: requirementsInput.value,
                materials: materialsInput.value.split(",").map(item => item.trim()).filter(item => item),
              };

              if (isEditAffairOpen && selectedAffair) {
                handleUpdateAffair({
                  ...formData,
                  id: selectedAffair.id,
                });
              } else {
                handleAddAffair(formData);
              }
            }}>
              {isEditAffairOpen ? "保存" : "添加"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}