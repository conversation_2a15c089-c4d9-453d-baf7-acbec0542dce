"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Calendar,
  FileText,
  User,
  Users,
  Clock,
  CheckCircle2,
  BookOpen,
  Award,
  GraduationCap
} from "lucide-react"

interface TrainingRecord {
  id: string
  title: string
  type: string
  trainer: string
  date: string
  duration: string
  location: string
  participants: string
  status: string
  materials: string
  description: string
}

export function SafetyTraining() {
  // 初始培训记录数据
  const initialTrainings: TrainingRecord[] = [
    {
      id: "1",
      title: "矿山安全操作规程培训",
      type: "安全操作",
      trainer: "张教员",
      date: "2025-03-15",
      duration: "4小时",
      location: "培训中心A室",
      participants: "采矿部全体人员",
      status: "已完成",
      materials: "安全操作手册、PPT演示文稿",
      description: "针对矿山作业人员的安全操作规程培训，包括设备使用、应急处理等内容。"
    },
    {
      id: "2",
      title: "消防安全知识培训",
      type: "消防安全",
      trainer: "李教员",
      date: "2025-03-20",
      duration: "3小时",
      location: "培训中心B室",
      participants: "全体员工",
      status: "计划中",
      materials: "消防手册、灭火器实操",
      description: "基础消防知识培训，包括火灾预防、灭火器使用、疏散逃生等内容。"
    },
    {
      id: "3",
      title: "应急救援演练",
      type: "应急救援",
      trainer: "王教员",
      date: "2025-03-25",
      duration: "6小时",
      location: "矿区操作现场",
      participants: "应急救援队成员",
      status: "计划中",
      materials: "救援设备、急救包",
      description: "模拟矿难情况下的应急救援演练，提高救援队伍的实战能力。"
    },
    {
      id: "4",
      title: "职业健康防护培训",
      type: "职业健康",
      trainer: "赵教员",
      date: "2025-03-10",
      duration: "2小时",
      location: "培训中心C室",
      participants: "一线作业人员",
      status: "已完成",
      materials: "防护手册、防护装备展示",
      description: "职业病防护知识培训，包括粉尘防护、噪声防护、有害气体防护等内容。"
    },
  ]

  // 状态管理
  const [trainings, setTrainings] = useState<TrainingRecord[]>(initialTrainings)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentTraining, setCurrentTraining] = useState<TrainingRecord>({
    id: "",
    title: "",
    type: "",
    trainer: "",
    date: "",
    duration: "",
    location: "",
    participants: "",
    status: "",
    materials: "",
    description: ""
  })
  const [activeTab, setActiveTab] = useState("all")

  // 过滤培训记录
  const filteredTrainings = trainings.filter(
    (training) => {
      const matchesSearch =
        training.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        training.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        training.trainer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        training.participants.toLowerCase().includes(searchTerm.toLowerCase());

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "pending") return matchesSearch && training.status === "计划中";
      if (activeTab === "completed") return matchesSearch && training.status === "已完成";

      return matchesSearch;
    }
  )

  // 添加培训记录
  const handleAddTraining = () => {
    const newTraining = {
      ...currentTraining,
      id: (trainings.length + 1).toString()
    }
    setTrainings([...trainings, newTraining])
    setCurrentTraining({
      id: "",
      title: "",
      type: "",
      trainer: "",
      date: "",
      duration: "",
      location: "",
      participants: "",
      status: "",
      materials: "",
      description: ""
    })
    setIsAddDialogOpen(false)
  }

  // 编辑培训记录
  const handleEditTraining = () => {
    const updatedTrainings = trainings.map((training) =>
      training.id === currentTraining.id ? currentTraining : training
    )
    setTrainings(updatedTrainings)
    setCurrentTraining({
      id: "",
      title: "",
      type: "",
      trainer: "",
      date: "",
      duration: "",
      location: "",
      participants: "",
      status: "",
      materials: "",
      description: ""
    })
    setIsEditDialogOpen(false)
  }

  // 删除培训记录
  const handleDeleteTraining = () => {
    const updatedTrainings = trainings.filter(
      (training) => training.id !== currentTraining.id
    )
    setTrainings(updatedTrainings)
    setCurrentTraining({
      id: "",
      title: "",
      type: "",
      trainer: "",
      date: "",
      duration: "",
      location: "",
      participants: "",
      status: "",
      materials: "",
      description: ""
    })
    setIsDeleteDialogOpen(false)
  }

  // 打开编辑对话框
  const openEditDialog = (training: TrainingRecord) => {
    setCurrentTraining(training)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (training: TrainingRecord) => {
    setCurrentTraining(training)
    setIsDeleteDialogOpen(true)
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "计划中":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">计划中</Badge>
      case "进行中":
        return <Badge variant="secondary">进行中</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取培训类型对应的图标
  const getTrainingTypeIcon = (type: string) => {
    switch (type) {
      case "安全操作":
        return <CheckCircle2 className="h-4 w-4 text-blue-500" />
      case "消防安全":
        return <FileText className="h-4 w-4 text-red-500" />
      case "应急救援":
        return <Award className="h-4 w-4 text-yellow-500" />
      case "职业健康":
        return <BookOpen className="h-4 w-4 text-green-500" />
      default:
        return <GraduationCap className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">安全培训管理</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加培训记录
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加新培训记录</DialogTitle>
              <DialogDescription>
                请填写新培训活动的详细信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">培训主题</Label>
                <Input
                  id="title"
                  value={currentTraining.title}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, title: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">培训类型</Label>
                  <Select
                    value={currentTraining.type}
                    onValueChange={(value) => setCurrentTraining({ ...currentTraining, type: value })}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="选择培训类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="安全操作">安全操作</SelectItem>
                      <SelectItem value="消防安全">消防安全</SelectItem>
                      <SelectItem value="应急救援">应急救援</SelectItem>
                      <SelectItem value="职业健康">职业健康</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trainer">培训讲师</Label>
                  <Input
                    id="trainer"
                    value={currentTraining.trainer}
                    onChange={(e) => setCurrentTraining({ ...currentTraining, trainer: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">培训日期</Label>
                  <Input
                    id="date"
                    type="date"
                    value={currentTraining.date}
                    onChange={(e) => setCurrentTraining({ ...currentTraining, date: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">培训时长</Label>
                  <Input
                    id="duration"
                    value={currentTraining.duration}
                    onChange={(e) => setCurrentTraining({ ...currentTraining, duration: e.target.value })}
                    placeholder="例如: 4小时"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">培训地点</Label>
                  <Input
                    id="location"
                    value={currentTraining.location}
                    onChange={(e) => setCurrentTraining({ ...currentTraining, location: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={currentTraining.status}
                    onValueChange={(value) => setCurrentTraining({ ...currentTraining, status: value })}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="计划中">计划中</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="participants">参训人员</Label>
                <Input
                  id="participants"
                  value={currentTraining.participants}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, participants: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="materials">培训材料</Label>
                <Input
                  id="materials"
                  value={currentTraining.materials}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, materials: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">培训内容描述</Label>
                <Textarea
                  id="description"
                  value={currentTraining.description}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddTraining}>确认添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索培训记录..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-[400px]" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="pending">计划中</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTrainings.map((training) => (
          <Card key={training.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center">
                {getTrainingTypeIcon(training.type)}
                <CardTitle className="text-sm font-medium ml-2">{training.title}</CardTitle>
              </div>
              {getStatusBadge(training.status)}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  {training.date} ({training.duration})
                </div>
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  讲师: {training.trainer}
                </div>
                <div className="flex items-center text-sm">
                  <Users className="h-4 w-4 mr-2 text-gray-500" />
                  参训人员: {training.participants}
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium">培训内容:</div>
                  <div className="text-gray-500 text-xs mt-1 line-clamp-2">{training.description}</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openEditDialog(training)}>
                    <Edit className="h-4 w-4 mr-2" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => openDeleteDialog(training)}>
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>培训记录列表</CardTitle>
          <CardDescription>管理所有安全培训活动</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>培训主题</TableHead>
                <TableHead>培训类型</TableHead>
                <TableHead>讲师</TableHead>
                <TableHead>日期</TableHead>
                <TableHead>时长</TableHead>
                <TableHead>地点</TableHead>
                <TableHead>参训人员</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTrainings.map((training) => (
                <TableRow key={training.id}>
                  <TableCell>{training.title}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {getTrainingTypeIcon(training.type)}
                      <span className="ml-2">{training.type}</span>
                    </div>
                  </TableCell>
                  <TableCell>{training.trainer}</TableCell>
                  <TableCell>{training.date}</TableCell>
                  <TableCell>{training.duration}</TableCell>
                  <TableCell>{training.location}</TableCell>
                  <TableCell className="max-w-xs truncate">{training.participants}</TableCell>
                  <TableCell>{getStatusBadge(training.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(training)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(training)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑培训记录</DialogTitle>
            <DialogDescription>
              修改培训活动的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">培训主题</Label>
              <Input
                id="edit-title"
                value={currentTraining.title}
                onChange={(e) => setCurrentTraining({ ...currentTraining, title: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">培训类型</Label>
                <Select
                  value={currentTraining.type}
                  onValueChange={(value) => setCurrentTraining({ ...currentTraining, type: value })}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue placeholder="选择培训类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="安全操作">安全操作</SelectItem>
                    <SelectItem value="消防安全">消防安全</SelectItem>
                    <SelectItem value="应急救援">应急救援</SelectItem>
                    <SelectItem value="职业健康">职业健康</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-trainer">培训讲师</Label>
                <Input
                  id="edit-trainer"
                  value={currentTraining.trainer}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, trainer: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-date">培训日期</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={currentTraining.date}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-duration">培训时长</Label>
                <Input
                  id="edit-duration"
                  value={currentTraining.duration}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, duration: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-location">培训地点</Label>
                <Input
                  id="edit-location"
                  value={currentTraining.location}
                  onChange={(e) => setCurrentTraining({ ...currentTraining, location: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select
                  value={currentTraining.status}
                  onValueChange={(value) => setCurrentTraining({ ...currentTraining, status: value })}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="计划中">计划中</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-participants">参训人员</Label>
              <Input
                id="edit-participants"
                value={currentTraining.participants}
                onChange={(e) => setCurrentTraining({ ...currentTraining, participants: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-materials">培训材料</Label>
              <Input
                id="edit-materials"
                value={currentTraining.materials}
                onChange={(e) => setCurrentTraining({ ...currentTraining, materials: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">培训内容描述</Label>
              <Textarea
                id="edit-description"
                value={currentTraining.description}
                onChange={(e) => setCurrentTraining({ ...currentTraining, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditTraining}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 "{currentTraining.title}" 的培训记录吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteTraining}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}