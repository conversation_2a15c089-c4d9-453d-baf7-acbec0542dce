@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* 自定义主题变量 - 亮色模式 */
    --app-background: 240 20% 97%;
    --app-foreground: 240 10% 3.9%;
    --card-bg: 0 0% 100%;
    --card-fg: 240 10% 3.9%;
    --card-border: 240 5.9% 90%;
    --card-hover-border: 240 5.9% 80%;
    --sidebar-bg: 0 0% 100%;
    --sidebar-fg: 240 5.3% 26.1%;
    --sidebar-border: 240 5.9% 90%;
    --header-bg: 240 20% 97%;
    --header-fg: 240 10% 3.9%;
    --header-border: 240 5.9% 90%;
    --input-bg: 0 0% 100%;
    --input-border: 240 5.9% 90%;
    --button-primary-bg: 240 5.9% 10%;
    --button-primary-fg: 0 0% 98%;
    --button-secondary-bg: 240 4.8% 95.9%;
    --button-secondary-fg: 240 5.9% 10%;
    --divider: 240 6% 90%;
    --hover-bg: 240 4.8% 95.9%;
    --hover-fg: 240 5.9% 10%;
    --dropdown-bg: 0 0% 100%;
    --dropdown-fg: 240 10% 3.9%;
    --dropdown-border: 240 5.9% 90%;
    --dropdown-hover: 240 4.8% 95.9%;
    --toast-bg: 0 0% 100%;
    --toast-fg: 240 10% 3.9%;
    --toast-border: 240 5.9% 90%;
    --scrollbar-thumb: 240 6% 85%;
    --scrollbar-track: 0 0% 100%;
  }
  
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* 自定义主题变量 - 暗色模式 */
    --app-background: 240 10% 4%;
    --app-foreground: 0 0% 98%;
    --card-bg: 240 5% 12%;
    --card-fg: 0 0% 95%;
    --card-border: 240 3.7% 18%;
    --card-hover-border: 240 5% 25%;
    --sidebar-bg: 240 5% 10%;
    --sidebar-fg: 0 0% 95%;
    --sidebar-border: 240 5% 15%;
    --header-bg: 240 10% 4%;
    --header-fg: 0 0% 95%;
    --header-border: 240 5% 15%;
    --input-bg: 240 5% 15%;
    --input-border: 240 4% 25%;
    --button-primary-bg: 0 0% 98%;
    --button-primary-fg: 240 5.9% 10%;
    --button-secondary-bg: 240 3.7% 18%;
    --button-secondary-fg: 0 0% 98%;
    --divider: 240 5% 18%;
    --hover-bg: 240 5% 18%;
    --hover-fg: 0 0% 98%;
    --dropdown-bg: 240 5% 12%;
    --dropdown-fg: 0 0% 95%;
    --dropdown-border: 240 5% 18%;
    --dropdown-hover: 240 5% 20%;
    --toast-bg: 240 5% 12%;
    --toast-fg: 0 0% 95%;
    --toast-border: 240 5% 18%;
    --scrollbar-thumb: 240 5% 25%;
    --scrollbar-track: 240 5% 12%;
  }
}

/* 创建具有主题变量的辅助类 */
@layer components {
  .app-bg {
    background-color: hsl(var(--app-background));
  }
  
  .app-text {
    color: hsl(var(--app-foreground));
  }
  
  .card-bg {
    background-color: hsl(var(--card-bg));
  }
  
  .card-text {
    color: hsl(var(--card-fg));
  }
  
  .card-border {
    border-color: hsl(var(--card-border));
  }
  
  .sidebar-bg {
    background-color: hsl(var(--sidebar-bg));
  }
  
  .sidebar-text {
    color: hsl(var(--sidebar-fg));
  }
  
  .sidebar-border {
    border-color: hsl(var(--sidebar-border));
  }
  
  .divider {
    border-color: hsl(var(--divider));
  }
  
  .hover-bg:hover {
    background-color: hsl(var(--hover-bg));
  }
  
  .hover-text:hover {
    color: hsl(var(--hover-fg));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleUp {
  from { 
    opacity: 0; 
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 新增动画效果 */
@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  .animate-slideUp {
    animation: slideUp 0.5s ease-out forwards;
  }
  
  .animate-scaleUp {
    animation: scaleUp 0.3s ease-out forwards;
  }
  
  .animate-pulse {
    animation: pulse 2s infinite;
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }
  
  /* 动画延迟类 */
  .delay-300 {
    animation-delay: 300ms;
  }
  
  .delay-500 {
    animation-delay: 500ms;
  }
  
  .delay-700 {
    animation-delay: 700ms;
  }
  
  .delay-1000 {
    animation-delay: 1000ms;
  }
  
  /* 动画持续时间 */
  .duration-700 {
    animation-duration: 700ms;
  }
  
  .duration-2000 {
    animation-duration: 2000ms;
  }
  
  /* 滚动条美化 */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 10px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgba(209, 213, 219, 0.5);
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }
  
  /* 暗色模式的滚动条 */
  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }
  
  .dark .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }
  
  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
  }
}
