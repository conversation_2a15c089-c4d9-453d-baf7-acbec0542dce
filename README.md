# 矿业公司综合管理系统

一个完整的企业级管理系统，具有安全管理、工程管理、人事管理等多个功能模块。

## 主题系统

应用支持完整的亮色/暗色主题切换功能，具有以下特点：

1. **集中式主题管理**：使用 React Context API 实现全局主题状态管理，位于 `contexts/theme-context.tsx`。
2. **响应系统偏好**：自动检测并适应系统主题偏好设置，同时允许用户手动切换。
3. **主题持久化**：将用户的主题偏好保存在 localStorage 中，确保刷新页面或重新访问站点时保持一致的主题体验。
4. **CSS变量系统**：使用 CSS 变量定义主题颜色和样式，便于集中管理和修改。
5. **暗色模式优化**：为暗色模式专门设计的视觉风格，确保良好的对比度和阅读舒适度。

### 使用方法

在组件中使用主题：

```tsx
import { useTheme } from "@/contexts/theme-context";

export function MyComponent() {
  const { themeMode, toggleTheme } = useTheme();
  
  return (
    <div className={themeMode === "dark" ? "bg-gray-900 text-white" : "bg-white text-gray-900"}>
      <button onClick={toggleTheme}>
        切换主题
      </button>
      <p>当前主题: {themeMode}</p>
    </div>
  );
}
```

### 定制颜色

系统定义了大量的颜色变量，可在 `styles/globals.css` 文件中找到和修改：

- 亮色模式下的主要背景颜色：`--app-background`
- 暗色模式下的卡片背景：`--card-bg`
- 等等

## 其他功能

- **响应式设计**：应用适应不同屏幕大小
- **动态侧边栏**：可收起的侧边栏菜单
- **粒子效果背景**：在登录页面提供动态视觉效果
- **动画过渡**：提供丰富的动画效果，增强用户体验

## 技术栈

- Next.js
- React
- Tailwind CSS
- TypeScript
- Shadcn UI 组件库

## 功能模块

- 系统管理
- 安全管理
- 工程管理
- 人事管理
- 财务管理
- 固定资产管理
- 能源管理
- 保卫管理
- 办公与行政管理
- 物资与供应链管理
- 任务与流程管理
- 综合展示与报表
- 其他模块

## 安装与运行

1. 安装依赖：

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

2. 启动开发服务器：

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

3. 构建生产版本：

```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

## Netlify部署指南

本项目支持以静态网站形式部署到Netlify或其他静态网站托管服务。

### 静态导出步骤

1. 确保安装了所有依赖：
```bash
npm install
```

2. 运行导出命令生成静态文件：
```bash
npm run export
```
这会在项目根目录创建一个`static-export`文件夹，其中包含所有静态文件及必要的Netlify配置。

### Netlify部署方法

#### 方法一：通过Netlify拖放部署

1. 登录到[Netlify](https://app.netlify.com/)
2. 在主仪表板中，找到并点击"Sites"部分
3. 将生成的`static-export`文件夹整个拖放到指定区域
4. Netlify将自动上传和部署您的网站
5. 网站部署完成后，Netlify会提供一个随机生成的URL

#### 方法二：通过Netlify CLI部署

1. 安装Netlify CLI：
```bash
npm install -g netlify-cli
```

2. 登录到Netlify账户：
```bash
netlify login
```

3. 初始化新网站：
```bash
cd static-export
netlify init
```

4. 按照CLI提示操作，选择"Create & configure a new site"

5. 部署网站：
```bash
netlify deploy --prod
```

### 持续集成部署

您也可以设置GitHub仓库与Netlify的持续集成，每次推送代码时自动部署：

1. 将项目推送到GitHub仓库
2. 在Netlify上，选择"New site from Git"
3. 选择GitHub并授权访问
4. 选择您的仓库
5. 配置构建设置：
   - 构建命令：`npm run export`
   - 发布目录：`static-export`
6. 点击"Deploy site"按钮

### 测试账号

```
用户名：admin
密码：admin123
```

