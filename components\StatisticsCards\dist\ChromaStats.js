"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
exports.__esModule = true;
exports.ChromaStats = void 0;
var react_1 = require("react");
var gsap_1 = require("gsap");
var ChromaCard_1 = require("./ChromaCard");
var lucide_react_1 = require("lucide-react");
require("./ChromaStats.css");
exports.ChromaStats = function () {
    var containerRef = react_1.useRef(null);
    var stats = [
        {
            title: "总任务数",
            value: "2,846",
            icon: React.createElement(lucide_react_1.Activity, { className: "h-6 w-6" }),
            trend: {
                value: 12.5,
                label: "较上周"
            },
            color: "#3B82F6"
        },
        {
            title: "进行中",
            value: "485",
            icon: React.createElement(lucide_react_1.Package, { className: "h-6 w-6" }),
            trend: {
                value: 8.2,
                label: "较上周"
            },
            color: "#10B981"
        },
        {
            title: "预警任务",
            value: "24",
            icon: React.createElement(lucide_react_1.AlertCircle, { className: "h-6 w-6" }),
            trend: {
                value: -5.4,
                label: "较上周"
            },
            color: "#EF4444"
        },
        {
            title: "完成率",
            value: "92.6%",
            icon: React.createElement(lucide_react_1.BarChart2, { className: "h-6 w-6" }),
            trend: {
                value: 3.2,
                label: "较上周"
            },
            color: "#8B5CF6"
        }
    ];
    react_1.useEffect(function () {
        var container = containerRef.current;
        if (!container)
            return;
        gsap_1.gsap.fromTo(container.children, {
            y: 20,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            stagger: 0.1,
            ease: "power3.out"
        });
    }, []);
    return (React.createElement("div", { ref: containerRef, className: "chroma-stats-container" }, stats.map(function (stat, index) { return (React.createElement(ChromaCard_1.ChromaCard, __assign({ key: index }, stat))); })));
};
