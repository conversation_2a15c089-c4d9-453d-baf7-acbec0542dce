"use client"

import type React from "react"
import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Search, Plus, Edit, Trash2, Download, FileText, Wrench, CheckCircle2, AlertCircle, XCircle, RefreshCw, Eye, Clock, ArrowLeft } from "lucide-react"
import * as XLSX from 'xlsx'
import ReactECharts from 'echarts-for-react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
  SheetClose,
  SheetTrigger
} from "@/components/ui/sheet"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"

// 维护记录接口
interface MaintenanceRecord {
  id: string
  assetName: string
  assetCode: string
  maintenanceType: "定期保养" | "故障维修" | "系统升级" | "其他"
  maintenanceDate: string
  completionDate?: string
  cost: number
  responsible: string
  status: "计划中" | "进行中" | "已完成" | "已取消"
  description: string
  department: string
  maintenanceProvider?: string
  contactPerson?: string
  contactPhone?: string
  nextMaintenanceDate?: string
  parts?: string[]
  issues?: string[]
  solutions?: string[]
  createdAt: string
  updatedAt: string
}

// 模拟维护记录数据
const maintenanceRecords: MaintenanceRecord[] = [
  {
    id: "1",
    assetName: "采矿设备A型",
    assetCode: "ZC2025001",
    maintenanceType: "定期保养",
    maintenanceDate: "2025-03-15",
    completionDate: "2025-03-15",
    cost: 15000,
    responsible: "张工",
    status: "已完成",
    description: "按计划进行季度保养",
    department: "生产部",
    nextMaintenanceDate: "2025-04-15",
    maintenanceProvider: "重工机械维修公司",
    contactPerson: "李师傅",
    contactPhone: "13800138000",
    parts: ["润滑油", "滤芯", "密封圈"],
    issues: ["轴承磨损"],
    solutions: ["更换轴承", "添加润滑油"],
    createdAt: "2025-03-10",
    updatedAt: "2025-03-15"
  },
  {
    id: "2",
    assetName: "运输车辆B型",
    assetCode: "ZC2025002",
    maintenanceType: "故障维修",
    maintenanceDate: "2025-03-20",
    completionDate: "2025-03-22",
    cost: 8500,
    responsible: "李工",
    status: "已完成",
    description: "制动系统异常维修",
    department: "物流部",
    maintenanceProvider: "汽车维修中心",
    contactPerson: "王师傅",
    contactPhone: "13900139000",
    parts: ["刹车片", "制动油"],
    issues: ["刹车响声异常", "制动距离增加"],
    solutions: ["更换刹车片", "更换制动油"],
    createdAt: "2025-03-20",
    updatedAt: "2025-03-22"
  },
  {
    id: "3",
    assetName: "安全监控系统",
    assetCode: "ZC2025045",
    maintenanceType: "系统升级",
    maintenanceDate: "2025-03-25",
    cost: 25000,
    responsible: "王工",
    status: "进行中",
    description: "系统软件升级及硬件检查",
    department: "安全管理部",
    maintenanceProvider: "安防科技公司",
    contactPerson: "张工",
    contactPhone: "13700137000",
    parts: ["摄像头", "存储设备"],
    issues: ["系统版本过低", "部分摄像头图像模糊"],
    solutions: ["升级系统版本", "更换老化摄像头"],
    createdAt: "2025-03-23",
    updatedAt: "2025-03-25"
  },
  {
    id: "4",
    assetName: "钻探设备C型",
    assetCode: "ZC2025010",
    maintenanceType: "定期保养",
    maintenanceDate: "2025-04-01",
    cost: 20000,
    responsible: "赵工",
    status: "计划中",
    description: "年度大保养",
    department: "工程部",
    nextMaintenanceDate: "2025-04-01",
    maintenanceProvider: "专业钻探设备维修公司",
    contactPerson: "刘师傅",
    contactPhone: "13600136000",
    parts: ["钻头", "传动轴", "电机"],
    issues: [],
    solutions: [],
    createdAt: "2025-03-25",
    updatedAt: "2025-03-25"
  }
];

export function FixedAssetsMaintenance() {
  const { toast } = useToast()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<MaintenanceRecord | null>(null)
  const [maintenanceTypeFilter, setMaintenanceTypeFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [records, setRecords] = useState<MaintenanceRecord[]>(maintenanceRecords)
  const [isAddSheetOpen, setIsAddSheetOpen] = useState(false)

  // 过滤功能
  const filteredRecords = useMemo(() => {
    return records.filter(record => {
      const searchTermLower = searchTerm.toLowerCase()
      const matchesSearch = searchTerm === "" || (
        (record.assetName?.toLowerCase() || "").includes(searchTermLower) ||
        (record.assetCode?.toLowerCase() || "").includes(searchTermLower) ||
        (record.responsible?.toLowerCase() || "").includes(searchTermLower)
      )

      const matchesType = maintenanceTypeFilter === "all" || record.maintenanceType === maintenanceTypeFilter
      const matchesStatus = statusFilter === "all" || record.status === statusFilter

      return matchesSearch && matchesType && matchesStatus
    })
  }, [records, searchTerm, maintenanceTypeFilter, statusFilter])

  // 状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case "已完成":
        return "bg-green-100 text-green-800"
      case "进行中":
        return "bg-blue-100 text-blue-800"
      case "计划中":
        return "bg-yellow-100 text-yellow-800"
      case "已取消":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // 处理删除
  const handleDelete = (id: string) => {
    const newRecords = records.filter(record => record.id !== id)
    setRecords(newRecords)
    toast({
      title: "删除成功",
      description: "维护记录已被删除",
    })
  }

  // 处理查看
  const handleView = (record: MaintenanceRecord) => {
    setSelectedRecord(record)
    setIsViewDialogOpen(true)
  }

  // 处理编辑
  const handleEdit = (record: MaintenanceRecord) => {
    setSelectedRecord(record)
    setIsEditDialogOpen(true)
  }

  // 导出功能
  const handleExport = () => {
    try {
      const ws = XLSX.utils.json_to_sheet(records)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, "维护记录")
      XLSX.writeFile(wb, "维护记录.xlsx")
      toast({
        title: "导出成功",
        description: "维护记录已导出为Excel文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出过程中发生错误",
        variant: "destructive",
      })
    }
  }

  // 计算统计数据
  const statistics = useMemo(() => {
    return {
      totalRecords: records.length,
      completedCount: records.filter(r => r.status === "已完成").length,
      inProgressCount: records.filter(r => r.status === "进行中").length,
      plannedCount: records.filter(r => r.status === "计划中").length,
      totalCost: records.reduce((sum, r) => sum + r.cost, 0),
    }
  }, [records])

  // 添加新记录
  const handleOpenAdd = () => {
    setIsAddSheetOpen(true)
  }

  // 添加新记录
  const handleAdd = (data: {
    assetName: string
    assetCode: string
    maintenanceType: MaintenanceRecord["maintenanceType"]
    maintenanceDate: string
    completionDate?: string
    cost: number
    responsible: string
    status: MaintenanceRecord["status"]
    description: string
    department: string
    maintenanceProvider?: string
    contactPerson?: string
    contactPhone?: string
    nextMaintenanceDate?: string
  }) => {
    const newRecord: MaintenanceRecord = {
      id: `MR-${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    }

    setRecords([newRecord, ...records])
    toast({
      title: "添加成功",
      description: "新的维护记录已添加"
    })
  }

  return (
    <div className="space-y-6">
      {/* 顶部区域 */}
      <div className="flex items-start justify-between py-2 mb-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Wrench className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">固定资产维护管理</h1>
          </div>
          <p className="text-muted-foreground">查看和管理资产维护记录</p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport} className="hover:bg-green-50">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button
            variant="default"
            size="sm"
            className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 shadow-md"
            onClick={handleOpenAdd}
          >
            <Plus className="h-4 w-4" />
            添加维护记录
          </Button>
        </div>
      </div>

      {/* 统计卡片区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总记录数</p>
                  <h3 className="text-lg font-semibold">{statistics.totalRecords}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已完成</p>
                  <h3 className="text-lg font-semibold">{statistics.completedCount}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">进行中</p>
                  <h3 className="text-lg font-semibold">{statistics.inProgressCount}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">计划中</p>
                  <h3 className="text-lg font-semibold">{statistics.plannedCount}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Wrench className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总费用</p>
                  <h3 className="text-lg font-semibold">¥{statistics.totalCost.toLocaleString()}</h3>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-4 items-center">
        <div className="flex-1">
          <Input
            placeholder="搜索资产名称、编号或负责人..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Select value={maintenanceTypeFilter} onValueChange={setMaintenanceTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="维护类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="定期保养">定期保养</SelectItem>
            <SelectItem value="故障维修">故障维修</SelectItem>
            <SelectItem value="系统升级">系统升级</SelectItem>
            <SelectItem value="其他">其他</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="计划中">计划中</SelectItem>
            <SelectItem value="进行中">进行中</SelectItem>
            <SelectItem value="已完成">已完成</SelectItem>
            <SelectItem value="已取消">已取消</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>维护记录列表</CardTitle>
          <CardDescription>
            共 {filteredRecords.length} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>资产名称</TableHead>
                <TableHead>资产编号</TableHead>
                <TableHead>维护类型</TableHead>
                <TableHead>维护日期</TableHead>
                <TableHead>完成日期</TableHead>
                <TableHead>费用(元)</TableHead>
                <TableHead>负责人</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.assetName}</TableCell>
                  <TableCell>{record.assetCode}</TableCell>
                  <TableCell>{record.maintenanceType}</TableCell>
                  <TableCell>{record.maintenanceDate}</TableCell>
                  <TableCell>{record.completionDate || "-"}</TableCell>
                  <TableCell>{record.cost.toLocaleString()}</TableCell>
                  <TableCell>{record.responsible}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(record.status)}>
                      {record.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon" onClick={() => handleView(record)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleEdit(record)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDelete(record.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 查看对话框 */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-[800px]">
          <DialogHeader>
            <DialogTitle>维护记录详情</DialogTitle>
            <DialogDescription>查看维护记录的详细信息</DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label>资产信息</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">资产名称：</span>
                      {selectedRecord.assetName}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">资产编号：</span>
                      {selectedRecord.assetCode}
                    </div>
                  </div>
                </div>
                <div>
                  <Label>维护信息</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">维护类型：</span>
                      {selectedRecord.maintenanceType}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">维护日期：</span>
                      {selectedRecord.maintenanceDate}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">完成日期：</span>
                      {selectedRecord.completionDate || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">维护费用：</span>
                      {selectedRecord.cost.toLocaleString()} 元
                    </div>
                  </div>
                </div>
                <div>
                  <Label>负责人信息</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">负责人：</span>
                      {selectedRecord.responsible}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">维护单位：</span>
                      {selectedRecord.maintenanceProvider || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">联系人：</span>
                      {selectedRecord.contactPerson || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">联系电话：</span>
                      {selectedRecord.contactPhone || "-"}
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label>状态信息</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">当前状态：</span>
                      <Badge className={getStatusColor(selectedRecord.status)}>
                        {selectedRecord.status}
                      </Badge>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">下次维护日期：</span>
                      {selectedRecord.nextMaintenanceDate || "-"}
                    </div>
                  </div>
                </div>
                <div>
                  <Label>维护内容</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">描述：</span>
                      {selectedRecord.description || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">更换配件：</span>
                      {selectedRecord.parts?.join("、") || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">发现问题：</span>
                      {selectedRecord.issues?.join("、") || "-"}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">解决方案：</span>
                      {selectedRecord.solutions?.join("、") || "-"}
                    </div>
                  </div>
                </div>
                <div>
                  <Label>记录信息</Label>
                  <div className="mt-1 space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">创建时间：</span>
                      {selectedRecord.createdAt}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">更新时间：</span>
                      {selectedRecord.updatedAt}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader className="border-b pb-4">
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-blue-600" />
              编辑维护记录
            </DialogTitle>
            <DialogDescription>修改维护记录的详细信息</DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="flex-1 overflow-hidden">
              <Tabs defaultValue="basic" className="w-full h-full">
                <TabsList className="w-full grid grid-cols-3 mb-4">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="maintenance">维护信息</TabsTrigger>
                  <TabsTrigger value="contact">联系方式</TabsTrigger>
                </TabsList>

                <ScrollArea className="h-[calc(85vh-220px)]">
                  <form id="editForm" onSubmit={(e) => {
                    e.preventDefault()
                    const formData = new FormData(e.currentTarget)

                    // 验证必填字段
                    const requiredFields = ["assetName", "assetCode", "maintenanceType", "maintenanceDate", "cost", "responsible", "status"]
                    const missingFields = requiredFields.filter(field => !formData.get(field))

                    if (missingFields.length > 0) {
                      toast({
                        title: "验证失败",
                        description: "请填写所有必填字段",
                        variant: "destructive"
                      })
                      return
                    }

                    try {
                      const cost = Number(formData.get("cost"))
                      if (isNaN(cost) || cost < 0) {
                        toast({
                          title: "验证失败",
                          description: "维护费用必须是有效的正数",
                          variant: "destructive"
                        })
                        return
                      }

                      const updatedRecord = {
                        ...selectedRecord,
                        assetName: formData.get("assetName") as string,
                        assetCode: formData.get("assetCode") as string,
                        maintenanceType: formData.get("maintenanceType") as MaintenanceRecord["maintenanceType"],
                        maintenanceDate: formData.get("maintenanceDate") as string,
                        completionDate: formData.get("completionDate") as string || undefined,
                        cost: cost,
                        responsible: formData.get("responsible") as string,
                        status: formData.get("status") as MaintenanceRecord["status"],
                        description: formData.get("description") as string || "无描述",
                        department: selectedRecord.department,
                        nextMaintenanceDate: formData.get("nextMaintenanceDate") as string || undefined,
                        maintenanceProvider: formData.get("maintenanceProvider") as string || undefined,
                        contactPerson: formData.get("contactPerson") as string || undefined,
                        contactPhone: formData.get("contactPhone") as string || undefined,
                        parts: formData.get("parts") ? (formData.get("parts") as string).split("、").filter(Boolean) : undefined,
                        issues: formData.get("issues") ? (formData.get("issues") as string).split("、").filter(Boolean) : undefined,
                        solutions: formData.get("solutions") ? (formData.get("solutions") as string).split("、").filter(Boolean) : undefined,
                        updatedAt: new Date().toISOString().split("T")[0]
                      }

                      setRecords(records.map(record =>
                        record.id === updatedRecord.id ? updatedRecord : record
                      ))

                      toast({
                        title: "更新成功",
                        description: "维护记录已更新",
                      })

                      setIsEditDialogOpen(false)
                    } catch (error) {
                      toast({
                        title: "更新失败",
                        description: "保存过程中发生错误",
                        variant: "destructive"
                      })
                    }
                  }}>
                    <TabsContent value="basic" className="mt-0 px-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base font-medium">资产基本信息</CardTitle>
                        </CardHeader>
                        <CardContent className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="assetName">资产名称 <span className="text-red-500">*</span></Label>
                            <Input
                              id="assetName"
                              name="assetName"
                              defaultValue={selectedRecord.assetName}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="assetCode">资产编号 <span className="text-red-500">*</span></Label>
                            <Input
                              id="assetCode"
                              name="assetCode"
                              defaultValue={selectedRecord.assetCode}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="responsible">负责人 <span className="text-red-500">*</span></Label>
                            <Input
                              id="responsible"
                              name="responsible"
                              defaultValue={selectedRecord.responsible}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="description">描述</Label>
                            <Input
                              id="description"
                              name="description"
                              defaultValue={selectedRecord.description}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="maintenance" className="mt-0 px-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base font-medium">维护详细信息</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="maintenanceType">维护类型 <span className="text-red-500">*</span></Label>
                              <Select name="maintenanceType" defaultValue={selectedRecord.maintenanceType}>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择维护类型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="定期保养">定期保养</SelectItem>
                                  <SelectItem value="故障维修">故障维修</SelectItem>
                                  <SelectItem value="系统升级">系统升级</SelectItem>
                                  <SelectItem value="其他">其他</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="status">状态 <span className="text-red-500">*</span></Label>
                              <Select name="status" defaultValue={selectedRecord.status}>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择状态" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="计划中">计划中</SelectItem>
                                  <SelectItem value="进行中">进行中</SelectItem>
                                  <SelectItem value="已完成">已完成</SelectItem>
                                  <SelectItem value="已取消">已取消</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="maintenanceDate">维护日期 <span className="text-red-500">*</span></Label>
                              <Input
                                id="maintenanceDate"
                                name="maintenanceDate"
                                type="date"
                                defaultValue={selectedRecord.maintenanceDate}
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="completionDate">完成日期</Label>
                              <Input
                                id="completionDate"
                                name="completionDate"
                                type="date"
                                defaultValue={selectedRecord.completionDate}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="cost">维护费用 <span className="text-red-500">*</span></Label>
                              <Input
                                id="cost"
                                name="cost"
                                type="number"
                                min="0"
                                step="0.01"
                                defaultValue={selectedRecord.cost}
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="nextMaintenanceDate">下次维护日期</Label>
                              <Input
                                id="nextMaintenanceDate"
                                name="nextMaintenanceDate"
                                type="date"
                                defaultValue={selectedRecord.nextMaintenanceDate}
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="parts">更换配件（用、分隔）</Label>
                            <Input
                              id="parts"
                              name="parts"
                              defaultValue={selectedRecord.parts?.join("、")}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="issues">发现问题（用、分隔）</Label>
                            <Input
                              id="issues"
                              name="issues"
                              defaultValue={selectedRecord.issues?.join("、")}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="solutions">解决方案（用、分隔）</Label>
                            <Input
                              id="solutions"
                              name="solutions"
                              defaultValue={selectedRecord.solutions?.join("、")}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="contact" className="mt-0 px-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base font-medium">联系方式信息</CardTitle>
                        </CardHeader>
                        <CardContent className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="maintenanceProvider">维护单位</Label>
                            <Input
                              id="maintenanceProvider"
                              name="maintenanceProvider"
                              defaultValue={selectedRecord.maintenanceProvider}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="contactPerson">联系人</Label>
                            <Input
                              id="contactPerson"
                              name="contactPerson"
                              defaultValue={selectedRecord.contactPerson}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="contactPhone">联系电话</Label>
                            <Input
                              id="contactPhone"
                              name="contactPhone"
                              defaultValue={selectedRecord.contactPhone}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </form>
                </ScrollArea>
              </Tabs>
            </div>
          )}

          <div className="border-t pt-4 mt-4">
            <div className="flex justify-end gap-4">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit" form="editForm">
                保存
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 侧边滑动抽屉式添加记录界面 */}
      <Sheet open={isAddSheetOpen} onOpenChange={setIsAddSheetOpen}>
        <SheetContent className="sm:max-w-md md:max-w-lg overflow-y-auto">
          <SheetHeader className="pb-4">
            <SheetTitle className="text-xl flex items-center gap-2">
              <Wrench className="h-5 w-5 text-blue-500" />
              添加维护记录
            </SheetTitle>
            <SheetDescription>
              填写以下信息添加新的维护记录
            </SheetDescription>
          </SheetHeader>

          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)

            // 确保所有必填字段都存在
            const assetName = formData.get("assetName") as string
            const assetCode = formData.get("assetCode") as string
            const maintenanceType = formData.get("maintenanceType") as MaintenanceRecord["maintenanceType"]
            const maintenanceDate = formData.get("maintenanceDate") as string
            const cost = Number(formData.get("cost") || 0)
            const responsible = formData.get("responsible") as string
            const status = formData.get("status") as MaintenanceRecord["status"]
            const description = formData.get("description") as string || "无描述"
            const department = formData.get("department") as string || "未指定部门"

            // 只有在所有必填字段都有值的情况下才提交
            if (assetName && assetCode && maintenanceType && maintenanceDate && cost && responsible && status && description && department) {
              handleAdd({
                assetName,
                assetCode,
                maintenanceType,
                maintenanceDate,
                completionDate: formData.get("completionDate") as string || undefined,
                cost,
                responsible,
                status,
                description,
                department,
                maintenanceProvider: formData.get("maintenanceProvider") as string || undefined,
                contactPerson: formData.get("contactPerson") as string || undefined,
                contactPhone: formData.get("contactPhone") as string || undefined,
                nextMaintenanceDate: formData.get("nextMaintenanceDate") as string || undefined
              })
              setIsAddSheetOpen(false)
            } else {
              toast({
                title: "提交失败",
                description: "请填写所有必填字段",
                variant: "destructive"
              })
            }
          }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="assetName">资产名称 *</Label>
                <Input id="assetName" name="assetName" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="assetCode">资产编号 *</Label>
                <Input id="assetCode" name="assetCode" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">所属部门 *</Label>
                <Input id="department" name="department" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="responsible">负责人 *</Label>
                <Input id="responsible" name="responsible" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenanceType">维护类型 *</Label>
                <Select name="maintenanceType" defaultValue="定期保养">
                  <SelectTrigger id="maintenanceType">
                    <SelectValue placeholder="选择维护类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="定期保养">定期保养</SelectItem>
                    <SelectItem value="故障维修">故障维修</SelectItem>
                    <SelectItem value="系统升级">系统升级</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">状态 *</Label>
                <Select name="status" defaultValue="计划中">
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="计划中">计划中</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="已取消">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenanceDate">维护日期 *</Label>
                <Input
                  id="maintenanceDate"
                  name="maintenanceDate"
                  type="date"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="completionDate">完成日期</Label>
                <Input
                  id="completionDate"
                  name="completionDate"
                  type="date"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cost">维护费用 *</Label>
                <Input
                  id="cost"
                  name="cost"
                  type="number"
                  min="0"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nextMaintenanceDate">下次维护日期</Label>
                <Input
                  id="nextMaintenanceDate"
                  name="nextMaintenanceDate"
                  type="date"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenanceProvider">维护单位</Label>
                <Input
                  id="maintenanceProvider"
                  name="maintenanceProvider"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPerson">联系人</Label>
                <Input
                  id="contactPerson"
                  name="contactPerson"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPhone">联系电话</Label>
                <Input
                  id="contactPhone"
                  name="contactPhone"
                />
              </div>
            </div>
            <div className="space-y-2 mb-4">
              <Label htmlFor="description">维护内容描述 *</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="请详细描述维护内容"
                rows={4}
                required
              />
            </div>

            <SheetFooter className="flex justify-end gap-2 pt-4 border-t">
              <SheetClose asChild>
                <Button variant="outline">取消</Button>
              </SheetClose>
              <Button type="submit">保存记录</Button>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>

      {/* 底部操作按钮区域 */}
      <div className="flex justify-end mt-8 gap-4">
        <Button variant="outline" className="gap-2" onClick={() => router.push("/fixed-assets-management")}>
          <ArrowLeft className="h-4 w-4" />
          返回资产管理
        </Button>
      </div>
    </div>
  )
}

