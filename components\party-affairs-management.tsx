"use client"

import { useState } from "react"
import { Search, Plus, Edit, Trash2, Download, MoreHorizontal, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export function PartyAffairsManagement() {
  const [isAddAffairO<PERSON>, setIsAddAffairOpen] = useState(false)

  // TODO: Replace with actual data and functionality
  const partyAffairs = [
    {
      id: "1",
      title: "学习习近平新时代中国特色社会主义思想",
      date: "2025-03-05",
      participants: 30,
      location: "公司会议室",
      organizer: "党支部",
      type: "理论学习",
    },
    {
      id: "2",
      title: "组织生活会",
      date: "2025-02-20",
      participants: 25,
      location: "党支部活动室",
      organizer: "党支部",
      type: "组织生活",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">党务管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>党务活动列表</CardTitle>
          <CardDescription>管理党务活动信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="搜索活动..." className="pl-8 w-[250px]" />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="活动类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="theory">理论学习</SelectItem>
                    <SelectItem value="organization">组织生活</SelectItem>
                    <SelectItem value="other">其他活动</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Dialog open={isAddAffairOpen} onOpenChange={setIsAddAffairOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      添加活动
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>添加党务活动</DialogTitle>
                      <DialogDescription>创建新的党务活动记录</DialogDescription>
                    </DialogHeader>
                    {/* TODO: Add form fields for adding a new party affair */}
                    <DialogFooter className="mt-6">
                      <Button variant="outline" onClick={() => setIsAddAffairOpen(false)}>
                        取消
                      </Button>
                      <Button onClick={() => setIsAddAffairOpen(false)}>保存</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>活动名称</TableHead>
                    <TableHead>活动类型</TableHead>
                    <TableHead>活动日期</TableHead>
                    <TableHead>参与人数</TableHead>
                    <TableHead>地点</TableHead>
                    <TableHead>组织者</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {partyAffairs.map((affair) => (
                    <TableRow key={affair.id}>
                      <TableCell className="font-medium">{affair.title}</TableCell>
                      <TableCell>{affair.type}</TableCell>
                      <TableCell>{affair.date}</TableCell>
                      <TableCell>{affair.participants}</TableCell>
                      <TableCell>{affair.location}</TableCell>
                      <TableCell>{affair.organizer}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <FileText className="h-4 w-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {partyAffairs.length} 条记录</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

