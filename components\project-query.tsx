"use client"

import { useState, use<PERSON>ffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back } from "react"
import { Download, Search, Settings, CheckCircle2, Clock, HardHat, AlertTriangle, AlertCircle, Loader2, RefreshCcw, Eye, Pencil, Upload, Sparkles, History, MessageSquare, Lightbulb, ChevronRight, XCircle, Info, Save, Star, StarOff, Cpu, RotateCcw, Zap, PlusCircle, ArrowRight, ThumbsUp, ThumbsDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { NetworkGraph } from "@/components/ui/network-graph"
import * as XLSX from 'xlsx'
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line } from 'recharts'
import { Node, Edge } from "@/components/ui/network-graph"
import { ResponsiveContainer } from 'recharts'
import { useToast } from "@/components/ui/use-toast"

interface ProjectRelation {
  sourceId: string
  targetId: string
  relationType: '依赖' | '并行' | '前置' | '后置'
}

interface SearchParams {
  keyword: string
  type: string
  status: string
  manager: string
  department: string
  dateRange: { start: string; end: string }
  budgetRange: { min: number; max: number }
  progressRange: { min: number; max: number }
}

interface SearchHistory {
  id: string
  query: string
  type: 'ai' | 'basic' | 'advanced'
  timestamp: number
  isFavorite?: boolean
  results?: number
}

interface AISearchSuggestion {
  id: string
  text: string
  category: string
}

export function ProjectQuery() {
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [hasSearched, setHasSearched] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [searchParams, setSearchParams] = useState<SearchParams>({
    keyword: '',
    type: '',
    status: '',
    manager: '',
    department: '',
    dateRange: { start: '', end: '' },
    budgetRange: { min: 0, max: 0 },
    progressRange: { min: 0, max: 100 }
  })
  const [sortConfig, setSortConfig] = useState({
    key: '',
    direction: 'asc'
  })
  const [searchMode, setSearchMode] = useState<'basic' | 'advanced' | 'ai'>('basic')
  const [aiQuery, setAiQuery] = useState('')
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [showSearchHistory, setShowSearchHistory] = useState(false)
  const [aiSearchSuggestions, setAiSearchSuggestions] = useState<AISearchSuggestion[]>([
    { id: '1', text: '查找预算超过200万的工程', category: '预算' },
    { id: '2', text: '进度低于30%但已经过半的工程', category: '进度' },
    { id: '3', text: '李四负责的所有工程', category: '人员' },
    { id: '4', text: '最近开始的三个环保工程', category: '类型' },
    { id: '5', text: '查找即将结束但进度落后的工程', category: '风险' },
    { id: '6', text: '安全部门负责的工程按进度排序', category: '部门' },
    { id: '7', text: '预算最高的三个工程', category: '排序' },
    { id: '8', text: '设备工程和采矿工程的进度对比', category: '对比' }
  ])
  const [aiExplanation, setAiExplanation] = useState<string>('')
  const [activeAiFeature, setActiveAiFeature] = useState<'search' | 'analyze' | 'recommend'>('search')
  const { toast } = useToast()

  // 示例数据
  const projects = [
    {
      id: "1",
      name: "矿区A3开发项目",
      type: "采矿工程",
      manager: "张三",
      department: "开发部",
      startDate: "2025-01-15",
      endDate: "2025-04-15",
      budget: 5800000,
      progress: 35,
      status: "进行中",
    },
    {
      id: "2",
      name: "设备更新计划",
      type: "设备工程",
      manager: "李四",
      department: "设备部",
      startDate: "2025-01-20",
      endDate: "2025-03-15",
      budget: 2300000,
      progress: 60,
      status: "进行中",
    },
    {
      id: "3",
      name: "安全系统升级",
      type: "安全工程",
      manager: "王五",
      department: "安全部",
      startDate: "2025-02-01",
      endDate: "2025-04-01",
      budget: 1200000,
      progress: 25,
      status: "进行中",
    },
    {
      id: "4",
      name: "新矿区勘探",
      type: "勘探工程",
      manager: "赵六",
      department: "勘探部",
      startDate: "2025-02-15",
      endDate: "2025-04-10",
      budget: 1800000,
      progress: 10,
      status: "准备中",
    },
    {
      id: "5",
      name: "环保设施改造",
      type: "环保工程",
      manager: "钱七",
      department: "环保部",
      startDate: "2025-03-05",
      endDate: "2025-04-15",
      budget: 3500000,
      progress: 0,
      status: "准备中",
    },
  ]

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "进行中":
        return <Badge className="bg-green-500">进行中</Badge>
      case "准备中":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            准备中
          </Badge>
        )
      case "已完成":
        return <Badge variant="secondary">已完成</Badge>
      case "已暂停":
        return <Badge variant="destructive">已暂停</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理搜索参数变化
  const handleParamChange = (key: keyof SearchParams, value: any) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // 实现搜索功能
  const handleSearch = async () => {
    try {
      setIsLoading(true)
      // 根据搜索条件过滤项目
      const filteredProjects = projects.filter(project => {
        // 名称关键词匹配
        if (searchParams.keyword &&
            !project.name.toLowerCase().includes(searchParams.keyword.toLowerCase())) {
          return false
        }

        // 工程类型匹配
        if (searchParams.type && searchParams.type !== 'all' &&
            project.type !== searchParams.type) {
          return false
        }

        // 状态匹配
        if (searchParams.status && searchParams.status !== 'all' &&
            project.status !== searchParams.status) {
          return false
        }

        // 负责人匹配
        if (searchParams.manager &&
            !project.manager.includes(searchParams.manager)) {
          return false
        }

        // 部门匹配
        if (searchParams.department && searchParams.department !== 'all' &&
            project.department !== searchParams.department) {
          return false
        }

        // 日期范围匹配
        if (searchParams.dateRange.start &&
            project.startDate < searchParams.dateRange.start) {
          return false
        }
        if (searchParams.dateRange.end &&
            project.endDate > searchParams.dateRange.end) {
          return false
        }

        // 预算范围匹配
        if (searchParams.budgetRange.min &&
            project.budget < searchParams.budgetRange.min) {
          return false
        }
        if (searchParams.budgetRange.max &&
            project.budget > searchParams.budgetRange.max) {
          return false
        }

        // 进度范围匹配
        if (project.progress < searchParams.progressRange.min ||
            project.progress > searchParams.progressRange.max) {
          return false
        }

        return true
      })

      setSearchResults(filteredProjects)
      setHasSearched(true)

      toast({
        title: "搜索完成",
        description: `找到 ${filteredProjects.length} 个匹配项目`,
      })
    } catch (error) {
      toast({
        title: "搜索失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 重置搜索条件
  const handleReset = () => {
    setSearchParams({
      keyword: '',
      type: '',
      status: '',
      manager: '',
      department: '',
      dateRange: { start: '', end: '' },
      budgetRange: { min: 0, max: 0 },
      progressRange: { min: 0, max: 100 }
    })
    setSearchResults([])
    setHasSearched(false)
  }

  // 修改快速查询功能
  const handleQuickSearch = (type: 'in-progress' | 'preparing' | 'completed') => {
    const statusMap = {
      'in-progress': '进行中',
      'preparing': '准备中',
      'completed': '已完成'
    }

    // 重置其他搜索条件
    setSearchParams(prev => ({
      ...prev,
      keyword: '',
      type: '',
      manager: '',
      department: '',
      dateRange: { start: '', end: '' },
      budgetRange: { min: 0, max: 0 },
      progressRange: { min: 0, max: 100 },
      status: statusMap[type]
    }))

    // 只按状态筛选
    const filteredProjects = projects.filter(project => project.status === statusMap[type])
    setSearchResults(filteredProjects)
    setHasSearched(true)
  }

  // AI智能查询 - 增强版
  const handleAISearch = async (query: string) => {
    if (!query.trim()) {
      toast({
        title: "请输入查询内容",
        description: "请输入您想要查询的内容",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // 记录搜索历史
      const historyItem: SearchHistory = {
        id: Date.now().toString(),
        query: query,
        type: 'ai',
        timestamp: Date.now()
      }

      // 解析自然语言查询 (这里是模拟的智能解析逻辑)
      const normalizedQuery = query.toLowerCase()

      // 定义一组关键词映射和解析规则
      const parsedParams: Partial<SearchParams> = { keyword: '' }

      // 解析工程类型
      if (normalizedQuery.includes('采矿') || normalizedQuery.includes('矿区')) {
        parsedParams.type = '采矿工程'
      } else if (normalizedQuery.includes('设备')) {
        parsedParams.type = '设备工程'
      } else if (normalizedQuery.includes('安全')) {
        parsedParams.type = '安全工程'
      } else if (normalizedQuery.includes('勘探')) {
        parsedParams.type = '勘探工程'
      } else if (normalizedQuery.includes('环保')) {
        parsedParams.type = '环保工程'
      }

      // 解析状态
      if (normalizedQuery.includes('进行中') || normalizedQuery.includes('正在进行')) {
        parsedParams.status = '进行中'
      } else if (normalizedQuery.includes('准备中') || normalizedQuery.includes('即将开始')) {
        parsedParams.status = '准备中'
      } else if (normalizedQuery.includes('已完成') || normalizedQuery.includes('完成的')) {
        parsedParams.status = '已完成'
      } else if (normalizedQuery.includes('暂停')) {
        parsedParams.status = '已暂停'
      }

      // 解析预算范围
      const budgetMatches = normalizedQuery.match(/预算(大于|超过|高于)(\d+)(万|百万|千万)?/) ||
                             normalizedQuery.match(/(\d+)(万|百万|千万)(以上|及以上)的?(预算)?/)

      if (budgetMatches) {
        let amount = parseInt(budgetMatches[2])
        const unit = budgetMatches[3] || ''

        if (unit === '万') {
          amount = amount * 10000
        } else if (unit === '百万') {
          amount = amount * 1000000
        } else if (unit === '千万') {
          amount = amount * 10000000
        }

        parsedParams.budgetRange = { min: amount, max: 0 }
      }

      // 解析进度范围
      const progressMatches = normalizedQuery.match(/进度(小于|低于|少于|不足)(\d+)%/) ||
                              normalizedQuery.match(/进度(大于|高于|超过)(\d+)%/)

      if (progressMatches) {
        const comparison = progressMatches[1]
        const value = parseInt(progressMatches[2])

        if (['小于', '低于', '少于', '不足'].includes(comparison)) {
          parsedParams.progressRange = { min: 0, max: value }
        } else {
          parsedParams.progressRange = { min: value, max: 100 }
        }
      }

      // 解析负责人
      const managerMatches = normalizedQuery.match(/(张三|李四|王五|赵六|钱七)负责的/)
      if (managerMatches) {
        parsedParams.manager = managerMatches[1]
      }

      // 解析部门
      const departments = ['开发部', '设备部', '安全部', '勘探部', '环保部']
      for (const dept of departments) {
        if (normalizedQuery.includes(dept)) {
          parsedParams.department = dept
          break
        }
      }

      // 解析排序要求 - 这需要自定义处理
      let sortKey = ''
      let sortDirection: 'asc' | 'desc' = 'asc'

      if (normalizedQuery.includes('按预算') || normalizedQuery.includes('预算排序')) {
        sortKey = 'budget'
      } else if (normalizedQuery.includes('按进度') || normalizedQuery.includes('进度排序')) {
        sortKey = 'progress'
      } else if (normalizedQuery.includes('按日期') || normalizedQuery.includes('时间排序')) {
        sortKey = 'startDate'
      }

      if (normalizedQuery.includes('降序') || normalizedQuery.includes('从高到低') ||
          normalizedQuery.includes('最多') || normalizedQuery.includes('最高')) {
        sortDirection = 'desc'
      } else if (normalizedQuery.includes('升序') || normalizedQuery.includes('从低到高') ||
                 normalizedQuery.includes('最少') || normalizedQuery.includes('最低')) {
        sortDirection = 'asc'
      } else if (sortKey && !['最近'].includes(normalizedQuery)) {
        // 默认为降序，除非是时间相关
        sortDirection = 'desc'
      }

      if (sortKey) {
        setSortConfig({
          key: sortKey,
          direction: sortDirection
        })
      }

      // 限制数量
      const limitMatches = normalizedQuery.match(/(前|最[近新]的|最|近)(\d+)个?/)
      let limit = 0
      if (limitMatches) {
        limit = parseInt(limitMatches[2])
      }

      // 应用解析出的条件到过滤器
      let filteredProjects = projects.filter(project => {
        // 工程类型匹配
        if (parsedParams.type && project.type !== parsedParams.type) {
          return false
        }

        // 状态匹配
        if (parsedParams.status && project.status !== parsedParams.status) {
          return false
        }

        // 预算范围匹配
        if (parsedParams.budgetRange?.min && project.budget < parsedParams.budgetRange.min) {
          return false
        }
        if (parsedParams.budgetRange?.max && parsedParams.budgetRange.max > 0 &&
            project.budget > parsedParams.budgetRange.max) {
          return false
        }

        // 进度范围匹配
        if (parsedParams.progressRange?.min &&
            parsedParams.progressRange.min > 0 &&
            project.progress < parsedParams.progressRange.min) {
          return false
        }
        if (parsedParams.progressRange?.max &&
            parsedParams.progressRange.max < 100 &&
            project.progress > parsedParams.progressRange.max) {
          return false
        }

        // 负责人匹配
        if (parsedParams.manager && project.manager !== parsedParams.manager) {
          return false
        }

        // 部门匹配
        if (parsedParams.department && project.department !== parsedParams.department) {
          return false
        }

        // 如果没有明确的过滤条件但有关键词，尝试匹配名称、类型等
        if (!Object.keys(parsedParams).length && normalizedQuery) {
          return project.name.toLowerCase().includes(normalizedQuery) ||
                 project.type.toLowerCase().includes(normalizedQuery) ||
                 project.status.toLowerCase().includes(normalizedQuery) ||
                 project.manager.toLowerCase().includes(normalizedQuery) ||
                 project.department.toLowerCase().includes(normalizedQuery);
        }

        return true
      })

      // 应用排序
      if (sortConfig.key) {
        filteredProjects.sort((a, b) => {
          // 类型安全的索引访问
          const aValue = sortConfig.key === 'budget' ? a.budget :
                       sortConfig.key === 'progress' ? a.progress :
                       sortConfig.key === 'startDate' ? a.startDate :
                       '';
          const bValue = sortConfig.key === 'budget' ? b.budget :
                       sortConfig.key === 'progress' ? b.progress :
                       sortConfig.key === 'startDate' ? b.startDate :
                       '';

          if (typeof aValue === 'number' && typeof bValue === 'number') {
            return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue
          } else if (typeof aValue === 'string' && typeof bValue === 'string') {
            return sortConfig.direction === 'asc'
              ? aValue.localeCompare(bValue)
              : bValue.localeCompare(aValue)
          }
          return 0
        })
      }

      // 应用结果限制
      if (limit > 0 && filteredProjects.length > limit) {
        filteredProjects = filteredProjects.slice(0, limit)
      }

      // 生成结果解释
      let explanation = `根据您的查询 "${query}"，我理解您想要找：`

      // 添加过滤条件解释
      const conditions = []
      if (parsedParams.type) conditions.push(`类型为"${parsedParams.type}"的工程`)
      if (parsedParams.status) conditions.push(`状态为"${parsedParams.status}"的工程`)
      if (parsedParams.manager) conditions.push(`${parsedParams.manager}负责的工程`)
      if (parsedParams.department) conditions.push(`${parsedParams.department}的工程`)

      if (parsedParams.budgetRange?.min)
        conditions.push(`预算大于${(parsedParams.budgetRange.min/10000).toFixed(0)}万元的工程`)

      if (parsedParams.progressRange?.min && parsedParams.progressRange.min > 0)
        conditions.push(`进度大于${parsedParams.progressRange.min}%的工程`)

      if (parsedParams.progressRange?.max && parsedParams.progressRange.max < 100)
        conditions.push(`进度小于${parsedParams.progressRange.max}%的工程`)

      if (conditions.length > 0) {
        explanation += `\n${conditions.join('、')}`
      } else {
        explanation += `\n包含"${normalizedQuery}"关键词的所有工程`
      }

      // 添加排序解释
      if (sortConfig.key) {
        const sortFieldMap: Record<string, string> = {
          'budget': '预算',
          'progress': '进度',
          'startDate': '开始日期'
        }

        explanation += `\n并按${sortFieldMap[sortConfig.key] || sortConfig.key}${sortConfig.direction === 'asc' ? '从低到高' : '从高到低'}排序`
      }

      // 添加限制解释
      if (limit > 0) {
        explanation += `\n只显示前${limit}个结果`
      }

      explanation += `\n\n共找到 ${filteredProjects.length} 个符合条件的工程`

      setAiExplanation(explanation)

      // 更新搜索历史
      historyItem.results = filteredProjects.length
      setSearchHistory(prev => [historyItem, ...prev.slice(0, 9)])

      // 更新结果
      setSearchResults(filteredProjects)
        setHasSearched(true)

        toast({
        title: "AI 智能查询完成",
        description: `找到 ${filteredProjects.length} 个符合条件的工程项目`,
        })
    } catch (error) {
      console.error("AI 查询失败:", error)
      toast({
        title: "查询失败",
        description: "AI 处理查询时出错，请重试或调整查询语句",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 添加一个处理收藏历史记录的函数
  const toggleFavoriteHistory = (id: string) => {
    setSearchHistory(prevHistory =>
      prevHistory.map(item =>
        item.id === id
          ? { ...item, isFavorite: !item.isFavorite }
          : item
      )
    )
  }

  // 添加一个重用历史查询的函数
  const reuseHistoryQuery = (historyItem: SearchHistory) => {
    if (historyItem.type === 'ai') {
      setAiQuery(historyItem.query)
      setSearchMode('ai')
      handleAISearch(historyItem.query)
    } else {
      // 实现基础和高级搜索的重用逻辑
      setSearchMode(historyItem.type)
      // 这里需要解析查询并设置相应的搜索参数
    }
  }

  // 添加一个清除历史记录的函数
  const clearSearchHistory = () => {
    // 保留收藏的记录
    setSearchHistory(prevHistory => prevHistory.filter(item => item.isFavorite))
    toast({
      title: "历史记录已清除",
      description: "非收藏的历史记录已被清除",
    })
  }

  // 更新AI搜索组件
  const AISearchSection = () => {
    const [showSuggestions, setShowSuggestions] = useState(false)

    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-4">
          <div className="flex items-center gap-2 mb-2 text-blue-700">
            <Cpu className="h-5 w-5" />
            <h3 className="font-medium">AI 智能检索助手</h3>
          </div>
          <p className="text-sm text-blue-600 mb-2">
            您可以使用自然语言描述您想要查找的工程，例如"查找环保部门负责的预算超过300万的工程"
          </p>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <div className="absolute left-3 top-3 text-gray-400">
              <Sparkles className="h-4 w-4" />
            </div>
          <Input
              placeholder="使用自然语言描述您要查找的工程..."
            value={aiQuery}
              onChange={(e) => {
                setAiQuery(e.target.value)
                setShowSuggestions(e.target.value.length > 0)
              }}
              className="pl-10 pr-10 py-6 text-base"
            />
            {aiQuery && (
              <button
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                onClick={() => {
                  setAiQuery('')
                  setShowSuggestions(false)
                }}
              >
                <XCircle className="h-4 w-4" />
              </button>
            )}
        </div>
          <Button
            onClick={() => handleAISearch(aiQuery)}
            disabled={!aiQuery.trim() || isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-6 h-auto"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                查询中...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                AI 查询
              </>
            )}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          <Button
            variant="outline"
            className={`justify-start ${activeAiFeature === 'search' ? 'bg-blue-50 border-blue-200' : ''}`}
            onClick={() => setActiveAiFeature('search')}
          >
            <Search className="h-4 w-4 mr-2 text-blue-600" />
            <span>智能搜索</span>
          </Button>
          <Button
            variant="outline"
            className={`justify-start ${activeAiFeature === 'analyze' ? 'bg-blue-50 border-blue-200' : ''}`}
            onClick={() => setActiveAiFeature('analyze')}
          >
            <BarChart className="h-4 w-4 mr-2 text-green-600" />
            <span>数据分析</span>
          </Button>
          <Button
            variant="outline"
            className={`justify-start ${activeAiFeature === 'recommend' ? 'bg-blue-50 border-blue-200' : ''}`}
            onClick={() => setActiveAiFeature('recommend')}
          >
            <Lightbulb className="h-4 w-4 mr-2 text-amber-600" />
            <span>智能推荐</span>
          </Button>
        </div>

        {activeAiFeature === 'search' && (
          <>
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">智能搜索示例</h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 text-blue-600"
                onClick={() => setShowSearchHistory(!showSearchHistory)}
              >
                <History className="h-4 w-4 mr-2" />
                搜索历史
              </Button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              {aiSearchSuggestions.slice(0, 4).map(suggestion => (
                <Button
                  key={suggestion.id}
                  variant="outline"
                  className="justify-start h-auto py-3 text-left"
                  onClick={() => {
                    setAiQuery(suggestion.text)
                    handleAISearch(suggestion.text)
                  }}
                >
                  <div>
                    <div className="flex items-center text-sm mb-1">
                      <span className="px-2 py-0.5 rounded-full bg-blue-100 text-blue-700 text-xs mr-2">
                        {suggestion.category}
                      </span>
                    </div>
                    <div className="flex items-center justify-between w-full">
                      <span className="text-sm text-gray-600">{suggestion.text}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </Button>
              ))}
            </div>

            {showSearchHistory && searchHistory.length > 0 && (
              <div className="border rounded-lg mt-4">
                <div className="flex justify-between items-center p-3 border-b bg-gray-50">
                  <h3 className="font-medium">最近的搜索</h3>
                  <Button variant="ghost" size="sm" onClick={clearSearchHistory}>
                    <RotateCcw className="h-3 w-3 mr-1" />
                    清除历史
                  </Button>
                </div>
                <div className="divide-y max-h-64 overflow-y-auto">
                  {searchHistory.map(item => (
                    <div key={item.id} className="p-3 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between">
                        <div className="flex items-center">
                          {item.type === 'ai' ? (
                            <Sparkles className="h-4 w-4 text-blue-500 mr-2" />
                          ) : (
                            <Search className="h-4 w-4 text-gray-500 mr-2" />
                          )}
                          <button
                            className="text-sm text-left hover:text-blue-600 transition-colors overflow-hidden text-ellipsis"
                            onClick={() => reuseHistoryQuery(item)}
                          >
                            {item.query}
                          </button>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500">
                            {item.results !== undefined ? `${item.results}个结果` : ''}
                          </span>
                          <button
                            className="text-gray-400 hover:text-amber-500"
                            onClick={() => toggleFavoriteHistory(item.id)}
                          >
                            {item.isFavorite ? (
                              <Star className="h-4 w-4 text-amber-500" />
                            ) : (
                              <StarOff className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(item.timestamp).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        {activeAiFeature === 'analyze' && (
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <BarChart className="h-5 w-5 text-green-600" />
              <h3 className="font-medium">项目数据分析</h3>
            </div>

            <div className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setAiQuery("分析各部门的工程进度情况")}
              >
                分析各部门的工程进度情况
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setAiQuery("统计不同类型工程的平均预算")}
              >
                统计不同类型工程的平均预算
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setAiQuery("找出进度与预算消耗不匹配的工程")}
              >
                找出进度与预算消耗不匹配的工程
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        )}

        {activeAiFeature === 'recommend' && (
          <div className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <Lightbulb className="h-5 w-5 text-amber-600" />
              <h3 className="font-medium">智能推荐</h3>
            </div>

            <div className="space-y-4">
              <div className="bg-amber-50 p-3 rounded-md">
                <div className="flex items-start gap-2">
                  <Info className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-800">需要关注的工程</h4>
                    <p className="text-sm text-amber-700 mt-1">
                      根据您的搜索历史和当前项目状态，以下工程可能需要您的关注
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setAiQuery("查找进度低于30%且已经过半的工程")}
                >
                  <AlertTriangle className="h-4 w-4 mr-2 text-amber-600" />
                  进度落后的工程
                  <span className="ml-auto text-xs bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full">
                    3个工程
                  </span>
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setAiQuery("查找预算使用超过70%但进度低于50%的工程")}
                >
                  <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
                  预算消耗异常的工程
                  <span className="ml-auto text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">
                    2个工程
                  </span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {aiExplanation && hasSearched && (
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mt-4 text-blue-800">
            <div className="flex items-center gap-2 border-b border-blue-100 pb-2 mb-2">
              <Info className="h-5 w-5" />
              <h3 className="font-medium">AI 理解了您的查询</h3>

              <div className="ml-auto flex items-center gap-2">
                <button className="p-1 hover:bg-blue-100 rounded-full transition-colors">
                  <ThumbsUp className="h-4 w-4" />
                </button>
                <button className="p-1 hover:bg-blue-100 rounded-full transition-colors">
                  <ThumbsDown className="h-4 w-4" />
                </button>
              </div>
            </div>
            <div className="text-sm whitespace-pre-line">
              {aiExplanation}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 修改 DashboardView 组件
  const DashboardView = () => {
    const { pieChartData, budgetData, progressData } = useMemo(() => {
      const statusData = projects.reduce((acc, project) => {
        const status = project.status
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const pieData = Object.entries(statusData).map(([name, value]) => ({
        name,
        value
      }))

      const bData = projects.map(project => ({
        name: project.name,
        预算: project.budget,
        实际支出: project.budget * (project.progress / 100)
      }))

      const pData = projects.map(project => ({
        name: project.name,
        进度: project.progress
      }))

      return {
        pieChartData: pieData,
        budgetData: bData,
        progressData: pData
      }
    }, [projects])

    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']

    const CustomTooltip = useCallback(({ active, payload, label }: any) => {
      if (active && payload && payload.length) {
        return (
          <div className="bg-white p-2 border rounded shadow">
            <p className="font-medium">{label}</p>
            {payload.map((item: any, index: number) => (
              <p key={index} style={{ color: item.color }}>
                {item.name}: {item.value.toLocaleString()}
              </p>
            ))}
          </div>
        )
      }
      return null
    }, [])

    // 添加自定义样式
    const chartStyles = {
      pieChart: {
        background: 'linear-gradient(to right bottom, #ffffff, #f8f9fa)',
        borderRadius: '12px',
        padding: '16px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '8px',
        padding: '12px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0'
      }
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
              工程状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div style={{ width: '100%', height: 240 }} className="p-4">
              <ResponsiveContainer>
                <PieChart style={chartStyles.pieChart}>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={CustomTooltip} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent">
              预算执行情况
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div style={{ width: '100%', height: 240 }} className="p-4">
              <ResponsiveContainer>
                <BarChart data={budgetData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip content={CustomTooltip} />
                  <Legend />
                  <Bar dataKey="预算" fill="#8884d8" />
                  <Bar dataKey="实际支出" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-red-600 to-red-400 bg-clip-text text-transparent">
              进度追踪
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div style={{ width: '100%', height: 240 }} className="p-4">
              <ResponsiveContainer>
                <LineChart data={progressData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip content={CustomTooltip} />
                  <Legend />
                  <Line type="monotone" dataKey="进度" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 添加关联分析功能
  const ProjectRelationView = () => {
    // 准备符合 Edge 接口的数据
    const projectRelations = projects.reduce<Edge[]>((acc, _, index) => {
      if (index < projects.length - 1) {
        acc.push({
          sourceId: projects[index].id,
          targetId: projects[index + 1].id,
          relationType: "依赖",
          source: projects[index],
          target: projects[index + 1]
        })
      }
      return acc
    }, [])

    const handleProjectClick = (node: Node) => {
      console.log("项目点击:", node)
    }

    return (
      <Card className="mt-4">
        <CardHeader>
          <CardTitle>工程关联分析</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <NetworkGraph
              nodes={projects}
              edges={projectRelations}
              onNodeClick={handleProjectClick}
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // 修改进度分析组件
  const AnalysisTools = () => {
    const [selectedDimension, setSelectedDimension] = useState<'department' | 'type' | 'manager' | 'status'>('department')

    // 使用类型安全的数据处理
    const getAnalysisData = () => {
      const groupedData = projects.reduce((acc, project) => {
        const key = project[selectedDimension]
        if (typeof key === 'string') {
          if (!acc[key]) {
            acc[key] = {
              name: key,
              平均进度: 0,
              项目数: 0
            }
          }
          acc[key].平均进度 += project.progress
          acc[key].项目数 += 1
        }
        return acc
      }, {} as Record<string, { name: string; 平均进度: number; 项目数: number }>)

      return Object.values(groupedData).map(item => ({
        ...item,
        平均进度: Math.round(item.平均进度 / item.项目数)
      }))
    }

    return (
      <div className="flex gap-4 mb-6">
        <Card className="flex-1">
          <CardHeader>
            <CardTitle>进度分析</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={selectedDimension}
              onValueChange={(value: 'department' | 'type' | 'manager' | 'status') => setSelectedDimension(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分析维度" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="department">按部门</SelectItem>
                <SelectItem value="type">按类型</SelectItem>
                <SelectItem value="manager">按负责人</SelectItem>
                <SelectItem value="status">按状态</SelectItem>
              </SelectContent>
            </Select>

            <div className="mt-4 h-[300px]">
              <BarChart
                width={500}
                height={300}
                data={getAnalysisData()}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="平均进度" fill="#8884d8" />
                <Bar dataKey="项目数" fill="#82ca9d" />
              </BarChart>
            </div>
          </CardContent>
        </Card>

        <Card className="flex-1">
          <CardHeader>
            <CardTitle>风险预警</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 风险预警列表 */}
              <div className="flex items-center gap-2 text-yellow-500">
                <AlertTriangle className="h-4 w-4" />
                <span>3个工程进度落后</span>
              </div>
              <div className="flex items-center gap-2 text-red-500">
                <AlertCircle className="h-4 w-4" />
                <span>2个工程预算超支</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 添加工作流追踪组件
  interface WorkflowStep {
    id: string
    name: string
    status: 'pending' | 'processing' | 'completed' | 'blocked'
    dependsOn: string[]
    assignee: string
    deadline: string
  }

  const WorkflowTracker = () => {
    const [workflows, setWorkflows] = useState<WorkflowStep[]>([
      {
        id: '1',
        name: '设计审批',
        status: 'completed',
        dependsOn: [],
        assignee: '张工',
        deadline: '2025-03-20'
      },
      {
        id: '2',
        name: '材料采购',
        status: 'processing',
        dependsOn: ['1'],
        assignee: '李工',
        deadline: '2025-04-01'
      },
      // ...其他步骤
    ])

    return (
      <Card className="hover:shadow-lg transition-all duration-200 border border-gray-100">
        <CardHeader className="space-y-1.5">
          <CardTitle className="text-xl font-semibold text-gray-900">
            工作流程追踪
          </CardTitle>
          <CardDescription className="text-gray-500">
            实时监控项目各阶段进展
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflows.map(step => (
              <div key={step.id} className="flex items-center gap-4 p-3 border rounded-lg">
                <div className={`w-2 h-2 rounded-full ${
                  step.status === 'completed' ? 'bg-green-500' :
                  step.status === 'processing' ? 'bg-blue-500' :
                  step.status === 'blocked' ? 'bg-red-500' : 'bg-gray-500'
                }`} />
                <div className="flex-1">
                  <div className="font-medium">{step.name}</div>
                  <div className="text-sm text-muted-foreground">
                    负责人: {step.assignee} | 截止日期: {step.deadline}
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  查看详情
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 添加资源分配组件
  interface Resource {
    id: string
    name: string
    type: '人力' | '设备' | '材料'
    status: '空闲' | '占用' | '维护中'
    currentProject?: string
    availableDate: string
  }

  const ResourceAllocationAssistant = () => {
    const [resources, setResources] = useState<Resource[]>([
      {
        id: '1',
        name: '挖掘机A',
        type: '设备',
        status: '占用',
        currentProject: '矿区A3开发项目',
        availableDate: '2025-04-15'
      },
      // ...其他资源
    ])

    return (
      <Card className="hover:shadow-lg transition-all duration-200 border border-gray-100">
        <CardHeader className="space-y-1.5">
          <CardTitle className="text-xl font-semibold text-gray-900">
            智能资源分配
          </CardTitle>
          <CardDescription className="text-gray-500">
            AI辅助资源调配建议
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">资源利用率</h3>
                <div className="text-2xl font-bold text-green-500">87%</div>
                <p className="text-sm text-muted-foreground">较上月提升5%</p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">空闲资源</h3>
                <div className="text-2xl font-bold text-blue-500">12</div>
                <p className="text-sm text-muted-foreground">可立即调配</p>
              </div>
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">资源冲突</h3>
                <div className="text-2xl font-bold text-red-500">3</div>
                <p className="text-sm text-muted-foreground">需要协调</p>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-medium mb-4">智能调配建议</h3>
              <div className="space-y-2">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm">建议将设备"挖掘机A"在4月15日后调配至"新矿区勘探"项目</p>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm">注意：3个项目在下周将出现人力资源冲突，建议提前协调</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 添加项目健康度监控组件
  interface HealthMetric {
    name: string
    score: number
    trend: '上升' | '下降' | '稳定'
    issues: string[]
  }

  const ProjectHealthMonitor = () => {
    const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([
      {
        name: '进度指标',
        score: 85,
        trend: '上升',
        issues: ['部分子任务延期']
      },
      {
        name: '成本指标',
        score: 92,
        trend: '稳定',
        issues: []
      },
      {
        name: '质量指标',
        score: 78,
        trend: '下降',
        issues: ['材料质量波动', '返工率上升']
      }
    ])

    return (
      <Card className="hover:shadow-lg transition-all duration-200 border border-gray-100">
        <CardHeader className="space-y-1.5">
          <CardTitle className="text-xl font-semibold text-gray-900">
            项目健康度监控
          </CardTitle>
          <CardDescription className="text-gray-500">
            实时监控项目健康状况
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {healthMetrics.map(metric => (
              <div key={metric.name} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{metric.name}</span>
                  <span className={`text-sm ${
                    metric.trend === '上升' ? 'text-green-500' :
                    metric.trend === '下降' ? 'text-red-500' : 'text-blue-500'
                  }`}>
                    {metric.trend}
                  </span>
                </div>
                <div className="w-full h-2 bg-gray-100 rounded-full">
                  <div
                    className={`h-full rounded-full ${
                      metric.score >= 90 ? 'bg-green-500' :
                      metric.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${metric.score}%` }}
                  />
                </div>
                {metric.issues.length > 0 && (
                  <div className="text-sm text-red-500">
                    {metric.issues.map(issue => (
                      <div key={issue}>• {issue}</div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 添加 handleSort 函数
  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }))

    // 根据排序配置对搜索结果进行排序
    const sortedResults = [...searchResults].sort((a, b) => {
      if (key === 'budget') {
        return sortConfig.direction === 'asc' ?
          a.budget - b.budget :
          b.budget - a.budget
      }
      return 0
    })

    setSearchResults(sortedResults)
  }

  // 查看详情功能
  const handleViewDetails = (project: any) => {
    // 实现查看详情的逻辑
    console.log('查看详情:', project)
    // 可以打开一个对话框显示详细信息
  }

  // 编辑项目功能
  const handleEditProject = (project: any) => {
    // 实现编辑功能的逻辑
    console.log('编辑项目:', project)
    // 可以打开一个编辑对话框
  }

  // 刷新功能
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true)
      setIsLoading(true)

      // 重置所有状态
      setSearchParams({
        keyword: '',
        type: '',
        status: '',
        manager: '',
        department: '',
        dateRange: { start: '', end: '' },
        budgetRange: { min: 0, max: 0 },
        progressRange: { min: 0, max: 100 }
      })
      setSearchResults([])
      setHasSearched(false)

      // 模拟加载延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "刷新成功",
        description: "数据已重新加载",
      })
    } catch (error) {
      console.error('刷新失败:', error)
      toast({
        title: "刷新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
      setIsLoading(false)
    }
  }

  // 添加导出函数
  const handleExport = () => {
    try {
      // 准备导出数据
      const exportData = searchResults.length > 0 ? searchResults : projects

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData.map(project => ({
        '工程名称': project.name,
        '类型': project.type,
        '负责人': project.manager,
        '所属部门': project.department,
        '开始日期': project.startDate,
        '结束日期': project.endDate,
        '预算': project.budget,
        '进度': `${project.progress}%`,
        '状态': project.status
      })))

      // 创建工作簿
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '工程项目')

      // 生成文件并下载
      XLSX.writeFile(wb, '工程项目列表.xlsx')

      toast({
        title: "导出成功",
        description: "数据已成功导出为 Excel 文件",
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: "导出失败",
        description: "导出过程中发生错误，请重试",
        variant: "destructive",
      })
    }
  }

  // 添加导入函数
  const handleImport = async (file: File) => {
    try {
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取第一个工作表
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]

        // 转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        // 处理导入的数据
        const importedProjects = jsonData.map((row: any) => ({
          id: String(Math.random()).slice(2), // 生成临时ID
          name: row['工程名称'],
          type: row['类型'],
          manager: row['负责人'],
          department: row['所属部门'],
          startDate: row['开始日期'],
          endDate: row['结束日期'],
          budget: Number(row['预算']),
          progress: Number(String(row['进度']).replace('%', '')),
          status: row['状态']
        }))

        // 更新项目列表
        setSearchResults(importedProjects)
        setHasSearched(true)

        toast({
          title: "导入成功",
          description: `成功导入 ${importedProjects.length} 条记录`,
        })
      }

      reader.readAsArrayBuffer(file)
    } catch (error) {
      console.error('导入失败:', error)
      toast({
        title: "导入失败",
        description: "导入过程中发生错误，请检查文件格式",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          工程查询
        </h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-amber-50 hover:text-amber-600 transition-colors"
          >
            <Settings className="h-4 w-4 mr-2" />
            高级设置
          </Button>
          <Button
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white transition-colors"
            onClick={handleSearch}
          >
            <Search className="h-4 w-4 mr-2" />
            查询
          </Button>
        </div>
      </div>

      {/* 标准查询条件卡片 */}
      <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 pb-8">
          <CardTitle className="text-xl font-semibold">查询条件</CardTitle>
          <CardDescription>设置工程查询条件</CardDescription>
        </CardHeader>
        <CardContent className="mt-[-2rem] bg-white rounded-t-2xl px-6 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="project-name">工程名称</Label>
              <Input
                id="project-name"
                placeholder="输入工程名称关键词"
                value={searchParams.keyword}
                onChange={(e) => handleParamChange('keyword', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="project-type">工程类型</Label>
              <Select>
                <SelectTrigger id="project-type">
                  <SelectValue placeholder="选择工程类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="mining">采矿工程</SelectItem>
                  <SelectItem value="equipment">设备工程</SelectItem>
                  <SelectItem value="safety">安全工程</SelectItem>
                  <SelectItem value="exploration">勘探工程</SelectItem>
                  <SelectItem value="environmental">环保工程</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="project-status">工程状态</Label>
              <Select>
                <SelectTrigger id="project-status">
                  <SelectValue placeholder="选择工程状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="in-progress">进行中</SelectItem>
                  <SelectItem value="preparing">准备中</SelectItem>
                  <SelectItem value="completed">已完成</SelectItem>
                  <SelectItem value="paused">已暂停</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="project-manager">负责人</Label>
              <Input id="project-manager" placeholder="输入负责人姓名" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="department">所属部门</Label>
              <Select>
                <SelectTrigger id="department">
                  <SelectValue placeholder="选择所属部门" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有部门</SelectItem>
                  <SelectItem value="development">开发部</SelectItem>
                  <SelectItem value="equipment">设备部</SelectItem>
                  <SelectItem value="safety">安全部</SelectItem>
                  <SelectItem value="exploration">勘探部</SelectItem>
                  <SelectItem value="environmental">环保部</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date-range">日期范围</Label>
              <div className="flex items-center gap-2">
                <Input id="start-date" type="date" className="w-1/2" />
                <span>至</span>
                <Input id="end-date" type="date" className="w-1/2" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="budget-range">预算范围</Label>
              <div className="flex items-center gap-2">
                <Input type="number" placeholder="最小金额" />
                <span>-</span>
                <Input type="number" placeholder="最大金额" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="progress-range">进度范围</Label>
              <div className="flex items-center gap-2">
                <Input type="number" min="0" max="100" placeholder="最小进度" />
                <span>-</span>
                <Input type="number" min="0" max="100" placeholder="最大进度" />
              </div>
            </div>
          </div>

          <div className="mt-6 flex items-center gap-2">
            <Button
              onClick={handleSearch}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white transition-colors flex items-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  查询中...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  查询
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={handleReset}
              className="hover:bg-red-50 hover:text-red-600 transition-colors"
            >
              <RefreshCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <div className="ml-auto flex items-center gap-2">
              <Label htmlFor="file-import" className="cursor-pointer px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md flex items-center text-sm">
                <Upload className="h-4 w-4 mr-2" />
                导入数据
              </Label>
              <Input
                type="file"
                id="file-import"
                accept=".xlsx,.xls,.csv"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0]
                  if (file) handleImport(file)
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 快速查询选项 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button
          variant="outline"
          className="h-auto py-4 px-4 flex items-center justify-center hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 group"
          onClick={() => handleQuickSearch('in-progress')}
        >
          <div className="rounded-full bg-blue-100 p-2 mr-3 group-hover:bg-blue-200 transition-colors">
            <HardHat className="h-5 w-5 text-blue-600" />
          </div>
          <div className="text-left">
            <span className="block text-base font-medium">进行中的工程</span>
            <span className="text-xs text-muted-foreground">查看所有正在进行的工程项目</span>
          </div>
        </Button>
        <Button
          variant="outline"
          className="h-auto py-4 px-4 flex items-center justify-center hover:bg-amber-50 hover:border-amber-200 transition-all duration-200 group"
          onClick={() => handleQuickSearch('preparing')}
        >
          <div className="rounded-full bg-amber-100 p-2 mr-3 group-hover:bg-amber-200 transition-colors">
            <Clock className="h-5 w-5 text-amber-600" />
          </div>
          <div className="text-left">
            <span className="block text-base font-medium">即将开始的工程</span>
            <span className="text-xs text-muted-foreground">查看所有准备中的工程项目</span>
          </div>
        </Button>
        <Button
          variant="outline"
          className="h-auto py-4 px-4 flex items-center justify-center hover:bg-green-50 hover:border-green-200 transition-all duration-200 group"
          onClick={() => handleQuickSearch('completed')}
        >
          <div className="rounded-full bg-green-100 p-2 mr-3 group-hover:bg-green-200 transition-colors">
            <CheckCircle2 className="h-5 w-5 text-green-600" />
          </div>
          <div className="text-left">
            <span className="block text-base font-medium">已完成的工程</span>
            <span className="text-xs text-muted-foreground">查看所有已完成的工程项目</span>
          </div>
        </Button>
      </div>

      {/* AI智能查询卡片 */}
      <Card className="border border-blue-100 shadow-sm hover:shadow-md transition-shadow duration-200">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-blue-700">
            <Cpu className="h-5 w-5" />
            AI智能查询
          </CardTitle>
          <CardDescription>使用自然语言描述您要查找的工程</CardDescription>
        </CardHeader>
        <CardContent>
          <AISearchSection />
        </CardContent>
      </Card>

      {/* 查询结果区域 */}
      {hasSearched ? (
        <Card className="animate-fadeIn border-t-4 border-t-blue-500">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>查询结果</CardTitle>
                <CardDescription>共找到 {searchResults.length} 个工程项目</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <RefreshCcw className="h-4 w-4 mr-1" />
                  )}
                  刷新
                </Button>
              <Tabs defaultValue="all">
                <TabsList>
                  <TabsTrigger value="all">全部</TabsTrigger>
                  <TabsTrigger value="in-progress">进行中</TabsTrigger>
                  <TabsTrigger value="preparing">准备中</TabsTrigger>
                  <TabsTrigger value="completed">已完成</TabsTrigger>
                </TabsList>
              </Tabs>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-gray-50 to-gray-100">
                    <TableHead>工程名称</TableHead>
                    <TableHead>工程类型</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>所属部门</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>结束日期</TableHead>
                    <TableHead
                      onClick={() => handleSort('budget')}
                      className="cursor-pointer hover:bg-gray-200 transition-colors"
                    >
                      预算金额
                      {sortConfig.key === 'budget' && (
                        <span className="ml-1">{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </TableHead>
                    <TableHead
                      onClick={() => handleSort('progress')}
                      className="cursor-pointer hover:bg-gray-200 transition-colors"
                    >
                      进度
                      {sortConfig.key === 'progress' && (
                        <span className="ml-1">{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {searchResults.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8 text-muted-foreground">
                        <div className="flex flex-col items-center justify-center">
                          <Search className="h-8 w-8 text-gray-300 mb-2" />
                          <p>暂无符合条件的工程项目</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    searchResults.map((project) => (
                    <TableRow
                      key={project.id}
                      className="hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
                    >
                      <TableCell className="font-medium">{project.name}</TableCell>
                      <TableCell>{project.type}</TableCell>
                      <TableCell>{project.manager}</TableCell>
                      <TableCell>{project.department}</TableCell>
                      <TableCell>{project.startDate}</TableCell>
                      <TableCell>{project.endDate}</TableCell>
                      <TableCell>{project.budget.toLocaleString()} 元</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-[60px] h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                                className={`h-full rounded-full ${
                                  project.progress >= 70 ? 'bg-green-500' :
                                  project.progress >= 30 ? 'bg-blue-500' : 'bg-amber-500'
                                }`}
                              style={{ width: `${project.progress}%` }}
                            ></div>
                          </div>
                          <span>{project.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(project.status)}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                          onClick={() => handleViewDetails(project)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          详情
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="hover:bg-amber-50 hover:text-amber-600 transition-colors"
                          onClick={() => handleEditProject(project)}
                        >
                          <Pencil className="h-4 w-4 mr-1" />
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-4">
            <div className="text-sm text-muted-foreground">
              共 {searchResults.length} 条记录，当前显示 1-{Math.min(searchResults.length, 10)}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                上一页
              </Button>
              <Button variant="outline" size="sm" className="px-3 bg-blue-50 border-blue-300">
                1
              </Button>
              <Button variant="outline" size="sm" disabled>
                下一页
              </Button>
            </div>
          </CardFooter>
        </Card>
      ) : (
        <Card className="animate-fadeIn opacity-85 border border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <Search className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-medium mb-2">请输入查询条件</h3>
            <p className="text-muted-foreground text-center max-w-md">
              设置查询条件并点击"查询"按钮，系统将显示符合条件的工程项目
            </p>
          </CardContent>
        </Card>
      )}

      {/* 数据可视化区域 - 仅在有搜索结果时显示 */}
      {hasSearched && searchResults.length > 0 && (
        <>
          <h3 className="text-xl font-bold mt-8 mb-4 text-gray-800">数据分析与可视化</h3>
      <DashboardView />
        </>
      )}

      {isLoading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-8 w-8 rounded-full bg-white"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <p className="text-base font-medium text-gray-700">正在搜索数据...</p>
            <p className="text-sm text-gray-500">请稍候，这可能需要几秒钟时间</p>
          </div>
        </div>
      )}
    </div>
  )
}

