"use strict";
exports.__esModule = true;
exports.ChromaCard = void 0;
var react_1 = require("react");
var gsap_1 = require("gsap");
var theme_context_1 = require("@/contexts/theme-context");
require("./ChromaCard.css");
exports.ChromaCard = function (_a) {
    var title = _a.title, value = _a.value, icon = _a.icon, trend = _a.trend, _b = _a.color, color = _b === void 0 ? "#3B82F6" : _b, onClick = _a.onClick;
    var cardRef = react_1.useRef(null);
    var themeMode = theme_context_1.useTheme().themeMode;
    react_1.useEffect(function () {
        var card = cardRef.current;
        if (!card)
            return;
        var handleMouseMove = function (e) {
            var rect = card.getBoundingClientRect();
            var x = e.clientX - rect.left;
            var y = e.clientY - rect.top;
            card.style.setProperty("--mouse-x", x + "px");
            card.style.setProperty("--mouse-y", y + "px");
        };
        card.addEventListener("mousemove", handleMouseMove);
        return function () { return card.removeEventListener("mousemove", handleMouseMove); };
    }, []);
    react_1.useEffect(function () {
        var card = cardRef.current;
        if (!card)
            return;
        gsap_1.gsap.fromTo(card, {
            y: 20,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            ease: "power3.out"
        });
    }, []);
    return (React.createElement("div", { ref: cardRef, className: "chroma-stat-card " + (onClick ? 'clickable' : ''), onClick: onClick, style: {
            "--card-color": color,
            "--card-gradient": "linear-gradient(145deg, " + color + ", #000)"
        } },
        React.createElement("div", { className: "card-content" },
            React.createElement("div", { className: "icon-wrapper" }, icon),
            React.createElement("div", { className: "stat-info" },
                React.createElement("h3", { className: "stat-title" }, title),
                React.createElement("div", { className: "stat-value" }, value),
                trend && (React.createElement("div", { className: "trend " + (trend.value >= 0 ? 'positive' : 'negative') },
                    React.createElement("span", { className: "trend-value" },
                        trend.value >= 0 ? '+' : '',
                        trend.value,
                        "%"),
                    React.createElement("span", { className: "trend-label" }, trend.label))))),
        React.createElement("div", { className: "card-background" }),
        React.createElement("div", { className: "card-border" })));
};
