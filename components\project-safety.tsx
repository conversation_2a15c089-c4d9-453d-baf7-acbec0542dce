"use client"

import { useState, useMemo } from "react"
import {
  Alert<PERSON>riangle,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  Upload,
  Eye,
  Edit,
  Trash2,
  Ban,
  CheckCircle2,
  Clock,
  Shield,
  FileText,
  AlertCircle,
  Loader2,
  X,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

// 添加类型定义
interface SafetyRecord {
  id: string
  project: string
  checkType: string
  checkDate: string
  inspector: string
  status: string
  issues: number
  nextCheck: string
  description: string
  isDisabled: boolean
  department?: string
  location?: string
  riskLevel?: string
  measures?: string[]
  attachments?: string[]
}

interface FormData {
  project: string
  checkType: string
  checkDate: string
  inspector: string
  department: string
  location: string
  description: string
  measures: string[]
  attachments: string[]
}

export function ProjectSafety() {
  // 状态定义
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<any>(null)
  const { toast } = useToast()

  // 添加状态
  const [formData, setFormData] = useState<FormData>({
    project: "",
    checkType: "",
    checkDate: "",
    inspector: "",
    department: "",
    location: "",
    description: "",
    measures: [],
    attachments: []
  })
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [recordToDelete, setRecordToDelete] = useState<SafetyRecord | null>(null)
  const [selectedFilters, setSelectedFilters] = useState({
    department: "all",
    riskLevel: "all",
    dateRange: { start: "", end: "" }
  })

  // 示例数据
  const safetyRecords = [
    {
      id: "1",
      project: "矿区A3开发项目",
      checkType: "日常安全检查",
      checkDate: "2024-01-15",
      inspector: "张三",
      status: "正常",
      issues: 0,
      nextCheck: "2024-01-22",
      description: "设备运行正常，安全措施到位",
      isDisabled: false
    },
    {
      id: "2",
      project: "设备更新计划",
      checkType: "专项安全检查",
      checkDate: "2024-01-16",
      inspector: "李四",
      status: "警告",
      issues: 2,
      nextCheck: "2024-01-23",
      description: "发现部分设备存在安全隐患，需要及时处理",
      isDisabled: false
    },
    {
      id: "3",
      project: "安全系统升级",
      checkType: "季度安全评估",
      checkDate: "2024-01-17",
      inspector: "王五",
      status: "严重",
      issues: 5,
      nextCheck: "2024-01-24",
      description: "多处安全隐患，需要立即整改",
      isDisabled: true
    },
    // ... 更多数据
  ]

  // 筛选记录
  const filteredRecords = useMemo(() => {
    return safetyRecords.filter(record => {
      const matchesSearch = 
        record.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.inspector.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.checkType.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesStatus = selectedStatus === "all" ? true : record.status === selectedStatus
      
      return matchesSearch && matchesStatus
    })
  }, [safetyRecords, searchTerm, selectedStatus])

  // 更新统计数据
  const statistics = useMemo(() => {
    return {
      total: filteredRecords.length,
      normal: filteredRecords.filter(r => r.status === "normal").length,
      warning: filteredRecords.filter(r => r.status === "warning").length,
      critical: filteredRecords.filter(r => r.status === "critical").length
    }
  }, [filteredRecords])

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-500">正常</Badge>
      case "警告":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">警告</Badge>
      case "严重":
        return <Badge variant="destructive">严重</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 处理查看详情
  const handleViewDetails = (record: any) => {
    setCurrentRecord(record)
    setIsViewDetailsOpen(true)
  }

  // 处理导出
  const handleExport = async () => {
    setIsLoading(true)
    try {
      // 将数据转换为CSV格式
      const headers = ['工程项目', '检查类型', '检查日期', '检查人员', '问题数量', '状态']
      const csvData = safetyRecords.map(record => [
        record.project,
        record.checkType,
        record.checkDate,
        record.inspector,
        record.issues,
        record.status
      ])
      
      const csvContent = [headers, ...csvData].map(row => row.join(',')).join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `安全检查记录_${new Date().toLocaleDateString()}.csv`
      link.click()
      
    toast({
        title: "导出成功",
        description: "数据已成功导出为CSV文件",
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理导入
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    setIsLoading(true)
    try {
      const text = await file.text()
      const rows = text.split('\n').map(row => row.split(','))
      const headers = rows[0]
      
      const importedRecords = rows.slice(1).map(row => ({
        id: Math.random().toString(36).substr(2, 9),
        project: row[0],
        checkType: row[1],
        checkDate: row[2],
        inspector: row[3],
        issues: parseInt(row[4]),
        status: row[5],
        isDisabled: false
      }))
      
      // 这里应该调用API来保存导入的数据
      // setSafetyRecords([...safetyRecords, ...importedRecords])
      
      toast({
        title: "导入成功",
        description: `成功导入 ${importedRecords.length} 条记录`,
      })
      
      // 重置文件输入
      event.target.value = ''
    } catch (error) {
      toast({
        title: "导入失败",
        description: "请检查文件格式是否正确",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理禁用/启用
  const handleToggleStatus = async (record: any) => {
    setIsLoading(true)
    try {
      // 实现禁用/启用逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: record.isDisabled ? "已启用" : "已禁用",
        description: `安全检查记录已${record.isDisabled ? '启用' : '禁用'}`,
      })
    } catch (error) {
    toast({
        title: "操作失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理保存记录
  const handleSaveRecord = async () => {
    setIsLoading(true)
    try {
      // 这里添加保存记录的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "保存成功",
        description: "安全检查记录已保存",
      })
      setIsAddDialogOpen(false)
      // 重置表单
      setFormData({
        project: "",
        checkType: "",
        checkDate: "",
        inspector: "",
        department: "",
        location: "",
        description: "",
        measures: [],
        attachments: []
      })
    } catch (error) {
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理编辑记录
  const handleEditRecord = (record: SafetyRecord) => {
    setFormData({
      project: record.project,
      checkType: record.checkType,
      checkDate: record.checkDate,
      inspector: record.inspector,
      department: record.department || "",
      location: record.location || "",
      description: record.description,
      measures: record.measures || [],
      attachments: record.attachments || []
    })
    setIsViewDetailsOpen(false)
    setIsAddDialogOpen(true)
  }
  
  // 处理删除记录
  const handleDeleteRecord = (record: SafetyRecord) => {
    setRecordToDelete(record)
    setIsDeleteDialogOpen(true)
  }
  
  // 确认删除
  const handleConfirmDelete = async () => {
    if (!recordToDelete) return
    
    setIsLoading(true)
    try {
      // 这里添加删除记录的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "删除成功",
        description: "安全检查记录已删除",
      })
      setIsDeleteDialogOpen(false)
      setRecordToDelete(null)
    } catch (error) {
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.total}</h3>
            <p className="text-sm text-muted-foreground">总检查记录</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.normal}</h3>
            <p className="text-sm text-muted-foreground">正常记录</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.warning}</h3>
            <p className="text-sm text-muted-foreground">警告记录</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{statistics.critical}</h3>
            <p className="text-sm text-muted-foreground">严重问题</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  type="search" 
              placeholder="搜索..." 
                  className="pl-8 w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="normal">正常</SelectItem>
              <SelectItem value="warning">警告</SelectItem>
              <SelectItem value="critical">严重</SelectItem>
                </SelectContent>
              </Select>
            </div>
              <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <div className="relative">
            <input
              type="file"
              accept=".csv"
              onChange={handleImport}
              className="hidden"
              id="import-file"
            />
            <Button variant="outline" onClick={() => document.getElementById('import-file')?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              导入
              </Button>
            </div>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加检查
              </Button>
            </div>
          </div>

      {/* 主表格内容 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>安全检查记录</CardTitle>
              <CardDescription>查看和管理所有安全检查记录</CardDescription>
            </div>
            <Tabs defaultValue="all">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="daily">日常检查</TabsTrigger>
                <TabsTrigger value="special">专项检查</TabsTrigger>
                <TabsTrigger value="quarterly">季度评估</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>工程项目</TableHead>
                  <TableHead>检查类型</TableHead>
                  <TableHead>检查日期</TableHead>
                  <TableHead>检查人员</TableHead>
                  <TableHead>问题数量</TableHead>
                  <TableHead>下次检查</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.map((record) => (
                  <TableRow key={record.id} className={record.isDisabled ? 'opacity-50' : ''}>
                    <TableCell className="font-medium">{record.project}</TableCell>
                    <TableCell>{record.checkType}</TableCell>
                    <TableCell>{record.checkDate}</TableCell>
                    <TableCell>{record.inspector}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${
                          record.issues > 0 ? 'text-red-500' : 'text-green-500'
                        }`}>
                          {record.issues}
                        </span>
                        个问题
                      </div>
                    </TableCell>
                    <TableCell>{record.nextCheck}</TableCell>
                    <TableCell>{getStatusBadge(record.status)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-blue-50 hover:text-blue-600"
                        onClick={() => handleViewDetails(record)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        详情
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-amber-50 hover:text-amber-600"
                        onClick={() => handleToggleStatus(record)}
                      >
                        {record.isDisabled ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            启用
                          </>
                        ) : (
                          <>
                            <Ban className="h-4 w-4 mr-1" />
                            禁用
                          </>
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-red-50 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                              </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            共 {filteredRecords.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>检查记录详情</DialogTitle>
            <DialogDescription>查看安全检查的详细信息</DialogDescription>
          </DialogHeader>
          {currentRecord && (
            <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>工程项目</Label>
                  <p className="text-base mt-1">{currentRecord.project}</p>
              </div>
                <div>
                  <Label>检查类型</Label>
                  <p className="text-base mt-1">{currentRecord.checkType}</p>
              </div>
                <div>
                  <Label>检查日期</Label>
                  <p className="text-base mt-1">{currentRecord.checkDate}</p>
            </div>
                <div>
                  <Label>检查人员</Label>
                  <p className="text-base mt-1">{currentRecord.inspector}</p>
              </div>
                <div>
                  <Label>所属部门</Label>
                  <p className="text-base mt-1">{currentRecord.department}</p>
              </div>
                <div>
                  <Label>检查地点</Label>
                  <p className="text-base mt-1">{currentRecord.location}</p>
            </div>
                <div>
                  <Label>风险等级</Label>
                  <p className="text-base mt-1">{currentRecord.riskLevel}</p>
            </div>
                <div>
                  <Label>问题数量</Label>
                  <p className="text-base mt-1">{currentRecord.issues} 个</p>
              </div>
              </div>

              <div>
                <Label>问题描述</Label>
                <p className="text-base mt-1 whitespace-pre-wrap">{currentRecord.description}</p>
            </div>

              {currentRecord.measures && currentRecord.measures.length > 0 && (
                <div>
                  <Label>整改措施</Label>
                  <ul className="mt-2 space-y-2">
                    {currentRecord.measures.map((measure: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-500">•</span>
                        <span>{measure}</span>
                      </li>
                    ))}
                  </ul>
              </div>
              )}

              {currentRecord.attachments && currentRecord.attachments.length > 0 && (
                <div>
                  <Label>附件文件</Label>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    {currentRecord.attachments.map((file: string, index: number) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded">
                        <FileText className="h-4 w-4 text-blue-500" />
                        <span className="text-sm truncate">{file}</span>
              </div>
                    ))}
            </div>
            </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => handleEditRecord(currentRecord)}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </Button>
            <Button onClick={() => setIsViewDetailsOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加检查对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加安全检查</DialogTitle>
            <DialogDescription>创建新的安全检查记录</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project">工程项目</Label>
                <Select
                  value={formData.project}
                  onValueChange={(value) => setFormData({...formData, project: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择工程项目" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="project1">矿区A3开发项目</SelectItem>
                    <SelectItem value="project2">设备更新计划</SelectItem>
                    <SelectItem value="project3">安全系统升级</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="checkType">检查类型</Label>
                <Select
                  value={formData.checkType}
                  onValueChange={(value) => setFormData({...formData, checkType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择检查类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">日常安全检查</SelectItem>
                    <SelectItem value="special">专项安全检查</SelectItem>
                    <SelectItem value="quarterly">季度安全评估</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="checkDate">检查日期</Label>
                <Input 
                  id="checkDate"
                  type="date" 
                  value={formData.checkDate}
                  onChange={(e) => setFormData({...formData, checkDate: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="inspector">检查人员</Label>
                <Input 
                  id="inspector"
                  value={formData.inspector}
                  onChange={(e) => setFormData({...formData, inspector: e.target.value})}
                  placeholder="输入检查人员姓名"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
                <Label htmlFor="department">所属部门</Label>
              <Input 
                  id="department"
                  value={formData.department}
                  onChange={(e) => setFormData({...formData, department: e.target.value})}
                  placeholder="输入所属部门"
              />
            </div>
              <div className="space-y-2">
                <Label htmlFor="location">检查地点</Label>
                <Input 
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                  placeholder="输入检查地点"
                />
              </div>
            </div>

              <div className="space-y-2">
              <Label htmlFor="description">问题描述</Label>
              <Textarea 
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="详细描述发现的安全问题"
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label>整改措施</Label>
            <div className="space-y-2">
                {formData.measures.map((measure, index) => (
                  <div key={index} className="flex items-center gap-2">
              <Input 
                      value={measure}
                      onChange={(e) => {
                        const newMeasures = [...formData.measures]
                        newMeasures[index] = e.target.value
                        setFormData({...formData, measures: newMeasures})
                      }}
                      placeholder={`整改措施 ${index + 1}`}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        const newMeasures = formData.measures.filter((_, i) => i !== index)
                        setFormData({...formData, measures: newMeasures})
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
            </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFormData({...formData, measures: [...formData.measures, '']})}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加整改措施
                </Button>
            </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveRecord}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这条安全检查记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 加载状态 */}
      {isLoading && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <p className="text-sm text-gray-600">处理中...</p>
                    </div>
                      </div>
                    )}
    </div>
  )
}

