"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Calendar,
  Filter,
  FileText,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Upload,
  RefreshCw,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { message, Modal, Space, Upload as AntUpload, Progress, Row, Col, Statistic } from 'antd'
import { PlusOutlined, UploadOutlined, ExportOutlined, EyeOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import type { UploadProps } from 'antd'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx-js-style'

interface SafetyCheckItem {
  id: string
  name: string
  type: string
  department: string
  inspector: string
  date: string
  status: string
  issues: number
  description?: string
  checkItems?: string[]
  attachments?: string[]
  lastModified?: string
  modifiedBy?: string
  level?: string
  area?: string
}

export function SafetyCheck() {
  const [isAddCheckOpen, setIsAddCheckOpen] = useState(false)
  const [isViewModalVisible, setIsViewModalVisible] = useState(false)
  const [selectedCheck, setSelectedCheck] = useState<SafetyCheckItem | null>(null)
  const [searchText, setSearchText] = useState("")
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    type: "例行检查",
    department: "安全管理部",
    inspector: "",
    date: "",
    status: "计划中",
    area: "",
    checkItems: "",
    description: "",
    level: "一般"
  })
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheckItem[]>([
    {
      id: "1",
      name: "矿区A3安全例行检查",
      type: "例行检查",
      department: "安全管理部",
      inspector: "张三",
      date: "2025-03-01",
      status: "已完成",
      issues: 0,
      description: "对矿区A3进行安全例行检查，包括设备、人员、环境等方面。",
      checkItems: ["设备运行状态", "安全防护措施", "应急设施"],
      attachments: ["检查报告.docx", "现场照片.zip"],
      lastModified: "2023-12-01 15:30",
      modifiedBy: "张三",
      level: "一般",
      area: "A3区域"
    },
    {
      id: "2",
      name: "设备安全检查",
      type: "专项检查",
      department: "设备管理部",
      inspector: "李四",
      date: "2025-03-28",
      status: "已完成",
      issues: 2,
      description: "对关键设备进行专项安全检查。",
      checkItems: ["设备完整性", "运行参数", "维护记录"],
      attachments: ["设备检查表.xlsx"],
      lastModified: "2025-03-28 14:20",
      modifiedBy: "李四",
      level: "重要",
      area: "设备间"
    },
    {
      id: "3",
      name: "爆破区域安全检查",
      type: "专项检查",
      department: "安全管理部",
      inspector: "王五",
      date: "2025-03-25",
      status: "已完成",
      issues: 1,
      level: "紧急",
      area: "爆破区"
    },
    {
      id: "4",
      name: "矿区B2安全例行检查",
      type: "例行检查",
      department: "安全管理部",
      inspector: "赵六",
      date: "2025-04-02",
      status: "进行中",
      issues: 0,
      level: "一般",
      area: "B2区域"
    },
    {
      id: "5",
      name: "消防设备检查",
      type: "专项检查",
      department: "安全管理部",
      inspector: "张三",
      date: "2025-04-05",
      status: "计划中",
      issues: 0,
      level: "重要",
      area: "全区域"
    },
  ])

  // 统计数据
  const statistics = {
    total: safetyChecks.length,
    completed: safetyChecks.filter(check => check.status === "已完成").length,
    inProgress: safetyChecks.filter(check => check.status === "进行中").length,
    planned: safetyChecks.filter(check => check.status === "计划中").length,
    totalIssues: safetyChecks.reduce((acc, check) => acc + check.issues, 0),
    byType: {
      routine: safetyChecks.filter(check => check.type === "例行检查").length,
      special: safetyChecks.filter(check => check.type === "专项检查").length,
    },
    byLevel: {
      urgent: safetyChecks.filter(check => check.level === "紧急").length,
      important: safetyChecks.filter(check => check.level === "重要").length,
      normal: safetyChecks.filter(check => check.level === "一般").length,
    },
  }

  const handleView = (check: SafetyCheckItem) => {
    setSelectedCheck(check)
    setIsViewModalVisible(true)
  }

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条安全检查记录吗？",
      onOk: () => {
        setSafetyChecks(safetyChecks.filter(check => check.id !== id))
        message.success("记录已删除")
      }
    })
  }

  const handleStatusChange = (id: string, status: string) => {
    setSafetyChecks(safetyChecks.map(check =>
      check.id === id
        ? { ...check, status, lastModified: new Date().toLocaleString(), modifiedBy: "当前用户" }
        : check
    ))
    message.success("状态已更新")
  }

  // 导入Excel
  const handleImport = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        const newChecks = jsonData.map((item: any) => ({
          id: `check${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: item['检查名称'] || '',
          type: item['检查类型'] || '例行检查',
          department: item['负责部门'] || '',
          inspector: item['检查人员'] || '',
          date: item['检查日期'] || new Date().toLocaleDateString(),
          status: item['状态'] || '计划中',
          issues: item['问题数'] || 0,
          level: item['级别'] || '一般',
          area: item['区域'] || '',
        }))

        setSafetyChecks([...safetyChecks, ...newChecks])
        message.success(`成功导入 ${newChecks.length} 条记录`)
      } catch (error) {
        console.error('导入失败:', error)
        message.error('导入失败，请检查文件格式')
      }
    }
    reader.readAsArrayBuffer(file)
    return false
  }

  // 导出Excel
  const handleExport = () => {
    try {
      const exportData = safetyChecks.map(check => ({
        '检查名称': check.name,
        '检查类型': check.type,
        '负责部门': check.department,
        '检查人员': check.inspector,
        '检查日期': check.date,
        '状态': check.status,
        '问题数': check.issues,
        '级别': check.level,
        '区域': check.area,
        '最后修改时间': check.lastModified || '',
        '修改人': check.modifiedBy || '',
      }))

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 检查名称
        { wch: 15 }, // 检查类型
        { wch: 15 }, // 负责部门
        { wch: 15 }, // 检查人员
        { wch: 15 }, // 检查日期
        { wch: 10 }, // 状态
        { wch: 10 }, // 问题数
        { wch: 10 }, // 级别
        { wch: 15 }, // 区域
        { wch: 20 }, // 最后修改时间
        { wch: 15 }, // 修改人
      ]
      ws['!cols'] = colWidths

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      }

      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1')
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1"
        if (!ws[address]) continue
        ws[address].s = headerStyle
      }

      XLSX.utils.book_append_sheet(wb, ws, '安全检查记录')
      // 使用2025年的固定日期而不是当前日期
      XLSX.writeFile(wb, `安全检查记录_2025-03-15.xlsx`)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 上传配置
  const uploadProps: UploadProps = {
    accept: '.xlsx,.xls',
    showUploadList: false,
    beforeUpload: handleImport
  }

  // 筛选数据
  const filteredChecks = safetyChecks.filter(check => {
    const matchesSearch = check.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         check.inspector.toLowerCase().includes(searchText.toLowerCase()) ||
                         check.department.toLowerCase().includes(searchText.toLowerCase())
    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(check.type)
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(check.status)
    const matchesDepartment = selectedDepartments.length === 0 || selectedDepartments.includes(check.department)
    return matchesSearch && matchesType && matchesStatus && matchesDepartment
  })

  // 添加处理表单变化的函数
  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 添加表单提交函数
  const handleSubmit = () => {
    const checkItems = formData.checkItems
      .split('\n')
      .filter(item => item.trim() !== '')

    const newCheck: SafetyCheckItem = {
      id: `check${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: formData.name,
      type: formData.type,
      department: formData.department,
      inspector: formData.inspector,
      date: formData.date,
      status: formData.status,
      issues: 0,
      area: formData.area,
      checkItems: checkItems,
      description: formData.description,
      lastModified: new Date().toLocaleString(),
      modifiedBy: "当前用户",
      level: formData.level
    }

    setSafetyChecks(prev => [...prev, newCheck])
    setFormData({
      name: "",
      type: "例行检查",
      department: "安全管理部",
      inspector: "",
      date: "",
      status: "计划中",
      area: "",
      checkItems: "",
      description: "",
      level: "一般"
    })
    setIsAddCheckOpen(false)
    message.success("检查已创建")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
        <h2 className="text-2xl font-bold">安全检查</h2>
          <p className="text-muted-foreground">管理和追踪安全检查记录</p>
        </div>
        <div className="flex items-center gap-2">
          <AntUpload {...uploadProps}>
          <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              导入
          </Button>
          </AntUpload>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">总检查数</p>
                <p className="text-2xl font-bold">{statistics.total}</p>
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex justify-between text-sm text-muted-foreground">
              <span>例行检查: {statistics.byType.routine}</span>
              <span>专项检查: {statistics.byType.special}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">已完成检查</p>
                <p className="text-2xl font-bold">{statistics.completed}</p>
              </div>
              <div className="rounded-full bg-green-100 p-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress
                percent={Math.round((statistics.completed / statistics.total) * 100)}
                size="small"
                status="active"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">发现问题数</p>
                <p className="text-2xl font-bold">{statistics.totalIssues}</p>
              </div>
              <div className="rounded-full bg-yellow-100 p-3">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4 flex justify-between text-sm text-muted-foreground">
              <span>紧急: {statistics.byLevel.urgent}</span>
              <span>重要: {statistics.byLevel.important}</span>
              <span>一般: {statistics.byLevel.normal}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">进行中检查</p>
                <p className="text-2xl font-bold">{statistics.inProgress}</p>
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <RefreshCw className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex justify-between text-sm text-muted-foreground">
              <span>计划中: {statistics.planned}</span>
              <span>进行中: {statistics.inProgress}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>安全检查记录</CardTitle>
          <CardDescription>管理安全检查计划和记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索检查记录..."
                    className="pl-8 w-[250px]"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Select
                  value={selectedTypes.length === 0 ? "all" : selectedTypes[0]}
                  onValueChange={(value) => setSelectedTypes(value === "all" ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="检查类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有类型</SelectItem>
                    <SelectItem value="例行检查">例行检查</SelectItem>
                    <SelectItem value="专项检查">专项检查</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={selectedStatuses.length === 0 ? "all" : selectedStatuses[0]}
                  onValueChange={(value) => setSelectedStatuses(value === "all" ? [] : [value])}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="计划中">计划中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
                <Dialog open={isAddCheckOpen} onOpenChange={setIsAddCheckOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      新建检查
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>新建安全检查</DialogTitle>
                      <DialogDescription>创建新的安全检查计划或记录</DialogDescription>
                    </DialogHeader>
                    <Tabs defaultValue="basic" className="mt-4">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="basic">基本信息</TabsTrigger>
                        <TabsTrigger value="details">检查详情</TabsTrigger>
                      </TabsList>
                      <TabsContent value="basic" className="space-y-4 mt-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="check-name">检查名称</Label>
                          <Input
                            id="check-name"
                            placeholder="请输入检查名称"
                            value={formData.name}
                            onChange={(e) => handleFormChange('name', e.target.value)}
                          />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-type">检查类型</Label>
                          <Select value={formData.type} onValueChange={(value) => handleFormChange('type', value)}>
                              <SelectTrigger id="check-type">
                                <SelectValue placeholder="选择检查类型" />
                              </SelectTrigger>
                              <SelectContent>
                              <SelectItem value="例行检查">例行检查</SelectItem>
                              <SelectItem value="专项检查">专项检查</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="check-level">检查级别</Label>
                          <Select value={formData.level} onValueChange={(value) => handleFormChange('level', value)}>
                            <SelectTrigger id="check-level">
                              <SelectValue placeholder="选择检查级别" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="一般">一般</SelectItem>
                              <SelectItem value="重要">重要</SelectItem>
                              <SelectItem value="紧急">紧急</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-department">负责部门</Label>
                          <Select value={formData.department} onValueChange={(value) => handleFormChange('department', value)}>
                              <SelectTrigger id="check-department">
                                <SelectValue placeholder="选择负责部门" />
                              </SelectTrigger>
                              <SelectContent>
                              <SelectItem value="安全管理部">安全管理部</SelectItem>
                              <SelectItem value="设备管理部">设备管理部</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-inspector">检查人员</Label>
                          <Input
                            id="check-inspector"
                            placeholder="请输入检查人员"
                            value={formData.inspector}
                            onChange={(e) => handleFormChange('inspector', e.target.value)}
                          />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-date">检查日期</Label>
                          <Input
                            id="check-date"
                            type="date"
                            value={formData.date}
                            onChange={(e) => handleFormChange('date', e.target.value)}
                          />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-status">检查状态</Label>
                          <Select value={formData.status} onValueChange={(value) => handleFormChange('status', value)}>
                              <SelectTrigger id="check-status">
                                <SelectValue placeholder="选择检查状态" />
                              </SelectTrigger>
                              <SelectContent>
                              <SelectItem value="计划中">计划中</SelectItem>
                              <SelectItem value="进行中">进行中</SelectItem>
                              <SelectItem value="已完成">已完成</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </TabsContent>
                      <TabsContent value="details" className="space-y-4 mt-4">
                        <div className="space-y-4">
                          <div className="space-y-2">
                          <Label htmlFor="check-location">检查区域</Label>
                          <Input
                            id="check-location"
                            placeholder="请输入检查区域"
                            value={formData.area}
                            onChange={(e) => handleFormChange('area', e.target.value)}
                          />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-items">检查项目</Label>
                          <Textarea
                            id="check-items"
                            placeholder="请输入检查项目，每行一项"
                            rows={4}
                            value={formData.checkItems}
                            onChange={(e) => handleFormChange('checkItems', e.target.value)}
                          />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="check-description">检查说明</Label>
                          <Textarea
                            id="check-description"
                            placeholder="请输入检查说明"
                            rows={3}
                            value={formData.description}
                            onChange={(e) => handleFormChange('description', e.target.value)}
                          />
                        </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                    <DialogFooter className="mt-6">
                      <Button variant="outline" onClick={() => setIsAddCheckOpen(false)}>
                        取消
                      </Button>
                    <Button onClick={handleSubmit}>保存</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>检查名称</TableHead>
                    <TableHead>检查类型</TableHead>
                    <TableHead>负责部门</TableHead>
                    <TableHead>检查人员</TableHead>
                    <TableHead>检查日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>问题数</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredChecks.map((check) => (
                    <TableRow key={check.id}>
                      <TableCell>{check.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{check.type}</Badge>
                      </TableCell>
                      <TableCell>{check.department}</TableCell>
                      <TableCell>{check.inspector}</TableCell>
                      <TableCell>{check.date}</TableCell>
                      <TableCell>
                        <Badge variant={
                          check.status === "已完成" ? "default" :
                          check.status === "进行中" ? "secondary" : "outline"
                        }>
                          {check.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={check.issues > 0 ? "destructive" : "default"}>
                          {check.issues}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleView(check)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleStatusChange(check.id, "进行中")}>
                                <RefreshCw className="h-4 w-4 mr-2" />
                                设为进行中
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusChange(check.id, "已完成")}>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                设为已完成
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDelete(check.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 查看详情模态框 */}
      <Modal
        title="检查详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false)
          setSelectedCheck(null)
        }}
        footer={null}
        width={800}
      >
        {selectedCheck && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-muted-foreground">检查名称</Label>
                <p className="font-medium">{selectedCheck.name}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">检查类型</Label>
                <p><Badge variant="outline">{selectedCheck.type}</Badge></p>
              </div>
              <div>
                <Label className="text-muted-foreground">负责部门</Label>
                <p className="font-medium">{selectedCheck.department}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">检查人员</Label>
                <p className="font-medium">{selectedCheck.inspector}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">检查日期</Label>
                <p className="font-medium">{selectedCheck.date}</p>
              </div>
              <div>
                <Label className="text-muted-foreground">状态</Label>
                <p>
                  <Badge variant={
                    selectedCheck.status === "已完成" ? "default" :
                    selectedCheck.status === "进行中" ? "secondary" : "outline"
                  }>
                    {selectedCheck.status}
                  </Badge>
                </p>
              </div>
              <div>
                <Label className="text-muted-foreground">问题数</Label>
                <p>
                  <Badge variant={selectedCheck.issues > 0 ? "destructive" : "default"}>
                    {selectedCheck.issues}
                  </Badge>
                </p>
              </div>
              <div>
                <Label className="text-muted-foreground">检查区域</Label>
                <p className="font-medium">{selectedCheck.area}</p>
              </div>
            </div>

            {selectedCheck.description && (
              <div>
                <Label className="text-muted-foreground">检查描述</Label>
                <p className="mt-1">{selectedCheck.description}</p>
              </div>
            )}

            {selectedCheck.checkItems && selectedCheck.checkItems.length > 0 && (
              <div>
                <Label className="text-muted-foreground">检查项目</Label>
                <ul className="mt-1 space-y-1">
                  {selectedCheck.checkItems.map((item, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {selectedCheck.attachments && selectedCheck.attachments.length > 0 && (
              <div>
                <Label className="text-muted-foreground">附件</Label>
                <div className="mt-2 space-y-2">
                  {selectedCheck.attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2" />
                        <span>{file}</span>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        下载
            </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedCheck.lastModified && (
              <div className="text-sm text-muted-foreground">
                <p>最后修改时间：{selectedCheck.lastModified}</p>
                <p>修改人：{selectedCheck.modifiedBy}</p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

