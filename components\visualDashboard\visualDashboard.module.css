/* 全局样式 */
.dashboard {
  background: url('/images/bg.jpg') no-repeat top center;
  line-height: 1.15;
  width: 100%;
  height: 100%;
  min-width: 1024px;
  overflow: hidden;
}

/* 通知区域样式 */
.notificationArea {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.notification {
  background: rgba(14, 30, 73, 0.9);
  border-left: 4px solid #1677ff;
  border-radius: 4px;
  padding: 12px 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 15px rgba(32, 219, 255, 0.3);
  animation: notificationSlide 0.5s ease-out, notificationGlow 2s infinite;
  backdrop-filter: blur(5px);
  color: #fff;
  transition: all 0.3s ease;
  transform-origin: right;
}

.notification:hover {
  transform: translateX(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 0 20px rgba(32, 219, 255, 0.4);
}

.notification-info {
  border-left-color: #1677ff;
}

.notification-success {
  border-left-color: #52c41a;
}

.notification-warning {
  border-left-color: #faad14;
}

.notification-error {
  border-left-color: #ff4d4f;
}

.notificationTitle {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 5px;
  color: rgba(32, 219, 255, 0.9);
  display: flex;
  align-items: center;
}

.notificationTitle::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: rgba(32, 219, 255, 0.9);
  border-radius: 50%;
  margin-right: 6px;
}

.notificationMessage {
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 5px;
}

.notificationTime {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  text-align: right;
}

@keyframes notificationSlide {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes notificationGlow {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 15px rgba(32, 219, 255, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 25px rgba(32, 219, 255, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 0 15px rgba(32, 219, 255, 0.3);
  }
}

/* 头部样式 */
.header {
  height: 80px;
  background: url('/images/head_bg.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.header h1 {
  font-size: 30px;
  color: #fff;
  line-height: 80px;
  text-align: center;
}

.header .showTime {
  position: absolute;
  top: 0;
  right: 30px;
  line-height: 75px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
}

/* 返回首页按钮样式 */
.goHomeButton {
  position: absolute;
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(32, 219, 255, 0.5);
  border-radius: 4px;
  color: #fff;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 100;
}

.goHomeButton:hover {
  background-color: rgba(32, 219, 255, 0.2);
  box-shadow: 0 0 15px rgba(32, 219, 255, 0.5);
}

.goHomeButton span {
  margin-left: 6px;
  font-size: 14px;
}

/* 主体框架 */
.mainbox {
  display: flex;
  min-width: 1024px;
  max-width: 1920px;
  margin: 0 auto;
  padding: 10px;
  padding-bottom: 0;
}

.column {
  flex: 3;
}

.column:nth-child(2) {
  flex: 5;
  margin: 0 10px 15px;
}

/* 面板通用样式 */
.panel {
  position: relative;
  height: 310px;
  padding: 0 15px 40px;
  border: 1px solid rgba(25, 186, 139, 0.17);
  margin-bottom: 15px;
  background: url('/images/line.png') rgba(255, 255, 255, 0.03);
  overflow: hidden;
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(32, 219, 255, 0.1) 0%, 
    rgba(32, 219, 255, 0) 40%, 
    rgba(32, 219, 255, 0) 60%, 
    rgba(32, 219, 255, 0.1) 100%);
  animation: panelShine 4s infinite linear;
  pointer-events: none;
}

@keyframes panelShine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.panel::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    rgba(32, 219, 255, 0.3),
    rgba(32, 219, 255, 0.1),
    rgba(32, 219, 255, 0.3));
  z-index: -1;
  animation: borderRotate 4s linear infinite;
}

@keyframes borderRotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

.panelFooter {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.panelFooter::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  border-left: 2px solid #02a6b5;
  border-bottom: 2px solid #02a6b5;
  width: 10px;
  height: 10px;
}

.panelFooter::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  border-right: 2px solid #02a6b5;
  border-bottom: 2px solid #02a6b5;
  width: 10px;
  height: 10px;
}

.panel h2 {
  height: 48px;
  color: #fff;
  line-height: 48px;
  text-align: center;
  font-size: 20px;
  font-weight: normal;
  position: relative;
  display: inline-block;
  padding: 0 20px;
}

.panel h2::before,
.panel h2::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 15px;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(32, 219, 255, 0.8),
    rgba(32, 219, 255, 0.2));
}

.panel h2::before {
  left: 0;
  transform-origin: left;
  animation: titleLineLeft 2s infinite;
}

.panel h2::after {
  right: 0;
  transform-origin: right;
  animation: titleLineRight 2s infinite;
}

@keyframes titleLineLeft {
  0% {
    transform: scaleX(0.5) translateY(-50%);
    opacity: 0.5;
  }
  50% {
    transform: scaleX(1) translateY(-50%);
    opacity: 1;
  }
  100% {
    transform: scaleX(0.5) translateY(-50%);
    opacity: 0.5;
  }
}

@keyframes titleLineRight {
  0% {
    transform: scaleX(0.5) translateY(-50%);
    opacity: 0.5;
  }
  50% {
    transform: scaleX(1) translateY(-50%);
    opacity: 1;
  }
  100% {
    transform: scaleX(0.5) translateY(-50%);
    opacity: 0.5;
  }
}

.panel .chart {
  height: 240px;
}

/* 中间数字模块 - 扩展为多列统计面板 */
.no {
  background-color: rgba(101, 132, 226, 0.1);
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 6px;
  border: 1px solid rgba(25, 186, 139, 0.17);
  position: relative;
}

.no::before {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 30px;
  height: 10px;
  border-top: 2px solid #02a6b5;
  border-left: 2px solid #02a6b5;
}

.no::after {
  position: absolute;
  bottom: 0;
  right: 0;
  content: "";
  width: 30px;
  height: 10px;
  border-right: 2px solid #02a6b5;
  border-bottom: 2px solid #02a6b5;
}

.noHd {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px dashed rgba(25, 186, 139, 0.4);
  padding-bottom: 10px;
}

.noHd ul {
  display: flex;
  padding: 0;
  margin: 0;
  width: 50%;
}

.noHd ul li {
  flex: 1;
  height: 70px;
  font-size: 45px;
  color: #ffeb7b;
  text-align: center;
  font-family: "electronicFont";
  position: relative;
  list-style: none;
  text-shadow: 0 0 10px rgba(255, 235, 123, 0.6);
  animation: numberPulse 2s infinite;
}

.noHd ul li:first-child::after {
  content: "";
  position: absolute;
  top: 25%;
  right: 0;
  height: 50%;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.noBd {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.noBd ul {
  display: flex;
  padding: 0;
  margin: 0;
  width: 50%;
}

.noBd ul li {
  flex: 1;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 15px;
  height: 30px;
  line-height: 30px;
  list-style: none;
}

/* 中间logo样式 */
.centerLogo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 15;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 140px;
  height: 140px;
  box-shadow: 0 0 30px rgba(32, 219, 255, 0.5), 0 0 60px rgba(32, 219, 255, 0.3), 0 0 90px rgba(32, 219, 255, 0.1);
  border: 2px solid rgba(32, 219, 255, 0.3);
  animation: logoRotate 20s linear infinite,
             logoPulse 4s ease-in-out infinite;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Logo被点击时的样式 */
.logoClicked {
  transform: translate(-50%, -50%) scale(1.2);
  background-color: rgba(0, 0, 0, 0.7);
  box-shadow: 0 0 50px rgba(32, 219, 255, 0.9), 0 0 100px rgba(32, 219, 255, 0.4), 0 0 150px rgba(32, 219, 255, 0.2);
  border: 2px solid rgba(32, 219, 255, 0.8);
}

.centerLogo svg {
  color: #20dbff;
  filter: drop-shadow(0 0 10px rgba(32, 219, 255, 0.7));
  margin-bottom: 5px;
  transition: all 0.5s ease;
  animation: fadeInOut 4s infinite alternate;
}

.logoClicked svg {
  filter: drop-shadow(0 0 20px rgba(32, 219, 255, 1));
  transform: scale(1.1);
  animation: pulse 2s infinite alternate;
}

/* 添加新的渐隐渐现动画 */
@keyframes fadeInOut {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

/* 粒子效果容器 */
.particlesContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 14;
  pointer-events: none;
}

/* 粒子样式 */
.particle {
  position: absolute;
  background: radial-gradient(circle at center, rgba(32, 219, 255, 0.9), rgba(32, 219, 255, 0.2));
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 4px rgba(32, 219, 255, 0.6);
  pointer-events: none;
  will-change: transform, opacity;
  animation: particleFloat 15s ease-in-out infinite alternate,
             particleGlow 4s infinite alternate;
}

/* 爆发粒子样式 */
.burstParticle {
  position: absolute;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.95), rgba(32, 219, 255, 0.7));
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(32, 219, 255, 0.8);
  pointer-events: none;
  z-index: 16;
  will-change: transform, opacity;
}

@keyframes burstOut {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.2);
    filter: blur(0px);
  }
  20% {
    opacity: 1;
    filter: blur(1px);
  }
  100% {
    opacity: 0;
    transform: translate(calc(-50% + var(--target-x)), calc(-50% + var(--target-y))) scale(0.5);
    filter: blur(2px);
  }
}

@keyframes particleFloat {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
    opacity: 0.3;
    filter: blur(0.5px);
  }
  25% {
    opacity: 0.7;
    filter: blur(1px);
  }
  50% {
    transform: translate(-50%, -50%) translateX(30px) translateY(-25px);
    opacity: 1;
    filter: blur(0px);
  }
  75% {
    opacity: 0.7;
    filter: blur(1px);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-25px) translateY(30px);
    opacity: 0.3;
    filter: blur(0.5px);
  }
}

.logoText {
  color: white;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(32, 219, 255, 0.7);
  transition: all 0.5s ease;
}

.logoClicked .logoText {
  font-size: 18px;
  text-shadow: 0 0 20px rgba(32, 219, 255, 1);
}

/* 点击时Logo的额外效果 */
.logoClicked::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px dashed rgba(32, 219, 255, 0.8);
  border-radius: 50%;
  animation: pulseOut 3s ease-out forwards;
  z-index: -1;
}

.logoClicked::after {
  content: '';
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  background: radial-gradient(circle at center, rgba(32, 219, 255, 0.2), transparent 70%);
  border-radius: 50%;
  animation: pulseOut 3s ease-out forwards;
  z-index: -1;
}

@keyframes pulseOut {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

@keyframes rotateReverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

/* 公司信息样式 */
.companyInfo {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 0 10px rgba(32, 219, 255, 0.5);
  border: 1px solid rgba(32, 219, 255, 0.3);
  animation: fadeIn 0.5s forwards;
  z-index: 20;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes rotateFast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 中间地图 */
.map {
  position: relative;
  height: 500px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.map1 {
  width: 450px;
  height: 450px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-size: 100% 100%;
  opacity: .3;
  background-image: url('/images/map.png');
  z-index: 1;
}

.map2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  background-image: url('/images/lbx.png');
  background-size: 100% 100%;
  animation: rotate1 15s linear infinite;
  opacity: .6;
  z-index: 2;
}

.map3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 350px;
  height: 350px;
  background-image: url('/images/jt.png');
  background-size: 100% 100%;
  animation: rotate2 10s linear infinite;
  opacity: .6;
  z-index: 3;
}

.map .chart {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
}

/* 地图信息样式 */
.mapInfo {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  width: 80%;
  z-index: 10;
}

.mapInfoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(14, 30, 73, 0.7);
  border: 1px solid rgba(25, 186, 139, 0.3);
  padding: 15px 20px;
  border-radius: 6px;
  color: #fff;
  width: 30%;
}

.mapInfoValue {
  font-size: 24px;
  font-weight: bold;
  margin-top: 5px;
  color: #32fbc0;
  font-family: "electronicFont";
}

/* 动画效果 */
@keyframes rotate1 {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes rotate2 {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

/* 响应式 */
@media screen and (max-width: 1024px) {
  .header {
    height: 60px;
  }
  .header h1 {
    font-size: 24px;
    line-height: 60px;
  }
  .header .showTime {
    line-height: 60px;
    font-size: 16px;
  }
  .panel {
    height: 230px;
  }
  .panel .chart {
    height: 180px;
  }
  .noHd ul li {
    height: 60px;
    font-size: 50px;
  }
  .map {
    height: 300px;
  }
  .map .chart {
    height: 500px;
  }
  .progressCards {
    grid-template-columns: 1fr;
  }
  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  .goHomeButton {
    left: 15px;
  }
  .map1, .map2, .map3 {
    width: 350px;
    height: 350px;
  }
  .centerLogo {
    width: 140px;
    height: 140px;
  }
  .progressContainer {
    grid-template-columns: 1fr;
  }
}

@media screen and (min-width: 1920px) {
  .header {
    height: 100px;
  }
  .header h1 {
    font-size: 36px;
    line-height: 100px;
  }
  .header .showTime {
    line-height: 100px;
    font-size: 22px;
  }
  .panel {
    height: 380px;
  }
  .panel .chart {
    height: 300px;
  }
  .noHd ul li {
    height: 100px;
    font-size: 86px;
  }
  .map {
    height: 500px;
  }
  .map .chart {
    height: 700px;
  }
  .progressCards {
    grid-template-columns: repeat(4, 1fr);
  }
  .goHomeButton {
    left: 50px;
    padding: 8px 16px;
  }
  .goHomeButton span {
    font-size: 16px;
  }
  .map1 {
    width: 550px;
    height: 550px;
  }
  .map2 {
    width: 650px;
    height: 650px;
  }
  .map3 {
    width: 600px;
    height: 600px;
  }
  .centerLogo {
    width: 200px;
    height: 200px;
  }
  .progressContainer {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 额外添加一个响应式中间层 */
@media screen and (min-width: 1025px) and (max-width: 1919px) {
  .progressCards {
    grid-template-columns: repeat(2, 1fr);
  }
  .statsContainer {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 点击时的扩散环效果 */
.ring1, .ring2, .ring3 {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  background: transparent;
  border: 2px solid rgba(32, 219, 255, 0.8);
  transform: translate(-50%, -50%);
  z-index: 14;
  pointer-events: none;
}

.ring1 {
  width: 160px;
  height: 160px;
  animation: ringExpand 2s ease-out;
}

.ring2 {
  width: 160px;
  height: 160px;
  animation: ringExpand 2s ease-out 0.3s;
}

.ring3 {
  width: 160px;
  height: 160px;
  animation: ringExpand 2s ease-out 0.6s;
}

@keyframes ringExpand {
  0% {
    width: 160px;
    height: 160px;
    opacity: 1;
    border-width: 5px;
  }
  100% {
    width: 600px;
    height: 600px;
    opacity: 0;
    border-width: 1px;
  }
}

/* 特效动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 30px rgba(32, 219, 255, 0.5), 0 0 60px rgba(32, 219, 255, 0.2);
    border-color: rgba(32, 219, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 50px rgba(32, 219, 255, 0.8), 0 0 80px rgba(32, 219, 255, 0.3);
    border-color: rgba(32, 219, 255, 0.7);
  }
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotateFast {
  from {
    transform: rotate(0deg) scale(1.1);
  }
  to {
    transform: rotate(360deg) scale(1.1);
  }
}

/* 项目进度卡片样式 */
.progressCards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 15px;
}

.progressCard {
  background-color: rgba(14, 30, 73, 0.7);
  border: 1px solid rgba(25, 186, 139, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #fff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.progressCard:hover {
  box-shadow: 0 0 20px rgba(32, 219, 255, 0.3);
  transform: translateY(-2px);
}

.progressCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(32, 219, 255, 0.8), transparent);
}

.progressTitle {
  font-size: 14px;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  color: rgba(255, 255, 255, 0.9);
}

.progressBarContainer {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progressBar {
  height: 100%;
  background: linear-gradient(90deg, #00c9e0, #005fc1);
  border-radius: 5px;
  transition: width 1s cubic-bezier(0.22, 0.61, 0.36, 1);
  position: relative;
  overflow: hidden;
}

.progressBar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
  animation: progressBarShine 2s infinite linear;
  transform: skewX(-20deg);
}

@keyframes progressBarShine {
  0% {
    transform: translateX(-100%) skewX(-20deg);
  }
  100% {
    transform: translateX(200%) skewX(-20deg);
  }
}

/* 进度条脉冲效果 */
.pulseBar {
  position: relative;
}

.pulsing {
  animation: pulse-progress 1.5s ease;
}

@keyframes pulse-progress {
  0% {
    box-shadow: 0 0 0 0 rgba(32, 219, 255, 0.7);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(32, 219, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(32, 219, 255, 0);
  }
}

.progressInfo {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.progressPercent {
  font-weight: bold;
  color: #32fbc0;
}

/* 统计卡片容器 */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 15px;
}

/* 统计小卡片样式整合到顶部统计区 */
.statItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(14, 30, 73, 0.6);
  border-radius: 6px;
  margin: 5px;
  flex: 1;
  min-width: calc(25% - 10px);
  border: 1px solid rgba(25, 186, 139, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statItem:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 15px rgba(32, 219, 255, 0.3);
  background-color: rgba(20, 40, 100, 0.7);
}

.statItem::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center,
    rgba(32, 219, 255, 0.1) 0%,
    rgba(32, 219, 255, 0) 70%);
  transform: rotate(45deg);
  animation: statItemGlow 3s infinite linear;
  pointer-events: none;
}

@keyframes statItemGlow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.statIcon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: rgba(32, 219, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #32fbc0;
  margin-right: 10px;
  box-shadow: 0 0 10px rgba(32, 219, 255, 0.3);
}

.statContent {
  flex: 1;
}

.statValue {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  line-height: 1.2;
  font-family: "electronicFont";
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.statLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 3px;
}

/* 激活状态的项目卡片 */
.activeCard {
  background-color: rgba(20, 40, 100, 0.8);
  border: 1px solid rgba(32, 219, 255, 0.6);
  box-shadow: 0 0 15px rgba(32, 219, 255, 0.4);
  transform: translateY(-3px);
}

.activeCard::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(32, 219, 255, 0.8), transparent);
}

/* 项目详情样式 */
.projectDetails {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed rgba(32, 219, 255, 0.4);
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  animation: fadeIn 0.5s ease;
}

.projectDetails div {
  margin-bottom: 5px;
}

.contentItem {
  margin-bottom: 20px;
  position: relative;
  background-color: rgba(14, 30, 73, 0.4);
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 15px;
  border-radius: 6px;
}

.itemTitle {
  font-size: 16px;
  color: rgba(32, 219, 255, 0.9);
  margin-bottom: 10px;
  font-weight: 500;
  position: relative;
  padding-left: 10px;
}

.itemTitle:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #20dbff, #1677ff);
}

.progressContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 15px;
}

.progressTitle {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #fff;
}

.progressBarContainer {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progressValue {
  font-size: 18px;
  font-weight: bold;
  color: rgba(32, 219, 255, 0.9);
  margin-top: 5px;
}

.progressStatus {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 5px;
}

/* 地图说明文字 */
.mapDescription {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 4px 16px;
  border-radius: 15px;
  text-align: center;
  z-index: 16;
  white-space: nowrap;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(32, 219, 255, 0.3);
}

/* 图表相关样式 */
.bar, .line, .pie {
  background-color: rgba(19, 25, 47, 0.6);
}

.bar2, .line2, .pie2, .materialPanel {
  background-color: rgba(19, 25, 47, 0.6);
}

/* 物资周转率样式 */
.materialTurnoverContainer {
  height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 10px 5px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(32, 219, 255, 0.5) rgba(0, 0, 0, 0.2);
}

.materialTurnoverContainer::-webkit-scrollbar {
  width: 6px;
}

.materialTurnoverContainer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.materialTurnoverContainer::-webkit-scrollbar-thumb {
  background-color: rgba(32, 219, 255, 0.5);
  border-radius: 3px;
}

.turnoverItem {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.turnoverTitle {
  width: 70px;
  text-align: right;
  padding-right: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.turnoverBarContainer {
  flex: 1;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  margin: 0 10px;
}

.turnoverBar {
  height: 100%;
  border-radius: 6px;
  background-color: #1890ff;
  transition: width 1s ease-in-out;
}

.turnoverValue {
  width: 40px;
  text-align: right;
  color: #fff;
  font-size: 14px;
}

/* 中心地图区域 */
.map {
  position: relative;
  height: 500px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 项目状态摘要 */
.statusSummary {
  background-color: rgba(19, 25, 47, 0.6);
  border: 1px solid rgba(25, 186, 139, 0.17);
  border-radius: 6px;
  margin-bottom: 15px;
  position: relative;
  padding: 15px;
}

.statusSummary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  border-left: 2px solid #02a6b5;
  border-top: 2px solid #02a6b5;
  width: 10px;
  height: 10px;
}

.statusSummary::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-right: 2px solid #02a6b5;
  border-top: 2px solid #02a6b5;
  width: 10px;
  height: 10px;
}

.summaryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
}

.summaryHeader h3 {
  color: #fff;
  font-size: 18px;
  margin: 0;
  font-weight: normal;
}

.summaryInfo {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.summaryCards {
  display: flex;
  gap: 15px;
}

.summaryCard {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 15px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.summaryCard:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.summaryTitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 8px;
}

.summaryCount {
  color: #fff;
  font-size: 28px;
  font-weight: bold;
  font-family: "electronicFont";
  margin-bottom: 5px;
}

.summaryPercent {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 10px;
}

.summaryBar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background-color: #1890ff;
  transition: width 1s ease-in-out;
}

/* 智能洞察组件样式 */
.insightsContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
  overflow: hidden;
}

.insightsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.insightsHeader h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.insightsTabs {
  display: flex;
  gap: 10px;
}

.insightTab {
  padding: 5px 12px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
}

.insightTab:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.activeTab {
  background-color: #e6f7ff;
  color: #1890ff;
}

.insightsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.insightCard {
  display: flex;
  background-color: #fafafa;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.insightCard:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.insightIcon {
  flex-shrink: 0;
  margin-right: 12px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.insightContent {
  flex: 1;
}

.insightContent h4 {
  margin: 0 0 5px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.insightContent p {
  margin: 0 0 8px;
  font-size: 14px;
  color: #666;
}

.insightMeta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
}

.insightModule {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.insightBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 12px;
}

.highImportance {
  background-color: #ff4d4f;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
}

.mediumImportance {
  background-color: #faad14;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
}

.lowImportance {
  background-color: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
}

.viewAllInsights {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  padding: 8px;
  transition: all 0.3s ease;
}

.viewAllInsights:hover {
  background-color: #e6f7ff;
  border-radius: 4px;
}

.viewAllIcon {
  margin-left: 5px;
  font-size: 16px;
}

/* 智能分析卡片样式 */
.cardsContainer {
  margin-bottom: 24px;
}

.cardRow {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.analyticsCard {
  position: relative;
  overflow: hidden;
}

.analyticsCard:after {
  content: '';
  position: absolute;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  bottom: -50px;
  right: -50px;
}

.cardContent {
  padding: 20px;
  display: flex;
  align-items: flex-start;
}

.cardIconContainer {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  font-size: 24px;
}

.cardInfo {
  flex: 1;
}

.cardInfo h3 {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.cardInfo p {
  margin: 0 0 15px;
  font-size: 14px;
  color: #666;
}

.analyticsPreview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.analyticsPreviewItem {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

@keyframes logoRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes logoPulse {
  0% {
    box-shadow: 0 0 30px rgba(32, 219, 255, 0.5),
                0 0 60px rgba(32, 219, 255, 0.3),
                0 0 90px rgba(32, 219, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(32, 219, 255, 0.7),
                0 0 80px rgba(32, 219, 255, 0.4),
                0 0 120px rgba(32, 219, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 30px rgba(32, 219, 255, 0.5),
                0 0 60px rgba(32, 219, 255, 0.3),
                0 0 90px rgba(32, 219, 255, 0.1);
  }
}

@keyframes particleGlow {
  0% {
    filter: brightness(0.8) blur(1px);
  }
  50% {
    filter: brightness(1.2) blur(0px);
  }
  100% {
    filter: brightness(0.8) blur(1px);
  }
}

/* 新增炫酷统计卡片样式 */
.chromaGrid {
  position: relative;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(var(--cols, 4), 1fr);
  grid-auto-rows: auto;
  justify-content: center;
  gap: 0.75rem;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
  min-height: 160px;

  --x: 50%;
  --y: 50%;
  --r: 300px;
}

@media (max-width: 1124px) {
  .chromaGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .chromaGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }
}

.chromaCard {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 140px;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(25, 186, 139, 0.17);
  transition: all 0.3s ease;
  background: var(--card-gradient, linear-gradient(145deg, #3B82F6, #000));
  padding: 20px;
  cursor: pointer;

  --mouse-x: 50%;
  --mouse-y: 50%;
  --spotlight-color: rgba(255, 255, 255, 0.03);
}

.chromaCard:hover {
  border-color: var(--card-border, rgba(25, 186, 139, 0.5));
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.chromaCard::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at var(--mouse-x) var(--mouse-y),
      var(--spotlight-color),
      transparent 70%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 2;
}

.chromaCard:hover::before {
  opacity: 1;
}

.chromaInfo {
  position: relative;
  z-index: 1;
  margin-top: auto;
  color: #fff;
  font-family: system-ui, sans-serif;
}

.chromaOverlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 3;
  backdrop-filter: grayscale(0.2) brightness(0.9);
  -webkit-backdrop-filter: grayscale(0.2) brightness(0.9);
  background: rgba(0, 0, 0, 0.001);

  mask-image: radial-gradient(circle var(--r) at var(--x) var(--y),
      transparent 0%,
      transparent 15%,
      rgba(0, 0, 0, 0.10) 30%,
      rgba(0, 0, 0, 0.22) 45%,
      rgba(0, 0, 0, 0.35) 60%,
      rgba(0, 0, 0, 0.50) 75%,
      rgba(0, 0, 0, 0.68) 88%,
      white 100%);
  -webkit-mask-image: radial-gradient(circle var(--r) at var(--x) var(--y),
      transparent 0%,
      transparent 15%,
      rgba(0, 0, 0, 0.10) 30%,
      rgba(0, 0, 0, 0.22) 45%,
      rgba(0, 0, 0, 0.35) 60%,
      rgba(0, 0, 0, 0.50) 75%,
      rgba(0, 0, 0, 0.68) 88%,
      white 100%);
}

.chromaFade {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 4;
  backdrop-filter: grayscale(0.2) brightness(0.9);
  -webkit-backdrop-filter: grayscale(0.2) brightness(0.9);
  background: rgba(0, 0, 0, 0.001);

  mask-image: radial-gradient(circle var(--r) at var(--x) var(--y),
      white 0%,
      white 15%,
      rgba(255, 255, 255, 0.90) 30%,
      rgba(255, 255, 255, 0.78) 45%,
      rgba(255, 255, 255, 0.65) 60%,
      rgba(255, 255, 255, 0.50) 75%,
      rgba(255, 255, 255, 0.32) 88%,
      transparent 100%);
  -webkit-mask-image: radial-gradient(circle var(--r) at var(--x) var(--y),
      white 0%,
      white 15%,
      rgba(255, 255, 255, 0.90) 30%,
      rgba(255, 255, 255, 0.78) 45%,
      rgba(255, 255, 255, 0.65) 60%,
      rgba(255, 255, 255, 0.50) 75%,
      rgba(255, 255, 255, 0.32) 88%,
      transparent 100%);

  opacity: 1;
  transition: opacity 0.25s ease;
}

.statIcon {
  font-size: 28px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.chromaCard:hover .statIcon {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.15);
}

.statValue {
  font-size: 28px;
  font-weight: bold;
  margin: 0;
  font-family: "electronicFont";
  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.7));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.statLabel {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 5px 0 0;
} 