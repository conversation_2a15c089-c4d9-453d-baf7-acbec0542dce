* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  background: url('../images/bg.jpg') 
  no-repeat top center;
  line-height: 1.15;
  min-width: 1024px;
  max-width: 1920px;
}
li {
  list-style: none;
}
/* 声明字体*/
@font-face {
  font-family: electronicFont;
  src: url(../font/DS-DIGIT.TTF);
}
header {
  height: 1.25rem;
  background: url('../images/head_bg.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  h1 {
    font-size: .475rem;
    color: #fff;
    line-height: 1rem;
    text-align: center;
  }
  .showTime {
    position: absolute;
    top: 0;
    right: 0.375rem;
    line-height: .9375rem;
    font-size: .25rem;
    color: rgba(255, 255, 255, .7);
  }
}
.mainbox {
  display: flex;
  min-width: 1024px;
  max-width: 1920px;
  margin: 0 auto;
  padding: .125rem;
  padding-bottom: 0;
  .column {
    flex: 3;
    &:nth-child(2) {
      flex: 5;
      margin: 0 .125rem .1875rem;
    }
  }
  // 公共面板
  .panel{
    position: relative;
    height: 3.875rem;
    padding: 0 0.1875rem 0.5rem;
    border: 1px solid rgba(25, 186, 139, 0.17);
    margin-bottom: 0.1875rem;
    background: url(../images/line\(1\).png) rgba(255, 255, 255, 0.03);
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      border-left: 2px solid #02a6b5;
      border-top: 2px solid #02a6b5;
      width: 10px;
      height: 10px;
    }
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      border-right: 2px solid #02a6b5;
      border-top: 2px solid #02a6b5;
      width: 10px;
      height: 10px;
    }
    .panel-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        border-left: 2px solid #02a6b5;
        border-bottom: 2px solid #02a6b5;
        width: 10px;
        height: 10px;
      }
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        border-right: 2px solid #02a6b5;
        border-bottom: 2px solid #02a6b5;
        width: 10px;
        height: 10px;
      }
    }
    h2 {
      height: .6rem;
      color: #fff;
      line-height: .6rem;
      text-align: center;
      font-size: .25rem;
      font-weight: normal;
    }
    .chart {
      height: 3rem;
    }
  }
}
.no {
  background-color: rgba(101,132,226,.1);
  padding: .1875rem;
  .no-hd{
    position: relative;
    border: 1px solid rgba(25, 186, 139, 0.17);
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      content: "";
      width: 30px;
      height: 10px;
      border-top: 2px solid #02a6b5;
      border-left: 2px solid #02a6b5;
    }
    &::after {
      position: absolute;
      bottom: 0;
      right: 0;
      content: "";
      width: 30px;
      height: 10px;
      border-right: 2px solid #02a6b5;
      border-bottom: 2px solid #02a6b5;
    }
    ul {
      display: flex;
      li {
        flex: 1px;
        height: 1rem;
        font-size: .875rem;
        color: #ffeb7b;
        text-align: center;
        font-family: electronicFont;
        position: relative;
        &:nth-child(1){
          &::after {
            content: "";
            position: absolute;
            top: 25%;
            right: 0;
            height: 50%;
            width: 1px;
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }
  .no-bd {
    ul {
      display: flex;
      li {
        flex: 1;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.225rem;
        height: 0.5rem;
        line-height: 0.5rem;
        padding-top: 0.125rem;
      }
    }
  }
}
.map {
  height: 10.125rem;
  position: relative;
  overflow: hidden;
  .map1 {
    width: 6.475rem;
    height: 6.475rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    // 背景图缩放
    background-size: 100% 100%;
    opacity: .3;
    background-image: url('../images/map.png');
  }
  .map2 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8.0375rem;
    height: 8.0375rem;
    background: url(../images/lbx.png);
    // 使用动画 15s将动画执行完毕 匀速 无限循环
    animation: rotate1 15s linear infinite;
    opacity: 0.6;
    background-size: 100% 100%;
  }
  .map3 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 7.075rem;
    height: 7.075rem;
    background: url(../images/jt.png);
    // 使用动画 10s 匀速 无限循环
    animation: rotate2 10s linear infinite;
    opacity: 0.6;
    background-size: 100% 100%;
  }
  .chart {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10.125rem;
  }
}
// map2的旋转动画
@keyframes rotate1 {
  form {
    // translate属性是为了在旋转的保持水平居中的平移
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
// map2的旋转动画
@keyframes rotate2 {
  form {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

/* 约束屏幕尺寸 */
@media screen and (max-width: 1024px) {
  html {
    font-size: 42px !important;
  }
}
@media screen and (min-width: 1920px) {
  html {
    font-size: 80px !important;
  }
}
