"use client"

import { useState, useEffect, useRef } from "react"
import {
  Download,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Info,
  Move,
  Search,
  Layers,
  ImageIcon,
  CheckCircle2
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import Image from "next/image"

// 图片映射
const imageMapping: Record<string, { file: string, description: string }> = {
  "总图": {
    file: "/qiye/图片14.png",
    description: "企业总体架构图，展示整体业务结构和系统关系"
  },
  "工程图": {
    file: "/qiye/图片15.png",
    description: "工程相关架构图，展示工程管理流程和系统架构"
  },
  "工程拓扑图": {
    file: "/qiye/图片16.png",
    description: "工程系统拓扑结构图，展示系统间连接关系"
  },
  "安全管理流程": {
    file: "/qiye/图片17.png",
    description: "安全管理相关流程图，展示安全管理体系和流程"
  },
  "保卫": {
    file: "/qiye/图片18.png",
    description: "保卫系统架构图，展示保卫管理体系和流程"
  },
  "党务": {
    file: "/qiye/图片19.png",
    description: "党务管理架构图，展示党务管理体系和流程"
  },
  "库存": {
    file: "/qiye/图片20.png",
    description: "库存管理架构图，展示库存管理体系和流程"
  },
  "固定资产管理": {
    file: "/qiye/图片21.png",
    description: "固定资产管理架构图，展示资产管理体系和流程"
  },
  "办公室管理": {
    file: "/qiye/图片22.png",
    description: "办公室管理架构图，展示办公管理体系和流程"
  },
  "人事管理": {
    file: "/qiye/图片23.png",
    description: "人事管理架构图，展示人事管理体系和流程"
  },
  "审批": {
    file: "/qiye/图片24.png",
    description: "审批流程架构图，展示审批管理体系和流程"
  },
  "绩效管理": {
    file: "/qiye/图片25.png",
    description: "绩效管理架构图，展示绩效管理体系和流程"
  }
}

export function EnterpriseArchitecture() {
  const [activeTab, setActiveTab] = useState("总图")
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [scale, setScale] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [isImageLoading, setIsImageLoading] = useState(true)
  const [showControls, setShowControls] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const imageContainerRef = useRef<HTMLDivElement>(null)

  // 处理缩放
  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.1, 3))
  }

  // 处理缩小
  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.1, 0.5))
  }

  // 重置缩放和位置
  const handleReset = () => {
    setScale(1)
    setPosition({ x: 0, y: 0 })
  }

  // 处理拖动开始
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y })
  }

  // 处理拖动过程
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  // 处理拖动结束
  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setIsDragging(false)
  }

  // 处理全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().then(() => {
        setIsFullscreen(true)
      }).catch(err => {
        console.error(`全屏错误: ${err.message}`)
      })
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen().then(() => {
          setIsFullscreen(false)
        }).catch(err => {
          console.error(`退出全屏错误: ${err.message}`)
        })
      }
    }
  }

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (e.key) {
        case '+': // 放大
        case '=': // 也可以用等号键（不需要按Shift）
          e.preventDefault()
          handleZoomIn()
          break
        case '-': // 缩小
          e.preventDefault()
          handleZoomOut()
          break
        case '0': // 重置
          e.preventDefault()
          handleReset()
          break
        case 'f': // 全屏
          e.preventDefault()
          toggleFullscreen()
          break
        case 'ArrowLeft': // 上一张
          e.preventDefault()
          handlePrevImage()
          break
        case 'ArrowRight': // 下一张
          e.preventDefault()
          handleNextImage()
          break
        case 'Escape': // 退出全屏
          if (isFullscreen) {
            document.exitFullscreen()
          }
          break
        case 'h': // 隐藏/显示控制面板
          e.preventDefault()
          toggleControls()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [activeTab, isFullscreen, showControls])

  // 处理下载图片
  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = imageMapping[activeTab].file
    link.download = `${activeTab}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 处理下一张图片
  const handleNextImage = () => {
    const keys = Object.keys(imageMapping)
    const currentIndex = keys.indexOf(activeTab)
    const nextIndex = (currentIndex + 1) % keys.length
    setActiveTab(keys[nextIndex])
  }

  // 处理上一张图片
  const handlePrevImage = () => {
    const keys = Object.keys(imageMapping)
    const currentIndex = keys.indexOf(activeTab)
    const prevIndex = (currentIndex - 1 + keys.length) % keys.length
    setActiveTab(keys[prevIndex])
  }

  // 处理图片加载状态
  const handleImageLoad = () => {
    setIsImageLoading(false)
    // 添加加载成功的视觉反馈
    if (imageContainerRef.current) {
      imageContainerRef.current.classList.add('image-loaded')
      setTimeout(() => {
        if (imageContainerRef.current) {
          imageContainerRef.current.classList.remove('image-loaded')
        }
      }, 500)
    }
  }

  // 处理图片加载错误
  const handleImageError = () => {
    setIsImageLoading(false)
  }

  // 切换控制按钮显示状态
  const toggleControls = () => {
    setShowControls(prev => !prev)
  }

  // 过滤图片列表
  const filteredImages = Object.keys(imageMapping).filter(name =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6 animate-in fade-in duration-500">
      {/* 自定义样式 */}
      <style jsx global>{`
        .image-loaded {
          animation: pulse 0.5s ease-out;
        }

        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(var(--primary), 0.4); }
          70% { box-shadow: 0 0 0 10px rgba(var(--primary), 0); }
          100% { box-shadow: 0 0 0 0 rgba(var(--primary), 0); }
        }

        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
          background: transparent;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
          background-color: rgba(var(--primary), 0.2);
          border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
          background-color: rgba(var(--primary), 0.4);
        }
      `}</style>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-2 border-b">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Layers className="h-8 w-8 text-primary" />
            <span>企业架构展示</span>
          </h2>
          <p className="text-muted-foreground mt-1">查看和浏览企业各部门架构图</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomIn} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">放大</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleZoomOut} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">缩小</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleReset} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">重置</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={toggleFullscreen} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  <Maximize className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">全屏</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={handleDownload} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">下载</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={toggleControls} className="transition-all hover:bg-primary hover:text-primary-foreground">
                  {showControls ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">{showControls ? '隐藏控制面板' : '显示控制面板'}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className={cn(
        "grid grid-cols-1 md:grid-cols-4 gap-6 transition-all duration-300",
        isFullscreen && "hidden"
      )}>
        {/* 左侧架构图列表 */}
        <div className={cn("md:col-span-1 transition-all duration-300", {
          "md:col-span-0 md:hidden": !showControls
        })}>
          <Card className="h-full border-primary/20 shadow-md overflow-hidden">
            <CardHeader className="pb-3">
              <CardTitle className="text-xl flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-primary" />
                <span>架构图列表</span>
              </CardTitle>
              <CardDescription>
                点击选择架构图进行查看
              </CardDescription>
              <div className="relative mt-2">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="搜索架构图..."
                  className="w-full rounded-md border border-input pl-8 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1.5 max-h-[calc(100vh-350px)] overflow-y-auto pr-1 scrollbar-thin">
                {filteredImages.length > 0 ? (
                  filteredImages.map((name) => (
                    <div
                      key={name}
                      className={cn(
                        "p-2.5 rounded-md cursor-pointer flex items-center gap-2 transition-all",
                        activeTab === name
                          ? "bg-primary text-primary-foreground shadow-sm"
                          : "hover:bg-muted/50"
                      )}
                      onClick={() => {
                        setActiveTab(name)
                        setIsImageLoading(true)
                        setScale(1)
                        setPosition({ x: 0, y: 0 })
                      }}
                    >
                      {activeTab === name && (
                        <CheckCircle2 className="h-4 w-4 shrink-0" />
                      )}
                      <span className={activeTab === name ? "font-medium" : ""}>{name}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    没有找到相关架构图
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧图片查看区域 */}
        <div className={cn("md:col-span-3 transition-all duration-300", {
          "md:col-span-4": !showControls
        })}>
          <Card className="overflow-hidden border-primary/20 shadow-md">
            <CardContent className="p-0">
              <div className="flex items-center justify-between p-4 border-b bg-muted/30">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="px-3 py-1.5 text-base font-medium bg-background/80 backdrop-blur-sm">
                    {activeTab}
                  </Badge>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="rounded-full hover:bg-primary/10">
                          <Info className="h-4 w-4 text-primary" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs bg-primary/90 text-primary-foreground">
                        {imageMapping[activeTab].description}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="icon" onClick={handlePrevImage} className="rounded-full hover:bg-primary/10">
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Badge variant="outline" className="mx-1 bg-background/80">
                    {Object.keys(imageMapping).indexOf(activeTab) + 1} / {Object.keys(imageMapping).length}
                  </Badge>
                  <Button variant="ghost" size="icon" onClick={handleNextImage} className="rounded-full hover:bg-primary/10">
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <div
                className={cn(
                  "relative overflow-hidden cursor-move transition-all duration-300",
                  isFullscreen
                    ? "h-screen bg-black/90"
                    : "h-[calc(100vh-300px)] bg-muted/10"
                )}
                ref={imageContainerRef}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
              >
                {/* 加载状态 */}
                {isImageLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm z-10">
                    <div className="flex flex-col items-center gap-3">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                      <p className="text-muted-foreground">正在加载架构图...</p>
                    </div>
                  </div>
                )}

                <div
                  className={cn("absolute flex items-center justify-center w-full h-full transition-opacity", {
                    "opacity-0": isImageLoading,
                    "opacity-100": !isImageLoading
                  })}
                  style={{
                    transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                    transition: isDragging ? 'none' : 'transform 0.2s cubic-bezier(0.22, 1, 0.36, 1)'
                  }}
                >
                  <img
                    src={imageMapping[activeTab].file}
                    alt={activeTab}
                    className="max-w-full max-h-full object-contain drop-shadow-md transition-all duration-300"
                    style={{
                      filter: `brightness(${isFullscreen ? 1.05 : 1}) contrast(${isFullscreen ? 1.05 : 1})`,
                    }}
                    draggable="false"
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                  />
                </div>

                {/* 拖动提示 */}
                <div className="absolute bottom-4 right-4 bg-black/70 text-white px-4 py-2 rounded-full flex items-center gap-2 text-sm shadow-lg backdrop-blur-sm transition-all duration-300 hover:bg-black/90 hover:scale-105 group cursor-help">
                  <Move className="h-4 w-4 group-hover:animate-bounce" />
                  <span>拖动查看</span>
                </div>

                {/* 缩放比例指示器 */}
                <div
                  className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1.5 rounded-full text-xs shadow-lg backdrop-blur-sm transition-all duration-300"
                  style={{
                    transform: scale !== 1 ? 'scale(1.1)' : 'scale(1)',
                    background: scale > 1.5 ? 'rgba(0,0,0,0.8)' : 'rgba(0,0,0,0.7)'
                  }}
                >
                  缩放: {Math.round(scale * 100)}%
                </div>

                {/* 全屏模式下的控制面板 */}
                {isFullscreen && (
                  <>
                    {/* 快捷键提示 */}
                    <div className="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg text-xs shadow-lg backdrop-blur-sm transition-all duration-300 hover:bg-black/90 hover:shadow-xl">
                      <div className="font-medium mb-1 text-primary-foreground">快捷键:</div>
                      <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">←</kbd>
                          <span>上一张</span>
                        </div>
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">→</kbd>
                          <span>下一张</span>
                        </div>
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">+</kbd>
                          <span>放大</span>
                        </div>
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">-</kbd>
                          <span>缩小</span>
                        </div>
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">0</kbd>
                          <span>重置</span>
                        </div>
                        <div className="flex items-center gap-1.5 transition-all duration-200 hover:bg-white/10 px-1.5 py-0.5 rounded">
                          <kbd className="px-1.5 py-0.5 bg-white/20 rounded">Esc</kbd>
                          <span>退出全屏</span>
                        </div>
                      </div>
                    </div>

                    {/* 全屏模式下的控制按钮 */}
                    <div className="absolute top-4 right-4 flex items-center gap-2">
                      <div className="bg-black/70 text-white px-3 py-1.5 rounded-full text-sm shadow-lg backdrop-blur-sm">
                        {Object.keys(imageMapping).indexOf(activeTab) + 1} / {Object.keys(imageMapping).length}
                      </div>
                      <div className="flex items-center gap-1.5 bg-black/70 rounded-full p-1 backdrop-blur-sm transition-transform duration-300 hover:scale-105">
                        <Button variant="ghost" size="icon" onClick={handlePrevImage} className="h-8 w-8 rounded-full text-white hover:bg-white/20 transition-transform active:scale-90">
                          <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={handleNextImage} className="h-8 w-8 rounded-full text-white hover:bg-white/20 transition-transform active:scale-90">
                          <ChevronRight className="h-5 w-5" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-1.5 bg-black/70 rounded-full p-1 backdrop-blur-sm transition-transform duration-300 hover:scale-105">
                        <Button variant="ghost" size="icon" onClick={handleZoomIn} className="h-8 w-8 rounded-full text-white hover:bg-white/20 transition-transform active:scale-90">
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={handleZoomOut} className="h-8 w-8 rounded-full text-white hover:bg-white/20 transition-transform active:scale-90">
                          <ZoomOut className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={handleReset} className="h-8 w-8 rounded-full text-white hover:bg-white/20 transition-transform active:scale-90">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button variant="ghost" size="icon" onClick={toggleFullscreen} className="h-8 w-8 rounded-full bg-black/70 text-white hover:bg-white/20 backdrop-blur-sm transition-transform duration-300 hover:scale-105 active:scale-90">
                        <Maximize className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* 全屏模式下的图片标题 */}
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm shadow-lg backdrop-blur-sm transition-all duration-500 hover:bg-black/90 hover:px-6">
                      <span className="font-medium">{activeTab}</span> - <span className="text-white/90">{imageMapping[activeTab].description}</span>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
