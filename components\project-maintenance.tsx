"use client"

import { useState } from "react"
import {
  BarChart2,
  Calendar,
  Download,
  Filter,
  Plus,
  Search,
  Settings,
  <PERSON>ch,
  AlertTriangle,
  CheckCircle2,
  Clock,
  PenToolIcon as Tool,
  Activity,
  RefreshCcw,
  DollarSign,
  FileText,
  Upload,
  Pencil,
  Eye,
  Edit,
  X,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { toast } from "@/components/ui/use-toast"

export function ProjectMaintenance() {
  // 示例数据
  const maintenanceRecords = [
    {
      id: "1",
      project: "矿区A3开发项目",
      equipmentName: "挖掘机XC-200",
      maintenanceType: "定期保养",
      scheduledDate: "2025-01-15",
      actualDate: "2025-01-15",
      status: "已完成",
      performer: "张三",
      cost: 5000,
      nextScheduled: "2025-04-15",
      description: "更换机油、滤芯，检查液压系统",
    },
    {
      id: "2",
      project: "设备更新计划",
      equipmentName: "输送带系统",
      maintenanceType: "故障维修",
      scheduledDate: "2025-01-10",
      actualDate: "2025-01-11",
      status: "已完成",
      performer: "李四",
      cost: 8000,
      nextScheduled: "2025-04-10",
      description: "更换损坏的传动轴，调整皮带张力",
    },
    {
      id: "3",
      project: "安全系统升级",
      equipmentName: "监控系统",
      maintenanceType: "系统升级",
      scheduledDate: "2025-01-20",
      actualDate: "",
      status: "待执行",
      performer: "王五",
      cost: 12000,
      nextScheduled: "2025-04-01",
      description: "升级监控软件，更换部分摄像头",
    },
    {
      id: "4",
      project: "新矿区勘探",
      equipmentName: "勘探钻机",
      maintenanceType: "定期保养",
      scheduledDate: "2025-02-08",
      actualDate: "2025-02-09",
      status: "已完成",
      performer: "赵六",
      cost: 6500,
      nextScheduled: "2025-04-08",
      description: "检查钻头磨损情况，更换液压油",
    },
    {
      id: "5",
      project: "环保设施改造",
      equipmentName: "废水处理设备",
      maintenanceType: "故障维修",
      scheduledDate: "2025-03-18",
      actualDate: "",
      status: "待执行",
      performer: "钱七",
      cost: 9000,
      nextScheduled: "2025-04-01",
      description: "更换过滤装置，清洗沉淀池",
    },
  ]

  // 状态定义
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<any>(null)
  const [formData, setFormData] = useState({
    id: "",
    project: "",
    equipmentName: "",
    maintenanceType: "",
    scheduledDate: "",
    actualDate: "",
    status: "",
    performer: "",
    cost: 0,
    nextScheduled: "",
    description: ""
  })
  const [selectedFilters, setSelectedFilters] = useState({
    maintenanceType: "all",
    status: "all",
    dateRange: ""
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredRecords, setFilteredRecords] = useState(maintenanceRecords)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [isRefreshing, setIsRefreshing] = useState(false)

  // 统计数据
  const statistics = {
    total: 28,
    completed: 18,
    pending: 7,
    delayed: 3,
    typeDistribution: {
      "定期保养": 15,
      "故障维修": 8,
      "系统升级": 5
    },
    costByMonth: [
      { month: "1月", cost: 18000 },
      { month: "2月", cost: 24000 },
      { month: "3月", cost: 16000 },
      { month: "4月", cost: 30000 },
      { month: "5月", cost: 25000 },
      { month: "6月", cost: 22000 }
    ],
    equipmentMaintenance: [
      { name: "挖掘机", count: 6, cost: 35000 },
      { name: "输送带", count: 5, cost: 28000 },
      { name: "勘探设备", count: 4, cost: 42000 },
      { name: "监控系统", count: 4, cost: 18000 },
      { name: "废水处理", count: 3, cost: 15000 },
      { name: "其他设备", count: 6, cost: 22000 }
    ],
    monthlyMaintenance: [
      { month: "1月", planned: 5, completed: 5 },
      { month: "2月", planned: 6, completed: 5 },
      { month: "3月", planned: 4, completed: 4 },
      { month: "4月", planned: 8, completed: 6 },
      { month: "5月", planned: 6, completed: 6 },
      { month: "6月", planned: 7, completed: 5 }
    ],
    completionRate: 78.6, // 完成率
    avgCost: 10821.43, // 平均费用
    totalCost: 303000 // 总费用
  }

  // 获取状态对应的徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "已完成":
        return <Badge className="bg-green-500">已完成</Badge>
      case "待执行":
        return (
          <Badge variant="outline" className="text-yellow-500 border-yellow-500">
            待执行
          </Badge>
        )
      case "进行中":
        return (
          <Badge variant="outline" className="text-blue-500 border-blue-500">
            进行中
          </Badge>
        )
      case "已延期":
        return <Badge variant="destructive">已延期</Badge>
      case "已取消":
        return <Badge variant="secondary">已取消</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // 获取维护类型对应的图标
  const getMaintenanceTypeIcon = (type: string) => {
    switch (type) {
      case "定期保养":
        return <Tool className="h-4 w-4 text-blue-500" />
      case "故障维修":
        return <Wrench className="h-4 w-4 text-red-500" />
      case "系统升级":
        return <Activity className="h-4 w-4 text-green-500" />
      default:
        return null
    }
  }

  // 搜索和筛选功能
  const applyFilters = (term: string, filters: any) => {
    try {
      let results = [...maintenanceRecords];

      // 应用搜索词过滤
      if (term) {
        results = results.filter(record =>
          record.project.toLowerCase().includes(term.toLowerCase()) ||
          record.equipmentName.toLowerCase().includes(term.toLowerCase()) ||
          record.performer.toLowerCase().includes(term.toLowerCase()) ||
          record.description.toLowerCase().includes(term.toLowerCase())
        );
      }

      // 应用维护类型过滤
      if (filters.maintenanceType && filters.maintenanceType !== "all") {
        results = results.filter(record => record.maintenanceType === filters.maintenanceType);
      }

      // 应用状态过滤
      if (filters.status && filters.status !== "all") {
        results = results.filter(record => record.status === filters.status);
      }

      // 应用日期范围过滤（如果有）
      if (filters.dateRange) {
        // 假设dateRange是一个ISO日期字符串范围 "startDate,endDate"
        const [start, end] = filters.dateRange.split(',');
        if (start && end) {
          results = results.filter(record => {
            const actualDate = new Date(record.actualDate);
            return actualDate >= new Date(start) && actualDate <= new Date(end);
          });
        }
      }

      return results;
    } catch (error) {
      console.error("筛选过程中发生错误:", error);
      toast({
        title: "筛选错误",
        description: "应用筛选条件时出现问题，请重试",
        variant: "destructive",
      });
      return maintenanceRecords;
    }
  };

  const handleSearch = (term: string) => {
    try {
      setIsLoading(true);
      setSearchTerm(term);

      // 模拟搜索过程
      setTimeout(() => {
        const results = applyFilters(term, selectedFilters);
        setFilteredRecords(results);
        setIsLoading(false);
        console.log(`搜索: "${term}", 找到 ${results.length} 条记录`);
      }, 300);
    } catch (error) {
      setIsLoading(false);
      console.error("搜索过程中发生错误:", error);
      toast({
        title: "搜索错误",
        description: "执行搜索时出现问题，请重试",
        variant: "destructive",
      });
    }
  };

  const handleFilter = (filters: any) => {
    try {
      setIsLoading(true);
      setSelectedFilters(filters);

      // 更新活动选项卡
      if (filters.status === "已完成") {
        setActiveTab("completed");
      } else if (filters.status === "待执行") {
        setActiveTab("pending");
      } else if (filters.status === "进行中") {
        setActiveTab("in-progress");
      } else if (filters.status === "all") {
        setActiveTab("all");
      }

      // 模拟筛选过程
      setTimeout(() => {
        const results = applyFilters(searchTerm, filters);
        setFilteredRecords(results);
        setIsLoading(false);
        console.log(`应用筛选器: ${JSON.stringify(filters)}, 找到 ${results.length} 条记录`);
      }, 300);
    } catch (error) {
      setIsLoading(false);
      console.error("应用筛选器时发生错误:", error);
      toast({
        title: "筛选错误",
        description: "应用筛选条件时出现问题，请重试",
        variant: "destructive",
      });
    }
  };

  const handleRefresh = () => {
    try {
      setIsLoading(true);
      setIsRefreshing(true);

      // 模拟数据加载过程
      setTimeout(() => {
        setSearchTerm("");
        setSelectedFilters({
          maintenanceType: "all",
          status: "all",
          dateRange: ""
        });
        setActiveTab("all");
        setFilteredRecords(maintenanceRecords);

        setIsLoading(false);
        setIsRefreshing(false);

        toast({
          title: "刷新成功",
          description: "维护记录已重新加载",
        });
      }, 600); // 模拟网络延迟
    } catch (error) {
      setIsLoading(false);
      setIsRefreshing(false);
      console.error("刷新过程中发生错误:", error);
      toast({
        title: "刷新错误",
        description: "重新加载数据时出现问题，请重试",
        variant: "destructive",
      });
    }
  };

  // 导出记录功能
  const handleExportRecords = () => {
    // 导出当前筛选的记录
    const recordsToExport = filteredRecords.map(record => ({
      '项目名称': record.project,
      '设备名称': record.equipmentName,
      '维护类型': record.maintenanceType,
      '计划日期': record.scheduledDate,
      '实际日期': record.actualDate,
      '状态': record.status,
      '执行人': record.performer,
      '费用(元)': record.cost,
      '下次计划日期': record.nextScheduled,
      '描述': record.description
    }));

    console.log('导出记录:', recordsToExport);
    alert(`已导出 ${recordsToExport.length} 条记录`);

    // 实际项目中这里会调用导出到Excel/CSV的函数
  };

  // 添加记录功能
  const handleAddRecord = () => {
    // 验证必填字段
    if (!formData.project || !formData.equipmentName || !formData.maintenanceType) {
      toast({
        title: "错误",
        description: "请填写所有必填字段",
        variant: "destructive",
      });
      return;
    }

    // 创建新记录
    const newRecord = {
      ...formData,
      id: (maintenanceRecords.length + 1).toString(), // 实际项目中通常使用UUID
    };

    // 在实际项目中，这里会调用API向后端添加记录
    // 这里我们只是模拟添加到列表中
    console.log('添加新记录:', newRecord);

    // 重置表单并关闭对话框
    setFormData({
      id: "",
      project: "",
      equipmentName: "",
      maintenanceType: "",
      scheduledDate: "",
      actualDate: "",
      status: "",
      performer: "",
      cost: 0,
      nextScheduled: "",
      description: ""
    });
    setIsAddDialogOpen(false);

    toast({
      title: "添加成功",
      description: "新的维护记录已添加",
    });
  };

  // 导入数据功能
  const handleImportData = () => {
    // 这里只是模拟导入操作
    const fileName = "示例导入文件.xlsx";
    console.log(`导入文件: ${fileName}`);

    // 实际项目中会解析Excel/CSV文件并添加数据
    setIsImportDialogOpen(false);

    toast({
      title: "导入成功",
      description: `已导入文件: ${fileName}`,
    });
  };

  // 查看详情功能
  const handleViewDetails = (record: any) => {
    setCurrentRecord(record);
    setIsViewDetailsOpen(true);
  };

  // 编辑记录功能
  const handleEditRecord = (record: any) => {
    setCurrentRecord(record);
    setFormData({...record});
    setIsEditDialogOpen(true);
  };

  // 保存编辑功能
  const handleSaveEdit = () => {
    // 验证必填字段
    if (!formData.project || !formData.equipmentName || !formData.maintenanceType) {
      toast({
        title: "错误",
        description: "请填写所有必填字段",
        variant: "destructive",
      });
      return;
    }

    // 在实际项目中，这里会调用API更新后端数据
    console.log('保存编辑:', formData);

    setIsEditDialogOpen(false);
    toast({
      title: "编辑成功",
      description: "维护记录已更新",
    });
  };

  // 在组件顶部定义一个显示筛选状态的函数
  const getFilterStatusText = () => {
    const filterItems = [];

    if (selectedFilters.maintenanceType !== "all") {
      filterItems.push(`维护类型: ${selectedFilters.maintenanceType}`);
    }

    if (selectedFilters.status !== "all") {
      filterItems.push(`状态: ${selectedFilters.status}`);
    }

    if (searchTerm) {
      filterItems.push(`搜索: "${searchTerm}"`);
    }

    if (filterItems.length === 0) {
      return "";
    }

    return `已筛选: ${filterItems.join(", ")}`;
  };

  // 添加计算统计数据的函数
  const calculateStats = () => {
    const total = filteredRecords.length;
    const completed = filteredRecords.filter(record => record.status === "已完成").length;
    const pending = filteredRecords.filter(record => record.status === "待执行").length;
    const delayed = filteredRecords.filter(record => record.status === "已延期").length;

    return { total, completed, pending, delayed };
  };

  // 创建一个变量存储当前计算出的统计数据
  const currentStats = calculateStats();

  // 修改选项卡切换函数
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // 根据选项卡更新筛选状态
    if (value === "all") {
      handleFilter({...selectedFilters, status: "all"});
    } else if (value === "completed") {
      handleFilter({...selectedFilters, status: "已完成"});
    } else if (value === "pending") {
      handleFilter({...selectedFilters, status: "待执行"});
    } else if (value === "in-progress") {
      handleFilter({...selectedFilters, status: "进行中"});
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">工程维护管理</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExportRecords()}>
            <Download className="h-4 w-4 mr-2" />
            导出记录
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            导入数据
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className={`transition-all hover:bg-blue-50 hover:text-blue-600 ${isRefreshing ? 'opacity-70' : ''}`}
            disabled={isRefreshing}
          >
            <RefreshCcw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? '刷新中...' : '刷新'}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
            if (!open) {
              setFormData({
                id: "",
                project: "",
                equipmentName: "",
                maintenanceType: "",
                scheduledDate: "",
                actualDate: "",
                status: "",
                performer: "",
                cost: 0,
                nextScheduled: "",
                description: ""
              });
            }
            setIsAddDialogOpen(open);
          }}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white transition-colors">
                <Plus className="h-4 w-4 mr-2" />
                添加维护
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] overflow-y-auto max-h-[80vh]">
              <DialogHeader>
                <DialogTitle>添加维护记录</DialogTitle>
                <DialogDescription>记录设备维护和保养信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">工程项目</Label>
                    <Select
                      value={formData.project}
                      onValueChange={(value) => setFormData({...formData, project: value})}
                    >
                      <SelectTrigger id="project">
                        <SelectValue placeholder="选择工程项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                        <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                        <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                        <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                        <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-name">设备名称</Label>
                    <Input
                      id="equipment-name"
                      placeholder="输入设备名称"
                      value={formData.equipmentName}
                      onChange={(e) => setFormData({...formData, equipmentName: e.target.value})}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maintenance-type">维护类型</Label>
                    <Select
                      value={formData.maintenanceType}
                      onValueChange={(value) => setFormData({...formData, maintenanceType: value})}
                    >
                      <SelectTrigger id="maintenance-type">
                        <SelectValue placeholder="选择维护类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="定期保养">定期保养</SelectItem>
                        <SelectItem value="故障维修">故障维修</SelectItem>
                        <SelectItem value="系统升级">系统升级</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => setFormData({...formData, status: value})}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="已完成">已完成</SelectItem>
                        <SelectItem value="待执行">待执行</SelectItem>
                        <SelectItem value="进行中">进行中</SelectItem>
                        <SelectItem value="已延期">已延期</SelectItem>
                        <SelectItem value="已取消">已取消</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="scheduled-date">计划日期</Label>
                    <Input
                      id="scheduled-date"
                      type="date"
                      value={formData.scheduledDate}
                      onChange={(e) => setFormData({...formData, scheduledDate: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="actual-date">实际日期</Label>
                    <Input
                      id="actual-date"
                      type="date"
                      value={formData.actualDate || ""}
                      onChange={(e) => setFormData({...formData, actualDate: e.target.value})}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="performer">执行人员</Label>
                    <Input
                      id="performer"
                      placeholder="输入执行人员姓名"
                      value={formData.performer}
                      onChange={(e) => setFormData({...formData, performer: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cost">维护费用 (元)</Label>
                    <Input
                      id="cost"
                      type="number"
                      min="0"
                      placeholder="输入维护费用"
                      value={formData.cost?.toString() || ""}
                      onChange={(e) => setFormData({...formData, cost: Number(e.target.value)})}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="next-scheduled">下次计划日期</Label>
                  <Input
                    id="next-scheduled"
                    type="date"
                    value={formData.nextScheduled}
                    onChange={(e) => setFormData({...formData, nextScheduled: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">维护描述</Label>
                  <Textarea
                    id="description"
                    placeholder="输入维护工作详细描述"
                    rows={3}
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                  />
                </div>
              </div>
              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                  className="hover:bg-red-50 hover:text-red-600 transition-colors"
                >
                  取消
                </Button>
                <Button
                  onClick={handleAddRecord}
                  className="bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <Wrench className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">{currentStats.total}</h3>
            <p className="text-sm text-muted-foreground">总维护记录</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">{currentStats.completed}</h3>
            <p className="text-sm text-muted-foreground">已完成维护</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-yellow-100 p-3 mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold">{currentStats.pending}</h3>
            <p className="text-sm text-muted-foreground">待执行维护</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-red-100 p-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-xl font-bold">{currentStats.delayed}</h3>
            <p className="text-sm text-muted-foreground">已延期维护</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>维护记录列表</CardTitle>
              <CardDescription>查看和管理所有设备维护记录</CardDescription>
            </div>
            <Tabs
              defaultValue="all"
              value={activeTab}
              onValueChange={handleTabChange}
              className="w-full transition-all duration-300"
            >
              <TabsList className="grid grid-cols-4 w-80">
                <TabsTrigger value="all" className="transition-all duration-200">全部</TabsTrigger>
                <TabsTrigger value="completed" className="transition-all duration-200">已完成</TabsTrigger>
                <TabsTrigger value="pending" className="transition-all duration-200">待执行</TabsTrigger>
                <TabsTrigger value="in-progress" className="transition-all duration-200">进行中</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2 flex-wrap">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="搜索记录..."
                  className="pl-8 w-[250px] transition-all focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
              <Select
                value={selectedFilters.maintenanceType}
                onValueChange={(value) => handleFilter({...selectedFilters, maintenanceType: value})}
              >
                <SelectTrigger className="w-[150px] transition-all hover:border-blue-200">
                  <SelectValue placeholder="维护类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="定期保养">定期保养</SelectItem>
                  <SelectItem value="故障维修">故障维修</SelectItem>
                  <SelectItem value="系统升级">系统升级</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={selectedFilters.status}
                onValueChange={(value) => handleFilter({...selectedFilters, status: value})}
              >
                <SelectTrigger className="w-[150px] transition-all hover:border-blue-200">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="已完成">已完成</SelectItem>
                  <SelectItem value="待执行">待执行</SelectItem>
                  <SelectItem value="进行中">进行中</SelectItem>
                  <SelectItem value="已延期">已延期</SelectItem>
                  <SelectItem value="已取消">已取消</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                  onClick={() => {
                    // 清除所有筛选条件
                    handleFilter({
                      maintenanceType: "all",
                      status: "all",
                      dateRange: ""
                    });
                  }}
                >
                  <X className="h-4 w-4 mr-1" />
                  清除筛选
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted transition-colors"
                  onClick={handleRefresh}
                >
                  <RefreshCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="rounded-md border overflow-hidden shadow-sm transition-all duration-300 hover:shadow-md">
            <Table>
              <TableHeader className="bg-muted/50">
                <TableRow>
                  <TableHead className="w-[80px] font-medium">ID</TableHead>
                  <TableHead className="font-medium">项目名称</TableHead>
                  <TableHead className="font-medium">设备名称</TableHead>
                  <TableHead className="font-medium">维护类型</TableHead>
                  <TableHead className="font-medium">计划日期</TableHead>
                  <TableHead className="font-medium">状态</TableHead>
                  <TableHead className="font-medium">执行人员</TableHead>
                  <TableHead className="font-medium">费用(元)</TableHead>
                  <TableHead className="text-right font-medium">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // 加载状态
                  <TableRow>
                    <TableCell colSpan={9} className="h-24">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-700"></div>
                        <span className="ml-2 text-sm text-muted-foreground">加载中...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRecords.length === 0 ? (
                  // 无数据状态
                  <TableRow>
                    <TableCell colSpan={9} className="h-32">
                      <div className="flex flex-col items-center justify-center text-center p-4">
                        <div className="rounded-full bg-muted p-3 mb-2">
                          <Search className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-1">未找到匹配的记录</h3>
                        <p className="text-sm text-muted-foreground mb-4 max-w-md">
                          {searchTerm ?
                            `没有找到包含 "${searchTerm}" 的记录` :
                            "当前筛选条件下没有匹配的维护记录"}
                        </p>
                        <Button variant="outline" size="sm" onClick={handleRefresh}>
                          <RefreshCcw className="h-4 w-4 mr-2" />
                          重置筛选
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecords.map((record) => (
                    <TableRow
                      key={record.id}
                      className="hover:bg-blue-50/30 transition-all duration-200 group cursor-default"
                    >
                      <TableCell className="font-medium transition-colors group-hover:text-blue-700">{record.id}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{record.project}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{record.equipmentName}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">
                        <div className="flex items-center gap-2">
                          {getMaintenanceTypeIcon(record.maintenanceType)}
                          <span>{record.maintenanceType}</span>
                        </div>
                      </TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{record.scheduledDate}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{getStatusBadge(record.status)}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{record.performer}</TableCell>
                      <TableCell className="transition-colors group-hover:text-blue-700">{record.cost.toLocaleString()}</TableCell>
                      <TableCell className="text-right font-medium">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewDetails(record)}
                            className="h-8 w-8 rounded-full hover:bg-blue-50 hover:text-blue-600 transition-colors"
                            title="查看详情"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditRecord(record)}
                            className="h-8 w-8 rounded-full hover:bg-amber-50 hover:text-amber-600 transition-colors"
                            title="编辑记录"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* 添加筛选状态显示 */}
          {getFilterStatusText() && (
            <div className="text-sm text-muted-foreground mt-2 flex items-center gap-2">
              <span>{getFilterStatusText()}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 rounded-full hover:bg-red-50 hover:text-red-500"
                onClick={handleRefresh}
                title="清除所有筛选"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">共 {filteredRecords.length} 条记录，从 {maintenanceRecords.length} 条总记录中筛选</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="px-3 hover:bg-blue-50 hover:text-blue-600">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>维护类型分布</CardTitle>
          </CardHeader>
          <CardContent className="h-70">
            <div className="h-full flex flex-col justify-center">
              <div className="space-y-6">
                {Object.entries(statistics.typeDistribution).map(([type, count]) => {
                  const percentage = Math.round((count / statistics.total) * 100);
                  const colors = {
                    "定期保养": "bg-blue-400",
                    "故障维修": "bg-red-400",
                    "系统升级": "bg-green-400"
                  };

                  const color = colors[type as keyof typeof colors] || "bg-gray-500";

                  return (
                    <div key={type} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${color}`}></div>
                          <span className="text-sm font-medium">{type}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{count}次</span>
                          <span className="text-xs bg-muted px-1.5 rounded-full">{percentage}%</span>
                        </div>
                      </div>
                      <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                        <div
                          className={`h-full rounded-full ${color}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="mt-8 flex justify-center">
                <div className="w-40 h-40 relative rounded-full border-8 border-muted overflow-hidden">
                  {Object.entries(statistics.typeDistribution).map(([type, count], index) => {
                    const total = Object.values(statistics.typeDistribution).reduce((sum, c) => sum + c, 0);
                    const percentage = (count / total) * 100;

                    let startPercentage = 0;
                    Object.entries(statistics.typeDistribution).slice(0, index).forEach(([, c]) => {
                      startPercentage += (c / total) * 100;
                    });

                    const colors = {
                      "定期保养": "#3b82f6",
                      "故障维修": "#ef4444",
                      "系统升级": "#22c55e"
                    };

                    const color = colors[type as keyof typeof colors] || "#94a3b8";

                    const styleObj = {
                      position: 'absolute',
                      top: '0',
                      left: '0',
                      width: '100%',
                      height: '100%',
                      background: `conic-gradient(${color} ${startPercentage}% ${startPercentage + percentage}%, transparent ${startPercentage + percentage}% 100%)`
                    } as React.CSSProperties;

                    return (
                      <div key={`maintenance-type-pie-${type}`} style={styleObj}></div>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>维护费用趋势</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <div className="h-full flex items-center justify-center">
              <div className="w-full h-[250px]">
                <div className="flex h-[200px] items-end gap-2">
                  {statistics.costByMonth.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div className="text-xs font-medium mb-1">{(item.cost / 1000).toFixed(0)}k</div>
                      <div
                        className="w-full bg-blue-500 rounded-t-md"
                        style={{
                          height: `${(item.cost / Math.max(...statistics.costByMonth.map(i => i.cost))) * 180}px`
                        }}
                      ></div>
                      <div className="mt-2 text-xs text-muted-foreground">{item.month}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 统计分析抽屉 */}
      <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
        <SheetContent className="w-full md:max-w-[680px] sm:max-w-md overflow-y-auto p-3">
          <SheetHeader className="mb-6">
            <SheetTitle className="text-2xl">维护统计分析</SheetTitle>
            <SheetDescription>查看工程维护统计数据和图表分析</SheetDescription>
          </SheetHeader>

          <div className="space-y-8">
            {/* 总览卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-3">
                    <div className="p-3 rounded-full bg-blue-50">
                      <FileText className="h-8 w-8 text-blue-500" />
                    </div>
                    <h3 className="text-2xl font-bold">{statistics.total}</h3>
                    <p className="text-sm text-center text-muted-foreground">
                      总维护记录
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-3">
                    <div className="p-3 rounded-full bg-green-50">
                      <CheckCircle2 className="h-8 w-8 text-green-500" />
                    </div>
                    <h3 className="text-2xl font-bold">{statistics.completed}</h3>
                    <p className="text-sm text-center text-muted-foreground">
                      已完成维护
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-3">
                    <div className="p-3 rounded-full bg-yellow-50">
                      <Clock className="h-8 w-8 text-yellow-500" />
                    </div>
                    <h3 className="text-2xl font-bold">{statistics.pending}</h3>
                    <p className="text-sm text-center text-muted-foreground">
                      待执行维护
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-3">
                    <div className="p-3 rounded-full bg-red-50">
                      <AlertTriangle className="h-8 w-8 text-red-500" />
                    </div>
                    <h3 className="text-2xl font-bold">{statistics.delayed}</h3>
                    <p className="text-sm text-center text-muted-foreground">
                      已延期维护
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 完成率进度 */}
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle>维护完成率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row items-center gap-6 justify-center">
                  <div className="w-48 h-48 rounded-full border-8 border-muted flex items-center justify-center relative overflow-hidden">
                    <div
                      className="absolute bottom-0 left-0 right-0 bg-green-500"
                      style={{ height: `${statistics.completionRate}%` }}
                    ></div>
                    <div className="z-10 bg-card rounded-full w-36 h-36 flex items-center justify-center shadow-sm">
                      <div className="text-center">
                        <div className="text-4xl font-bold">
                          {Math.round(statistics.completionRate)}%
                        </div>
                        <div className="text-sm text-muted-foreground">完成率</div>
                      </div>
                    </div>
                  </div>

                  <div className="w-full max-w-md space-y-4">
                    <div className="space-y-1.5">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">维护总数</span>
                        <span className="text-sm font-medium">{statistics.total}</span>
                      </div>
                      <Progress value={100} className="h-2.5" />
                    </div>

                    <div className="space-y-1.5">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">已完成维护</span>
                        <span className="text-sm font-medium">{statistics.completed}</span>
                      </div>
                      <Progress
                        value={statistics.total ? (statistics.completed / statistics.total) * 100 : 0}
                        className="h-2.5 bg-muted"
                      />
                    </div>

                    <div className="space-y-1.5">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">待执行维护</span>
                        <span className="text-sm font-medium">{statistics.pending}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2.5 overflow-hidden">
                        <div
                          className="bg-amber-500 h-full rounded-full"
                          style={{ width: `${statistics.total ? (statistics.pending / statistics.total) * 100 : 0}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="space-y-1.5">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">已延期维护</span>
                        <span className="text-sm font-medium">{statistics.delayed}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2.5 overflow-hidden">
                        <div
                          className="bg-red-500 h-full rounded-full"
                          style={{ width: `${statistics.total ? (statistics.delayed / statistics.total) * 100 : 0}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 月度维护情况 */}
            <Card className="h-[450px] flex flex-col hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle>月度维护情况</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto">
                <div className="h-full flex items-center justify-center">
                  <div className="w-full h-full flex flex-col">
                    <div className="flex-1 flex flex-col justify-center">
                      <div className="flex h-60 items-end gap-2">
                        {statistics.monthlyMaintenance.map((item, index) => (
                          <div key={index} className="flex-1 flex flex-col items-center">
                            <div className="relative w-full flex flex-col items-center gap-1">
                              <div className="w-full flex gap-1">
                                <div
                                  className="flex-1 bg-blue-500 rounded-t-md"
                                  style={{ height: `${Math.min(100, item.planned * 10)}px` }}
                                  title={`计划维护: ${item.planned}`}
                                ></div>
                                <div
                                  className="flex-1 bg-green-500 rounded-t-md"
                                  style={{ height: `${Math.min(100, item.completed * 10)}px` }}
                                  title={`完成维护: ${item.completed}`}
                                ></div>
                              </div>
                              <div className="absolute -top-6 text-xs font-medium">
                                <div className="flex gap-2">
                                  <span className="text-blue-500">{item.planned}</span>
                                  <span className="text-green-500">{item.completed}</span>
                                </div>
                              </div>
                            </div>
                            <div className="mt-2 text-xs text-muted-foreground">{item.month}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mt-4 flex justify-center gap-8">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded"></div>
                        <span className="text-sm">计划维护</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded"></div>
                        <span className="text-sm">完成维护</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 设备维护统计 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="h-[450px] flex flex-col hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle>设备维护频率</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto">
                  <div className="space-y-6">
                    {statistics.equipmentMaintenance.map((item, index) => {
                      const percentage = (item.count / statistics.total) * 100;
                      const colors = ["bg-blue-500", "bg-green-500", "bg-purple-500", "bg-yellow-500", "bg-red-500", "bg-indigo-500"];
                      const color = colors[index % colors.length];

                      return (
                        <div key={item.name} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${color}`}></div>
                              <span className="text-sm font-medium">{item.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">{item.count}次</span>
                              <span className="text-xs bg-muted px-1.5 rounded-full">{percentage.toFixed(1)}%</span>
                            </div>
                          </div>
                          <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full ${color}`}
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="h-[450px] flex flex-col hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle>维护费用分布</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto">
                  <div className="space-y-6">
                    {statistics.equipmentMaintenance.map((item, index) => {
                      const percentage = (item.cost / statistics.totalCost) * 100;
                      const colors = ["bg-blue-500", "bg-green-500", "bg-purple-500", "bg-yellow-500", "bg-red-500", "bg-indigo-500"];
                      const color = colors[index % colors.length];

                      return (
                        <div key={`cost-${item.name}`} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${color}`}></div>
                              <span className="text-sm font-medium">{item.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">{item.cost.toLocaleString()}元</span>
                              <span className="text-xs bg-muted px-1.5 rounded-full">{percentage.toFixed(1)}%</span>
                            </div>
                          </div>
                          <div className="w-full h-2.5 bg-secondary rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full ${color}`}
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <div className="mt-8 flex flex-col items-center">
                    <div className="flex items-center gap-4 mt-4">
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">总维护费用</p>
                        <p className="text-xl font-bold mt-1">{statistics.totalCost.toLocaleString()}元</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">平均单次费用</p>
                        <p className="text-xl font-bold mt-1">{Math.round(statistics.avgCost).toLocaleString()}元</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <SheetFooter className="pt-4">
            <Button onClick={() => setIsStatsDrawerOpen(false)}>关闭</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* 导入数据对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>导入维护记录</DialogTitle>
            <DialogDescription>上传Excel文件或CSV文件以批量导入维护记录数据</DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="flex flex-col items-center justify-center gap-4 border-2 border-dashed border-muted-foreground/25 rounded-lg p-8">
              <Upload className="h-8 w-8 text-muted-foreground" />
              <div className="text-center">
                <p className="font-medium">点击上传文件或拖放至此处</p>
                <p className="text-sm text-muted-foreground">支持 .xlsx, .xls, .csv 格式</p>
              </div>
              <Input
                type="file"
                className="hidden"
                id="file-upload"
                accept=".xlsx,.xls,.csv"
              />
              <Button variant="outline" onClick={() => document.getElementById('file-upload')?.click()}>
                选择文件
              </Button>
            </div>

            <div className="space-y-2">
              <Label htmlFor="import-options">导入选项</Label>
              <Select defaultValue="append">
                <SelectTrigger id="import-options">
                  <SelectValue placeholder="选择导入方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="append">追加到现有记录</SelectItem>
                  <SelectItem value="overwrite">覆盖现有记录</SelectItem>
                  <SelectItem value="update">更新现有记录（按ID匹配）</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>取消</Button>
            <Button onClick={handleImportData}>导入</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent className="sm:max-w-[600px] overflow-y-auto max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>维护记录详情</DialogTitle>
            <DialogDescription>查看完整的维护记录信息</DialogDescription>
          </DialogHeader>
          {currentRecord && (
            <div className="space-y-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">工程项目</h4>
                  <p className="text-base mt-1">{currentRecord.project}</p>
                </div>
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">设备名称</h4>
                  <p className="text-base mt-1">{currentRecord.equipmentName}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">维护类型</h4>
                  <div className="flex items-center gap-2 mt-1">
                    {getMaintenanceTypeIcon(currentRecord.maintenanceType)}
                    <span>{currentRecord.maintenanceType}</span>
                  </div>
                </div>
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">状态</h4>
                  <div className="mt-1">{getStatusBadge(currentRecord.status)}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">计划日期</h4>
                  <p className="text-base mt-1">{currentRecord.scheduledDate}</p>
                </div>
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">实际日期</h4>
                  <p className="text-base mt-1">{currentRecord.actualDate || "-"}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">执行人员</h4>
                  <p className="text-base mt-1">{currentRecord.performer}</p>
                </div>
                <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                  <h4 className="text-sm font-medium text-muted-foreground">维护费用</h4>
                  <p className="text-base mt-1">{currentRecord.cost.toLocaleString()} 元</p>
                </div>
              </div>

              <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                <h4 className="text-sm font-medium text-muted-foreground">下次计划日期</h4>
                <p className="text-base mt-1">{currentRecord.nextScheduled}</p>
              </div>

              <div className="hover:bg-blue-50/30 p-2 rounded-md transition-colors">
                <h4 className="text-sm font-medium text-muted-foreground">维护描述</h4>
                <p className="text-sm mt-1 bg-muted p-3 rounded-md whitespace-pre-wrap">
                  {currentRecord.description}
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => handleEditRecord(currentRecord)}
              className="hover:bg-amber-50 hover:text-amber-600 transition-colors"
            >
              <Pencil className="h-4 w-4 mr-2" />
              编辑记录
            </Button>
            <Button
              onClick={() => setIsViewDetailsOpen(false)}
              className="bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑记录对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑维护记录</DialogTitle>
            <DialogDescription>修改设备维护和保养信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-project">工程项目</Label>
                <Select
                  value={formData.project}
                  onValueChange={(value) => setFormData({...formData, project: value})}
                >
                  <SelectTrigger id="edit-project">
                    <SelectValue placeholder="选择工程项目" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="矿区A3开发项目">矿区A3开发项目</SelectItem>
                    <SelectItem value="设备更新计划">设备更新计划</SelectItem>
                    <SelectItem value="安全系统升级">安全系统升级</SelectItem>
                    <SelectItem value="新矿区勘探">新矿区勘探</SelectItem>
                    <SelectItem value="环保设施改造">环保设施改造</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-equipment-name">设备名称</Label>
                <Input
                  id="edit-equipment-name"
                  placeholder="输入设备名称"
                  value={formData.equipmentName}
                  onChange={(e) => setFormData({...formData, equipmentName: e.target.value})}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-maintenance-type">维护类型</Label>
                <Select
                  value={formData.maintenanceType}
                  onValueChange={(value) => setFormData({...formData, maintenanceType: value})}
                >
                  <SelectTrigger id="edit-maintenance-type">
                    <SelectValue placeholder="选择维护类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="定期保养">定期保养</SelectItem>
                    <SelectItem value="故障维修">故障维修</SelectItem>
                    <SelectItem value="系统升级">系统升级</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value})}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="已完成">已完成</SelectItem>
                    <SelectItem value="待执行">待执行</SelectItem>
                    <SelectItem value="进行中">进行中</SelectItem>
                    <SelectItem value="已延期">已延期</SelectItem>
                    <SelectItem value="已取消">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-scheduled-date">计划日期</Label>
                <Input
                  id="edit-scheduled-date"
                  type="date"
                  value={formData.scheduledDate}
                  onChange={(e) => setFormData({...formData, scheduledDate: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-actual-date">实际日期</Label>
                <Input
                  id="edit-actual-date"
                  type="date"
                  value={formData.actualDate || ""}
                  onChange={(e) => setFormData({...formData, actualDate: e.target.value})}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-performer">执行人员</Label>
                <Input
                  id="edit-performer"
                  placeholder="输入执行人员姓名"
                  value={formData.performer}
                  onChange={(e) => setFormData({...formData, performer: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-cost">维护费用 (元)</Label>
                <Input
                  id="edit-cost"
                  type="number"
                  min="0"
                  placeholder="输入维护费用"
                  value={formData.cost?.toString() || ""}
                  onChange={(e) => setFormData({...formData, cost: Number(e.target.value)})}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-next-scheduled">下次计划日期</Label>
              <Input
                id="edit-next-scheduled"
                type="date"
                value={formData.nextScheduled}
                onChange={(e) => setFormData({...formData, nextScheduled: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">维护描述</Label>
              <Textarea
                id="edit-description"
                placeholder="输入维护工作详细描述"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>保存更改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

