'use client';

import { MainLayout } from "@/components/main-layout"
import { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, InputNumber, Upload, message, Tooltip, Badge, Progress, Row, Col, Statistic, Popconfirm, Drawer } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined, FileTextOutlined, ClockCircleOutlined, TeamOutlined, CheckCircleOutlined, ExclamationCircleOutlined, SearchOutlined, FilterOutlined, ReloadOutlined, ExportOutlined, PrinterOutlined, EyeOutlined, CommentOutlined, BarChartOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import dayjs from 'dayjs';
import type { Rule } from 'antd/es/form';
import * as XLSX from 'xlsx-js-style';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

interface Task {
  id: string;
  title: string;
  type: string;
  status: string;
  priority: string;
  assignee: string;
  deadline: string;
  description: string;
  progress: number;
  attachments: string[];
  comments: {
    id: string;
    user: string;
    content: string;
    time: string;
  }[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  department?: string;
  tags?: string[];
  estimatedHours?: number;
  actualHours?: number;
}

export default function TaskManagementPage() {
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: '设备维护计划制定',
      type: '维护任务',
      status: '进行中',
      priority: '高',
      assignee: '张三',
      deadline: '2025-03-20',
      description: '制定2025年第一季度设备维护计划，包括日常维护和定期检修安排。',
      progress: 65,
      attachments: ['维护计划.docx', '设备清单.xlsx'],
      comments: [
        {
          id: '1',
          user: '李四',
          content: '已收到任务，正在制定计划。',
          time: '2025-03-14 10:30',
        },
      ],
      createdBy: '王五',
      createdAt: '2025-03-14 09:00',
      updatedAt: '2025-03-14 10:30',
    },
    {
      id: '2',
      title: '备件采购申请',
      type: '采购任务',
      status: '待处理',
      priority: '中',
      assignee: '李四',
      deadline: '2025-03-25',
      description: '根据设备维护计划，申请采购必要的备件和耗材。',
      progress: 0,
      attachments: ['采购清单.xlsx'],
      comments: [],
      createdBy: '张三',
      createdAt: '2025-03-14 14:00',
      updatedAt: '2025-03-14 14:00',
    },
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [commentForm] = Form.useForm();

  // 新增状态
  const [searchText, setSearchText] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isStatsVisible, setIsStatsVisible] = useState(false);

  // 统计数据
  const statistics = {
    total: tasks.length,
    pending: tasks.filter(t => t.status === '待处理').length,
    inProgress: tasks.filter(t => t.status === '进行中').length,
    completed: tasks.filter(t => t.status === '已完成').length,
    highPriority: tasks.filter(t => t.priority === '高').length,
    byType: {
      maintenance: tasks.filter(t => t.type === '维护任务').length,
      purchase: tasks.filter(t => t.type === '采购任务').length,
      other: tasks.filter(t => !['维护任务', '采购任务'].includes(t.type)).length,
    },
    byPriority: {
      high: tasks.filter(t => t.priority === '高').length,
      medium: tasks.filter(t => t.priority === '中').length,
      low: tasks.filter(t => t.priority === '低').length,
    },
    overdue: tasks.filter(t =>
      t.status !== '已完成' &&
      dayjs(t.deadline).isBefore(dayjs(), 'day')
    ).length,
    completionRate: tasks.length > 0
      ? ((tasks.filter(t => t.status === '已完成').length / tasks.length) * 100).toFixed(1)
      : '0',
  };

  const columns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: Task) => (
        <Tooltip title="点击查看详情">
          <a onClick={() => handleView(record)}>{text}</a>
        </Tooltip>
      ),
    },
    {
      title: '任务类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === '维护任务' ? 'blue' : 'green'}>{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={
            status === '进行中' ? 'processing' :
            status === '待处理' ? 'warning' :
            status === '已完成' ? 'success' : 'default'
          }
          text={status}
        />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={
          priority === '高' ? 'red' :
          priority === '中' ? 'orange' : 'green'
        }>{priority}</Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      render: (assignee: string) => (
        <Space>
          <TeamOutlined />
          {assignee}
        </Space>
      ),
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      render: (deadline: string) => (
        <Space>
          <ClockCircleOutlined />
          {deadline}
        </Space>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Task) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingTask(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (task: Task) => {
    setEditingTask(task);
    form.setFieldsValue({
      ...task,
      deadline: dayjs(task.deadline),
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newTasks = tasks.filter(task => task.id !== id);
        setTasks(newTasks);
        message.success('任务已删除');
      },
    });
  };

  const handleView = (task: Task) => {
    setSelectedTask(task);
    setIsViewModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const formattedValues = {
        ...values,
        deadline: values.deadline ? dayjs(values.deadline).format('YYYY-MM-DD') : '',
        attachments: values.attachments ? values.attachments.fileList?.map((file: any) => file.name) || [] : [],
      };

      if (editingTask) {
        // 编辑现有任务
        const updatedTasks = tasks.map(task =>
          task.id === editingTask.id
            ? {
                ...task,
                ...formattedValues,
                updatedAt: new Date().toLocaleString(),
              }
            : task
        );
        setTasks(updatedTasks);
        message.success('任务已更新');
      } else {
        // 添加新任务
        const newTask: Task = {
          id: Date.now().toString(),
          ...formattedValues,
          progress: formattedValues.progress || 0,
          attachments: formattedValues.attachments || [],
          comments: [],
          createdBy: '当前用户',
          createdAt: new Date().toLocaleString(),
          updatedAt: new Date().toLocaleString(),
        };
        setTasks([...tasks, newTask]);
        message.success('任务已创建');
      }
      setIsModalVisible(false);
      setEditingTask(null);
      form.resetFields();
    });
  };

  const handleAddComment = (taskId: string) => {
    commentForm.validateFields().then(values => {
      if (!values.comment.trim()) {
        message.error('评论内容不能为空');
        return;
      }

      setTasks(tasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            comments: [
              ...task.comments,
              {
                id: Date.now().toString(),
                user: '当前用户',
                content: values.comment.trim(),
                time: new Date().toLocaleString(),
              },
            ],
            updatedAt: new Date().toLocaleString(),
          };
        }
        return task;
      }));
      commentForm.resetFields();
      message.success('评论已添加');
    });
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/upload',
    headers: {
      authorization: 'authorization-text',
    },
    multiple: true,
    beforeUpload: (file) => {
      const isValidSize = file.size / 1024 / 1024 < 10; // 限制文件大小为10MB
      if (!isValidSize) {
        message.error(`${file.name} 超过10MB限制`);
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  // 批量操作函数
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个任务吗？`,
      onOk: () => {
        setTasks(tasks.filter(task => !selectedRowKeys.includes(task.id)));
        setSelectedRowKeys([]);
        message.success('批量删除成功');
      },
    });
  };

  const handleBatchUpdateStatus = (status: string) => {
    setTasks(tasks.map(task =>
      selectedRowKeys.includes(task.id) ? { ...task, status } : task
    ));
    setSelectedRowKeys([]);
    message.success('批量更新状态成功');
  };

  // 导出Excel
  const handleExportExcel = () => {
    try {
      // 准备导出数据
      const exportData = tasks.map(task => ({
        '任务标题': task.title,
        '任务类型': task.type,
        '状态': task.status,
        '优先级': task.priority,
        '负责人': task.assignee,
        '截止日期': task.deadline,
        '进度': `${task.progress}%`,
        '创建时间': task.createdAt,
        '更新时间': task.updatedAt,
        '描述': task.description,
      }));

      // 创建工作簿和工作表
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData, { header: Object.keys(exportData[0]) });

      // 设置列宽
      const colWidths = [
        { wch: 30 }, // 任务标题
        { wch: 15 }, // 任务类型
        { wch: 10 }, // 状态
        { wch: 10 }, // 优先级
        { wch: 10 }, // 负责人
        { wch: 15 }, // 截止日期
        { wch: 10 }, // 进度
        { wch: 20 }, // 创建时间
        { wch: 20 }, // 更新时间
        { wch: 50 }, // 描述
      ];
      ws['!cols'] = colWidths;

      // 添加样式
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4472C4" } },
        alignment: { horizontal: "center", vertical: "center" }
      };

      // 为表头添加样式
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + "1";
        if (!ws[address]) continue;
        ws[address].s = headerStyle;
      }

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '任务列表');

      // 导出文件
      XLSX.writeFile(wb, `任务列表_${dayjs().format('YYYY-MM-DD')}.xlsx`);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请检查是否安装了 xlsx-js-style 依赖');
    }
  };

  // 打印
  const handlePrint = () => {
    window.print();
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      // 这里可以添加实际的数据刷新逻辑
      setLoading(false);
      message.success('数据已刷新');
    }, 1000);
  };

  // 表格选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: Task[]) => {
      setSelectedRowKeys(selectedKeys as string[]);
    },
  };

  // 搜索和筛选
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchText.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchText.toLowerCase()) ||
                         task.assignee.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(task.type);
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(task.status);
    const matchesPriority = selectedPriorities.length === 0 || selectedPriorities.includes(task.priority);
    return matchesSearch && matchesType && matchesStatus && matchesPriority;
  });

  // 渲染统计抽屉
  const renderStatsDrawer = () => (
    <Drawer
      title={
        <Space>
          <BarChartOutlined />
          任务统计分析
        </Space>
      }
      placement="right"
      onClose={() => setIsStatsVisible(false)}
      open={isStatsVisible}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 总体完成情况 */}
        <Card>
          <Statistic
            title="任务完成率"
            value={statistics.completionRate}
            suffix="%"
            prefix={<CheckCircleOutlined />}
          />
          <Progress
            percent={Number(statistics.completionRate)}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </Card>

        {/* 任务状态分布 */}
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <Statistic
                title="总任务数"
                value={statistics.total}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="进行中"
                value={statistics.inProgress}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="待处理"
                value={statistics.pending}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 任务类型分布 */}
        <Card title="任务类型分布">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="维护任务"
                value={statistics.byType.maintenance}
                suffix={`/ ${statistics.total}`}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="采购任务"
                value={statistics.byType.purchase}
                suffix={`/ ${statistics.total}`}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="其他任务"
                value={statistics.byType.other}
                suffix={`/ ${statistics.total}`}
              />
            </Col>
          </Row>
        </Card>

        {/* 优先级分布 */}
        <Card title="优先级分布">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="高优先级"
                value={statistics.byPriority.high}
                valueStyle={{ color: '#cf1322' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="中优先级"
                value={statistics.byPriority.medium}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="低优先级"
                value={statistics.byPriority.low}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 逾期任务 */}
        <Card>
          <Statistic
            title="逾期任务数"
            value={statistics.overdue}
            valueStyle={{ color: statistics.overdue > 0 ? '#cf1322' : '#52c41a' }}
            prefix={<ExclamationCircleOutlined />}
            suffix={`/ ${statistics.total}`}
          />
        </Card>
      </Space>
    </Drawer>
  );

  // 修改表单验证规则
  const formRules: Record<string, Rule[]> = {
    title: [{ required: true, message: '请输入任务标题' }],
    type: [{ required: true, message: '请选择任务类型' }],
    status: [{ required: true, message: '请选择状态' }],
    priority: [{ required: true, message: '请选择优先级' }],
    assignee: [{ required: true, message: '请输入负责人' }],
    deadline: [{ required: true, message: '请选择截止日期' }],
    description: [{ required: true, message: '请输入任务描述' }],
    progress: [
      { required: true, message: '请输入进度' },
      { type: 'number', min: 0, max: 100, message: '进度必须在0-100之间' }
    ],
  };

  return (
    <MainLayout>
      <div style={{ padding: '24px' }}>
        <Card
          title={
            <Space>
              <FileTextOutlined />
              任务管理
            </Space>
          }
          extra={
            <Space>
              <Button icon={<BarChartOutlined />} onClick={() => setIsStatsVisible(true)}>
                统计
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExportExcel}>
                导出Excel
              </Button>
              <Button icon={<PrinterOutlined />} onClick={handlePrint}>
                打印
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                刷新
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新建任务
              </Button>
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* 统计卡片 */}
            <Row gutter={16}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总任务"
                    value={statistics.total}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="进行中"
                    value={statistics.inProgress}
                    valueStyle={{ color: '#1890ff' }}
                    prefix={<ClockCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="待处理"
                    value={statistics.pending}
                    valueStyle={{ color: '#faad14' }}
                    prefix={<ExclamationCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="已完成"
                    value={statistics.completed}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<CheckCircleOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            {/* 搜索和筛选 */}
            <Card>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <Row gutter={16}>
                  <Col span={8}>
                    <Input
                      placeholder="搜索任务标题或描述"
                      prefix={<SearchOutlined />}
                      value={searchText}
                      onChange={e => setSearchText(e.target.value)}
                    />
                  </Col>
                  <Col span={16}>
                    <Space>
                      <Select
                        mode="multiple"
                        placeholder="任务类型"
                        style={{ width: 200 }}
                        value={selectedTypes}
                        onChange={setSelectedTypes}
                      >
                        <Option value="维护任务">维护任务</Option>
                        <Option value="采购任务">采购任务</Option>
                        <Option value="开发任务">开发任务</Option>
                      </Select>
                      <Select
                        mode="multiple"
                        placeholder="状态"
                        style={{ width: 200 }}
                        value={selectedStatuses}
                        onChange={setSelectedStatuses}
                      >
                        <Option value="待处理">待处理</Option>
                        <Option value="进行中">进行中</Option>
                        <Option value="已完成">已完成</Option>
                      </Select>
                      <Select
                        mode="multiple"
                        placeholder="优先级"
                        style={{ width: 200 }}
                        value={selectedPriorities}
                        onChange={setSelectedPriorities}
                      >
                        <Option value="高">高</Option>
                        <Option value="中">中</Option>
                        <Option value="低">低</Option>
                      </Select>
                    </Space>
                  </Col>
                </Row>
              </Space>
            </Card>

            {/* 批量操作 */}
            {selectedRowKeys.length > 0 && (
              <Space>
                <Popconfirm
                  title="确定要删除选中的任务吗？"
                  onConfirm={handleBatchDelete}
                >
                  <Button danger icon={<DeleteOutlined />}>
                    批量删除
                  </Button>
                </Popconfirm>
                <Button
                  onClick={() => handleBatchUpdateStatus('进行中')}
                  icon={<ClockCircleOutlined />}
                >
                  设为进行中
                </Button>
                <Button
                  onClick={() => handleBatchUpdateStatus('已完成')}
                  icon={<CheckCircleOutlined />}
                >
                  设为已完成
                </Button>
              </Space>
            )}

            {/* 任务表格 */}
            <Table
              rowSelection={rowSelection}
              columns={columns}
              dataSource={filteredTasks}
              rowKey="id"
              loading={loading}
            />
          </Space>
        </Card>
      </div>

      {/* 任务统计抽屉 */}
      {renderStatsDrawer()}

      <Modal
        title={editingTask ? '编辑任务' : '新建任务'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="任务标题"
            rules={formRules.title}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>
          <Form.Item
            name="type"
            label="任务类型"
            rules={formRules.type}
          >
            <Select>
              <Option value="维护任务">维护任务</Option>
              <Option value="采购任务">采购任务</Option>
              <Option value="其他任务">其他任务</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={formRules.status}
          >
            <Select>
              <Option value="待处理">待处理</Option>
              <Option value="进行中">进行中</Option>
              <Option value="已完成">已完成</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级"
            rules={formRules.priority}
          >
            <Select>
              <Option value="高">高</Option>
              <Option value="中">中</Option>
              <Option value="低">低</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="assignee"
            label="负责人"
            rules={formRules.assignee}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="deadline"
            label="截止日期"
            rules={formRules.deadline}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="description"
            label="任务描述"
            rules={formRules.description}
          >
            <TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="progress"
            label="进度"
            rules={formRules.progress}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label="附件"
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>上传附件</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="任务详情"
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false);
          setSelectedTask(null);
        }}
        footer={null}
        width={800}
      >
        {selectedTask && (
          <div>
            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">基本信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">任务标题：</span>
                  {selectedTask.title}
                </div>
                <div>
                  <span className="font-medium">任务类型：</span>
                  <Tag color={selectedTask.type === '维护任务' ? 'blue' : 'green'}>
                    {selectedTask.type}
                  </Tag>
                </div>
                <div>
                  <span className="font-medium">状态：</span>
                  <Badge
                    status={
                      selectedTask.status === '进行中' ? 'processing' :
                      selectedTask.status === '待处理' ? 'warning' :
                      selectedTask.status === '已完成' ? 'success' : 'default'
                    }
                    text={selectedTask.status}
                  />
                </div>
                <div>
                  <span className="font-medium">优先级：</span>
                  <Tag color={
                    selectedTask.priority === '高' ? 'red' :
                    selectedTask.priority === '中' ? 'orange' : 'green'
                  }>{selectedTask.priority}</Tag>
                </div>
                <div>
                  <span className="font-medium">负责人：</span>
                  {selectedTask.assignee}
                </div>
                <div>
                  <span className="font-medium">截止日期：</span>
                  {selectedTask.deadline}
                </div>
                <div>
                  <span className="font-medium">创建人：</span>
                  {selectedTask.createdBy}
                </div>
                <div>
                  <span className="font-medium">创建时间：</span>
                  {selectedTask.createdAt}
                </div>
                <div>
                  <span className="font-medium">最后更新：</span>
                  {selectedTask.updatedAt}
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">任务描述</h3>
              <p>{selectedTask.description}</p>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">进度</h3>
              <Progress percent={selectedTask.progress} />
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-bold mb-2">附件</h3>
              <ul className="space-y-2">
                {selectedTask.attachments.map((attachment, index) => (
                  <li key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileTextOutlined className="mr-2" />
                      <span>{attachment}</span>
                    </div>
                    <Space>
                      <Button
                        type="link"
                        size="small"
                        onClick={() => {
                          // 这里添加下载逻辑
                          message.success(`开始下载: ${attachment}`);
                        }}
                      >
                        下载
                      </Button>
                      <Button
                        type="link"
                        danger
                        size="small"
                        onClick={() => {
                          // 这里添加删除附件逻辑
                          const newAttachments = selectedTask.attachments.filter((_, i) => i !== index);
                          setTasks(tasks.map(task =>
                            task.id === selectedTask.id
                              ? { ...task, attachments: newAttachments }
                              : task
                          ));
                          message.success('附件已删除');
                        }}
                      >
                        删除
                      </Button>
                    </Space>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-2">评论</h3>
              <div className="mb-4">
                <Form form={commentForm}>
                  <Form.Item name="comment">
                    <TextArea rows={2} placeholder="添加评论..." />
                  </Form.Item>
                  <Button type="primary" onClick={() => handleAddComment(selectedTask.id)}>
                    发表评论
                  </Button>
                </Form>
              </div>
              <div className="space-y-4">
                {selectedTask.comments.map(comment => (
                  <div key={comment.id} className="border-b pb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{comment.user}</span>
                      <span className="text-gray-500">{comment.time}</span>
                    </div>
                    <p>{comment.content}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </MainLayout>
  );
}