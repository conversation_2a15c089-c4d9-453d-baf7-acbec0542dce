"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {
  Plus,
  MoreVertical,
  Edit,
  Trash,
  Search,
  Zap,
  Flame,
  Droplet,
  Wind,
  BarChart,
  Download,
  Settings,
  RefreshCw,
  Info,
  Battery,
  Gauge,
  FileText,
  Loader2,
  ArrowUpRight,
  ArrowDownRight,
  ChevronRight,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  AreaChart,
  LinkIcon
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter
} from "@/components/ui/sheet"

// 添加能源类型接口
interface EnergyTypeItem {
  id: string;
  type: string;
  description: string;
  unit: string;
  icon?: React.ReactNode;
  color?: string;
  consumption?: number;
  cost?: number;
  trend?: {
    consumption: 'up' | 'down' | 'stable';
    cost: 'up' | 'down' | 'stable';
    percentage: number;
  };
  efficiencyScore?: number;
  lastUpdated?: string;
}

// 添加月度数据接口
interface MonthlyData {
  month: string;
  [key: string]: number | string;
}

// 添加优化建议接口
interface OptimizationSuggestion {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  estimatedSavings: number;
}

export function EnergyType() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedType, setSelectedType] = useState<string>("all")
  const [activeTab, setActiveTab] = useState("table")
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([])
  const [showRelatedConsumption, setShowRelatedConsumption] = useState(false)

  // 初始能源类型数据
  const initialEnergyTypes: EnergyTypeItem[] = [
    {
      id: "1",
      type: "电力",
      description: "用于供电的电力资源",
      unit: "kWh",
      icon: <Zap className="h-5 w-5 text-yellow-500" />,
      color: "yellow",
      consumption: 1250000,
      cost: 875000,
      trend: {
        consumption: 'up',
        cost: 'up',
        percentage: 12
      },
      efficiencyScore: 82,
      lastUpdated: "2025-03-10"
    },
    {
      id: "2",
      type: "天然气",
      description: "用于锅炉和加热的天然气资源",
      unit: "m³",
      icon: <Flame className="h-5 w-5 text-orange-500" />,
      color: "orange",
      consumption: 320000,
      cost: 512000,
      trend: {
        consumption: 'up',
        cost: 'up',
        percentage: 15
      },
      efficiencyScore: 75,
      lastUpdated: "2025-03-08"
    },
    {
      id: "3",
      type: "水",
      description: "用于冷却和清洗的水资源",
      unit: "吨",
      icon: <Droplet className="h-5 w-5 text-blue-500" />,
      color: "blue",
      consumption: 185000,
      cost: 203500,
      trend: {
        consumption: 'down',
        cost: 'down',
        percentage: 5
      },
      efficiencyScore: 88,
      lastUpdated: "2025-03-12"
    },
    {
      id: "4",
      type: "蒸汽",
      description: "用于加工和加热的蒸汽资源",
      unit: "吨",
      icon: <Wind className="h-5 w-5 text-gray-500" />,
      color: "gray",
      consumption: 78500,
      cost: 157000,
      trend: {
        consumption: 'stable',
        cost: 'stable',
        percentage: 2
      },
      efficiencyScore: 79,
      lastUpdated: "2025-03-09"
    },
  ]

  // 状态管理
  const [energyTypes, setEnergyTypes] = useState<EnergyTypeItem[]>(initialEnergyTypes)
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentEnergy, setCurrentEnergy] = useState<EnergyTypeItem>({ id: "", type: "", description: "", unit: "", consumption: 0, cost: 0 })
  const [isViewSheetOpen, setIsViewSheetOpen] = useState(false)

  // 添加示例优化建议
  const optimizationSuggestions = useMemo(() => {
    const suggestions: Record<string, OptimizationSuggestion[]> = {
      "电力": [
        {
          id: "e1",
          title: "更换LED照明设备",
          description: "将传统照明更换为LED照明，可减少30-50%的照明用电",
          impact: "medium",
          implementationDifficulty: "easy",
          estimatedSavings: 45000
        },
        {
          id: "e2",
          title: "安装智能电表和监控系统",
          description: "实时监控用电情况，发现异常用电并及时处理",
          impact: "high",
          implementationDifficulty: "medium",
          estimatedSavings: 87500
        }
      ],
      "天然气": [
        {
          id: "g1",
          title: "锅炉系统效率优化",
          description: "检查并优化锅炉运行参数，确保高效燃烧",
          impact: "high",
          implementationDifficulty: "medium",
          estimatedSavings: 76800
        }
      ],
      "水": [
        {
          id: "w1",
          title: "安装水循环利用系统",
          description: "收集处理后的废水用于冷却或清洗",
          impact: "medium",
          implementationDifficulty: "hard",
          estimatedSavings: 30525
        }
      ],
      "蒸汽": [
        {
          id: "s1",
          title: "蒸汽管道保温优化",
          description: "提高管道保温性能，减少热损失",
          impact: "medium",
          implementationDifficulty: "easy",
          estimatedSavings: 15700
        }
      ]
    };

    return suggestions;
  }, []);

  // 获取建议影响级别的颜色
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return "text-red-500";
      case 'medium': return "text-amber-500";
      case 'low': return "text-green-500";
      default: return "text-gray-500";
    }
  };

  // 获取建议难度的文本和颜色
  const getDifficultyInfo = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return { text: "容易", color: "text-green-500" };
      case 'medium': return { text: "中等", color: "text-amber-500" };
      case 'hard': return { text: "困难", color: "text-red-500" };
      default: return { text: "未知", color: "text-gray-500" };
    }
  };

  // 过滤能源类型
  const filteredEnergyTypes = useMemo(() => {
    return energyTypes.filter(
      (energy) =>
        (energy.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        energy.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
        (selectedType === "all" || energy.type === selectedType)
    )
  }, [energyTypes, searchTerm, selectedType])

  // 计算统计数据
  const statistics = useMemo(() => {
    return {
      total: energyTypes.length,
      totalConsumption: energyTypes.reduce((sum, item) => sum + (item.consumption || 0), 0),
      totalCost: energyTypes.reduce((sum, item) => sum + (item.cost || 0), 0),
      avgCost: energyTypes.reduce((sum, item) => sum + (item.cost || 0), 0) / energyTypes.length
    }
  }, [energyTypes])

  // 生成月度数据
  useEffect(() => {
    // 模拟生成过去6个月的数据
    const months = ["11月", "12月", "1月", "2月", "3月", "4月"];
    const generateData = () => {
      return months.map(month => {
        const data: MonthlyData = { month };
        energyTypes.forEach(energy => {
          // 生成每种能源类型的随机消耗量，基于当前消耗量上下浮动20%
          const baseConsumption = energy.consumption || 0;
          const randomFactor = 0.8 + Math.random() * 0.4; // 0.8到1.2之间
          data[energy.type] = Math.round(baseConsumption * randomFactor);
        });
        return data;
      });
    };

    setMonthlyData(generateData());
  }, [energyTypes]);

  // 获取趋势图标
  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    if (!trend) return null;
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-green-500" />;
      case 'stable':
        return <LineChart className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  // 模拟能源消耗记录数据
  const consumptionRecords = useMemo(() => {
    const records = [];
    for (const energy of energyTypes) {
      // 为每种能源类型生成3条记录
      for (let i = 0; i < 3; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i * 3);

        records.push({
          id: `${energy.id}-${i}`,
          date: date.toISOString().split('T')[0],
          type: energy.type,
          location: ["A区生产线", "B区锅炉房", "C区加工车间", "D区办公楼"][Math.floor(Math.random() * 4)],
          consumption: Math.round((energy.consumption || 0) / 30 * (0.9 + Math.random() * 0.2)),
          unit: energy.unit,
          cost: Math.round((energy.cost || 0) / 30 * (0.9 + Math.random() * 0.2)),
          status: Math.random() > 0.9 ? "异常" : "正常",
          trend: ["上升", "下降", "平稳"][Math.floor(Math.random() * 3)],
          manager: ["张工", "李工", "王工", "赵工"][Math.floor(Math.random() * 4)]
        });
      }
    }
    return records.sort((a, b) => b.date.localeCompare(a.date));
  }, [energyTypes]);

  // 获取能源使用效率评分的颜色
  const getEfficiencyColor = (score?: number) => {
    if (!score) return "bg-gray-400";
    if (score >= 85) return "bg-green-500";
    if (score >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  // 添加能源类型
  const handleAddEnergy = () => {
    // 验证表单
    if (!currentEnergy.type || !currentEnergy.unit) {
      toast({
        title: "请填写必要信息",
        description: "能源类型名称和计量单位是必填项",
        variant: "destructive"
      });
      return;
    }

    // 生成合适的图标和颜色
    let icon: React.ReactNode;
    let color = "gray";

    if (currentEnergy.type.includes("电")) {
      icon = <Zap className="h-5 w-5 text-yellow-500" />;
      color = "yellow";
    } else if (currentEnergy.type.includes("气") || currentEnergy.type.includes("油")) {
      icon = <Flame className="h-5 w-5 text-orange-500" />;
      color = "orange";
    } else if (currentEnergy.type.includes("水")) {
      icon = <Droplet className="h-5 w-5 text-blue-500" />;
      color = "blue";
    } else {
      icon = <Wind className="h-5 w-5 text-green-500" />;
      color = "green";
    }

    const newEnergy: EnergyTypeItem = {
      id: (energyTypes.length + 1).toString(),
      type: currentEnergy.type,
      description: currentEnergy.description,
      unit: currentEnergy.unit,
      icon: icon,
      color: color,
      consumption: currentEnergy.consumption || 0,
      cost: currentEnergy.cost || 0
    }

    setEnergyTypes([...energyTypes, newEnergy])
    setCurrentEnergy({ id: "", type: "", description: "", unit: "", consumption: 0, cost: 0 })
    setIsAddDialogOpen(false)

    toast({
      title: "添加成功",
      description: `能源类型 ${currentEnergy.type} 已添加到系统`,
    });
  }

  // 编辑能源类型
  const handleEditEnergy = () => {
    // 验证表单
    if (!currentEnergy.type || !currentEnergy.unit) {
      toast({
        title: "请填写必要信息",
        description: "能源类型名称和计量单位是必填项",
        variant: "destructive"
      });
      return;
    }

    const updatedEnergyTypes = energyTypes.map((energy) =>
      energy.id === currentEnergy.id ? { ...energy, ...currentEnergy } : energy
    )
    setEnergyTypes(updatedEnergyTypes)
    setCurrentEnergy({ id: "", type: "", description: "", unit: "", consumption: 0, cost: 0 })
    setIsEditDialogOpen(false)

    toast({
      title: "修改成功",
      description: `能源类型 ${currentEnergy.type} 已更新`,
    });
  }

  // 删除能源类型
  const handleDeleteEnergy = () => {
    const updatedEnergyTypes = energyTypes.filter((energy) => energy.id !== currentEnergy.id)
    setEnergyTypes(updatedEnergyTypes)
    setCurrentEnergy({ id: "", type: "", description: "", unit: "", consumption: 0, cost: 0 })
    setIsDeleteDialogOpen(false)

    toast({
      title: "删除成功",
      description: `能源类型 ${currentEnergy.type} 已从系统中删除`,
    });
  }

  // 打开编辑对话框
  const openEditDialog = (energy: EnergyTypeItem) => {
    setCurrentEnergy(energy)
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (energy: EnergyTypeItem) => {
    setCurrentEnergy(energy)
    setIsDeleteDialogOpen(true)
  }

  // 查看能源详情
  const handleViewDetails = (energy: EnergyTypeItem) => {
    setCurrentEnergy(energy);
    setIsViewSheetOpen(true);
  }

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);
    // 模拟加载
    setTimeout(() => {
      toast({
        title: "刷新成功",
        description: "能源类型数据已更新",
      });
      setIsLoading(false);
    }, 800);
  }

  // 导出数据
  const handleExport = () => {
    toast({
      title: "导出成功",
      description: "能源类型数据已导出到Excel文件",
    });
  }

  return (
    <div className="space-y-6">
      {/* 顶部区域 */}
      <div className="flex items-start justify-between py-2 mb-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Gauge className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">能源类型管理</h1>
          </div>
          <p className="text-muted-foreground">管理和维护企业能源资源类型信息</p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-blue-50 transition-colors"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="hover:bg-green-50 transition-colors"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button
            variant="default"
            size="sm"
            className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 shadow-md transition-all"
            onClick={() => setIsAddDialogOpen(true)}
          >
            <Plus className="h-4 w-4" />
            添加能源类型
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-green-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">能源类型总数</CardTitle>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <Settings className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.total}</div>
              <div className="text-sm text-muted-foreground">种类型</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              系统中管理的能源类型总数
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-blue-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">总消耗量</CardTitle>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <Battery className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">{statistics.totalConsumption.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">单位</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              所有能源类型的总消耗量
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-amber-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">总成本</CardTitle>
              <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                <BarChart className="h-4 w-4 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">¥{statistics.totalCost.toLocaleString()}</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              所有能源类型的总成本
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-purple-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">平均成本</CardTitle>
              <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                <FileText className="h-4 w-4 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">¥{Math.round(statistics.avgCost).toLocaleString()}</div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              每种能源类型的平均成本
            </div>
          </CardContent>
        </Card>

        {/* 新增效率评分卡片 */}
        <Card className="overflow-hidden shadow-md border-0 bg-gradient-to-br from-indigo-50 to-white">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium">平均能源效率</CardTitle>
              <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-indigo-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-bold">
                {Math.round(energyTypes.reduce((sum, item) => sum + (item.efficiencyScore || 0), 0) / energyTypes.length)}%
              </div>
            </div>
            <div className="mt-1 text-xs text-muted-foreground">
              能源使用效率评分
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选区域 */}
      <Card className="shadow-sm border border-blue-100/50 overflow-hidden bg-gradient-to-r from-blue-50 via-white to-blue-50">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索能源类型..."
                className="pl-8 w-full transition-colors hover:border-blue-300 focus-visible:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <Select
                value={selectedType}
                onValueChange={setSelectedType}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择能源类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {energyTypes.map(energy => (
                    <SelectItem key={energy.id} value={energy.type}>{energy.type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 能源类型卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredEnergyTypes.map((energy) => {
          const colorClass =
            energy.color === "yellow" ? "from-yellow-500 to-amber-600" :
            energy.color === "orange" ? "from-orange-500 to-red-600" :
            energy.color === "blue" ? "from-blue-500 to-indigo-600" :
            energy.color === "green" ? "from-green-500 to-emerald-600" :
            "from-gray-500 to-gray-600";

          return (
            <Card key={energy.id} className="overflow-hidden transition-all duration-200 hover:shadow-md hover:border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-br from-gray-50 to-white">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  {energy.icon}
                  {energy.type}
                </CardTitle>
                <Badge className={`bg-gradient-to-r ${colorClass} border-0 text-white`}>
                  {energy.unit}
                </Badge>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="text-sm text-gray-600 mb-3">{energy.description}</div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">消耗量</div>
                    <div className="font-medium">{energy.consumption?.toLocaleString()} {energy.unit}</div>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">成本</div>
                    <div className="font-medium">¥{energy.cost?.toLocaleString()}</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 px-4 py-2 flex justify-end gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 hover:bg-blue-50"
                  onClick={() => handleViewDetails(energy)}
                >
                  <Info className="h-4 w-4 text-blue-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 hover:bg-amber-50"
                  onClick={() => openEditDialog(energy)}
                >
                  <Edit className="h-4 w-4 text-amber-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 hover:bg-red-50"
                  onClick={() => openDeleteDialog(energy)}
                >
                  <Trash className="h-4 w-4 text-red-600" />
                </Button>
              </CardFooter>
            </Card>
          )
        })}
      </div>

      {/* 表格与图表视图 */}
      <Tabs defaultValue="table" className="mt-6" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="table">
            <FileText className="mr-2 h-4 w-4" />
            表格视图
          </TabsTrigger>
          <TabsTrigger value="chart">
            <BarChart className="mr-2 h-4 w-4" />
            图表视图
          </TabsTrigger>
          <TabsTrigger value="trend">
            <LineChart className="mr-2 h-4 w-4" />
            趋势分析
          </TabsTrigger>
          <TabsTrigger value="consumption">
            <AreaChart className="mr-2 h-4 w-4" />
            消耗记录
          </TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="mt-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
              <CardTitle>能源类型列表</CardTitle>
              <CardDescription>
                共 {filteredEnergyTypes.length} 种能源类型
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50/50 hover:bg-gray-50">
                    <TableHead>类型</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>计量单位</TableHead>
                    <TableHead>消耗量</TableHead>
                    <TableHead>成本</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEnergyTypes.map((energy) => (
                    <TableRow key={energy.id} className="hover:bg-blue-50/30 transition-colors">
                      <TableCell className="font-medium">
                        <Button variant="link" onClick={() => handleViewDetails(energy)} className="h-auto p-0 hover:text-blue-600 flex items-center gap-2">
                          {energy.icon}
                          {energy.type}
                        </Button>
                      </TableCell>
                      <TableCell>{energy.description}</TableCell>
                      <TableCell>{energy.unit}</TableCell>
                      <TableCell>{energy.consumption?.toLocaleString()} {energy.unit}</TableCell>
                      <TableCell>¥{energy.cost?.toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetails(energy)}>
                          <Info className="h-4 w-4 text-blue-600" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => openEditDialog(energy)}>
                          <Edit className="h-4 w-4 text-amber-600" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(energy)}>
                          <Trash className="h-4 w-4 text-red-600" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chart" className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>能源消耗分布</CardTitle>
                <CardDescription>
                  各类型能源消耗量占比
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {filteredEnergyTypes.map((energy) => {
                    const totalConsumption = statistics.totalConsumption || 1;
                    const percent = Math.round(((energy.consumption || 0) / totalConsumption) * 100);
                    const colorClass =
                      energy.color === "yellow" ? "bg-yellow-500" :
                      energy.color === "orange" ? "bg-orange-500" :
                      energy.color === "blue" ? "bg-blue-500" :
                      energy.color === "green" ? "bg-green-500" :
                      "bg-gray-500";

                    return (
                      <div key={energy.id} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            {energy.icon}
                            <span className="text-sm font-medium">{energy.type}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {energy.consumption?.toLocaleString()} {energy.unit} ({percent}%)
                          </span>
                        </div>
                        <div className="h-2 rounded-full bg-gray-100">
                          <div
                            className={`h-full rounded-full ${colorClass}`}
                            style={{ width: `${percent}%` }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>能源成本分布</CardTitle>
                <CardDescription>
                  各类型能源成本占比
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-80 flex flex-col gap-4">
                  {filteredEnergyTypes.map((energy) => {
                    const totalCost = statistics.totalCost || 1;
                    const percent = Math.round(((energy.cost || 0) / totalCost) * 100);
                    const colorClass =
                      energy.color === "yellow" ? "bg-yellow-500" :
                      energy.color === "orange" ? "bg-orange-500" :
                      energy.color === "blue" ? "bg-blue-500" :
                      energy.color === "green" ? "bg-green-500" :
                      "bg-gray-500";

                    return (
                      <div key={energy.id} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            {energy.icon}
                            <span className="text-sm font-medium">{energy.type}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            ¥{energy.cost?.toLocaleString()} ({percent}%)
                          </span>
                        </div>
                        <div className="h-2 rounded-full bg-gray-100">
                          <div
                            className={`h-full rounded-full ${colorClass}`}
                            style={{ width: `${percent}%` }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 趋势分析视图 */}
        <TabsContent value="trend" className="mt-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
              <CardTitle>能源消耗趋势</CardTitle>
              <CardDescription>
                最近6个月能源使用量变化趋势
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="h-80">
                {/* 趋势图表 - 这里使用简化的表格显示 */}
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>月份</TableHead>
                        {energyTypes.map(energy => (
                          <TableHead key={energy.id}>
                            <div className="flex items-center gap-1">
                              {energy.icon}
                              {energy.type}
                            </div>
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {monthlyData.map((data, index) => (
                        <TableRow key={index}>
                          <TableCell>{data.month}</TableCell>
                          {energyTypes.map(energy => (
                            <TableCell key={`${index}-${energy.id}`}>
                              {(data[energy.type] as number)?.toLocaleString()} {energy.unit}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                <div className="flex justify-center mt-6 text-sm text-muted-foreground">
                  <p>此处可集成更丰富的图表库，如Chart.js或Recharts来展示趋势</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {/* 能源类型效率评分卡片 */}
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>能源使用效率评分</CardTitle>
                <CardDescription>
                  各类型能源使用效率评分
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {energyTypes.map((energy) => (
                    <div key={energy.id} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          {energy.icon}
                          <span className="text-sm font-medium">{energy.type}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {energy.efficiencyScore}%
                          <span className="ml-2 inline-flex items-center">
                            {energy.trend && getTrendIcon(energy.trend.consumption)}
                            {energy.trend && energy.trend.percentage > 0 && (
                              <span className={`text-xs ml-1 ${energy.trend.consumption === 'down' ? 'text-green-500' : 'text-red-500'}`}>
                                {energy.trend.percentage}%
                              </span>
                            )}
                          </span>
                        </span>
                      </div>
                      <div className="h-2 rounded-full bg-gray-100">
                        <div
                          className={`h-full rounded-full ${getEfficiencyColor(energy.efficiencyScore)}`}
                          style={{ width: `${energy.efficiencyScore || 0}%` }}
                        />
                      </div>
                      <div className="text-xs text-muted-foreground">
                        最后更新: {energy.lastUpdated}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 月度成本变化卡片 */}
            <Card className="shadow-lg border-0">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white">
                <CardTitle>月度成本变化</CardTitle>
                <CardDescription>
                  各能源类型成本变化情况
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {energyTypes.map((energy) => (
                    <div key={energy.id} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          {energy.icon}
                          <span className="text-sm font-medium">{energy.type}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">¥{energy.cost?.toLocaleString()}</span>
                          {energy.trend && (
                            <span className={`inline-flex items-center text-xs ${
                              energy.trend.cost === 'up' ? 'text-red-500' :
                              energy.trend.cost === 'down' ? 'text-green-500' :
                              'text-gray-500'
                            }`}>
                              {energy.trend.cost === 'up' ? (
                                <ArrowUpRight className="h-3 w-3 mr-1" />
                              ) : energy.trend.cost === 'down' ? (
                                <ArrowDownRight className="h-3 w-3 mr-1" />
                              ) : (
                                <LineChart className="h-3 w-3 mr-1" />
                              )}
                              {energy.trend.percentage}%
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="h-1.5 rounded-full bg-gray-100">
                        <div
                          className={`h-full rounded-full ${
                            energy.color === "yellow" ? "bg-yellow-500" :
                            energy.color === "orange" ? "bg-orange-500" :
                            energy.color === "blue" ? "bg-blue-500" :
                            energy.color === "green" ? "bg-green-500" :
                            "bg-gray-500"
                          }`}
                          style={{ width: `${Math.round((energy.cost || 0) / statistics.totalCost * 100)}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 能源消耗记录视图 */}
        <TabsContent value="consumption" className="mt-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-white flex flex-row justify-between items-center">
              <div>
                <CardTitle>能源消耗记录</CardTitle>
                <CardDescription>
                  查看各能源类型的详细消耗记录
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={() => setShowRelatedConsumption(!showRelatedConsumption)}
              >
                <LinkIcon className="h-4 w-4" />
                {showRelatedConsumption ? "显示全部记录" : "只看选中类型"}
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50/50 hover:bg-gray-50">
                    <TableHead>日期</TableHead>
                    <TableHead>能源类型</TableHead>
                    <TableHead>使用位置</TableHead>
                    <TableHead>消耗量</TableHead>
                    <TableHead>成本</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>趋势</TableHead>
                    <TableHead>负责人</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(showRelatedConsumption && selectedType !== "all"
                    ? consumptionRecords.filter(record => record.type === selectedType)
                    : consumptionRecords).map((record) => (
                    <TableRow key={record.id} className="hover:bg-blue-50/30 transition-colors">
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {energyTypes.find(e => e.type === record.type)?.icon}
                          {record.type}
                        </div>
                      </TableCell>
                      <TableCell>{record.location}</TableCell>
                      <TableCell>{record.consumption.toLocaleString()} {record.unit}</TableCell>
                      <TableCell>¥{record.cost.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge className={record.status === "正常" ? "bg-green-500" : "bg-red-500"}>
                          {record.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={
                          record.trend === "上升" ? "text-red-500 border-red-500" :
                          record.trend === "下降" ? "text-green-500 border-green-500" :
                          "text-gray-500 border-gray-500"
                        }>
                          {record.trend}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.manager}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between items-center px-6 py-4 border-t">
              <div className="text-sm text-muted-foreground">
                共 {showRelatedConsumption && selectedType !== "all"
                  ? consumptionRecords.filter(record => record.type === selectedType).length
                  : consumptionRecords.length} 条记录
              </div>
              <Button variant="outline" size="sm">
                查看完整记录 <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 能源类型详情抽屉 */}
      <Sheet open={isViewSheetOpen} onOpenChange={setIsViewSheetOpen}>
        <SheetContent className="w-[600px] sm:w-[540px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              能源类型详情
            </SheetTitle>
            <SheetDescription>
              查看能源类型的详细信息
            </SheetDescription>
          </SheetHeader>

          {currentEnergy && (
            <div className="mt-6 space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-white rounded-lg shadow-sm border border-blue-100/50 p-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 rounded-full flex items-center justify-center bg-blue-100">
                    {currentEnergy.icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{currentEnergy.type}</h3>
                    <Badge variant="outline" className="mt-1">
                      计量单位: {currentEnergy.unit}
                    </Badge>
                  </div>
                </div>

                <div className="text-sm text-gray-600 mt-2">
                  {currentEnergy.description}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Card className="shadow-sm">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                      <Battery className="h-4 w-4 text-blue-600" />
                      消耗量
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {currentEnergy.consumption?.toLocaleString()} <span className="text-sm font-normal">{currentEnergy.unit}</span>
                    </div>
                    {currentEnergy.trend && (
                      <div className={`text-xs flex items-center mt-1 ${
                        currentEnergy.trend.consumption === 'up' ? 'text-red-500' :
                        currentEnergy.trend.consumption === 'down' ? 'text-green-500' :
                        'text-gray-500'
                      }`}>
                        {currentEnergy.trend.consumption === 'up' ? (
                          <ArrowUpRight className="h-3 w-3 mr-1" />
                        ) : currentEnergy.trend.consumption === 'down' ? (
                          <ArrowDownRight className="h-3 w-3 mr-1" />
                        ) : (
                          <LineChart className="h-3 w-3 mr-1" />
                        )}
                        较上月{currentEnergy.trend.consumption === 'up' ? '增加' : currentEnergy.trend.consumption === 'down' ? '减少' : '持平'} {currentEnergy.trend.percentage}%
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className="shadow-sm">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                      <BarChart className="h-4 w-4 text-amber-600" />
                      成本
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ¥{currentEnergy.cost?.toLocaleString()}
                    </div>
                    {currentEnergy.trend && (
                      <div className={`text-xs flex items-center mt-1 ${
                        currentEnergy.trend.cost === 'up' ? 'text-red-500' :
                        currentEnergy.trend.cost === 'down' ? 'text-green-500' :
                        'text-gray-500'
                      }`}>
                        {currentEnergy.trend.cost === 'up' ? (
                          <ArrowUpRight className="h-3 w-3 mr-1" />
                        ) : currentEnergy.trend.cost === 'down' ? (
                          <ArrowDownRight className="h-3 w-3 mr-1" />
                        ) : (
                          <LineChart className="h-3 w-3 mr-1" />
                        )}
                        较上月{currentEnergy.trend.cost === 'up' ? '增加' : currentEnergy.trend.cost === 'down' ? '减少' : '持平'} {currentEnergy.trend.percentage}%
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-2">
                  <Settings className="h-4 w-4 text-gray-500" />
                  数据分析
                </h3>
                <Card className="shadow-sm">
                  <CardContent className="p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">占总消耗比例</span>
                      <span className="text-sm font-medium">
                        {statistics.totalConsumption
                          ? Math.round(((currentEnergy.consumption || 0) / statistics.totalConsumption) * 100)
                          : 0}%
                      </span>
                    </div>
                    <div className="h-2 rounded-full bg-gray-100">
                      <div
                        className="h-full rounded-full bg-blue-500"
                        style={{
                          width: `${statistics.totalConsumption
                            ? Math.round(((currentEnergy.consumption || 0) / statistics.totalConsumption) * 100)
                            : 0}%`
                        }}
                      />
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <span className="text-sm">占总成本比例</span>
                      <span className="text-sm font-medium">
                        {statistics.totalCost
                          ? Math.round(((currentEnergy.cost || 0) / statistics.totalCost) * 100)
                          : 0}%
                      </span>
                    </div>
                    <div className="h-2 rounded-full bg-gray-100">
                      <div
                        className="h-full rounded-full bg-amber-500"
                        style={{
                          width: `${statistics.totalCost
                            ? Math.round(((currentEnergy.cost || 0) / statistics.totalCost) * 100)
                            : 0}%`
                        }}
                      />
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <span className="text-sm">单位成本</span>
                      <span className="text-sm font-medium">
                        ¥{currentEnergy.consumption
                          ? Math.round((currentEnergy.cost || 0) / currentEnergy.consumption * 100) / 100
                          : 0}/{currentEnergy.unit}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 新增使用效率评估 */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-gray-500" />
                  使用效率评估
                </h3>
                <Card className="shadow-sm overflow-hidden">
                  <CardContent className="p-0">
                    <div className="p-4 flex items-center justify-between bg-gradient-to-r from-gray-50 to-white">
                      <div className="flex items-center gap-2">
                        <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                          (currentEnergy.efficiencyScore || 0) >= 85 ? 'bg-green-100' :
                          (currentEnergy.efficiencyScore || 0) >= 70 ? 'bg-yellow-100' :
                          'bg-red-100'
                        }`}>
                          <div className="text-sm font-semibold">
                            {currentEnergy.efficiencyScore || 0}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium">
                            使用效率评分
                          </div>
                          <div className="text-xs text-gray-500 mt-0.5">
                            最后更新: {currentEnergy.lastUpdated}
                          </div>
                        </div>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        (currentEnergy.efficiencyScore || 0) >= 85 ? 'bg-green-100 text-green-600' :
                        (currentEnergy.efficiencyScore || 0) >= 70 ? 'bg-yellow-100 text-yellow-600' :
                        'bg-red-100 text-red-600'
                      }`}>
                        {(currentEnergy.efficiencyScore || 0) >= 85 ? '优' :
                         (currentEnergy.efficiencyScore || 0) >= 70 ? '良' : '差'}
                      </div>
                    </div>
                    <div className="px-4 py-2 border-t">
                      <div className="text-xs text-muted-foreground">评分解释</div>
                      <div className="text-sm mt-1">
                        {(currentEnergy.efficiencyScore || 0) >= 85 ?
                          '能源使用效率优秀，持续保持当前水平' :
                          (currentEnergy.efficiencyScore || 0) >= 70 ?
                          '能源使用效率良好，仍有优化空间' :
                          '能源使用效率较低，需要进行优化改进'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 新增优化建议 */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-2">
                  <Zap className="h-4 w-4 text-gray-500" />
                  优化建议
                </h3>
                {optimizationSuggestions[currentEnergy.type]?.length > 0 ? (
                  <div className="space-y-3">
                    {optimizationSuggestions[currentEnergy.type]?.map(suggestion => (
                      <Card key={suggestion.id} className="shadow-sm overflow-hidden">
                        <CardContent className="p-0">
                          <div className="p-4 border-b">
                            <div className="flex justify-between">
                              <h4 className="font-medium">{suggestion.title}</h4>
                              <Badge variant="outline" className={getImpactColor(suggestion.impact)}>
                                影响: {suggestion.impact === 'high' ? '高' : suggestion.impact === 'medium' ? '中' : '低'}
                              </Badge>
                            </div>
                            <p className="text-sm mt-2 text-gray-600">{suggestion.description}</p>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-gray-50">
                            <div>
                              <span className="text-xs text-gray-500">实施难度:</span>
                              <span className={`text-xs ml-1 ${getDifficultyInfo(suggestion.implementationDifficulty).color}`}>
                                {getDifficultyInfo(suggestion.implementationDifficulty).text}
                              </span>
                            </div>
                            <div>
                              <span className="text-xs text-gray-500">预计节省:</span>
                              <span className="text-xs font-medium ml-1">¥{suggestion.estimatedSavings.toLocaleString()}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-center text-gray-500 text-sm py-6">
                        暂无可用的优化建议
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* 使用量变化图 - 简化版 */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-2">
                  <AreaChart className="h-4 w-4 text-gray-500" />
                  近期使用量变化
                </h3>
                <Card className="shadow-sm">
                  <CardContent className="p-4">
                    <div className="h-48">
                      <div className="flex items-end h-32 gap-1 mb-2">
                        {[85, 90, 75, 120, 95, 110].map((value, i) => {
                          const height = `${(value / 120) * 100}%`;
                          return (
                            <div key={i} className="grow relative">
                              <div
                                className={`w-full rounded-t-sm ${
                                  currentEnergy.color === "yellow" ? "bg-yellow-400" :
                                  currentEnergy.color === "orange" ? "bg-orange-400" :
                                  currentEnergy.color === "blue" ? "bg-blue-400" :
                                  currentEnergy.color === "green" ? "bg-green-400" :
                                  "bg-gray-400"
                                }`}
                                style={{ height }}
                              ></div>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 pt-1">
                        <div>11月</div>
                        <div>12月</div>
                        <div>1月</div>
                        <div>2月</div>
                        <div>3月</div>
                        <div>4月</div>
                      </div>
                    </div>
                    <div className="text-center text-sm text-muted-foreground mt-4">
                      此处可集成更丰富的图表库展示详细的使用量变化趋势
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsViewSheetOpen(false)}>
                  关闭
                </Button>
                <Button
                  variant="default"
                  className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
                  onClick={() => {
                    setIsViewSheetOpen(false);
                    openEditDialog(currentEnergy);
                  }}
                >
                  <Edit className="h-4 w-4" />
                  编辑
                </Button>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 添加能源类型对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">添加能源类型</DialogTitle>
            <DialogDescription>
              请填写新能源类型的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">类型名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="type"
                  placeholder="输入能源类型名称"
                  value={currentEnergy.type}
                  onChange={(e) => setCurrentEnergy({...currentEnergy, type: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="unit">计量单位 <span className="text-red-500">*</span></Label>
                <Input
                  id="unit"
                  placeholder="例如：kWh、吨、m³"
                  value={currentEnergy.unit}
                  onChange={(e) => setCurrentEnergy({...currentEnergy, unit: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="consumption">消耗量</Label>
                <Input
                  id="consumption"
                  type="number"
                  placeholder="输入消耗量"
                  value={currentEnergy.consumption || ""}
                  onChange={(e) => setCurrentEnergy({
                    ...currentEnergy,
                    consumption: e.target.value === "" ? 0 : Number(e.target.value)
                  })}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cost">成本</Label>
                <Input
                  id="cost"
                  type="number"
                  placeholder="输入成本"
                  value={currentEnergy.cost || ""}
                  onChange={(e) => setCurrentEnergy({
                    ...currentEnergy,
                    cost: e.target.value === "" ? 0 : Number(e.target.value)
                  })}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                placeholder="输入能源类型的详细描述"
                value={currentEnergy.description}
                onChange={(e) => setCurrentEnergy({...currentEnergy, description: e.target.value})}
                className="min-h-[100px] transition-all border-blue-100 focus-visible:ring-blue-500"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleAddEnergy}
              className="gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600"
            >
              <Plus className="h-4 w-4" />
              添加能源类型
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">编辑能源类型</DialogTitle>
            <DialogDescription>
              修改能源类型的详细信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-type">类型名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-type"
                  placeholder="输入能源类型名称"
                  value={currentEnergy.type}
                  onChange={(e) => setCurrentEnergy({...currentEnergy, type: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-unit">计量单位 <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-unit"
                  placeholder="例如：kWh、吨、m³"
                  value={currentEnergy.unit}
                  onChange={(e) => setCurrentEnergy({...currentEnergy, unit: e.target.value})}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-consumption">消耗量</Label>
                <Input
                  id="edit-consumption"
                  type="number"
                  placeholder="输入消耗量"
                  value={currentEnergy.consumption || ""}
                  onChange={(e) => setCurrentEnergy({
                    ...currentEnergy,
                    consumption: e.target.value === "" ? 0 : Number(e.target.value)
                  })}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-cost">成本</Label>
                <Input
                  id="edit-cost"
                  type="number"
                  placeholder="输入成本"
                  value={currentEnergy.cost || ""}
                  onChange={(e) => setCurrentEnergy({
                    ...currentEnergy,
                    cost: e.target.value === "" ? 0 : Number(e.target.value)
                  })}
                  className="transition-all border-blue-100 focus-visible:ring-blue-500"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">描述</Label>
              <Textarea
                id="edit-description"
                placeholder="输入能源类型的详细描述"
                value={currentEnergy.description}
                onChange={(e) => setCurrentEnergy({...currentEnergy, description: e.target.value})}
                className="min-h-[100px] transition-all border-blue-100 focus-visible:ring-blue-500"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleEditEnergy}
              className="gap-2 bg-gradient-to-r from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600"
            >
              <Edit className="h-4 w-4" />
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl text-red-600">确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 <span className="font-semibold">"{currentEnergy.type}"</span> 能源类型吗？
              <br />
              此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteEnergy}
              className="bg-red-600 hover:bg-red-700"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}