"use client"

import { useEffect, useRef, useState } from "react";
import { motion, useMotionValue, useAnimation, useTransform, PanInfo } from "framer-motion";

interface RollingGalleryProps {
  autoplay?: boolean;
  pauseOnHover?: boolean;
  images: string[];
}

const RollingGallery: React.FC<RollingGalleryProps> = ({ 
  autoplay = false, 
  pauseOnHover = false, 
  images = [] 
}) => {
  const [isScreenSizeSm, setIsScreenSizeSm] = useState(false);

  // 在组件挂载时初始化屏幕尺寸状态
  useEffect(() => {
    setIsScreenSizeSm(window.innerWidth <= 640);
    const handleResize = () => setIsScreenSizeSm(window.innerWidth <= 640);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 调整圆柱体宽度以适应更大的图片和更大的半径
  const cylinderWidth = isScreenSizeSm ? 2000 : 3600; // 增加圆柱体宽度
  const faceCount = images.length;
  const faceWidth = (cylinderWidth / faceCount) * 1.5;
  const dragFactor = 0.05;
  const radius = cylinderWidth / (2 * Math.PI) * 1.2; // 增加半径系数

  const rotation = useMotionValue(0);
  const controls = useAnimation();
  const autoplayRef = useRef<NodeJS.Timeout>();

  const handleDrag = (_: any, info: PanInfo) => {
    rotation.set(rotation.get() + info.offset.x * dragFactor);
  };

  const handleDragEnd = (_: any, info: PanInfo) => {
    controls.start({
      rotateY: rotation.get() + info.velocity.x * dragFactor,
      transition: { type: "spring", stiffness: 60, damping: 20, mass: 0.1, ease: "easeOut" },
    });
  };

  const transform = useTransform(rotation, (value) => {
    return `rotate3d(0, 1, 0, ${value}deg)`;
  });

  useEffect(() => {
    if (autoplay) {
      autoplayRef.current = setInterval(() => {
        controls.start({
          rotateY: rotation.get() - (360 / faceCount),
          transition: { duration: 2, ease: "linear" },
        });
        rotation.set(rotation.get() - (360 / faceCount));
      }, 2000);

      return () => {
        if (autoplayRef.current) {
          clearInterval(autoplayRef.current);
        }
      };
    }
  }, [autoplay, rotation, controls, faceCount]);

  const handleMouseEnter = () => {
    if (autoplay && pauseOnHover && autoplayRef.current) {
      clearInterval(autoplayRef.current);
      controls.stop();
    }
  };

  const handleMouseLeave = () => {
    if (autoplay && pauseOnHover) {
      controls.start({
        rotateY: rotation.get() - (360 / faceCount),
        transition: { duration: 2, ease: "linear" },
      });
      rotation.set(rotation.get() - (360 / faceCount));

      autoplayRef.current = setInterval(() => {
        controls.start({
          rotateY: rotation.get() - (360 / faceCount),
          transition: { duration: 2, ease: "linear" },
        });
        rotation.set(rotation.get() - (360 / faceCount));
      }, 2000);
    }
  };

  return (
    <div className="relative h-[450px] w-full overflow-hidden"> {/* 增加容器高度以适应更大的图片 */}
      <div className="flex h-full items-center justify-center perspective-[2000px] transform-style-preserve-3d"> {/* 增加透视距离 */}
        <motion.div
          drag="x"
          className="flex h-auto min-h-[200px] w-full cursor-grab items-center justify-center transform-style-preserve-3d"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          style={{
            transform: transform,
            rotateY: rotation,
            width: cylinderWidth,
            transformStyle: "preserve-3d",
          }}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          animate={controls}
        >
          {images.map((url, i) => (
            <div
              key={i}
              className="absolute flex h-fit items-center justify-center p-[5%] backface-hidden"
              style={{
                width: `${faceWidth}px`,
                transform: `rotateY(${i * (360 / faceCount)}deg) translateZ(${radius}px)`,
              }}
            >
              <img
                src={url}
                alt={`施工现场图片 ${i + 1}`}
                className="pointer-events-none rounded-xl border-2 border-white/10 object-cover transition-transform duration-300 ease-in-out hover:scale-105 shadow-lg"
                style={{
                  width: isScreenSizeSm ? '400px' : '600px', // 增加图片宽度
                  height: isScreenSizeSm ? '200px' : '300px', // 2:1 比例
                  backgroundColor: 'transparent'
                }}
              />
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default RollingGallery; 