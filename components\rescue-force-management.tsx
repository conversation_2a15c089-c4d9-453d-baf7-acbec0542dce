"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Filter,
  FileText,
  Users,
  Truck,
  Phone,
  Calendar,
  Shield,
  UserCheck,
  Headphones,
  RefreshCcw,
  BarChart2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog"

interface RescueTeam {
  id: string
  name: string
  type: string
  leader: string
  members: number
  location: string
  phone: string
  status: string
  lastDrillDate: string
  nextDrillDate: string
  specialties?: string
  equipment?: string
  remarks?: string
  disabled?: boolean
}

interface RescueEquipment {
  id: string
  name: string
  type: string
  model: string
  quantity: number
  location: string
  status: string
  lastMaintenanceDate: string
  nextMaintenanceDate: string
  responsible: string
  remarks?: string
  disabled?: boolean
}

interface Statistics {
  teams: {
    total: number
    standby: number
    dispatched: number
    training: number
  }
  members: {
    total: number
    fullTime: number
    partTime: number
  }
  equipment: {
    total: number
    normal: number
    needCheck: number
    repairing: number
  }
}

export function RescueForceManagement() {
  const { toast } = useToast()
  const [isAddTeamOpen, setIsAddTeamOpen] = useState(false)
  const [isAddEquipmentOpen, setIsAddEquipmentOpen] = useState(false)
  const [isViewTeamOpen, setIsViewTeamOpen] = useState(false)
  const [isViewEquipmentOpen, setIsViewEquipmentOpen] = useState(false)
  const [isEditTeamOpen, setIsEditTeamOpen] = useState(false)
  const [isEditEquipmentOpen, setIsEditEquipmentOpen] = useState(false)
  const [isStatsDrawerOpen, setIsStatsDrawerOpen] = useState(false)
  const [selectedTeam, setSelectedTeam] = useState<RescueTeam | null>(null)
  const [selectedEquipment, setSelectedEquipment] = useState<RescueEquipment | null>(null)
  const [searchTeamText, setSearchTeamText] = useState("")
  const [searchEquipmentText, setSearchEquipmentText] = useState("")
  const [selectedTeamTypes, setSelectedTeamTypes] = useState<string[]>([])
  const [selectedEquipmentTypes, setSelectedEquipmentTypes] = useState<string[]>([])
  const [selectedEquipmentStatus, setSelectedEquipmentStatus] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedTeamKeys, setSelectedTeamKeys] = useState<string[]>([])
  const [selectedEquipmentKeys, setSelectedEquipmentKeys] = useState<string[]>([])
  const [isDeleteTeamDialogOpen, setIsDeleteTeamDialogOpen] = useState(false)
  const [isDeleteEquipmentDialogOpen, setIsDeleteEquipmentDialogOpen] = useState(false)
  const [deleteTeamId, setDeleteTeamId] = useState<string>("")
  const [deleteEquipmentId, setDeleteEquipmentId] = useState<string>("")
  const [teamFormData, setTeamFormData] = useState<Partial<RescueTeam>>({})
  const [equipmentFormData, setEquipmentFormData] = useState<Partial<RescueEquipment>>({})
  const [rescueTeams, setRescueTeams] = useState<RescueTeam[]>([
    {
      id: "1",
      name: "矿山救护队",
      type: "专职救护队",
      leader: "张三",
      members: 12,
      location: "矿区A3附近",
      phone: "13800138001",
      status: "待命",
      lastDrillDate: "2023-10-15",
      nextDrillDate: "2023-12-15",
    },
    {
      id: "2",
      name: "消防应急小组",
      type: "兼职救援队",
      leader: "李四",
      members: 8,
      location: "办公楼",
      phone: "13800138002",
      status: "待命",
      lastDrillDate: "2023-09-20",
      nextDrillDate: "2023-11-20",
    },
    {
      id: "3",
      name: "医疗救护组",
      type: "兼职救援队",
      leader: "王五",
      members: 6,
      location: "医务室",
      phone: "13800138003",
      status: "待命",
      lastDrillDate: "2023-11-05",
      nextDrillDate: "2024-01-05",
    },
    {
      id: "4",
      name: "抢险救援队",
      type: "专职救护队",
      leader: "赵六",
      members: 15,
      location: "矿区B2附近",
      phone: "13800138004",
      status: "出勤中",
      lastDrillDate: "2023-10-25",
      nextDrillDate: "2023-12-25",
    },
  ])

  const [rescueEquipments, setRescueEquipments] = useState<RescueEquipment[]>([
    {
      id: "1",
      name: "矿山救护车",
      type: "车辆",
      model: "JX-120",
      quantity: 2,
      location: "车库A",
      status: "正常",
      lastMaintenanceDate: "2023-10-10",
      nextMaintenanceDate: "2023-11-10",
      responsible: "张三",
    },
    {
      id: "2",
      name: "正压氧气呼吸器",
      type: "呼吸器材",
      model: "AE-240",
      quantity: 10,
      location: "装备室",
      status: "正常",
      lastMaintenanceDate: "2023-09-15",
      nextMaintenanceDate: "2023-11-15",
      responsible: "李四",
    },
    {
      id: "3",
      name: "担架",
      type: "救援工具",
      model: "FD-100",
      quantity: 5,
      location: "医务室",
      status: "正常",
      lastMaintenanceDate: "2023-10-20",
      nextMaintenanceDate: "2023-12-20",
      responsible: "王五",
    },
    {
      id: "4",
      name: "救援绳索",
      type: "救援工具",
      model: "RS-50",
      quantity: 8,
      location: "装备室",
      status: "需检查",
      lastMaintenanceDate: "2023-08-25",
      nextMaintenanceDate: "2023-10-25",
      responsible: "赵六",
    },
    {
      id: "5",
      name: "便携式气体检测仪",
      type: "检测设备",
      model: "GD-350",
      quantity: 6,
      location: "装备室",
      status: "正常",
      lastMaintenanceDate: "2023-10-05",
      nextMaintenanceDate: "2023-12-05",
      responsible: "钱七",
    },
  ])

  // 统计数据
  const statistics: Statistics = {
    teams: {
      total: rescueTeams.length,
      standby: rescueTeams.filter(t => t.status === "待命").length,
      dispatched: rescueTeams.filter(t => t.status === "出勤中").length,
      training: rescueTeams.filter(t => t.status === "训练中").length,
    },
    members: {
      total: rescueTeams.reduce((acc, team) => acc + team.members, 0),
      fullTime: rescueTeams
        .filter(t => t.type === "专职救护队")
        .reduce((acc, team) => acc + team.members, 0),
      partTime: rescueTeams
        .filter(t => t.type === "兼职救援队")
        .reduce((acc, team) => acc + team.members, 0),
    },
    equipment: {
      total: rescueEquipments.length,
      normal: rescueEquipments.filter(e => e.status === "正常").length,
      needCheck: rescueEquipments.filter(e => e.status === "需检查").length,
      repairing: rescueEquipments.filter(e => e.status === "维修中").length,
    },
  }

  // 消息提示
  const showMessage = (type: "success" | "error", content: string) => {
    toast({
      variant: type === "success" ? "default" : "destructive",
      title: content,
    })
  }

  // 导出Excel
  const handleExportExcel = () => {
    showMessage("success", "导出成功")
  }

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      showMessage("success", "数据已刷新")
    }, 1000)
  }

  // 处理查看队伍详情
  const handleViewTeam = (team: RescueTeam) => {
    setSelectedTeam(team)
    setIsViewTeamOpen(true)
  }

  // 处理查看装备详情
  const handleViewEquipment = (equipment: RescueEquipment) => {
    setSelectedEquipment(equipment)
    setIsViewEquipmentOpen(true)
  }

  // 处理编辑队伍
  const handleEditTeam = (team: RescueTeam) => {
    setSelectedTeam(team)
    setTeamFormData(team)
    setIsEditTeamOpen(true)
  }

  // 处理编辑装备
  const handleEditEquipment = (equipment: RescueEquipment) => {
    setSelectedEquipment(equipment)
    setEquipmentFormData(equipment)
    setIsEditEquipmentOpen(true)
  }

  // 处理保存队伍编辑
  const handleSaveTeamEdit = () => {
    if (!selectedTeam || !teamFormData) return

    const updatedTeams = rescueTeams.map(team =>
      team.id === selectedTeam.id ? { ...team, ...teamFormData } : team
    )
    setRescueTeams(updatedTeams)
    setIsEditTeamOpen(false)
    setSelectedTeam(null)
    setTeamFormData({})
    showMessage("success", "修改成功")
  }

  // 处理保存装备编辑
  const handleSaveEquipmentEdit = () => {
    if (!selectedEquipment || !equipmentFormData) return

    const updatedEquipments = rescueEquipments.map(equipment =>
      equipment.id === selectedEquipment.id ? { ...equipment, ...equipmentFormData } : equipment
    )
    setRescueEquipments(updatedEquipments)
    setIsEditEquipmentOpen(false)
    setSelectedEquipment(null)
    setEquipmentFormData({})
    showMessage("success", "修改成功")
  }

  // 处理删除队伍
  const handleDeleteTeam = (id: string) => {
    setDeleteTeamId(id)
    setIsDeleteTeamDialogOpen(true)
  }

  // 确认删除队伍
  const confirmDeleteTeam = () => {
    const newTeams = rescueTeams.filter(team => team.id !== deleteTeamId)
    setRescueTeams(newTeams)
    setIsDeleteTeamDialogOpen(false)
    showMessage("success", "队伍已删除")
  }

  // 处理删除装备
  const handleDeleteEquipment = (id: string) => {
    setDeleteEquipmentId(id)
    setIsDeleteEquipmentDialogOpen(true)
  }

  // 确认删除装备
  const confirmDeleteEquipment = () => {
    const newEquipments = rescueEquipments.filter(equipment => equipment.id !== deleteEquipmentId)
    setRescueEquipments(newEquipments)
    setIsDeleteEquipmentDialogOpen(false)
    showMessage("success", "装备已删除")
  }

  // 处理批量删除队伍
  const handleBatchDeleteTeams = () => {
    setIsDeleteTeamDialogOpen(true)
  }

  // 确认批量删除队伍
  const confirmBatchDeleteTeams = () => {
    setRescueTeams(rescueTeams.filter(team => !selectedTeamKeys.includes(team.id)))
    setSelectedTeamKeys([])
    showMessage("success", "批量删除成功")
    setIsDeleteTeamDialogOpen(false)
  }

  // 处理批量删除装备
  const handleBatchDeleteEquipments = () => {
    setIsDeleteEquipmentDialogOpen(true)
  }

  // 确认批量删除装备
  const confirmBatchDeleteEquipments = () => {
    setRescueEquipments(rescueEquipments.filter(equipment => !selectedEquipmentKeys.includes(equipment.id)))
    setSelectedEquipmentKeys([])
    showMessage("success", "批量删除成功")
    setIsDeleteEquipmentDialogOpen(false)
  }

  // 处理添加队伍
  const handleAddTeam = () => {
    if (!teamFormData.name || !teamFormData.type || !teamFormData.leader || !teamFormData.members) {
      showMessage("error", "请填写必填项")
      return
    }

    const newTeam: RescueTeam = {
      id: Date.now().toString(),
      ...teamFormData as RescueTeam,
      status: teamFormData.status || "待命",
      disabled: false,
    }

    setRescueTeams([...rescueTeams, newTeam])
    setIsAddTeamOpen(false)
    setTeamFormData({})
    showMessage("success", "添加成功")
  }

  // 处理添加装备
  const handleAddEquipment = () => {
    if (!equipmentFormData.name || !equipmentFormData.type || !equipmentFormData.model || !equipmentFormData.quantity) {
      showMessage("error", "请填写必填项")
      return
    }

    const newEquipment: RescueEquipment = {
      id: Date.now().toString(),
      ...equipmentFormData as RescueEquipment,
      status: equipmentFormData.status || "正常",
      disabled: false,
    }

    setRescueEquipments([...rescueEquipments, newEquipment])
    setIsAddEquipmentOpen(false)
    setEquipmentFormData({})
    showMessage("success", "添加成功")
  }

  // 处理搜索和筛选
  const filteredTeams = rescueTeams.filter(team => {
    const matchSearch = team.name.toLowerCase().includes(searchTeamText.toLowerCase()) ||
      team.leader.toLowerCase().includes(searchTeamText.toLowerCase()) ||
      team.location.toLowerCase().includes(searchTeamText.toLowerCase())
    
    const matchType = selectedTeamTypes.length === 0 || selectedTeamTypes.includes(team.type)

    return matchSearch && matchType
  })

  const filteredEquipments = rescueEquipments.filter(equipment => {
    const matchSearch = equipment.name.toLowerCase().includes(searchEquipmentText.toLowerCase()) ||
      equipment.model.toLowerCase().includes(searchEquipmentText.toLowerCase()) ||
      equipment.location.toLowerCase().includes(searchEquipmentText.toLowerCase())
    
    const matchType = selectedEquipmentTypes.length === 0 || selectedEquipmentTypes.includes(equipment.type)
    const matchStatus = selectedEquipmentStatus.length === 0 || selectedEquipmentStatus.includes(equipment.status)

    return matchSearch && matchType && matchStatus
  })

  // 渲染统计抽屉
  const renderStatsDrawer = () => (
    <Sheet open={isStatsDrawerOpen} onOpenChange={setIsStatsDrawerOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <BarChart2 className="h-5 w-5" />
              救援力量统计分析
            </div>
          </SheetTitle>
          <SheetDescription>查看救援队伍和装备的统计数据</SheetDescription>
        </SheetHeader>
        <div className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>救援队伍状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">待命</span>
                    <span className="text-sm text-muted-foreground">{statistics.teams.standby}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-green-500"
                      style={{ width: `${(statistics.teams.standby / statistics.teams.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">出勤中</span>
                    <span className="text-sm text-muted-foreground">{statistics.teams.dispatched}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-blue-500"
                      style={{ width: `${(statistics.teams.dispatched / statistics.teams.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">训练中</span>
                    <span className="text-sm text-muted-foreground">{statistics.teams.training}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-yellow-500"
                      style={{ width: `${(statistics.teams.training / statistics.teams.total) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>人员构成</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">专职人员</p>
                          <h3 className="text-2xl font-bold mt-1">{statistics.members.fullTime}</h3>
                        </div>
                        <div className="rounded-full bg-blue-100 p-2">
                          <UserCheck className="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium">兼职人员</p>
                          <h3 className="text-2xl font-bold mt-1">{statistics.members.partTime}</h3>
                        </div>
                        <div className="rounded-full bg-green-100 p-2">
                          <Users className="h-4 w-4 text-green-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>装备状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">正常</span>
                    <span className="text-sm text-muted-foreground">{statistics.equipment.normal}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-green-500"
                      style={{ width: `${(statistics.equipment.normal / statistics.equipment.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">需检查</span>
                    <span className="text-sm text-muted-foreground">{statistics.equipment.needCheck}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-yellow-500"
                      style={{ width: `${(statistics.equipment.needCheck / statistics.equipment.total) * 100}%` }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">维修中</span>
                    <span className="text-sm text-muted-foreground">{statistics.equipment.repairing}</span>
                  </div>
                  <div className="h-2 rounded-full bg-gray-100">
                    <div
                      className="h-full rounded-full bg-red-500"
                      style={{ width: `${(statistics.equipment.repairing / statistics.equipment.total) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  )

  // 渲染查看队伍详情抽屉
  const renderViewTeamDrawer = () => (
    <Sheet open={isViewTeamOpen} onOpenChange={setIsViewTeamOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              队伍详情
            </div>
          </SheetTitle>
          <SheetDescription>查看救援队伍的详细信息</SheetDescription>
        </SheetHeader>
        {selectedTeam && (
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>队伍名称</Label>
                <p className="mt-1">{selectedTeam.name}</p>
              </div>
              <div>
                <Label>队伍类型</Label>
                <p className="mt-1">{selectedTeam.type}</p>
              </div>
              <div>
                <Label>队长</Label>
                <p className="mt-1">{selectedTeam.leader}</p>
              </div>
              <div>
                <Label>人数</Label>
                <p className="mt-1">{selectedTeam.members} 人</p>
              </div>
              <div>
                <Label>驻地位置</Label>
                <p className="mt-1">{selectedTeam.location}</p>
              </div>
              <div>
                <Label>联系电话</Label>
                <p className="mt-1">{selectedTeam.phone}</p>
              </div>
              <div>
                <Label>状态</Label>
                <p className="mt-1">
                  <Badge
                    variant={
                      selectedTeam.status === "待命"
                        ? "default"
                        : selectedTeam.status === "出勤中"
                          ? "secondary"
                          : "outline"
                    }
                  >
                    {selectedTeam.status}
                  </Badge>
                </p>
              </div>
              <div>
                <Label>上次演练</Label>
                <p className="mt-1">{selectedTeam.lastDrillDate}</p>
              </div>
              <div>
                <Label>下次演练</Label>
                <p className="mt-1">{selectedTeam.nextDrillDate}</p>
              </div>
            </div>

            {selectedTeam.specialties && (
              <div>
                <Label>专业特长</Label>
                <p className="mt-1 text-sm text-muted-foreground">{selectedTeam.specialties}</p>
              </div>
            )}

            {selectedTeam.equipment && (
              <div>
                <Label>配备装备</Label>
                <p className="mt-1 text-sm text-muted-foreground">{selectedTeam.equipment}</p>
              </div>
            )}

            {selectedTeam.remarks && (
              <div>
                <Label>备注</Label>
                <p className="mt-1 text-sm text-muted-foreground">{selectedTeam.remarks}</p>
              </div>
            )}
          </div>
        )}
      </SheetContent>
    </Sheet>
  )

  // 渲染查看装备详情抽屉
  const renderViewEquipmentDrawer = () => (
    <Sheet open={isViewEquipmentOpen} onOpenChange={setIsViewEquipmentOpen}>
      <SheetContent className="w-[600px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              装备详情
            </div>
          </SheetTitle>
          <SheetDescription>查看救援装备的详细信息</SheetDescription>
        </SheetHeader>
        {selectedEquipment && (
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>装备名称</Label>
                <p className="mt-1">{selectedEquipment.name}</p>
              </div>
              <div>
                <Label>装备类型</Label>
                <p className="mt-1">{selectedEquipment.type}</p>
              </div>
              <div>
                <Label>型号规格</Label>
                <p className="mt-1">{selectedEquipment.model}</p>
              </div>
              <div>
                <Label>数量</Label>
                <p className="mt-1">{selectedEquipment.quantity}</p>
              </div>
              <div>
                <Label>存放位置</Label>
                <p className="mt-1">{selectedEquipment.location}</p>
              </div>
              <div>
                <Label>状态</Label>
                <p className="mt-1">
                  <Badge
                    variant={
                      selectedEquipment.status === "正常"
                        ? "default"
                        : selectedEquipment.status === "需检查"
                          ? "secondary"
                          : "outline"
                    }
                  >
                    {selectedEquipment.status}
                  </Badge>
                </p>
              </div>
              <div>
                <Label>上次维护</Label>
                <p className="mt-1">{selectedEquipment.lastMaintenanceDate}</p>
              </div>
              <div>
                <Label>下次维护</Label>
                <p className="mt-1">{selectedEquipment.nextMaintenanceDate}</p>
              </div>
              <div>
                <Label>责任人</Label>
                <p className="mt-1">{selectedEquipment.responsible}</p>
              </div>
            </div>

            {selectedEquipment.remarks && (
              <div>
                <Label>备注</Label>
                <p className="mt-1 text-sm text-muted-foreground">{selectedEquipment.remarks}</p>
              </div>
            )}
          </div>
        )}
      </SheetContent>
    </Sheet>
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">救援力量管理</h2>
          <p className="text-muted-foreground">管理和维护救援队伍与装备信息，提高应急响应能力</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsStatsDrawerOpen(true)}>
            <BarChart2 className="h-4 w-4 mr-2" />
            统计分析
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportExcel}>
            <Download className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-blue-100 p-3 mb-4">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold">4</h3>
            <p className="text-sm text-muted-foreground">救援队伍</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-green-100 p-3 mb-4">
              <UserCheck className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold">41</h3>
            <p className="text-sm text-muted-foreground">救援人员</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 flex flex-col items-center">
            <div className="rounded-full bg-amber-100 p-3 mb-4">
              <Shield className="h-6 w-6 text-amber-600" />
            </div>
            <h3 className="text-xl font-bold">31</h3>
            <p className="text-sm text-muted-foreground">救援装备</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="teams">
        <TabsList className="w-full">
          <TabsTrigger value="teams" className="flex-1">
            救援队伍
          </TabsTrigger>
          <TabsTrigger value="equipment" className="flex-1">
            救援装备
          </TabsTrigger>
        </TabsList>
        <TabsContent value="teams" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>救援队伍列表</CardTitle>
                  <CardDescription>管理矿山救援队伍信息</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input type="search" placeholder="搜索队伍..." className="pl-8 w-[250px]" />
                    </div>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="队伍类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有类型</SelectItem>
                        <SelectItem value="full-time">专职救护队</SelectItem>
                        <SelectItem value="part-time">兼职救援队</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog open={isAddTeamOpen} onOpenChange={setIsAddTeamOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          添加队伍
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle>添加救援队伍</DialogTitle>
                          <DialogDescription>添加新的救援队伍信息</DialogDescription>
                        </DialogHeader>
                        <Tabs defaultValue="basic" className="mt-4">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="basic">基本信息</TabsTrigger>
                            <TabsTrigger value="members">队伍成员</TabsTrigger>
                          </TabsList>
                          <TabsContent value="basic" className="space-y-4 mt-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="team-name">队伍名称</Label>
                                <Input id="team-name" placeholder="请输入队伍名称" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-type">队伍类型</Label>
                                <Select>
                                  <SelectTrigger id="team-type">
                                    <SelectValue placeholder="选择队伍类型" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="full-time">专职救护队</SelectItem>
                                    <SelectItem value="part-time">兼职救援队</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-leader">队长</Label>
                                <Input id="team-leader" placeholder="请输入队长姓名" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-members">队员人数</Label>
                                <Input id="team-members" type="number" min="1" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-location">驻地位置</Label>
                                <Input id="team-location" placeholder="请输入驻地位置" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-phone">联系电话</Label>
                                <Input id="team-phone" placeholder="请输入联系电话" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-status">状态</Label>
                                <Select>
                                  <SelectTrigger id="team-status">
                                    <SelectValue placeholder="选择状态" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="standby">待命</SelectItem>
                                    <SelectItem value="dispatched">出勤中</SelectItem>
                                    <SelectItem value="training">训练中</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </TabsContent>
                          <TabsContent value="members" className="space-y-4 mt-4">
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label htmlFor="team-last-drill">上次演练日期</Label>
                                <Input id="team-last-drill" type="date" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-next-drill">下次演练日期</Label>
                                <Input id="team-next-drill" type="date" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-specialties">专业特长</Label>
                                <Textarea id="team-specialties" placeholder="请输入队伍专业特长" rows={3} />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-equipment">配备装备</Label>
                                <Textarea id="team-equipment" placeholder="请输入配备装备" rows={3} />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="team-remarks">备注</Label>
                                <Textarea id="team-remarks" placeholder="请输入备注信息" rows={3} />
                              </div>
                            </div>
                          </TabsContent>
                        </Tabs>
                        <DialogFooter className="mt-6">
                          <Button variant="outline" onClick={() => setIsAddTeamOpen(false)}>
                            取消
                          </Button>
                          <Button onClick={() => setIsAddTeamOpen(false)}>保存</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>队伍名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>队长</TableHead>
                        <TableHead>人数</TableHead>
                        <TableHead>驻地位置</TableHead>
                        <TableHead>联系电话</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>上次演练</TableHead>
                        <TableHead>下次演练</TableHead>
                        <TableHead className="w-24">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTeams.map((team) => (
                        <TableRow key={team.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              <Shield className="h-4 w-4 mr-2 text-blue-500" />
                              <span>{team.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>{team.type}</TableCell>
                          <TableCell>{team.leader}</TableCell>
                          <TableCell>{team.members}</TableCell>
                          <TableCell>{team.location}</TableCell>
                          <TableCell>{team.phone}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                team.status === "待命" ? "default" : team.status === "出勤中" ? "secondary" : "outline"
                              }
                            >
                              {team.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{team.lastDrillDate}</TableCell>
                          <TableCell>{team.nextDrillDate}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="icon">
                                <Phone className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <FileText className="h-4 w-4 mr-2" />
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    编辑
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Users className="h-4 w-4 mr-2" />
                                    队员管理
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Calendar className="h-4 w-4 mr-2" />
                                    演练记录
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">共 {filteredTeams.length} 条记录</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  上一页
                </Button>
                <Button variant="outline" size="sm" className="px-3">
                  1
                </Button>
                <Button variant="outline" size="sm" disabled>
                  下一页
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="equipment" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>救援装备列表</CardTitle>
                  <CardDescription>管理矿山救援装备信息</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input type="search" placeholder="搜索装备..." className="pl-8 w-[250px]" />
                    </div>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="装备类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有类型</SelectItem>
                        <SelectItem value="vehicle">车辆</SelectItem>
                        <SelectItem value="breathing">呼吸器材</SelectItem>
                        <SelectItem value="tool">救援工具</SelectItem>
                        <SelectItem value="detection">检测设备</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="装备状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有状态</SelectItem>
                        <SelectItem value="normal">正常</SelectItem>
                        <SelectItem value="check">需检查</SelectItem>
                        <SelectItem value="repair">维修中</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog open={isAddEquipmentOpen} onOpenChange={setIsAddEquipmentOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          添加装备
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle>添加救援装备</DialogTitle>
                          <DialogDescription>添加新的救援装备信息</DialogDescription>
                        </DialogHeader>
                        <Tabs defaultValue="basic" className="mt-4">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="basic">基本信息</TabsTrigger>
                            <TabsTrigger value="maintenance">维护信息</TabsTrigger>
                          </TabsList>
                          <TabsContent value="basic" className="space-y-4 mt-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="equipment-name">装备名称</Label>
                                <Input id="equipment-name" placeholder="请输入装备名称" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-type">装备类型</Label>
                                <Select>
                                  <SelectTrigger id="equipment-type">
                                    <SelectValue placeholder="选择装备类型" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="vehicle">车辆</SelectItem>
                                    <SelectItem value="breathing">呼吸器材</SelectItem>
                                    <SelectItem value="tool">救援工具</SelectItem>
                                    <SelectItem value="detection">检测设备</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-model">型号规格</Label>
                                <Input id="equipment-model" placeholder="请输入型号规格" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-quantity">数量</Label>
                                <Input id="equipment-quantity" type="number" min="1" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-location">存放位置</Label>
                                <Input id="equipment-location" placeholder="请输入存放位置" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-status">状态</Label>
                                <Select>
                                  <SelectTrigger id="equipment-status">
                                    <SelectValue placeholder="选择状态" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="normal">正常</SelectItem>
                                    <SelectItem value="check">需检查</SelectItem>
                                    <SelectItem value="repair">维修中</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </TabsContent>
                          <TabsContent value="maintenance" className="space-y-4 mt-4">
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label htmlFor="equipment-last-maintenance">上次维护日期</Label>
                                <Input id="equipment-last-maintenance" type="date" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-next-maintenance">下次维护日期</Label>
                                <Input id="equipment-next-maintenance" type="date" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-responsible">责任人</Label>
                                <Input id="equipment-responsible" placeholder="请输入责任人" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="equipment-remarks">备注</Label>
                                <Textarea id="equipment-remarks" placeholder="请输入备注信息" rows={3} />
                              </div>
                            </div>
                          </TabsContent>
                        </Tabs>
                        <DialogFooter className="mt-6">
                          <Button variant="outline" onClick={() => setIsAddEquipmentOpen(false)}>
                            取消
                          </Button>
                          <Button onClick={() => setIsAddEquipmentOpen(false)}>保存</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>装备名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>型号</TableHead>
                        <TableHead>数量</TableHead>
                        <TableHead>存放位置</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>上次维护</TableHead>
                        <TableHead>下次维护</TableHead>
                        <TableHead>责任人</TableHead>
                        <TableHead className="w-24">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEquipments.map((equipment) => (
                        <TableRow key={equipment.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              {equipment.type === "车辆" ? (
                                <Truck className="h-4 w-4 mr-2 text-blue-500" />
                              ) : equipment.type === "呼吸器材" ? (
                                <Headphones className="h-4 w-4 mr-2 text-green-500" />
                              ) : (
                                <Shield className="h-4 w-4 mr-2 text-amber-500" />
                              )}
                              <span>{equipment.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>{equipment.type}</TableCell>
                          <TableCell>{equipment.model}</TableCell>
                          <TableCell>{equipment.quantity}</TableCell>
                          <TableCell>{equipment.location}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                equipment.status === "正常"
                                  ? "default"
                                  : equipment.status === "需检查"
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {equipment.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{equipment.lastMaintenanceDate}</TableCell>
                          <TableCell>{equipment.nextMaintenanceDate}</TableCell>
                          <TableCell>{equipment.responsible}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="icon">
                                <FileText className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <FileText className="h-4 w-4 mr-2" />
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    编辑
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Calendar className="h-4 w-4 mr-2" />
                                    维护记录
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    删除
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">共 {filteredEquipments.length} 条记录</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  上一页
                </Button>
                <Button variant="outline" size="sm" className="px-3">
                  1
                </Button>
                <Button variant="outline" size="sm" disabled>
                  下一页
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加统计抽屉 */}
      {renderStatsDrawer()}

      {/* 添加查看详情抽屉 */}
      {renderViewTeamDrawer()}
      {renderViewEquipmentDrawer()}

      {/* 添加删除确认对话框 */}
      <AlertDialog open={isDeleteTeamDialogOpen} onOpenChange={setIsDeleteTeamDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>删除确认</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedTeamKeys.length > 0
                ? `确定要删除选中的 ${selectedTeamKeys.length} 个队伍吗？`
                : "确定要删除该队伍吗？"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={selectedTeamKeys.length > 0 ? confirmBatchDeleteTeams : confirmDeleteTeam}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isDeleteEquipmentDialogOpen} onOpenChange={setIsDeleteEquipmentDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>删除确认</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedEquipmentKeys.length > 0
                ? `确定要删除选中的 ${selectedEquipmentKeys.length} 个装备吗？`
                : "确定要删除该装备吗？"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={selectedEquipmentKeys.length > 0 ? confirmBatchDeleteEquipments : confirmDeleteEquipment}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

